package ouc.isclab.dataset.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.dataset.entity.DatasetEntity;
import ouc.isclab.dataset.pojo.DatasetInfo;
import ouc.isclab.dataset.pojo.DatasetDetailDTO;
import ouc.isclab.dataset.service.DatasetService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys/dataset")
public class DatasetController {

    @Autowired
    private DatasetService datasetService;

    /**
     * 上传文件类型的数据集
     */
    @PostMapping("/upload/file")
    public DatasetEntity uploadFileDataset(
            @RequestParam("file") MultipartFile file,
            @RequestParam("name") String name,
            @RequestParam("description") String description,
            @RequestParam("mockData") String mockData,
            @RequestParam(value = "nodeIds", required = false) List<Long> nodeIds,
            @CurrentUserId Long userId
    ) {
        DatasetInfo datasetInfo = new DatasetInfo();
        datasetInfo.setName(name);
        datasetInfo.setDescription(description);
        datasetInfo.setMockData(mockData);
        datasetInfo.setNodeIds(nodeIds);
        
        log.info("Uploading file dataset: {}", datasetInfo);
        return datasetService.uploadFileDataset(file, datasetInfo, userId);
    }

    /**
     * 上传URL类型的数据集
     */
    @PostMapping("/upload/url")
    public DatasetEntity uploadUrlDataset(
            @RequestBody DatasetInfo datasetInfo,
            @CurrentUserId Long userId
    ) {
        log.info("Uploading URL dataset: {}", datasetInfo);
        return datasetService.uploadUrlDataset(datasetInfo, userId);
    }

    /**
     * 获取所有数据集
     */
    @GetMapping
    public Map<String, Object> listDatasets(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<DatasetEntity> datasetPage = datasetService.getAllDatasets(pageable);
        
        Map<String, Object> data = new HashMap<>();
        data.put("datasets", datasetPage.getContent());
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", datasetPage.getTotalElements());
        data.put("pagination", pagination);
        
        return data;
    }

    /**
     * 获取用户的数据集
     */
    @GetMapping("/my")
    public Map<String, Object> listMyDatasets(
            @CurrentUserId Long userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<DatasetEntity> datasetPage = datasetService.getUserDatasets(userId, pageable);
        
        Map<String, Object> data = new HashMap<>();
        data.put("datasets", datasetPage.getContent());
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", datasetPage.getTotalElements());
        data.put("pagination", pagination);
        
        return data;
    }

    /**
     * 更新数据集信息
     */
    @PutMapping("/{id}")
    public DatasetEntity updateDataset(
            @PathVariable Long id,
            @RequestBody DatasetInfo datasetInfo,
            @CurrentUserId Long userId
    ) {
        return datasetService.updateDataset(id, datasetInfo, userId);
    }

    /**
     * 删除数据集
     */
    @DeleteMapping("/{id}")
    public void deleteDataset(
            @PathVariable Long id,
            @CurrentUserId Long userId
    ) {
        datasetService.deleteDataset(id, userId);
    }

    /**
     * 批量删除数据集
     */
    @DeleteMapping("/batch")
    public void batchDeleteDatasets(
            @RequestParam List<Long> ids,
            @CurrentUserId Long userId
    ) {
        datasetService.batchDeleteDatasets(ids, userId);
    }

    /**
     * 获取数据集统计信息
     */
    @GetMapping("/statistics")
    public Map<String, Object> getDatasetStatistics() {
        return datasetService.getDatasetStatistics();
    }

    /**
     * 获取数据集详情
     */
    @GetMapping("/{id}")
    public DatasetDetailDTO getDatasetDetail(@PathVariable Long id) {
        return datasetService.getDatasetDetail(id);
    }

    /**
     * 获取数据集的分享链接
     */
    @GetMapping("/{id}/share")
    public Object getDatasetShareLink(
            @PathVariable Long id,
            @RequestParam(required = false) Integer expirySeconds
    ) {
        return datasetService.getDatasetShareLink(id, expirySeconds);
    }
} 