package ouc.isclab.task.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;
import ouc.isclab.node.entity.NodeEntity;

/**
 * 节点账号申请实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_NODE_ACCOUNT_REQUEST")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class NodeAccountRequestEntity extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "node_id", nullable = false)
    private NodeEntity node; // 申请的节点
    
    @Column(name = "applicant_id", nullable = false)
    private Long applicantId; // 申请人ID
    
    @Column(nullable = false)
    private String username; // 申请的用户名
    
    @Column(nullable = false)
    private String reason; // 申请理由
    
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ApprovalStatus status; // 审批状态
    
    @Column
    private String rejectReason; // 拒绝理由
    
    @Column
    private Long approverId; // 审批人ID
    
    @Column
    private String nodeUsername; // 节点上创建的用户名
    
    @Column
    private String nodePassword; // 节点上创建的密码
    
    public enum ApprovalStatus {
        PENDING,    // 待审批
        APPROVED,   // 已批准
        REJECTED    // 已拒绝
    }
} 