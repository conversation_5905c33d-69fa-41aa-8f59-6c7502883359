package ouc.isclab.task.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;
import ouc.isclab.node.entity.NodeEntity;

/**
 * 代码实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_CODE")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class CodeEntity extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "node_id", nullable = false)
    private NodeEntity node; // 关联的节点

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private NodeType nodeType = NodeType.Sycee; // 节点类型，默认为SYCEE类型

    @Column
    private String description; // 代码描述

    @Column(nullable = false)
    private Long creatorId; // 创建者ID

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ApprovalStatus status; // 审批状态

    @Column
    private String rejectReason; // 拒绝理由

    @Column
    private Long approverId; // 审批人ID

    // Sycee类型特有字段
    @Column
    private String funcName; // 函数名称

    @Column(columnDefinition = "TEXT")
    private String codeContent; // 代码内容

    @Column
    private String syceeCodeId; // Sycee系统中的代码ID

    @Column
    private String syceeRequestId; // Sycee系统中的请求ID

    @Column
    private String syceeJobId; // Sycee系统中的任务ID

    @Column(columnDefinition = "TEXT")
    private String inputKwargs; // 函数输入参数列表，以JSON格式存储

    // Pyxis类型特有字段
    @Column(columnDefinition = "TEXT")
    private String savedFiles; // 保存的文件，JSON格式

    @Column
    private String pyxisTaskId; // Pyxis系统中的任务ID

    @Column
    private Boolean executed = false; // 标记任务是否已执行过

    public enum ApprovalStatus {
        PENDING,    // 待审批
        APPROVED,   // 已批准
        REJECTED,    // 已拒绝
        RUNNING,
        COMPLETED,
        ERROR
    }
    
    public enum NodeType {
        Sycee,    // Sycee类型
        Pyxis     // Pyxis类型
    }
}