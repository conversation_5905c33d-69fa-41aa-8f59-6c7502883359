package ouc.isclab.storage.pojo;

import lombok.Data;
import lombok.Builder;

@Data
@Builder
public class FileItemDTO {
    private String name;           // 文件名
    private String path;           // 完整路径
    private String url;            // 访问URL
    private Long size;             // 文件大小(字节)
    private String directory;      // 所在目录
    private String lastModified;   // 最后修改时间
    private String description;    // 文件描述
    private Boolean isDirectory;
} 