package ouc.isclab.node.service;

import java.util.ArrayList;
import java.util.Date;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.entity.NodeResourceEntity;
import ouc.isclab.node.repository.NodeRepository;
import ouc.isclab.node.repository.NodeResourceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
@Slf4j
@Service
public class NodeResourceService {

    @Autowired
    private NodeResourceRepository nodeResourceRepository;

    @Autowired
    private NodeRepository nodeRepository;

    @Autowired
    private RestTemplate restTemplate;  // 注入 RestTemplate

    private static final String NODE_CHECK_URL_TEMPLATE = "http://%s:%d";  // 节点检查的 URL 模板

    /**
     *  更新节点状态
     */
    public NodeResourceEntity updateNodeResource(long nodeId) {
        // 查找节点信息，若节点不存在则抛出异常
        NodeEntity nodeEntity = nodeRepository.findById(nodeId)
                .orElseThrow(() -> new RuntimeException("Node not found"));

        // 获取节点的最新资源记录，如果不存在则创建新的
        NodeResourceEntity nodeResourceEntity = nodeResourceRepository.findByNodeId(nodeId);
        if (nodeResourceEntity == null) {
            nodeResourceEntity = new NodeResourceEntity();
            nodeResourceEntity.setNode(nodeEntity);
            nodeResourceEntity.setTimeCreated(new Date());
            // 设置初始值
            nodeResourceEntity.setCpuUsage(0f);
            nodeResourceEntity.setMemoryUsage(0f);
            nodeResourceEntity.setBandwidthUsage(0f);
        }

        // 拼接检查的 URL
        String healthCheckUrl = String.format(NODE_CHECK_URL_TEMPLATE, nodeEntity.getIpAddress(), nodeEntity.getPort());
        log.info("Health check url: {}", healthCheckUrl);

        try {
            // 发送 HTTP 请求，检查节点状态
            ResponseEntity<String> response = restTemplate.exchange(
                    healthCheckUrl, HttpMethod.GET, null, String.class);

            // 如果返回状态为 200，更新为 ONLINE 并处理资源信息
            if (response.getStatusCode() == HttpStatus.OK) {
                nodeResourceEntity.setStatus(NodeResourceEntity.NodeStatus.ONLINE);
                log.info("Node resource status is ONLINE");

                String responseBody = response.getBody();
                if (responseBody != null) {
//                    ObjectMapper objectMapper = new ObjectMapper();
//                    JsonNode rootNode = objectMapper.readTree(responseBody);
//
//                    // 提取资源信息并更新
//                    nodeResourceEntity.setCpuUsage(rootNode.path("cpuUsage").floatValue());
//                    nodeResourceEntity.setMemoryUsage(rootNode.path("memoryUsage").floatValue());
//                    nodeResourceEntity.setBandwidthUsage(rootNode.path("bandwidthUsage").floatValue());
                }
            } else {
                nodeResourceEntity.setStatus(NodeResourceEntity.NodeStatus.ERROR);
            }
        } catch (HttpStatusCodeException ex) {
            // 捕获 HTTP 状态码异常，处理 404 为 OFFLINE
            if (ex.getStatusCode() == HttpStatus.NOT_FOUND) {
                nodeResourceEntity.setStatus(NodeResourceEntity.NodeStatus.OFFLINE);
            } else {
                nodeResourceEntity.setStatus(NodeResourceEntity.NodeStatus.ERROR);
            }
        } catch (Exception e) {
            // 捕获其他异常
            nodeResourceEntity.setStatus(NodeResourceEntity.NodeStatus.ERROR);
        }

        // 更新时间并保存记录
        nodeResourceEntity.setTimeUpdated(new Date());
        return nodeResourceRepository.save(nodeResourceEntity);
    }

    /**
     * 获取某个节点的资源记录
     */
    public NodeResourceEntity getNodeResources(Long nodeId) {
        return nodeResourceRepository.findByNodeId(nodeId);
    }

//    //获取最新记录
//    public NodeResourceEntity getLatestNodeResource(Long nodeId) {
//        Optional<NodeResourceEntity> latestResource = nodeResourceRepository.findTopByNodeIdOrderByTimeUpdatedDesc(nodeId);
//
//        // 处理 Optional
//        return latestResource.orElseThrow(() -> new RuntimeException("No resource data found for node with ID: " + nodeId));
//    }

    /**
     * 查看所有节点状态
     */
    public Page<NodeResourceEntity> getAllNodeResources(Pageable pageable) {
        return nodeResourceRepository.findAll(pageable);
    }

    /**
     * 检查所有节点的状态并更新其资源信息
     */
    public List<NodeResourceEntity> checkAllNodeStatuses() {
        List<NodeResourceEntity> updatedResources = new ArrayList<>();

        // 获取所有节点
        List<NodeEntity> allNodes = nodeRepository.findAll();

        // 遍历所有节点并检查状态
        for (NodeEntity nodeEntity : allNodes) {
            try {
                // 更新节点资源并将结果加入列表
                NodeResourceEntity updatedResource = updateNodeResource(nodeEntity.getId());
                updatedResources.add(updatedResource);  // 将更新后的资源信息添加到列表中
            } catch (Exception e) {
                // 处理可能发生的任何异常
                log.info("Error checking node status for node ID: " + nodeEntity.getId() + " - " + e.getMessage());
            }
        }
        return updatedResources;  // 返回所有更新后的节点资源列表
    }

    /**
     * 删除节点状态
     */
    @Transactional
    public void deleteNodeResources(Long nodeId) {
        nodeResourceRepository.deleteByNodeId(nodeId);
    }

    /**
     * 根据节点名称查找节点资源
     */
    public NodeResourceEntity findNodeResourceByNodeName(String name) {
        NodeEntity node = nodeRepository.findByName(name);
        if (node == null) {
            return null;
        }
        return nodeResourceRepository.findByNodeId(node.getId());
    }

    /**
     * 批量删除节点资源
     */
    @Transactional
    public void batchDeleteNodeResources(List<Long> nodeIds) {
        nodeResourceRepository.deleteByNodeIdIn(nodeIds);
    }

}

