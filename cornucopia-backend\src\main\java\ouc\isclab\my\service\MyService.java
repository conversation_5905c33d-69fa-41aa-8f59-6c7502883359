package ouc.isclab.my.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.system.repository.UserRepository;

@Service
public class MyService {
    @Autowired
    private UserRepository userRepository;

    /**
     * 更新用户基本信息
     */
    public UserEntity updateMyInfo(Long userId, String fullname, String email) {    
        UserEntity userEntity = userRepository.getUserEntityById(userId);
        userEntity.setFullname(fullname);
        userEntity.setEmail(email);
        
        return userRepository.save(userEntity);
    }

    /**
     * 更新当前用户密码
     */
    public UserEntity updateMyPassword(Long userId, String oldPassword, String newPassword) {
        UserEntity userEntity = userRepository.getUserEntityById(userId);
        if (userEntity == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "用户不存在");
        }
        
        // 验证旧密码
        if (!userEntity.getPassword().equals(oldPassword)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "原密码错误");
        }
        
        // 更新密码
        userEntity.setPassword(newPassword);
        return userRepository.save(userEntity);
    }
}
