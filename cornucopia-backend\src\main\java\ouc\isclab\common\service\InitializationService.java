package ouc.isclab.common.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.annotation.PostConstruct;
import ouc.isclab.system.repository.UserRepository;
import ouc.isclab.system.service.PermissionService;
import ouc.isclab.system.service.RoleService;
import ouc.isclab.system.service.UserService;
import ouc.isclab.system.repository.PermissionRepository;
import ouc.isclab.system.entity.PermissionEntity;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.system.repository.RoleRepository;
import ouc.isclab.system.entity.RoleEntity;

@Slf4j
@Service
public class InitializationService {

    @Autowired
    private RoleService roleService;

    @Autowired
    private UserService userService;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @PostConstruct
    @Transactional
    public void initialize() {
        try {
            // 打印 Cornucopia ASCII 艺术标志
            System.out.println(" $$$$$$\\                                                                        $$\\           ");
            System.out.println("$$  __$$\\                                                                       \\__|          ");
            System.out.println("$$ /  \\__| $$$$$$\\   $$$$$$\\  $$$$$$$\\  $$\\   $$\\  $$$$$$$\\  $$$$$$\\   $$$$$$\\  $$\\  $$$$$$\\  ");
            System.out.println("$$ |      $$  __$$\\ $$  __$$\\ $$  __$$\\ $$ |  $$ |$$  _____|$$  __$$\\ $$  __$$\\ $$ | \\____$$\\ ");
            System.out.println("$$ |      $$ /  $$ |$$ |  \\__|$$ |  $$ |$$ |  $$ |$$ /      $$ /  $$ |$$ /  $$ |$$ | $$$$$$$ |");
            System.out.println("$$ |  $$\\ $$ |  $$ |$$ |      $$ |  $$ |$$ |  $$ |$$ |      $$ |  $$ |$$ |  $$ |$$ |$$  __$$ |");
            System.out.println("\\$$$$$$  |\\$$$$$$  |$$ |      $$ |  $$ |\\$$$$$$  |\\$$$$$$$\\ \\$$$$$$  |$$$$$$$  |$$ |\\$$$$$$$ |");
            System.out.println(" \\______/  \\______/ \\__|      \\__|  \\__| \\______/  \\_______| \\______/ $$  ____/ \\__| \\_______|");
            System.out.println("                                                                      $$ |                    ");
            System.out.println("                                                                      $$ |                    ");
            System.out.println("                                                                      \\__|           (v.0.0.1)");
            // 1. 初始化权限
            initPermissions();
            
            // 2. 初始化默认角色
            initDefaultRole();
            
            // 3. 重置所有用户在线状态
            resetOnlineStatus();

        } catch (Exception e) {
            log.error("系统初始化失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "系统初始化失败: " + e.getMessage());
        }
    }

    /**
     * 清除在线状态
     */
    @Transactional
    public void resetOnlineStatus() {
        try {
            userRepository.resetAllUsersOffline();
        } catch (Exception e) {
            log.error("Failed to reset users' online status on startup", e);
        }
    }

    /**
     * 初始化默认角色
     */
    private void initDefaultRole() {
        try {
            if (roleRepository.count() == 0) {
                RoleEntity defaultRole = new RoleEntity();
                defaultRole.setId(1L);
                defaultRole.setName("USER");
                defaultRole.setDescription("默认用户角色");
                roleRepository.save(defaultRole);
                
                log.info("默认角色初始化完成");
            }
        } catch (Exception e) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "初始化默认角色失败");
        }
    }

    private void initPermissions() {
        if (permissionRepository.count() == 0) {
            // 日志相关权限
            createPermission("log:view", "查看日志", "查看系统操作日志的权限");
            createPermission("log:delete", "删除日志", "删除系统操作日志的权限");
            createPermission("log:manage", "日志管理", "日志管理相关的所有权限");

            //菜单相关权限
            createPermission("menu:user","查看用户菜单","前端显示用户菜单的权限");
            createPermission("menu:node","查看节点菜单","前端显示节点菜单的权限");
            createPermission("menu:node:my","查看我的节点二级菜单","前端显示我的节点二级菜单的权限");
            createPermission("menu:model","查看模型菜单","前端显示模型菜单的权限");
            createPermission("menu:task","查看任务菜单","前端显示任务菜单的权限");
            createPermission("menu:task:approve","查看任务审批二级菜单","前端显示任务审批二级菜单的权限");
            createPermission("menu:task:approveNode","查看节点任务审批三级菜单","前端显示节点任务审批三级菜单的权限");
            createPermission("menu:task:approveData","查看数据使用审批三级菜单","前端显示数据使用审批三级菜单的权限");
            createPermission("menu:data","查看数据菜单","前端显示数据菜单的权限");
            createPermission("menu:data:my","查看我的数据集二级菜单","前端显示我的数据集二级菜单的权限");
            createPermission("menu:data:minio","查看minio二级菜单","前端显示minio二级菜单的权限");
            createPermission("menu:log","查看日志菜单","前端显示日志菜单的权限");

            log.info("基础权限初始化完成");
        }
    }

    private void createPermission(String code, String name, String description) {
        PermissionEntity permission = new PermissionEntity();
        permission.setCode(code);
        permission.setName(name);
        permission.setDescription(description);
        permissionRepository.save(permission);
    }
} 