package ouc.isclab.node.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;

/**
 * @desc 节点认证信息实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_NODE_AUTH")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class NodeAuthEntity extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "node_id", nullable = false, unique = true)
    private NodeEntity node;  // 关联的节点

    @Column(nullable = false)
    private String username;  // 节点访问用户名

    @Column(nullable = false)
    private String password;  // 节点访问密码
} 