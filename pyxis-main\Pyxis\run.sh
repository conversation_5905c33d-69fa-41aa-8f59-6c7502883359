#!/bin/bash
set -e  # Exit immediately on error

# Configuration Variables
export MAIN_IMAGE="pyxis-core"
export WORKER_IMAGE="pyxis-worker"
export GATEWAY_IMAGE="pyxis-gateway"
export MAIN_PORT=20000
export AUTH_ENABLE="False"
export SECRET_KEY="pyxis-secret-key"
export HOST_DIR=$(pwd)

# Build all Docker images
build_images() {
  echo "=== BUILDING IMAGES ==="
  docker compose build
}

# Start the services
start_services() {
  echo "=== STARTING SERVICES ==="
  docker compose up -d
}

# Stop running containers
stop_services() {
  echo "=== STOPPING SERVICES ==="
  
  # Stop all task services (not managed by compose)
  # DO FIRST because of network using
  for container in $(docker ps -aq -f "name=pyxis_task_"); do
    docker stop $container || true
    docker rm $container || true
  done

  docker compose down
}

# Remove all resources
clean_all() {
  stop_services
  echo "=== CLEANING IMAGES ==="
  docker compose rm -sfv
  docker rmi $MAIN_IMAGE $WORKER_IMAGE $GATEWAY_IMAGE || true
  
  echo "=== CLEANING TEMP FILES ==="
  rm -rf temp_*
}

# Show usage information
show_help() {
  cat << EOF
Usage: $0 [COMMAND]

Available Commands:
  build      Only build images
  start      Start all services
  stop       Stop all services
  clean      Remove all resources (containers, images)
  help       Show this help message

Default behavior (no arguments): Build and start services
EOF
}

# Main execution
case "$1" in
  build)
    build_images
    ;;
  start)
    start_services
    ;;
  stop)
    stop_services
    ;;
  clean)
    clean_all
    ;;
  help|--help|-h)
    show_help
    ;;
  *)
    # Default behavior: build and start
    build_images
    start_services
    ;;
esac

echo "=== OPERATION COMPLETE ==="