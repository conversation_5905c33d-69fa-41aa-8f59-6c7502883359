
from typing import Dict, Optional
import threading


class KeyValueStash:
    """A thread-safe key-value storage implementation using fine-grained RLock.

    Attributes:
        _store: The dictionary holding key-value pairs
        _key_locks: Dictionary of RLocks for each key
        _locks_lock: RLock for protecting the _key_locks dictionary
    """

    def __init__(self):
        """Initialize the key-value store with empty storage and lock structures."""
        self._store: Dict[str, any] = {}
        # Dictionary to store individual RLock for each key
        self._key_locks: Dict[str, threading.RLock] = {}
        # RLock to protect access to the _key_locks dictionary
        self._locks_lock = threading.RLock()

    def _get_key_lock(self, key: str) -> threading.RLock:
        """Get or create an RLock for the specified key.

        Args:
            key: The key to get the lock for

        Returns:
            The RLock associated with the specified key
        """
        with self._locks_lock:
            if key not in self._key_locks:
                self._key_locks[key] = threading.RLock()
            return self._key_locks[key]

    def set(self, key: str, value: any) -> None:
        """Set a key-value pair in the store.

        Args:
            key: The key to set
            value: The value to associate with the key
        """
        key_lock = self._get_key_lock(key)
        with key_lock:
            self._store[key] = value

    def get(self, key: str) -> Optional[str]:
        """Get the value associated with a key.

        Args:
            key: The key to look up

        Returns:
            The value associated with the key, or None if the key doesn't exist
        """
        key_lock = self._get_key_lock(key)
        with key_lock:
            return self._store.get(key)

    def delete(self, key: str) -> bool:
        """Delete a key-value pair from the store.

        Args:
            key: The key to delete

        Returns:
            True if the key was found and deleted, False otherwise
        """
        key_lock = self._get_key_lock(key)
        with key_lock:
            if key in self._store:
                del self._store[key]
                # Clean up unused locks
                with self._locks_lock:
                    if key in self._key_locks and key not in self._store:
                        del self._key_locks[key]
                return True
            return False

    def keys(self) -> list:
        """Get a list of all keys in the store.

        Returns:
            A list of all keys currently in the store
        """
        # Safe to read without lock due to Python's GIL and atomic dictionary iteration
        return list(self._store.keys())

    def __len__(self) -> int:
        """Get the number of key-value pairs in the store.

        Returns:
            The number of items in the store
        """
        return len(self._store)
