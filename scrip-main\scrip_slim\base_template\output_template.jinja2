{% macro render_field(field) %}
<div class="field field-{{ field.type }}">
    {% if "name" in field %}

    {{'{% if result is defined and "'+ field.name+'" in result %}'}}

    {% if field.label %}<label class="field-label">{{ field.label }}</label>{% endif %}

    {% if field.type == 'text' %}
    <div class="field-value text-value">{{ '{{ result.' + field.name + ' }}' }}</div>

    {% elif field.type == 'list' %}
    <ul class="field-value list-value">
        {{ '{% for item in result.' + field.name + ' %}' }}
        <li>{{ '{{ item }}' }}</li>
        {{ '{% endfor %}' }}
    </ul>

    {% elif field.type == 'json' %}
    <pre class="field-value json-value">{{ '{{ result.' + field.name + ' | tojson(indent=2) }}' }}</pre>

    {% elif field.type == 'img' %}
    <div class="field-value img-value" style="text-align: center;">
        <img src="{{ '{{ result.' + field.name + ' }}' }}" alt="{{ field.name  }} image"
            style="max-width: 100%; display:inline-block;">
    </div>

    {% elif field.type == 'svg' %}
    <div class="field-value svg-value" style="max-width:100%;margin:0 auto;">
        {{ '{{ result.' + field.name + ' | safe }}' }}
    </div>

    {% elif field.type == 'html' %}
    <div class="field-value html-value">{{ '{{ result.' + field.name + ' | safe }}' }}</div>

    {% elif field.type == 'table' %}
    <div class="field-value table-value table-wrapper">
        <table class="data-table">
            <thead>
                <tr>
                    {% for header in field.headers %}
                    <th>{{ header }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {{ '{% for row in result.' + field.name + ' %}' }}
                <tr>
                    {% for header in field.headers %}
                    <td data-label="{{ header }}">
                        {{ '{% if row is mapping %}' }}
                        {{ '{{ row["' + header + '"] }}' }}
                        {{ '{% else %}' }}
                        {{ '{{ row[' + loop.index0|string + '] }}' }}
                        {{ '{% endif %}' }}
                    </td>
                    {% endfor %}
                </tr>
                {{ '{% endfor %}' }}
            </tbody>
        </table>
    </div>

    {% else %}
    <div class="field-value unknown-value">
        <pre class="pre-wrap">{{ '{{ result.' + field.name + ' }}' }}</pre>
    </div>
    {% endif %}

    {{
    '{% else %}
    <div class="field-value missing-value-'+field.name+'" style="color:red;">missing value: '+field.name+'</div>
    {% endif %}'
    }}

    {%else%}
    <div class="default-results" title="no name in field,using default">
        <div class="result-value">
            {{ '{% if result is string %}' }}
            <div class="pre-wrap">{{ '{{ result }}' }}</div>
            {{ '{% else %}' }}
            <pre class="pre-wrap">{{ '{{ result | tojson(indent=4) }}' }}</pre>
            {{ '{% endif %}' }}
        </div>
    </div>
    {%endif%}

</div>
{% endmacro %}


<div class="results">
    <h2>{{ config.title if config.title is defined else 'Results' }}</h2>
    {% if config and config.fields %}
    <div class="configured-fields">
        {% for field in config.fields %}
        {{ render_field(field) }}
        {% endfor %}
    </div>
    {% endif %}
</div>