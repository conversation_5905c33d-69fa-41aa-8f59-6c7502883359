package ouc.isclab.system.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.common.response.ResponseResult;
import ouc.isclab.system.entity.PermissionEntity;
import ouc.isclab.system.entity.RoleEntity;
import ouc.isclab.system.repository.PermissionRepository;
import ouc.isclab.system.repository.RoleRepository;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class PermissionService {

    @Autowired
    private PermissionRepository permissionRepository;
    
    @Autowired
    private RoleRepository roleRepository;

    /**
     * 创建权限
     */
    @Transactional
    public PermissionEntity createPermission(String code, String name, String description) {
        // 检查权限码是否已存在
        if (permissionRepository.findByCode(code) != null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "权限码已存在");
        }

        PermissionEntity permission = new PermissionEntity();
        permission.setCode(code);
        permission.setName(name);
        permission.setDescription(description);

        return permissionRepository.save(permission);
    }

    /**
     * 分页获取权限列表
     */
    public Page<PermissionEntity> listPermissions(Pageable pageable) {
        return permissionRepository.findAll(pageable);
    }

    /**
     * 更新权限信息
     */
    @Transactional
    public PermissionEntity updatePermission(Long id, String name, String description) {
        PermissionEntity permission = permissionRepository.findById(id)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "权限不存在"));

        if (name != null) {
            permission.setName(name);
        }
        if (description != null) {
            permission.setDescription(description);
        }

        return permissionRepository.save(permission);
    }

    /**
     * 删除权限
     */
    @Transactional
    public ResponseResult deletePermission(Long id) {
        PermissionEntity permission = permissionRepository.findById(id)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "权限不存在"));

        // 检查权限是否被角色使用
        if (roleRepository.existsByPermissionsContaining(permission)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "权限正在被使用，无法删除");
        }

        permissionRepository.delete(permission);
        return new ResponseResult(ResponseCode.SUCCESS.getCode(), "删除成功", null);
    }

    /**
     * 批量删除权限
     */
    @Transactional
    public ResponseResult deletePermissions(List<Long> ids) {
        for (Long id : ids) {
            deletePermission(id);
        }
        return new ResponseResult(ResponseCode.SUCCESS.getCode(), "批量删除成功", null);
    }

    /**
     * 根据code查询权限
     */
    public PermissionEntity findByCode(String code) {
        return permissionRepository.findByCode(code);
    }

    /**
     * 根据ID查询权限
     */
    public PermissionEntity findById(Long id) {
        return permissionRepository.findById(id)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "权限不存在"));
    }

    /**
     * 获取所有权限
     */
    public List<PermissionEntity> getAllPermissions() {
        return permissionRepository.findAll();
    }

//    /**
//     * 为角色分配权限
//     */
//    @Transactional
//    public ResponseResult assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
//        RoleEntity role = roleRepository.findById(roleId)
//            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "角色不存在"));
//
//        List<PermissionEntity> permissions = permissionRepository.findAllById(permissionIds);
//        if (permissions.size() != permissionIds.size()) {
//            throw new BaseException(ResponseCode.SERVICE_ERROR, "部分权限不存在");
//        }
//
//        role.getPermissions().addAll(permissions);
//        roleRepository.save(role);
//
//        return new ResponseResult(ResponseCode.SUCCESS.getCode(), "权限分配成功", null);
//    }

    /**
     * 移除角色的权限
     */
    @Transactional
    public ResponseResult removePermissionsFromRole(Long roleId, List<Long> permissionIds) {
        RoleEntity role = roleRepository.findById(roleId)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "角色不存在"));

        List<PermissionEntity> permissions = permissionRepository.findAllById(permissionIds);
        role.getPermissions().removeAll(permissions);
        roleRepository.save(role);
        
        return new ResponseResult(ResponseCode.SUCCESS.getCode(), "权限移除成功", null);
    }

    /**
     * 获取角色的所有权限
     */
    public List<PermissionEntity> getRolePermissions(Long roleId) {
        RoleEntity role = roleRepository.findById(roleId)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "角色不存在"));
        return new ArrayList<>(role.getPermissions());
    }
} 