package ouc.isclab.task.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import ouc.isclab.task.entity.ResourceEntity;

public interface ResourceRepository extends JpaRepository<ResourceEntity, Long> {
    
    ResourceEntity findByResourceTypeAndResourceIdAndNodeId(String resourceType, Long resourceId, Long nodeId);
    
    @Query("SELECT r FROM ResourceEntity r JOIN UserResourceEntity ur ON r.id = ur.resource.id " +
           "WHERE ur.userId = ?1 AND ur.active = ?2")
    Page<ResourceEntity> findByUserIdAndActive(Long userId, boolean active, Pageable pageable);
    
    @Query("SELECT r FROM ResourceEntity r JOIN UserResourceEntity ur ON r.id = ur.resource.id " +
           "WHERE ur.userId = ?1 AND r.resourceType = ?2 AND ur.active = ?3")
    Page<ResourceEntity> findByUserIdAndResourceTypeAndActive(Long userId, String resourceType, boolean active, Pageable pageable);
    
    // 获取用户资源的完整信息，包含节点详细信息
    @Query("SELECT r, n FROM ResourceEntity r " +
           "JOIN UserResourceEntity ur ON r.id = ur.resource.id " +
           "JOIN NodeEntity n ON r.nodeId = n.id " +
           "WHERE ur.userId = ?1 AND ur.active = ?2")
    Page<Object[]> findResourcesWithNodeInfoByUserIdAndActive(Long userId, boolean active, Pageable pageable);
    
    @Query("SELECT r, n FROM ResourceEntity r " +
           "JOIN UserResourceEntity ur ON r.id = ur.resource.id " +
           "JOIN NodeEntity n ON r.nodeId = n.id " +
           "WHERE ur.userId = ?1 AND r.resourceType = ?2 AND ur.active = ?3")
    Page<Object[]> findResourcesWithNodeInfoByUserIdAndResourceTypeAndActive(Long userId, String resourceType, boolean active, Pageable pageable);
} 