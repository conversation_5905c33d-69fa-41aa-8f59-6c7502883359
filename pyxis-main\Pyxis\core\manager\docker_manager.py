import json
import time
from pathlib import Path
import logging
import os
import asyncio
import uuid

import docker

from ..stash import kv_stash, queues
from ..utils._tools import load_json_config


class DockerTaskManager:
    """A manager for handling Docker-based task execution with monitoring capabilities.

    This class provides functionality to launch, monitor, stop, and kill Docker containers
    for individual tasks, with status tracking through JSON files.

    Attributes:
        client: Docker SDK client instance
        base_dir: Path to the base directory inside containers
        host_dir: Path to the host mount directory
        logger: Logger instance for tracking operations
        monitor_interval: Interval (in seconds) for monitoring running containers
    """

    def __init__(self, base_dir: Path = Path("./"), host_dir: Path = Path(os.getenv("HOST_DIR")), monitor_interval=10):
        """Initialize the Docker task manager.

        Args:
            base_dir: Base directory path inside containers (default: current directory)
            host_dir: Host mount directory path (default: from HOST_DIR environment variable)
            monitor_interval: Interval in seconds for monitoring container status (default: 10)
        """
        self.client = docker.from_env()
        self.base_dir = base_dir  # Directory inside containers
        self.host_dir = host_dir  # Host mount directory
        self.logger = logging.getLogger("docker_manager")
        self.monitor_interval = monitor_interval
        self.host_id = uuid.uuid4().hex

        worker_img_name = os.getenv("WORKER_IMAGE", "pyxis-worker")
        self.default_worker_image = self.client.images.get(worker_img_name)

        self.running_task = kv_stash.KeyValueStash()

        self.config = load_json_config("./config.json")

        self.task_queue = queues.AsyncFunctionQueue(
            max_running_number=self.config.get("max_running_task_number", 2),
            max_queue_number=self.config.get("max_waiting_task_queue", 0)
        )

    def _write_status(self, task_id: str, status_data: dict):
        """Write status information to a task's STATUS.json file.

        Args:
            task_id: ID of the task to update
            status_data: Dictionary containing status information to write
        """
        status_file = self.base_dir / "task_workspace" / task_id / "STATUS.json"
        status_file.parent.mkdir(parents=True, exist_ok=True)
        status_file.touch(exist_ok=True)

        status_data["last_update"] = time.strftime("%Y-%m-%d %H:%M:%S")

        old_data = {}
        if status_file.exists():
            try:
                with open(status_file, "r") as f:
                    old_data = json.load(f)
            except json.JSONDecodeError:
                old_data = {}

        new_data = {**old_data, **status_data}

        with open(status_file, "w") as f:
            json.dump(new_data, f, indent=2)

    async def monitor_container_exit(self, task_id, container: docker):
        """Monitor if the container exit

        Args:
            task_id:Task id
            container:Docker container
        """
        loop = asyncio.get_running_loop()

        def container_wait():
            return container.wait()

        exit_status = await loop.run_in_executor(None, container_wait)
        exit_code = exit_status['StatusCode']
        new_data = {"exit_code": exit_code,
                    "end_timestamp":time.time()}

        if exit_code != 0:
            new_data['status'] = 'exited'
        else:
            new_data['status'] = 'finished'

        try:
            container.remove()

            use_image = container.image

            if not use_image == self.default_worker_image:
                containers = self.client.containers.list(
                    all=True, filters={"ancestor": use_image.id})
                if len(containers) <= 0:
                    self.client.images.remove(use_image.id)

        except Exception as e:
            self.logger.error(
                f"Task {task_id} container remove error: {str(e)}")
        finally:
            self.running_task.delete(task_id)
            await self.task_queue.notify()

        return self._write_status(task_id, new_data)

    def find_dockerfile(self, task_id):
        """find if custom dockefile has been submitted

        Args:
            task_id: ID of the task to launch

        Returns:
            Dockerfile's path or None
        """
        user_file = self.base_dir / "task_workspace" / task_id / "user_code"

        for file in user_file.iterdir():
            if file.is_file():
                if file.name.lower() == "dockerfile" or file.suffix.lower() == ".dockerfile":
                    return file.resolve()
        return None

    def find_docker_run_kwargs_json(self, task_id):
        """find if custom docker_run_kwargs.json has been submitted

        Args:
            task_id: ID of the task to launch

        Returns:
            dict of custom docker_run_kwargs
        """
        try:
            user_file = self.base_dir / "task_workspace" / task_id / "user_code"
            docker_run_kwargs_file = user_file / "docker_run_kwargs.json"
            if not docker_run_kwargs_file.exists():
                return {}
            docker_run_kwargs = docker_run_kwargs_file.read_text()
            docker_run_kwargs = json.loads(docker_run_kwargs)

            ret_kwargs = {}
            custom_keys = self.config.get("docker_customizable_kwargs", [])
            for key in custom_keys:
                if key in docker_run_kwargs:
                    ret_kwargs[key] = docker_run_kwargs[key]

            return ret_kwargs

        except Exception:
            return {}

    def build_image(self, task_id: str, dockerfile: Path):
        """build image with dockerfile

        Args:
            task_id: ID of the task to launch
            dockerfile:Path of dockerfile

        Returns:
            Image object
        """

        dockerfile = dockerfile.resolve()

        image_tag = f"pyxis-custom-{task_id}".lower()

        try:
            images = self.client.images.list(name=f"{image_tag}")
            if len(images) > 0:
                return images[0]
        except docker.errors.ImageNotFound:
            pass

        image, build_logs = self.client.images.build(
            path=str(dockerfile.parent),
            tag=image_tag,
            dockerfile=dockerfile.name,
            rm=True,
            forcerm=True
        )

        user_log_file = self.base_dir / "task_workspace" / task_id / "logs"
        build_log_file = user_log_file / "build_logs.log"
        build_log_file.touch(exist_ok=True)

        with build_log_file.open("w", encoding="utf-8") as f:
            for chunk in build_logs:
                if 'stream' in chunk:
                    f.write(chunk['stream'])
                elif 'error' in chunk:
                    f.write(f"ERROR: {chunk['error']}\n")

        return image

    async def launch_task(self, task_id: str) -> dict:
        """Launch a new task container and start monitoring it.

        Args:
            task_id: ID of the task to launch

        Returns:
            Dictionary containing launch information:
            {
                "task_id": str,
                "status": str
            }

        Raises:
            Exception: If container launch fails
        """
        try:

            self.running_task.set(task_id, "launching")

            status_file = self.base_dir / "task_workspace" / task_id / "STATUS.json"
            status_file.parent.mkdir(parents=True, exist_ok=True)

            self._write_status(task_id, {
                "task_id": task_id,
                "status": "waiting",
                "error": None,
                "container_id": None,
                "start_time": None,
                "exit_code": None,
                "signal": None,
                "start_timestamp":None,
                "end_timestamp":None,
            })

            async def start_container():
                try:
                    self._write_status(task_id, {
                        "status": "launching",
                    })

                    image = self.default_worker_image
                    dockerfile = self.find_dockerfile(task_id)

                    if dockerfile:
                        self._write_status(task_id, {
                            "status": "building image",
                        })
                        loop = asyncio.get_running_loop()
                        image = await loop.run_in_executor(None, self.build_image, task_id, dockerfile)

                    self._write_status(task_id, {
                        "status": "launching docker",
                    })

                    # get custom docker run kwargs
                    devices = self.find_docker_run_kwargs_json(task_id)
                    custom_kwargs = {**devices}

                    # default kwargs
                    default_kwargs = self.config.get("docker_run_kwargs", {})

                    if "nano_cpus" in default_kwargs:
                        default_kwargs["nano_cpus"] = int(
                            default_kwargs["nano_cpus"])

                    append_kwargs = {**default_kwargs, **custom_kwargs}

                    container = self.client.containers.run(
                        image=image,
                        command=["python3", "/runtime/launcher.py"],
                        volumes={
                            str(self.host_dir / "core/launcher.py"): {"bind": "/runtime/launcher.py", "mode": "ro"},
                            str(self.host_dir / "framework"): {"bind": "/runtime/framework", "mode": "ro"},
                            str(self.host_dir / "assets"): {"bind": "/runtime/assets", "mode": "ro"},
                            str(self.host_dir / "task_workspace" / task_id / "user_code"): {"bind": "/runtime/user_code", "mode": "ro"},
                            str(self.host_dir / "task_workspace" / task_id / "workspace"): {"bind": "/runtime/workspace", "mode": "rw"},
                            str(self.host_dir / "task_workspace" / task_id / "logs"): {"bind": "/runtime/logs", "mode": "rw"},
                            str(self.host_dir / "task_workspace" / task_id / "results"): {"bind": "/runtime/results", "mode": "rw"},
                            str(self.host_dir / "task_workspace" / task_id / "STATUS.json"): {"bind": "/runtime/STATUS.json", "mode": "rw"}
                        },
                        environment={
                            "TASK_ID": task_id,
                            "TASK_DIR": "/runtime",
                            "HOST_PORT": os.getenv("HOST_PORT"),
                            "HOST_ID": self.host_id
                        },
                        detach=True,
                        name=f"pyxis_task_{task_id}",
                        auto_remove=False,
                        remove=False,
                        network="pyxis-network",
                        **append_kwargs
                    )

                    self._write_status(task_id, {
                        "container_id": container.id,
                        "status": "running",
                        "start_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "start_timestamp":time.time()
                    })

                    asyncio.create_task(
                        self.monitor_container_exit(task_id, container))

                except Exception as e:
                    self.logger.error(
                        f"Failed to launch task {task_id}: {str(e)}")

                    self._write_status(task_id, {
                        "task_id": task_id,
                        "status": "launch_failed",
                        "error": str(e),
                    })

                    self.running_task.delete(task_id)

            await self.task_queue.add_function(task_id, start_container)

            return {
                "task_id": task_id,
                "status": "waiting"
            }

        except Exception as e:
            self.logger.error(f"Failed to launch task {task_id}: {str(e)}")

            self._write_status(task_id, {
                "task_id": task_id,
                "status": "launch_failed",
                "error": str(e),
                "last_update": time.strftime("%Y-%m-%d %H:%M:%S")
            })

            self.running_task.delete(task_id)
            raise

    def get_running_task_container(self, task_id):
        """Get task container by task_id

        Args:
            task_id: ID of the task

        Returns:
            container
        """

        if not self.is_task_running(task_id):
            raise Exception(f"Task {task_id} is not running")

        container = self.client.containers.get(f"pyxis_task_{task_id}")

        if not self.is_container_running(task_id):
            raise Exception(f"Container of task {task_id} is not running")

        return container

    async def cancel_task_in_queue(self, task_id):
        """Cancel the task in task_queue

         Args:
            task_id: ID of the task

        """
        if not self.is_task_running(task_id):
            raise Exception(f"Task {task_id} is not running")

        isfound = await self.task_queue.cancel(task_id)

        if isfound:
            self._write_status(task_id, {
                "status": "cancelled"
            })
            self.running_task.delete(task_id)

        return isfound

    async def stop_task(self, task_id: str, timeout: int = 5) -> dict:
        """Gracefully stop a running task container.

        Sends SIGTERM and waits for the container to stop gracefully within the timeout period.

        Args:
            task_id: ID of the task to stop
            timeout: Timeout in seconds before force stopping (default: 10)

        Returns:
            Dictionary containing stop information:
            {
                "task_id": str,
                "status": str,
                "container_id": str,
                "message": str (optional)
            }

        Raises:
            docker.errors.NotFound: If container doesn't exist
            docker.errors.APIError: For Docker API errors
            Exception: For other unexpected errors
        """
        try:
            if await self.cancel_task_in_queue(task_id):
                return {
                    "task_id": task_id,
                    "status": "cancelled"
                }

            container = self.get_running_task_container(task_id)
            container.stop(timeout=timeout)

            self._write_status(task_id, {
                "task_id": task_id,
                "status": "stopped",
                "stopped_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                "exit_code": container.attrs["State"]["ExitCode"]
            })

            return {
                "task_id": task_id,
                "status": "stopped",
                "container_id": container.id
            }
        except docker.errors.NotFound:
            message = f"Container for task {task_id} not found"
            self.logger.warning(message)
            return {
                "task_id": task_id,
                "status": "not_found",
                "message": message
            }
        except Exception as e:
            self.logger.error(f"Failed to stop task {task_id}: {str(e)}")
            self._write_status(task_id, {
                "task_id": task_id,
                "status": "stop_failed",
                "error": str(e),
                "last_update": time.strftime("%Y-%m-%d %H:%M:%S")
            })
            raise

    async def kill_task(self, task_id: str, signal: str = "SIGKILL") -> dict:
        """Forcefully kill a task container.

        Immediately terminates the container with the specified signal (default: SIGKILL).

        Args:
            task_id: ID of the task to kill
            signal: Signal to send (default: "SIGKILL")

        Returns:
            Dictionary containing kill information:
            {
                "task_id": str,
                "status": str,
                "container_id": str,
                "signal": str,
                "message": str (optional)
            }

        Raises:
            docker.errors.NotFound: If container doesn't exist
            docker.errors.APIError: For Docker API errors
            Exception: For other unexpected errors
        """
        try:
            if await self.cancel_task_in_queue(task_id):
                return {
                    "task_id": task_id,
                    "status": "cancelled"
                }

            container = self.get_running_task_container(task_id)

            container.kill(signal=signal)

            self._write_status(task_id, {
                "task_id": task_id,
                "status": "killed",
                "killed_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                "signal": signal
            })

            return {
                "task_id": task_id,
                "status": "killed",
                "container_id": container.id,
                "signal": signal
            }
        except docker.errors.NotFound:
            message = f"Container for task {task_id} not found"
            self.logger.warning(message)
            return {
                "task_id": task_id,
                "status": "not_found",
                "signal": signal,
                "message": message
            }
        except Exception as e:
            self.logger.error(f"Failed to kill task {task_id}: {str(e)}")
            self._write_status(task_id, {
                "task_id": task_id,
                "status": "kill_failed",
                "error": str(e),
                "last_update": time.strftime("%Y-%m-%d %H:%M:%S")
            })
            raise

    def get_service_url(self, task_id: str, api_path: str) -> str:
        """Construct the service URL using container name in pyxis-network.

        Args:
            task_id: The task ID
            api_path: API path to forward to

        Returns:
            Full service URL (e.g., "http://pyxis_task_abc123:8000/api/endpoint")
        """
        container_name = f"pyxis_task_{task_id}"
        return f"http://{container_name}:8000/{api_path.lstrip('/')}"

    def is_container_running(self, task_id: str) -> bool:
        """Check if a task container is running.

        Args:
            task_id: ID of the task

        Returns:
            True if container is running, False otherwise
        """
        try:
            container = self.client.containers.get(f"pyxis_task_{task_id}")
            container.reload()
            return container.status == "running"
        except docker.errors.NotFound:
            return False

    def is_container_exists(self, task_id: str) -> bool:
        """Check if a task container exists.

        Args:
            task_id: ID of the task

        Returns:
            True if container exists, False otherwise
        """
        try:
            container = self.client.containers.get(f"pyxis_task_{task_id}")
            return True
        except docker.errors.NotFound:
            return False

    def is_task_running(self, task_id):
        """Check if a task is running.

        Args:
            task_id: ID of the task

        Returns:
            True if task is running, False otherwise
        """
        task = self.running_task.get(task_id)
        if task is not None:
            return True
        return False
