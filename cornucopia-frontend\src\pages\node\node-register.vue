<template>
  <div class="node-register-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Plus /></el-icon>
          <h2>注册节点</h2>
          <HelpDrawer :content="helpContent" />
        </div>
        <div class="sub-title">添加新的计算节点到联邦学习网络中</div>
      </div>
      <div class="header-right">
        <el-button 
          @click="$router.push('/node/list')"
          plain
          round
        >
          <el-icon><Back /></el-icon>
          返回节点列表
        </el-button>
      </div>
    </div>

    <div class="main-content">
      <el-card class="form-card" shadow="hover">
        <div class="form-header">
          <div class="step-info">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
            <span>请填写节点基本信息，带 * 号的为必填项</span>
          </div>
        </div>
        
        <el-form :model="nodeForm" :rules="rules" ref="formRef" label-width="120px" class="node-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="节点类型" prop="nodeType" class="form-item">
                <el-select 
                  v-model="nodeForm.nodeType" 
                  placeholder="请选择节点类型"
                  size="large"
                  class="w-full"
                >
                  <el-option label="Sycee节点" value="Sycee" />
                  <el-option label="Pyxis节点" value="Pyxis" />
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="节点名称" prop="name" class="form-item">
                <el-input 
                  v-model="nodeForm.name" 
                  placeholder="请输入节点名称，如：node-001"
                  size="large"
                >
                  <template #prefix>
                    <el-icon><Monitor /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="IP地址" prop="ipAddress" class="form-item">
                <el-input 
                  v-model="nodeForm.ipAddress" 
                  placeholder="请输入IP地址，如：*************"
                  size="large"
                >
                  <template #prefix>
                    <el-icon><Connection /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="端口号" prop="port" class="form-item">
                <el-input-number 
                  v-model="nodeForm.port" 
                  :min="1" 
                  :max="65535"
                  size="large"
                  class="w-full"
                  controls-position="right"
                  placeholder="请输入端口号，默认8765"
                />
                <div class="form-tip">推荐使用 1024-65535 之间的端口</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="节点描述" prop="description" class="form-item">
                <el-input 
                  type="textarea" 
                  v-model="nodeForm.description" 
                  placeholder="请输入节点描述信息，如：用于图像识别的GPU计算节点"
                  :rows="3"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="认证用户名" prop="username" class="form-item">
                <el-input 
                  v-model="nodeForm.username" 
                  placeholder="请输入节点认证用户名"
                  size="large"
                >
                  <template #prefix>
                    <el-icon><User /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="认证密码" prop="password" class="form-item">
                <el-input 
                  v-model="nodeForm.password" 
                  type="password"
                  placeholder="请输入节点认证密码"
                  size="large"
                  show-password
                >
                  <template #prefix>
                    <el-icon><Lock /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item class="form-buttons">
            <el-button 
              type="primary" 
              @click="submitForm" 
              :loading="loading"
              size="large"
              class="submit-btn"
              round
              color="#626aef"
            >
              <el-icon><Check /></el-icon>注册节点
            </el-button>
            <el-button 
              @click="resetForm"
              size="large"
              round
            >
              <el-icon><RefreshRight /></el-icon>重置表单
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { 
  Plus, 
  Monitor, 
  Connection, 
  Check, 
  RefreshRight, 
  Back, 
  InfoFilled,
  Warning,
  Lock,
  User
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const formRef = ref(null)

const nodeForm = reactive({
  name: '',
  ipAddress: '',
  port: 8765,
  description: '',
  nodeType: '',
  username: '',
  password: ''
})

const rules = {
  name: [
    { required: true, message: '请输入节点名称', trigger: 'blur' }
  ],
  ipAddress: [
    { required: true, message: '请输入IP地址或域名', trigger: 'blur' },
    { 
      pattern: /^((\d{1,3}\.){3}\d{1,3})|([a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?)$/,
      message: '请输入正确的IP地址或域名格式',
      trigger: 'blur'
    }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  nodeType: [
    { required: true, message: '请选择节点类型', trigger: 'change' }
  ],
  username: [
    { required: true, message: '请输入认证用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入认证密码', trigger: 'blur' }
  ]
}

// 帮助内容
const helpContent = `
<h2>节点注册说明</h2>
<p>在联邦学习网络中注册新的计算节点，需要填写以下信息：</p>

<h3>节点类型</h3>
<ul>
  <li><strong>Sycee节点</strong>：用于执行联邦学习任务的计算节点</li>
  <li><strong>Pyxis节点</strong>：用于快速部署模型应用的节点</li>
</ul>

<h3>基本信息</h3>
<ul>
  <li><strong>节点名称</strong>：用于标识节点的唯一名称</li>
  <li><strong>IP地址</strong>：节点的网络地址，支持IPv4地址或域名</li>
  <li><strong>端口号</strong>：节点服务的端口，建议使用1024-65535之间的端口</li>
  <li><strong>节点描述</strong>：对节点的详细描述，如用途、配置等</li>
</ul>

<h3>认证信息</h3>
<ul>
  <li><strong>认证用户名</strong>：用于节点认证的用户名</li>
  <li><strong>认证密码</strong>：用于节点认证的密码</li>
</ul>

<h3>注意事项</h3>
<ul>
  <li>请确保节点能够正常访问网络</li>
  <li>建议使用安全的密码，并定期更换</li>
  <li>节点注册后需要等待管理员审核</li>
  <li>请妥善保管认证信息，避免泄露</li>
</ul>
`

const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await service.post('/api/v1.0/sys/node', nodeForm)
        if (response.code === 10000) {
          toast('成功', '节点注册成功')
          resetForm()
        } else {
          toast('错误', response.message || '注册失败', 'error')
        }
      } catch (error) {
        toast('错误', '注册失败：' + error.message, 'error')
      }
    }
  })
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.node-register-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.main-content {
  .form-card {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .form-header {
    margin-bottom: 24px;
    
    .step-info {
      display: flex;
      align-items: center;
      color: #909399;
      
      .info-icon {
        margin-right: 8px;
        color: var(--el-color-primary);
      }
    }
  }
}

.node-form {
  .form-item {
    margin-bottom: 24px;
  }
  
  .form-buttons {
    margin-top: 32px;
    text-align: center;
    
    .submit-btn {
      margin-right: 16px;
      min-width: 120px;
    }
  }
}

:deep(.el-input-number) {
  width: 100%;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  padding-left: 4px;
  line-height: 1.4;
}

:deep(.el-form-item.is-required > .el-form-item__label::before) {
  content: '*';
  color: var(--el-color-danger);
  margin-right: 4px;
}

:deep(.el-row) {
  margin-bottom: 0;
  
  &:not(:last-child) {
    margin-bottom: 24px;
  }
}

:deep(.el-col) {
  padding-left: 10px;
  padding-right: 10px;
}

@media (max-width: 1400px) {
  .main-content {
    padding: 24px 20px;
  }
  
  .form-card {
    margin: 0 auto;
  }
}
</style>

