package ouc.isclab.application.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.application.entity.ApplicationTaskEntity;
import ouc.isclab.application.pojo.ApplicationTaskDTO;
import ouc.isclab.application.service.ApplicationService;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.system.service.UserService;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.system.pojo.UserInfo;
import org.springframework.web.multipart.MultipartFile;
import ouc.isclab.pyxis.service.PyxisService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys/application")
public class ApplicationController {

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private UserService userService;

    @Autowired
    private PyxisService pyxisService;

    /**
     * 部署模型
     */
    @PostMapping("/deploy")
    public Map<String, Object> deployModel(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam("nodeId") Long nodeId,
            @RequestParam(value = "folder_name", required = false) String folderName,
            @CurrentUserId Long userId) {
        log.info("部署模型: 节点ID={}, 用户ID={}, 文件夹名称={}", nodeId, userId, folderName);
        return pyxisService.createPyxisTask(nodeId, userId, files, folderName);
    }   

    /**
     * 创建部署任务
     */
    @PostMapping("/task")
    public ApplicationTaskEntity createDeployTask(
            @RequestBody ApplicationTaskDTO taskDTO,
            @CurrentUserId Long userId) {
        log.info("创建模型部署任务: {}, 用户ID: {}", taskDTO, userId);
        return applicationService.createDeployTask(taskDTO, userId);
    }
    
    /**
     * 获取用户的部署任务列表
     */
    @GetMapping("/tasks")
    public Map<String, Object> getUserDeployTasks(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @CurrentUserId Long userId) {
        log.info("获取用户部署任务列表: 用户ID={}, 页码={}, 大小={}, 关键词={}", userId, page, size, keyword);
        
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<ApplicationTaskEntity> tasksPage = applicationService.getUserDeployTasks(userId, keyword, pageable);
        
        Map<String, Object> result = new HashMap<>();
        result.put("tasks", tasksPage.getContent());
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", tasksPage.getTotalElements());
        result.put("pagination", pagination);
        
        return result;
    }
    
    /**
     * 获取部署任务详情
     */
    @GetMapping("/task/{taskId}")
    public ApplicationTaskEntity getDeployTaskDetail(
            @PathVariable Long taskId,
            @CurrentUserId Long userId) {
        log.info("获取部署任务详情: 任务ID={}, 用户ID={}", taskId, userId);
        return applicationService.getDeployTaskDetail(taskId, userId);
    }

    /**
     * 删除部署任务
     */
    @DeleteMapping("/task/{taskId}")
    public void deleteDeployTask(
            @PathVariable Long taskId,
            @CurrentUserId Long userId) {
        log.info("删除部署任务: taskId={}, userId={}", taskId, userId);
        applicationService.deleteDeployTask(taskId, userId);
    }

    /**
     * 批量删除部署任务
     */
    @DeleteMapping("/tasks")
    public void batchDeleteDeployTasks(
            @RequestBody List<Long> ids,
            @CurrentUserId Long userId) {
        log.info("批量删除部署任务: ids={}, userId={}", ids, userId);
        applicationService.batchDeleteDeployTasks(ids, userId);
    }

    /**
     * 获取节点所有者需要审批的部署任务列表
     */
    @GetMapping("/examine")
    public Map<String, Object> getDeployTasksForExamine(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @CurrentUserId Long userId) {
        log.info("获取节点所有者部署任务审批列表: status={}, userId={}", status, userId);
        
        Pageable pageable = PageRequest.of(page - 1, size);
        
        Page<ApplicationTaskEntity> tasksPage;
        if (status != null && !status.isEmpty()) {
            // 如果提供了状态参数，按状态过滤
            ApplicationTaskEntity.DeployStatus deployStatus = 
                ApplicationTaskEntity.DeployStatus.valueOf(status);
            tasksPage = applicationService.getPendingDeployTasksForNodeOwner(userId, deployStatus, pageable);
        } else {
            // 否则获取所有状态的审批任务
            tasksPage = applicationService.getAllDeployTasksByNodeOwner(userId, pageable);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("tasks", tasksPage.getContent());
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", tasksPage.getTotalElements());
        result.put("pagination", pagination);
        
        return result;
    }

    /**
     * 审批部署任务
     */
    @PostMapping("/approve/{taskId}")
    public ApplicationTaskEntity approveDeployTask(
            @PathVariable Long taskId,
            @RequestParam boolean approved,
            @RequestParam String message,
            @CurrentUserId Long userId) {
        log.info("审批部署任务: taskId={}, approved={}, message={}, userId={}", taskId, approved, message, userId);
        return applicationService.approveDeployTask(taskId, approved, message, userId);
    }

    /**
     * 执行部署任务
     */
    @PostMapping("/execute/{taskId}")
    public Map<String, Object> executeDeployTask(
        @PathVariable Long taskId,
        @CurrentUserId Long userId) {
        log.info("执行部署任务: taskId={}, userId={}", taskId, userId);
        return applicationService.executePyxisTask(taskId, userId);
    }   

    /**
     * 获取部署任务状态
     */
    @GetMapping("/status/{taskId}")
    public Map<String, Object> getTaskStatus(
        @PathVariable Long taskId,
        @CurrentUserId Long userId) {
        log.info("获取部署任务状态: taskId={}, userId={}", taskId, userId);
        return applicationService.getTaskStatus(taskId, userId);
    }

    /**
     * 停止部署任务
     */
    @PostMapping("/stop/{taskId}")
    public Map<String, Object> stopTask(
        @PathVariable Long taskId,
        @CurrentUserId Long userId) {
        log.info("停止部署任务: taskId={}, userId={}", taskId, userId);
        return applicationService.stopTask(taskId, userId);
    }

    /**
     * 终止部署任务 
     */
    @PostMapping("/kill/{taskId}")
    public Map<String, Object> killTask(
        @PathVariable Long taskId,
        @CurrentUserId Long userId) {
        log.info("终止部署任务: taskId={}, userId={}", taskId, userId); 
        return applicationService.killTask(taskId, userId);
    }

    /**
     * 浏览部署任务文件目录
     */
    @GetMapping("/browse/{taskId}")
    public Map<String, Object> browseTaskFiles(
        @PathVariable Long taskId,
        @RequestParam(required = false) String path,
        @CurrentUserId Long userId) {
        log.info("浏览部署任务文件目录: taskId={}, path={}, userId={}", taskId, path, userId);
        return applicationService.browseTaskFiles(taskId, userId, path);
    }

    /**
     * 下载任务文件
     * @param taskId 任务ID
     * @param filePath 文件路径
     * @param userId 当前用户ID
     * @return ResponseEntity<byte[]> 文件内容
     */
    @GetMapping("/download/{taskId}")
    public ResponseEntity<byte[]> downloadTaskFile(
            @PathVariable Long taskId,
            @RequestParam String filePath,
            @CurrentUserId Long userId) {
        log.info("下载任务文件请求: taskId={}, filePath={}, userId={}", taskId, filePath, userId);
        return applicationService.downloadTaskFile(taskId, userId, filePath);
    }

    /**
     * 获取申请人的用户信息
     */
    @GetMapping("/approval/user/{userId}")
    public Object getUserInfo(
            @PathVariable Long userId,
            @CurrentUserId Long currentUserId) {
        log.info("获取申请人信息: userId={}, currentUserId={}", userId, currentUserId);
        
        // 验证当前用户是否有权限查看该申请人的信息
        boolean hasRequestToCurrentUser = applicationService.hasApprovalRequestFromUserToNodeOwner(userId, currentUserId);
        
        if (!hasRequestToCurrentUser) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权查看该用户信息");
        }
        
        UserEntity userEntity = userService.findUserById(userId);
        return UserInfo.fromEntity(userEntity);
    }

    /**
     * 预览任务文本文件
     * @param taskId 任务ID
     * @param filePath 文件路径
     * @param userId 当前用户ID
     * @return String 文件内容文本
     */
    @GetMapping("/preview/{taskId}")
    @ResponseBody
    public ResponseEntity<String> previewTaskFile(
            @PathVariable Long taskId,
            @RequestParam String filePath,
            @CurrentUserId Long userId) {
        log.info("预览任务文件请求: taskId={}, filePath={}, userId={}", taskId, filePath, userId);
        String content = applicationService.previewTaskFile(taskId, userId, filePath);
        return ResponseEntity.ok()
                .contentType(MediaType.TEXT_PLAIN)
                .body(content);
    }

    /**
     * 申请部署任务重新审批
     * @param taskId 任务ID
     * @param userId 当前用户ID
     * @return 申请结果
     */
    @PostMapping("/reapproval/{taskId}")
    public Map<String, Object> requestReapproval(
            @PathVariable Long taskId,
            @CurrentUserId Long userId) {
        log.info("申请部署任务重新审批: taskId={}, userId={}", taskId, userId);
        return applicationService.requestReapproval(taskId, userId);
    }

    /**
     * 获取应用统计数据
     */
    @GetMapping("/statistics")
    public Map<String, Object> getApplicationStatistics() {
        log.info("获取应用统计数据");
        return applicationService.getApplicationStatistics();
    }

}