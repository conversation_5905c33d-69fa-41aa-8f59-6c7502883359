upstream pyxis_core {
    server pyxis-core:8000;
}

server {
    listen 8000;
    server_name localhost;

    client_max_body_size 10m;

    location ~ ^/service/(?<task_id>[^/]+)/(?<api_path>.*)$ {
        resolver 127.0.0.11 valid=30s;
        set $container_name "pyxis_task_$task_id";

        proxy_pass http://$container_name:8000/$api_path;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        add_header X-Request-Time $request_time;
    }

    location / {
        proxy_pass http://pyxis-core:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}