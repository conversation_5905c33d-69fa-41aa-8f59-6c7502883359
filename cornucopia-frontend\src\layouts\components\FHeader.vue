<script setup>
import { useDark, useToggle, useFullscreen } from '@vueuse/core'
import { <PERSON>, <PERSON>, Refresh, FullScreen, Rank, ElementPlus, User, Edit, SwitchButton, Document } from '@element-plus/icons-vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { showModal, toast } from "~/composables/util"
import { logout } from "~/api/manager"

const store = useStore()
const router = useRouter()

const isDark = useDark()
const toggleDark = useToggle(isDark)

const handleRefresh = () => {
  window.location.reload()
}

const { isFullscreen, toggle: toggleFullscreen } = useFullscreen()

const handleLogout = () => {
  showModal("是否要退出登录？").then(res => {
    logout()
      .finally(() => {
        store.dispatch("logout")
        router.push("/login")
        toast("退出登录成功")
      })
  })
}

const handleMyInfo = () => {
  router.push('/my')
}
</script>

<template>
  <el-menu class="el-menu-demo" mode="horizontal" :ellipsis="false" router>
    <el-menu-item index="/" style="min-width: 200px;">
      <div class="flex items-center justify-center gap-3">
        <div class="text-primary" style="width: 28px; height: 28px;">
          <svg viewBox="0 0 1024 1024"width="28" height="28">
            <path d="M687.991494 432.007319c0 17.699781-14.299823 31.999604-31.999604 31.999604H367.99545c-8.799891 0-16.799792-3.599955-22.59972-9.399883-5.799928-5.799928-9.399884-13.799829-9.399884-22.599721 0-60.69925 30.799619-114.298587 77.499042-145.898196C376.09535 256.809485 351.995648 211.210049 351.995648 160.010682 351.995648 72.311766 422.494777 1.112646 509.893696 0.01266 597.692611-1.087327 670.591709 69.611799 671.991692 157.410714c0.79999 52.299353-23.399711 98.898777-61.49924 128.698409 23.399711 15.799805 42.799471 37.099541 56.299304 61.999233 13.499833 24.999691 21.199738 53.499339 21.199738 83.898963zM1023.98734 992.000396c0 17.699781-14.299823 31.999604-31.999604 31.999604H703.991296c-8.799891 0-16.799792-3.599955-22.59972-9.399884-5.799928-5.799928-9.399884-13.799829-9.399884-22.59972 0-60.69925 30.799619-114.298587 77.499042-145.898197C712.091196 816.802562 687.991494 771.203125 687.991494 720.003758c0-87.698916 70.499128-158.898035 157.898048-159.998022 87.798915-1.099986 160.698013 69.59914 162.097996 157.398055 0.79999 52.299353-23.399711 98.898777-61.49924 128.698408 23.399711 15.799805 42.799471 37.099541 56.299304 61.999234 13.499833 24.999691 21.199738 53.499339 21.199738 83.898963zM351.995648 992.000396c0 17.699781-14.299823 31.999604-31.999604 31.999604H31.999604c-8.799891 0-16.799792-3.599955-22.59972-9.399884-5.799928-5.799928-9.399884-13.799829-9.399884-22.59972 0-60.69925 30.799619-114.298587 77.499042-145.898197C40.099504 816.802562 15.999802 771.203125 15.999802 720.003758c0-87.698916 70.499128-158.898035 157.898048-159.998022 87.798915-1.099986 160.698013 69.59914 162.097996 157.398055 0.79999 52.299353-23.399711 98.898777-61.49924 128.698408 23.399711 15.799805 42.799471 37.099541 56.299304 61.999234 13.499833 24.999691 21.199738 53.499339 21.199738 83.898963zM635.292146 720.003758c-5.899927 10.299873-16.699794 15.999802-27.699658 15.999803-5.399933 0-10.899865-1.399983-15.999802-4.299947L511.99367 685.804181l-79.599016 45.899433c-15.299811 8.799891-34.899569 3.599955-43.69946-11.699856-2.899964-4.999938-4.299947-10.49987-4.299946-15.999802 0-11.099863 5.69993-21.79973 15.999802-27.699657l79.599016-45.899433V544.005934c0-17.699781 14.299823-31.999604 31.999604-31.999604 8.799891 0 16.799792 3.599955 22.599721 9.399884 5.799928 5.799928 9.399884 13.799829 9.399883 22.59972v86.298933l79.599016 45.899433c15.299811 8.89989 20.499747 28.499648 11.699856 43.799458z" p-id="9504" fill="currentColor"></path>
          </svg>
        </div>
        <span class="text-lg font-semibold">Cornucopia</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/node" style="min-width: 120px;" v-permission="['menu:node']">
        <span class="text-base">节点管理</span>
    </el-menu-item>
    <el-menu-item index="/user" style="min-width: 120px;" v-permission="['menu:user']">
        <span class="text-base">用户管理</span>
    </el-menu-item>
    <el-menu-item index="/data" style="min-width: 120px;" v-permission="['menu:data']">
        <span class="text-base">数据管理</span>
    </el-menu-item>
    <el-menu-item index="/task" style="min-width: 120px;" v-permission="['menu:task']">
        <span class="text-base">任务管理</span>
    </el-menu-item>
    <el-menu-item index="/model" style="min-width: 120px;" v-permission="['menu:model']">
        <span class="text-base">模型管理</span>
    </el-menu-item>
    <el-menu-item index="/application" style="min-width: 120px;">
        <span class="text-base">应用管理</span>
    </el-menu-item>

    <div class="flex-1" />
    
    <div class="right-menu">
      <el-menu-item 
        index="/minio" 
        class="right-menu-item" 
        v-permission="['menu:data:minio']"
      >
       <el-icon><Folder /></el-icon>
       <span class="text-base">存储服务配置</span>
      </el-menu-item>

      <el-menu-item 
        v-permission="['menu:log']"
        index="/logs" 
        class="right-menu-item"
      >
        <el-icon><Document /></el-icon>
        <span class="text-base">系统日志</span>
      </el-menu-item>

      <el-tooltip
        effect="dark"
        :content="isFullscreen ? '退出全屏' : '全屏'"
        placement="bottom"
      >
        <el-menu-item h="full" @click="toggleFullscreen()" class="right-menu-item">
          <button class="w-full h-full flex items-center justify-center">
            <el-icon :size="20">
              <Rank v-if="isFullscreen" />
              <FullScreen v-else />
            </el-icon>
          </button>
        </el-menu-item>
      </el-tooltip>

      <el-tooltip
        effect="dark"
        content="刷新页面"
        placement="bottom"
      >
        <el-menu-item h="full" @click="handleRefresh" class="right-menu-item">
          <button class="w-full h-full flex items-center justify-center">
            <el-icon :size="20">
              <Refresh />
            </el-icon>
          </button>
        </el-menu-item>
      </el-tooltip>

      <el-tooltip
        effect="dark"
        :content="isDark ? '切换浅色模式' : '切换深色模式'"
        placement="bottom"
      >
        <el-menu-item h="full" @click="toggleDark()" class="right-menu-item">
          <button class="w-full h-full flex items-center justify-center">
            <el-icon :size="20">
              <Moon v-if="isDark" />
              <Sunny v-else />
            </el-icon>
          </button>
        </el-menu-item>
      </el-tooltip>

      <el-tooltip
        effect="dark"
        content="用户信息"
        placement="bottom"
      >
        <el-dropdown class="h-[60px] flex items-center px-4 cursor-pointer hover:bg-primary/5 right-menu-item">
          <div class="flex items-center gap-2">
            <el-icon :size="20">
              <User />
            </el-icon>
            <span>{{ $store.state.user.username }}</span>
          </div>
          
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleMyInfo">
                <el-icon><Edit /></el-icon>
                <span class="ml-2">我的信息</span>
              </el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">
                <el-icon><SwitchButton /></el-icon>
                <span class="ml-2">退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-tooltip>
    </div>
  </el-menu>
</template>

<style scoped>
:deep(.el-menu) {
    margin: 0 !important;
    width: 100% !important;
    --el-menu-bg-color: var(--el-bg-color);
    --el-menu-text-color: var(--el-text-color-primary);
    --el-menu-hover-text-color: var(--el-color-primary);
    --el-menu-active-color: var(--el-color-primary);
    border-bottom: 1px solid var(--el-border-color-light);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    padding: 0 !important;
}

:deep(.el-menu--horizontal > .el-menu-item:nth-child(5)) {
    margin-right: auto;
}

:deep(.el-menu-item) {
    height: 60px;
    line-height: 60px;
    padding: 0 20px !important;
    margin: 0 !important;
    border-right: 1px solid var(--el-border-color-light) !important;
}

:deep(.el-menu-item:first-child) {
    border-left: none !important;
}

.el-dropdown {
    height: 60px;
    padding: 0 20px !important;
    margin: 0 !important;
    border-left: 1px solid var(--el-border-color-light);
}

:deep(.el-header) {
    padding: 0 !important;
    margin: 0 !important;
}

:deep(.el-menu-demo) {
    display: flex !important;
    width: 100% !important;
    padding: 0 !important;
}

/* 深色模式样式优化 */
html.dark {
    :deep(.el-menu) {
        --el-menu-bg-color: var(--el-bg-color-overlay);
        --el-menu-hover-bg-color: var(--el-color-primary-dark-2);
        --el-menu-text-color: var(--el-text-color-primary);
        --el-menu-hover-text-color: var(--el-color-primary);
        --el-menu-active-color: var(--el-color-primary);
        border-bottom: 1px solid var(--el-border-color-darker);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        background-color: var(--el-bg-color-overlay);
    }

    .flex-1 {
        background-color: var(--el-bg-color-overlay);
    }

    :deep(.el-menu-item) {
        border-right: 1px solid var(--el-border-color-darker) !important;
        background-color: var(--el-bg-color-overlay);
    }

    :deep(.el-menu-item button) {
        background-color: var(--el-bg-color-overlay);
    }

    :deep(.el-menu-item:hover) {
        background-color: var(--el-color-primary-dark-2) !important;
        border-color: var(--el-color-primary-dark-1);
    }

    :deep(.el-menu-item:hover button) {
        background-color: var(--el-color-primary-dark-2) !important;
    }

    :deep(.el-menu-item.is-active) {
        background-color: var(--el-color-primary-dark-2);
        border-color: var(--el-color-primary-dark-1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .el-dropdown {
        background-color: var(--el-bg-color-overlay);
        border-left: 1px solid var(--el-border-color-darker);
    }

    .el-dropdown:hover {
        background-color: var(--el-color-primary-dark-2) !important;
        border-color: var(--el-color-primary-dark-1);
    }
}

/* 图标样式优化 */
:deep(.el-icon) {
    margin-right: 8px;
    font-size: 17px;
    flex-shrink: 0;
    color: var(--el-text-color-regular);
}

:deep(.is-active .el-icon) {
    color: var(--el-color-primary);
}

/* 添加文字平滑效果 */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 修改右侧菜单项的样式 */
:deep(.el-menu-item:not(:first-child)) {
  border-left: 1px solid var(--el-border-color-light);
}

/* 深色模式下的边框颜色 */
html.dark {
  :deep(.el-menu-item:not(:first-child)) {
    border-left: 1px solid var(--el-border-color-darker);
  }
}

/* 右侧菜单样式 */
.right-menu {
  display: flex;
  align-items: center;
  height: 100%;
}

.right-menu-item {
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 100%;
  font-size: 14px;
  color: var(--el-text-color-primary);
  border-left: 1px solid var(--el-border-color-light);
}

.right-menu-item:hover {
  background-color: var(--el-color-primary-light-9);
}

/* 深色模式适配 */
html.dark {
  .right-menu-item {
    border-left: 1px solid var(--el-border-color-darker);
  }
  
  .right-menu-item:hover {
    background-color: var(--el-color-primary-dark-2);
  }
}

/* 图标样式 */
.right-menu-item .el-icon {
  margin-right: 6px;
  font-size: 18px;
}
</style>