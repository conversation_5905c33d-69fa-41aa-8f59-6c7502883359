
import shutil
import os
from pathlib import Path
from jinja2 import Template
from typing import Callable, Dict, Any
import threading
import time

from flask import Flask, request, render_template_string, jsonify
from werkzeug.utils import secure_filename
from werkzeug.serving import make_server

from . import base_template


def default_preprocessor(app, request) -> Dict[str, Any]:
    """默认预处理函数"""
    input_data = {}

    TEMP_DIR = Path(app.config["temp_dir"])

    # 处理表单数据
    for key, value in request.form.items():
        if value.lower() in ('true', 'false'):
            input_data[key] = value.lower() == 'true'
        elif value.isdigit():
            input_data[key] = int(value)
        elif value.replace('.', '', 1).isdigit():
            input_data[key] = float(value)
        else:
            input_data[key] = value

    for file_key, file in request.files.items():
        if file.filename:
            filepath = TEMP_DIR / secure_filename(file.filename)
            file.save(str(filepath))

            input_data[file_key] = str(filepath)
    return input_data


def default_input_template(**config: Dict[str, Any]) -> str:
    if not config.get('fields'):
        config['fields'] = [
            {
                "name": "file",
                "label": "Upload File",
                "type": "file"
            }
        ]

    template = Template(base_template.input_html)

    return template.render(config=config)


def default_output_template(**config) -> str:
    return base_template.output_html


def create_app(
    get_model: Callable,
    input_template_generate: Callable = default_input_template,
    output_template_generate: Callable = default_output_template,
    preprocessor: Callable = default_preprocessor,
    input_template_config: Dict = {},
    output_template_config: Dict = {},
    timeout_minutes: int = 30,
    temp_dir='./TEMP_DIR',
    **kwargs
) -> Flask:
    """创建模型服务应用"""
    app = Flask(__name__)
    model = get_model()

    app.config["timeout_minutes"] = timeout_minutes
    app.config["temp_dir"] = temp_dir

    os.makedirs(temp_dir, exist_ok=True)

    # 生成模板
    input_template = input_template_generate(**input_template_config)
    output_template = output_template_generate(**output_template_config)

    # 基础页面模板
    base_html = base_template.base_html

    destroy_time = int((time.time() + timeout_minutes * 60 - 30) * 1000)

    @app.route('/')
    def home():
        return render_template_string(
            base_html,
            input_template=input_template,
            destroy_time=destroy_time
        )

    @app.route('/predict', methods=['POST'])
    def predict():
        input_data = {}
        try:
            # 预处理输入
            input_data = preprocessor(app,request)

            # 模型预测
            result = model.predict(**input_data)

            # 渲染输出
            return render_template_string(
                output_template,
                result=result
            )

        except Exception as e:
            return jsonify({
                "status": "error",
                "message": str(e)
            }), 500

    return app


def cleanup_resources(app):
    """Clean up temporary resources"""
    try:
        if app.config.get("temp_dir"):
            TEMP_DIR = app.config.get("temp_dir")
            if os.path.exists(TEMP_DIR):
                shutil.rmtree(TEMP_DIR)
    except Exception as e:
        print(f"Cleanup error: {str(e)}")


def run_app(app, port=8000):
    try:
        server = make_server('0.0.0.0', port, app)

        timeout_minutes = app.config.get("timeout_minutes", 30)
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.start()
        time.sleep(timeout_minutes * 60)

    except Exception:
        pass
    finally:
        server.shutdown()
        print("shutdown server")
        cleanup_resources(app)
        server_thread.join()
