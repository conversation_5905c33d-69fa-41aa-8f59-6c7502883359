<template>
  <div class="model-deploy-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Upload /></el-icon>
          <h2>模型应用部署</h2>
        </div>
        <div class="sub-title">配置并部署模型应用</div>
      </div>
    </div>

    <el-card class="deploy-card" shadow="hover">
      <template v-if="activeStep === 0">
        <el-steps :active="0" finish-status="success" simple>
          <el-step title="应用类型" icon="el-icon-collection" />
          <el-step title="模型信息" icon="el-icon-edit" />
          <el-step title="任务信息" icon="el-icon-document" />
          <el-step title="部署配置" icon="el-icon-setting" />
          <el-step title="上传代码" icon="el-icon-upload" />
          <el-step title="创建任务" icon="el-icon-check" />
        </el-steps>
        <div class="step-content">
          <div class="info-section">
            <div class="section-title">选择应用类型</div>
            <div class="model-source-cards">
              <div 
                class="source-card" 
                :class="{ active: modelSource === 'platform' }"
                @click="modelSource = 'platform'"
              >
                <div class="card-icon">
                  <el-icon><DataAnalysis /></el-icon>
                </div>
                <div class="card-title">使用平台模型部署应用</div>
                <div class="card-description">从平台上已有的训练好的模型中选择</div>
              </div>
              <div 
                class="source-card" 
                :class="{ active: modelSource === 'external' }"
                @click="modelSource = 'external'"
              >
                <div class="card-icon">
                  <el-icon><UploadFilled /></el-icon>
                </div>
                <div class="card-title">自定义部署应用</div>
                <div class="card-description">使用您自定义的Pyxis应用文件</div>
              </div>
            </div>
          </div>
          <div class="step-actions">
            <el-button type="primary" @click="handleModelSourceSelection">下一步</el-button>
          </div>
        </div>
      </template>
      <template v-else>
        <el-steps :active="activeStep" finish-status="success" simple>
          <el-step title="应用类型" icon="el-icon-collection" />
          <el-step title="基本信息" icon="el-icon-edit" />
          <el-step title="任务信息" icon="el-icon-document" />
          <el-step title="部署配置" icon="el-icon-setting" />
          <el-step title="上传代码" icon="el-icon-upload" />
          <el-step title="创建任务" icon="el-icon-check" />
        </el-steps>

        <!-- 步骤1：基本信息 -->
        <div v-if="activeStep === 1" class="step-content">
          <div class="info-section">
            <div class="section-title">模型文件信息</div>
            <template v-if="modelSource === 'platform'">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="模型ID">
                  {{ route.query.modelId }}
                </el-descriptions-item>
                <el-descriptions-item label="文件路径">
                  {{ route.query.filePath }}
                </el-descriptions-item>
                <el-descriptions-item label="文件名">
                  {{ route.query.fileName }}
                </el-descriptions-item>
              </el-descriptions>
            </template>
            <template v-else>
              <el-alert
                title="使用外部模型无需提供模型文件信息，请直接进行下一步操作"
                type="info"
                :closable="false"
                show-icon
                style="margin-bottom: 16px;"
              />
            </template>
          </div>

          <div class="info-section">
            <div class="section-title">存储配置信息</div>
            <el-descriptions v-if="minioConfig" :column="1" border>
              <el-descriptions-item label="端点">
                {{ minioConfig.endpoint }}
              </el-descriptions-item>
              <el-descriptions-item label="访问密钥">
                {{ minioConfig.accessKey }}
              </el-descriptions-item>
              <el-descriptions-item label="密钥">
                {{ minioConfig.secretKey }}
              </el-descriptions-item>
              <el-descriptions-item label="存储桶">
                {{ minioConfig.bucket }}
              </el-descriptions-item>
            </el-descriptions>
            <el-empty v-else description="暂无存储配置信息" />
          </div>
          
          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button type="primary" @click="nextStep" :disabled="!canContinueFromBasicInfo">下一步</el-button>
          </div>
        </div>

        <!-- 步骤2：任务信息 -->
        <div v-if="activeStep === 2" class="step-content">
          <div class="info-section">
            <div class="section-title">任务基本信息</div>
            <el-form :model="taskForm" label-width="120px" class="task-form">
              <el-form-item label="任务名称" required>
                <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
              </el-form-item>
              <el-form-item label="任务描述">
                <el-input
                  v-model="taskForm.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入任务描述"
                />
              </el-form-item>
            </el-form>
          </div>
          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button type="primary" :disabled="!taskForm.name" @click="nextStep">下一步</el-button>
          </div>
        </div>

        <!-- 步骤3：节点选择 -->
        <div v-if="activeStep === 3" class="step-content">
          <div class="info-section">
            <div class="section-title">选择应用部署节点资源</div>
            <el-table
              ref="nodeTableRef"
              :data="modelResources"
              style="width: 100%"
              border
              highlight-current-row
              row-key="id"
              @selection-change="handleNodeSelectionChange"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="name" label="资源名称" min-width="180">
                <template #default="scope">
                  <el-tag type="primary">{{ scope.row.name }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" min-width="280" show-overflow-tooltip />
              <el-table-column prop="nodeId" label="所属节点" width="120">
                <template #default="scope">
                  <el-tag 
                    type="success" 
                    style="cursor: pointer"
                    @click="viewNodeDetail(scope.row.nodeId)"
                  >
                    节点 {{ scope.row.nodeId }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button type="primary" :disabled="selectedNodes.length === 0" @click="nextStep">下一步</el-button>
          </div>
        </div>

        <!-- 步骤4：上传代码 -->
        <div v-if="activeStep === 4" class="step-content">
          <div class="info-section">
            <div class="section-title" style="display: flex; align-items: center; justify-content: space-between;">
              <span>上传代码文件夹</span>
              <el-button @click="downloadDefaultTemplate" size="small">下载默认模板</el-button>
            </div>
            <el-alert
              title="请上传完整的代码文件夹，包含所有必要的文件（如 __init__.py、scrip_slim模板文件等）。__init__.py 中需包含 main 入口函数。你也可以上传自定义的模板文件夹。"
              type="info"
              :closable="false"
              show-icon
              style="margin-bottom: 16px;"
            />
            <div class="upload-container">
              <input
                type="file"
                ref="folderInput"
                @change="handleFolderSelect"
                webkitdirectory
                directory
                style="display: none"
              />
              <el-button type="primary" @click="triggerFolderSelect">
                选择文件夹
              </el-button>
              <div v-if="selectedFolder" class="selected-folder">
                已选择文件夹: {{ selectedFolder }}
              </div>
            </div>

            <!-- 文件列表展示 -->
            <div v-if="codeFileList.length > 0" class="file-list-container">
              <div class="section-title">
                <span>文件列表</span>
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="clearFiles"
                  style="margin-left: 10px"
                >
                  清空文件
                </el-button>
              </div>
              <el-table :data="codeFileList" style="width: 100%" border>
                <el-table-column type="expand">
                  <template #default="props">
                    <div class="file-details">
                      <p><strong>完整路径：</strong>{{ props.row.webkitRelativePath }}</p>
                      <p><strong>最后修改时间：</strong>{{ formatDate(props.row.lastModified) }}</p>
                      <p><strong>文件类型：</strong>{{ props.row.type || '未知' }}</p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="webkitRelativePath" label="文件路径" min-width="300">
                  <template #default="scope">
                    <span>{{ scope.row.webkitRelativePath }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="size" label="大小" width="120">
                  <template #default="scope">
                    {{ formatFileSize(scope.row.size) }}
                  </template>
                </el-table-column>
                <el-table-column prop="type" label="类型" width="120">
                  <template #default="scope">
                    <el-tag size="small" :type="getFileTypeTag(scope.row.name)">
                      {{ getFileExtension(scope.row.name) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                  <template #default="scope">
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeFile(scope.$index)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div v-if="initContent" class="code-preview">
              <div class="preview-title">__init__.py 文件内容预览：</div>
              <pre><code>{{ initContent }}</code></pre>
            </div>
            <el-alert
              v-if="initContent && !hasMain"
              title="__init__.py 中未检测到 main 函数，请确保包含 main 入口函数！"
              type="error"
              :closable="false"
              show-icon
              style="margin-top: 16px;"
            />
          </div>
          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button type="primary" :disabled="!canProceedUpload" @click="deployModel">部署应用</el-button>
          </div>
        </div>

        <!-- 步骤5：创建任务 -->
        <div v-if="activeStep === 5" class="step-content">
          <div class="info-section">
            <div class="section-title">创建任务</div>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="任务名称">
                {{ taskForm.name }}
              </el-descriptions-item>
              <el-descriptions-item label="任务描述">
                {{ taskForm.description || '无' }}
              </el-descriptions-item>
              <el-descriptions-item label="模型ID">
                {{ route.query.modelId }}
              </el-descriptions-item>
              <el-descriptions-item label="模型文件">
                {{ route.query.fileName }}
              </el-descriptions-item>
              <el-descriptions-item label="部署节点">
                节点 {{ selectedNodes[0]?.nodeId }}
              </el-descriptions-item>
              <el-descriptions-item label="代码文件夹">
                {{ selectedFolder }}
              </el-descriptions-item>
              <el-descriptions-item label="文件数量">
                {{ codeFileList.length }} 个文件
              </el-descriptions-item>
              <el-descriptions-item label="部署任务ID">
                {{ deployResult?.task_id || '暂无' }}
              </el-descriptions-item>
              <el-descriptions-item label="保存的文件">
                <div v-if="deployResult?.saved_files?.length">
                  <el-tag v-for="file in deployResult.saved_files" :key="file" class="file-tag">
                    {{ file }}
                  </el-tag>
                </div>
                <span v-else>暂无</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button type="primary" @click="createTask">创建任务记录</el-button>
          </div>
        </div>
      </template>
    </el-card>

    <!-- 节点详情对话框 -->
    <el-dialog
      v-model="nodeDetailVisible"
      title="节点详情"
      width="600px"
      destroy-on-close
    >
      <div v-loading="nodeDetailLoading">
        <el-descriptions v-if="nodeDetail" :column="1" border>
          <el-descriptions-item label="节点ID">{{ nodeDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="节点名称">{{ nodeDetail.name }}</el-descriptions-item>
          <el-descriptions-item label="节点地址">{{ nodeDetail.ipAddress }}:{{ nodeDetail.port }}</el-descriptions-item>
          <el-descriptions-item label="节点类型">
            <el-tag type="success">{{ nodeDetail.nodeType }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="描述">{{ nodeDetail.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(nodeDetail.timeCreated) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(nodeDetail.timeUpdated) }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="nodeDetailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Upload, UploadFilled, DataAnalysis } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const route = useRoute()
const router = useRouter()
const activeStep = ref(0)
const modelSource = ref('platform')
const minioConfig = ref(null)
const modelResources = ref([])
const selectedNodes = ref([])
const nodeTableRef = ref(null)
const codeFileList = ref([])
const initContent = ref('')
const hasMain = ref(false)
const folderInput = ref(null)
const selectedFolder = ref('')
const nodeDetailVisible = ref(false)
const nodeDetailLoading = ref(false)
const nodeDetail = ref(null)
const taskForm = ref({
  name: '',
  description: ''
})
const externalModelForm = ref({})
const deployResult = ref(null)

// 检查是否包含必要的参数
const hasRequiredParams = computed(() => {
  return route.query.modelId && route.query.filePath && route.query.fileName
})

// 检查是否可以从基本信息页面继续
const canContinueFromBasicInfo = computed(() => {
  if (modelSource.value === 'platform') {
    return hasRequiredParams.value
  } else {
    return true // 外部模型不需要检查这些字段
  }
})

// 处理模型来源选择
const handleModelSourceSelection = () => {
  if (modelSource.value === 'platform') {
    // 跳转到我的模型页面
    router.push('/model/my')
  } else {
    // 直接进入基本信息步骤
    activeStep.value = 1
  }
}

// 获取Minio配置
const fetchMinioConfig = async () => {
  try {
    const response = await service.get('/api/v1.0/sys/storage/minio/config/active/MODEL')
    if (response.code === 10000) {
      minioConfig.value = response.data
    } else {
      toast('错误', response.message || '获取存储配置失败', 'error')
    }
  } catch (error) {
    console.error('获取存储配置失败:', error)
    toast('错误', '获取存储配置失败', 'error')
  }
}

// 获取用户可用的模型资源
const fetchModelResources = async () => {
  try {
    const response = await service.get('/api/v1.0/sys/user/resources', {
      params: {
        page: 1,
        size: 1000
      }
    })
    if (response.code === 10000) {
      // 筛选出MODEL类型的资源
      modelResources.value = response.data.resources.filter(resource => resource.resourceType === 'MODEL')
    } else {
      toast('错误', response.message || '获取模型资源失败', 'error')
    }
  } catch (error) {
    console.error('获取模型资源失败:', error)
    toast('错误', '获取模型资源失败', 'error')
  }
}

// 如果没有必要参数，显示提示并返回
onMounted(() => {
  if (hasRequiredParams.value) {
    // 如果有必要参数，说明是从模型页面跳转过来的，直接进入基本信息步骤
    activeStep.value = 1
    modelSource.value = 'platform'
  }
  fetchMinioConfig()
  fetchModelResources()
})

// 下一步
const nextStep = () => {
  if (activeStep.value < 6) {
    activeStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--
  }
}

const handleNodeSelectionChange = (selection) => {
  selectedNodes.value = selection
}

const triggerFolderSelect = () => {
  folderInput.value.click()
}

const handleFolderSelect = (event) => {
  const files = event.target.files
  if (files.length > 0) {
    selectedFolder.value = files[0].webkitRelativePath.split('/')[0]
    codeFileList.value = Array.from(files)
    
    // 优先查找根目录下的 __init__.py
    const initFile = Array.from(files).find(f => {
      const path = f.webkitRelativePath
      // 检查是否在根目录下（路径中只有一个斜杠）
      return path.split('/').length === 2 && f.name === '__init__.py'
    })
    
    if (initFile) {
      const reader = new FileReader()
      reader.onload = (e) => {
        initContent.value = e.target.result
        hasMain.value = /def\s+main\s*\(/.test(initContent.value)
      }
      reader.readAsText(initFile)
    } else {
      initContent.value = ''
      hasMain.value = false
    }
  }
}

const canProceedUpload = computed(() => {
  return codeFileList.value.length > 0 && hasMain.value
})

const downloadDefaultTemplate = async () => {
  try {
    const response = await service.get('/api/v1.0/sys/template/download/scrip-slim', {
      responseType: 'blob'
    })

    // 创建下载链接
    const blob = new Blob([response], { type: 'application/zip' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'scrip-slim.zip'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    toast('成功', '模板下载成功', 'success')
  } catch (error) {
    console.error('下载模板失败:', error)
    toast('错误', '模板下载失败', 'error')
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件扩展名
const getFileExtension = (filename) => {
  const ext = filename.split('.').pop().toLowerCase()
  return ext ? `.${ext}` : '无扩展名'
}

// 获取文件类型标签样式
const getFileTypeTag = (filename) => {
  const ext = filename.split('.').pop().toLowerCase()
  const typeMap = {
    'py': 'success',
    'jinja2': 'warning',
    'json': 'info',
    'pyc': 'info',
    'md': '',
    'txt': ''
  }
  return typeMap[ext] || 'info'
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '未知'
  const date = new Date(timestamp)
  return date.toLocaleString()
}

// 删除单个文件
const removeFile = (index) => {
  codeFileList.value.splice(index, 1)
  // 如果删除的是 __init__.py，清空相关状态
  if (codeFileList.value.findIndex(f => f.name === '__init__.py') === -1) {
    initContent.value = ''
    hasMain.value = false
  }
}

// 清空所有文件
const clearFiles = () => {
  codeFileList.value = []
  initContent.value = ''
  hasMain.value = false
  selectedFolder.value = ''
}

// 查看节点详情
const viewNodeDetail = async (nodeId) => {
  nodeDetailVisible.value = true
  nodeDetailLoading.value = true
  
  try {
    const response = await service.get(`/api/v1.0/sys/node/${nodeId}`)
    if (response.code === 10000) {
      nodeDetail.value = response.data
    } else {
      toast('错误', response.message || '获取节点详情失败', 'error')
    }
  } catch (error) {
    console.error('获取节点详情失败:', error)
    toast('错误', '获取节点详情失败', 'error')
  } finally {
    nodeDetailLoading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString()
}

// 添加 deployModel 方法
const deployModel = async () => {
  try {
    // 创建 FormData 对象
    const formData = new FormData()
    
    // 添加文件
    for (const file of codeFileList.value) {
      const relativePath = file.webkitRelativePath.split('/').slice(1).join('/')
      const newFile = new File([file], relativePath, {
        type: file.type,
        lastModified: file.lastModified
      })
      formData.append('files', newFile)
    }
    
    // 添加节点ID和文件夹名称
    formData.append('nodeId', selectedNodes.value[0].nodeId)
    formData.append('folder_name', selectedFolder.value)

    // 调用部署接口
    const response = await service.post('/api/v1.0/sys/model/deploy', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (response.code === 10000) {
      deployResult.value = response.data
      toast('成功', '模型部署成功', 'success')
      nextStep()
    } else {
      toast('错误', response.message || '模型部署失败', 'error')
    }
  } catch (error) {
    console.error('模型部署失败:', error)
    toast('错误', '模型部署失败', 'error')
  }
}

// 添加 createTask 方法
const createTask = async () => {
  try {
    const taskDTO = {
      name: taskForm.value.name,
      description: taskForm.value.description,
      modelId: route.query.modelId,
      nodeId: selectedNodes.value[0].nodeId,
      modelPath: route.query.filePath,
      taskId: deployResult.value.task_id,
      savedFiles: deployResult.value.saved_files
    }

    const taskResponse = await service.post('/api/v1.0/sys/model/deploy/task', taskDTO)
    
    if (taskResponse.code === 10000) {
      toast('成功', '创建任务记录成功', 'success')
      // 创建成功后跳转到模型任务列表页
      router.push('/model/deploy/task')
    } else {
      toast('错误', taskResponse.message || '创建任务记录失败', 'error')
    }
  } catch (error) {
    console.error('创建任务记录失败:', error)
    toast('错误', '创建任务记录失败', 'error')
  }
}
</script>

<style scoped>
.model-deploy-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
.title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

.title-icon {
      margin-right: 8px;
  font-size: 24px;
      color: var(--el-color-primary);
}

    h2 {
  margin: 0;
      font-size: 24px;
  font-weight: 600;
    }
}

.sub-title {
  color: #909399;
  font-size: 14px;
    margin-left: 32px;
  }
}

.deploy-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.step-content {
  margin-top: 30px;
}

.step-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.info-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
}

:deep(.el-descriptions) {
  padding: 20px;
}

:deep(.el-descriptions__label) {
  width: 120px;
  font-weight: bold;
  font-size: 16px;
}

:deep(.el-descriptions__content) {
  font-size: 16px;
  font-family: monospace;
  word-break: break-all;
}

/* 深色模式样式 */
html.dark .deploy-card {
  background-color: var(--el-bg-color-overlay);
}

.selected-row {
  background-color: #e6f7ff !important;
}

.template-card {
  margin-bottom: 24px;
}
.template-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}
.code-preview {
  margin-top: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
  font-family: monospace;
  font-size: 14px;
  white-space: pre-wrap;
}
.preview-title {
  font-weight: bold;
  margin-bottom: 8px;
}
.upload-container {
  margin-bottom: 20px;
}

.selected-folder {
  margin-top: 10px;
  color: var(--el-text-color-regular);
}

.file-list-container {
  margin: 20px 0;
}

.file-list-container :deep(.el-table) {
  margin-top: 10px;
}

.file-list-container :deep(.el-tag) {
  text-transform: uppercase;
}

.file-details {
  padding: 10px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.file-details p {
  margin: 5px 0;
}

.task-form {
  max-width: 600px;
  margin: 0 auto;
}

.task-form :deep(.el-form-item__label) {
  font-weight: bold;
}

.file-tag {
  margin: 2px;
}

.model-source-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
}

.source-card {
  flex: 1;
  padding: 24px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.source-card:hover {
  box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
  border-color: var(--el-color-primary-light-5);
}

.source-card.active {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08);
}

.card-icon {
  font-size: 40px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.card-description {
  font-size: 14px;
  color: #909399;
}

/* 深色模式适配 */
html.dark .source-card {
  background-color: var(--el-bg-color);
  border-color: var(--el-border-color-darker);
}

html.dark .source-card.active {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}
</style>