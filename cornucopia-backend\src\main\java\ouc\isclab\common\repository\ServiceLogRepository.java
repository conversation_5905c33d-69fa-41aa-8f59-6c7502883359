package ouc.isclab.common.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import ouc.isclab.common.entity.ServiceLogEntity;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface ServiceLogRepository extends JpaRepository<ServiceLogEntity, Long> {

    @Query(nativeQuery = true, value = 
        "SELECT * FROM sys_service_log WHERE " +
        "(:ip IS NULL OR client_ip = :ip) AND " +
        "(:method IS NULL OR http_method = :method) AND " +
        "(:startTime IS NULL OR time_created >= :startTime) AND " +
        "(:endTime IS NULL OR time_created <= :endTime) " +
        "ORDER BY id DESC LIMIT :size OFFSET :offset")
    List<ServiceLogEntity> findLogs(
        @Param("offset") int offset,
        @Param("size") int size,
        @Param("ip") String ip,
        @Param("method") String method,
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime
    );

    @Query(nativeQuery = true, value = 
        "SELECT COUNT(*) FROM sys_service_log WHERE " +
        "(:ip IS NULL OR client_ip = :ip) AND " +
        "(:method IS NULL OR http_method = :method) AND " +
        "(:startTime IS NULL OR time_created >= :startTime) AND " +
        "(:endTime IS NULL OR time_created <= :endTime)")
    long countLogs(
        @Param("ip") String ip,
        @Param("method") String method,
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime
    );

    @Modifying
    @Query(nativeQuery = true, value =
        "DELETE FROM sys_service_log WHERE " +
        "(:ip IS NULL OR client_ip = :ip) AND " +
        "(:method IS NULL OR http_method = :method) AND " +
        "(:startTime IS NULL OR time_created >= :startTime) AND " +
        "(:endTime IS NULL OR time_created <= :endTime)")
    int deleteLogs(
         @Param("ip") String ip,
         @Param("method") String method,
         @Param("startTime") Date startTime,
         @Param("endTime") Date endTime
    );
}
