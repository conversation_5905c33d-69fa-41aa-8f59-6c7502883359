FROM ubuntu:latest

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone


RUN apt-get update && apt-get install -y --no-install-recommends \
    python3 \
    python3-pip \
    libgomp1 \
    zip \
    curl \
    lsof \
    ffmpeg \
    libsm6 \ 
    libxext6 \
    libgl1 \
    && rm -rf /var/lib/apt/lists/*

RUN pip3 install --no-cache-dir \
    torch \
    torchvision \
    numpy \
    pillow \
    pandas \
    matplotlib \
    tqdm \
    scikit-learn
RUN pip3 install --no-cache-dir \
    fastapi \
    uvicorn \
    flask \
    flask-socketio \
    websockets \
    httpx \
    requests \
    werkzeug
RUN pip3 install --no-cache-dir \
    datasets \
    evaluate \
    transformers \
    sentencepiece
RUN pip3 install --no-cache-dir \
    opencv-python-headless \
    ultralytics \
    minio
RUN pip3 install --no-cache-dir \
    prophet
RUN pip3 install --no-cache-dir \
    etcd3 \
    prefect
RUN pip3 install --no-cache-dir \
    protobuf

RUN mkdir -p /tmp/pycache
ENV PYTHONPYCACHEPREFIX=/tmp/pycache

EXPOSE 8000
EXPOSE 30000