package ouc.isclab.pyxis.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.pyxis.service.PyxisService;
import ouc.isclab.common.response.BaseResponse;
import java.util.Map;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/pyxis")
public class PyxisController {

    @Autowired
    private PyxisService pyxisService;

    @RequestMapping(value = "/{nodeId}/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    public Map<String, Object> forwardRequest(
        @CurrentUserId Long userId,
        @PathVariable Long nodeId,
        HttpServletRequest request
    ) {
        return pyxisService.forwardRequest(userId, nodeId, request);
    }

    @RequestMapping(value = "/download/{nodeId}/**", method = RequestMethod.GET)
    public ResponseEntity<byte[]> downloadFile(
        @CurrentUserId Long userId,
        @PathVariable Long nodeId,
        HttpServletRequest request
    ) {
        return pyxisService.downloadPyxisFile(userId, nodeId, request);
    }

    @PostMapping("/task/{nodeId}")
    public Map<String, Object> deployModel(
            @RequestParam("files") MultipartFile[] files,
            @PathVariable Long nodeId,
            @RequestParam(value = "folder_name", required = false) String folderName,
            @CurrentUserId Long userId) {
        log.info("部署模型: 节点ID={}, 用户ID={}, 文件夹名称={}", nodeId, userId, folderName);
        return pyxisService.createPyxisTask(nodeId, userId, files, folderName);
    }   

}
