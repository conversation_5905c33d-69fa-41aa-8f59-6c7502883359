from flask import (
    Flask, request,
    render_template, jsonify,
    Response, redirect,
)
import requests
import subprocess
import time
import os
import socket
import uuid
import threading
from collections import defaultdict
import shutil
import pathlib

app = Flask(__name__)

# Configuration
SERVICE_TIMEOUT_MINUTES = 30  # Service runtime in minutes
SUBPROCESS_SCRIPT = pathlib.Path(__file__).parent.resolve() / "scrip.py"  # Subprocess script filename
START_PORT = 5010  # Starting port for services

# Store active services {service_id: {'process': subprocess.Popen, 'port': int, 'stop_time': float}}
active_services = defaultdict(dict)
used_ports = set()

# Simplified upload template
UPLOAD_TEMPLATE = "upload.jinja2"

def is_port_available(port):
    """Check if port is available"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return True
        except socket.error:
            return False


def find_available_port(start_port=START_PORT):
    """Find an available port"""
    port = start_port
    while port <= 65535:
        if port not in used_ports and is_port_available(port):
            return port
        port += 1
    raise RuntimeError("No available ports found")


def start_subprocess_service(port, code_filename, service_id):
    """Start subprocess service"""
    
    return subprocess.Popen([
        'python3', os.path.abspath(SUBPROCESS_SCRIPT),
        f'--port={port}',
        f'--code={code_filename}',
        f'--service_id={service_id}',
        f'--timeout={SERVICE_TIMEOUT_MINUTES}'
    ])


@app.route('/', methods=['GET'])
def home():
    """Main page with upload form"""
    return render_template(UPLOAD_TEMPLATE)


@app.route('/service/<service_id>/', methods=['GET'])
@app.route('/service/<service_id>/<path:service_api>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'])
def access_service(service_id, service_api=''):
    if service_id not in active_services:
        return "Service not found or expired", 404

    port = active_services[service_id]['port']
    target_url = f'http://127.0.0.1:{port}/{service_api}'

    # Filter and set safe headers
    headers = {
        key: value for key, value in request.headers
        # Filter unsafe headers
        if key.lower() not in ['host', 'x-forwarded-for']
    }

    # Add necessary headers
    headers['X-Forwarded-For'] = request.remote_addr

    try:
        # Add timeout settings
        resp = requests.request(
            method=request.method,
            url=target_url,
            headers=headers,
            data=request.get_data(),
            cookies=request.cookies,
            allow_redirects=False,  # Handle redirects ourselves
            timeout=(10, 30)  # Connect and read timeout
        )

        # Handle redirects
        if 300 <= resp.status_code < 400:
            return redirect(resp.headers['Location'], code=resp.status_code)

        # Stream response
        response = Response(
            response=resp.iter_content(chunk_size=8192),
            status=resp.status_code,
            headers=dict(resp.headers)
        )

        # Remove sensitive headers
        response.headers.remove('Server')

        return response

    except requests.exceptions.Timeout:
        return "Backend service timeout", 504
    except requests.exceptions.ConnectionError:
        return "Failed to connect to backend service, please refresh and try again", 502
    except requests.exceptions.RequestException as e:
        return f"Request forwarding failed: {str(e)}", 500


@app.route('/upload', methods=['POST'])
def upload_code():
    """Handle code upload and service creation"""
    service_id = str(uuid.uuid4().hex)
    proc = None  # Track process for cleanup

    try:
        # Find available port
        port = find_available_port()
        used_ports.add(port)

        # Save user code
        code_filename = f'temp_{service_id}.py'
        with open(code_filename, 'w') as f:
            f.write(request.form['code'])

        # Start service process
        proc = start_subprocess_service(port, code_filename, service_id)

        # Record service info
        active_services[service_id] = {
            'process': proc,
            'port': port,
            'stop_time': time.time() + SERVICE_TIMEOUT_MINUTES * 60,
            'code_file': code_filename,
            'ready': False
        }

        # Start timeout thread
        threading.Thread(
            target=stop_service_after_timeout,
            args=(service_id,),
            daemon=True
        ).start()

        # Wait for service readiness
        if not wait_for_service_ready(service_id, port, proc):
            raise Exception("Service startup timed out")

        return jsonify({
            'status': 'success',
            'service_id': service_id,
            'access_url': f"/service/{service_id}/"
        })

    except Exception as e:
        # Force cleanup including process termination
        cleanup(service_id, force=True)
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


def wait_for_service_ready(service_id, port, proc, max_retries=10, retry_interval=1):
    """Wait for service to become ready with health check"""
    for _ in range(max_retries):
        returncode = proc.poll()
        if returncode is not None:
            raise Exception(f'Service process died with code {returncode}')

        try:
            resp = requests.get(f'http://127.0.0.1:{port}/health', timeout=1)
            if resp.status_code == 200:
                active_services[service_id]['ready'] = True
                return True
        except:
            pass
        time.sleep(retry_interval)
    return False


def cleanup(service_id, force=False):
    """Comprehensive cleanup with process termination"""
    if service_id not in active_services:
        return

    service = active_services[service_id]

    # Terminate process if forced or if still running
    if force or service['process'].poll() is None:
        service['process'].terminate()
        try:
            service['process'].wait(timeout=2)
        except subprocess.TimeoutExpired:
            service['process'].kill()

    # Release port
    if 'port' in service:
        used_ports.discard(service['port'])

    # Remove code file
    if 'code_file' in service and os.path.exists(service['code_file']):
        try:
            os.remove(service['code_file'])
        except Exception as e:
            print(f"Error removing code file: {e}")

    # Remove upload directory
    upload_folder = f'temp_uploads_{service_id}'
    if os.path.exists(upload_folder):
        try:
            shutil.rmtree(upload_folder)
        except Exception as e:
            print(f"Error removing upload folder: {e}")

    # Remove service record
    del active_services[service_id]
    print(f"Service {service_id} cleaned up {'(forced)' if force else ''}")


def stop_service_after_timeout(service_id, force=False):
    """Stop service after timeout"""
    if service_id not in active_services:
        return

    service = active_services[service_id]
    time_left = service['stop_time'] - time.time()

    if time_left > 0 and not force:
        time.sleep(time_left)

    cleanup(service_id, force=force)


@app.route('/services')
def list_services():
    """List all active services (for debugging)"""
    services = []
    for sid, info in active_services.items():
        services.append({
            'service_id': sid,
            'port': info['port'],
            'time_left': max(0, info['stop_time'] - time.time())
        })
    return jsonify(services)


def launch(port=5000):
    try:
        app.run(port=port, host='0.0.0.0')
    finally:
        # Cleanup all subprocesses
        for service_id in list(active_services.keys()):
            stop_service_after_timeout(service_id, force=True)


if __name__ == '__main__':
    launch()
