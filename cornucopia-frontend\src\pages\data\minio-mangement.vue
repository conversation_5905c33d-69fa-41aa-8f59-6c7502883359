<template>
  <div class="minio-management-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Setting /></el-icon>
          <h2>MinIO 存储服务管理</h2>
        </div>
        <div class="sub-title">管理 MinIO 对象存储服务的配置信息</div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshConfig" plain round>
          <el-icon><Refresh /></el-icon>刷新配置
        </el-button>
      </div>
    </div>

    <el-card class="config-card" shadow="hover" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>存储服务配置</span>
          <el-button type="primary" @click="handleEdit" size="small">
            <el-icon><Edit /></el-icon>编辑配置
          </el-button>
        </div>
      </template>

      <div class="config-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="服务端点 (Endpoint)">
            <el-link type="primary" :href="minioConfig.endpoint" target="_blank">
              {{ minioConfig.endpoint }}
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="访问密钥 (Access Key)">
            <div class="secret-value">
              <span>{{ maskValue(minioConfig.accessKey) }}</span>
              <el-button type="primary" link size="small" v-copy="minioConfig.accessKey">
                复制
              </el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="密钥 (Secret Key)">
            <div class="secret-value">
              <span>{{ maskValue(minioConfig.secretKey) }}</span>
              <el-button type="primary" link size="small" v-copy="minioConfig.secretKey">
                复制
              </el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="存储桶 (Bucket)">
            {{ minioConfig.bucket }}
          </el-descriptions-item>
          <el-descriptions-item label="管理控制台">
            <el-link type="primary" :href="getConsoleUrl()" target="_blank">
              {{ getConsoleUrl() }}
            </el-link>
          </el-descriptions-item>
        </el-descriptions>

        <div class="status-section">
          <div class="section-header">
            <h3>服务状态</h3>
            <el-button 
              type="primary" 
              link 
              size="small" 
              @click="checkServiceStatus" 
              :loading="statusLoading"
            >
              <el-icon v-if="!statusLoading"><Refresh /></el-icon>
              刷新状态
            </el-button>
          </div>
          
          <div class="status-overview">
            <el-tag :type="serviceStatus.online ? 'success' : 'danger'" class="status-tag">
              <el-icon class="status-icon"><component :is="serviceStatus.online ? 'Check' : 'Close'" /></el-icon>
              {{ serviceStatus.online ? '在线' : '离线' }}
            </el-tag>
            <span class="last-check">上次检查: {{ lastCheckTime }}</span>
          </div>
          
          <div v-loading="statusLoading" element-loading-text="正在检查服务状态...">
            <div class="status-details" v-if="serviceStatus.online">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="status-card">
                    <div class="status-card-icon">
                      <el-icon><Folder /></el-icon>
                    </div>
                    <div class="status-card-content">
                      <div class="status-card-value">{{ serviceStatus.bucketCount }}</div>
                      <div class="status-card-label">存储桶数量</div>
                    </div>
                  </div>
                </el-col>
                
                <el-col :span="8">
                  <div class="status-card">
                    <div class="status-card-icon">
                      <el-icon><Document /></el-icon>
                    </div>
                    <div class="status-card-content">
                      <div class="status-card-value">{{ formatNumber(serviceStatus.objectCount) }}</div>
                      <div class="status-card-label">总对象数</div>
                    </div>
                  </div>
                </el-col>
                
                <el-col :span="8">
                  <div class="status-card">
                    <div class="status-card-icon">
                      <el-icon><DataLine /></el-icon>
                    </div>
                    <div class="status-card-content">
                      <div class="status-card-value">{{ formatSize(serviceStatus.totalSize) }}</div>
                      <div class="status-card-label">总存储容量</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 编辑配置对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑 MinIO 配置" width="500px">
      <el-form :model="editForm" label-width="120px" :rules="rules" ref="editFormRef">
        <el-form-item label="服务端点" prop="endpoint">
          <el-input v-model="editForm.endpoint" placeholder="例如: http://10.140.34.208:9000"></el-input>
        </el-form-item>
        <el-form-item label="访问密钥" prop="accessKey">
          <el-input v-model="editForm.accessKey" placeholder="输入 Access Key"></el-input>
        </el-form-item>
        <el-form-item label="密钥" prop="secretKey">
          <el-input v-model="editForm.secretKey" placeholder="输入 Secret Key" show-password></el-input>
        </el-form-item>
        <el-form-item label="存储桶" prop="bucket">
          <el-input v-model="editForm.bucket" placeholder="输入默认存储桶名称"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit" :loading="submitting">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { Setting, Edit, Refresh, Check, Close, Folder, Document, DataLine } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

// 加载状态
const loading = ref(false)
const submitting = ref(false)
const statusLoading = ref(false)  // 新增状态检查的加载状态

// MinIO 配置
const minioConfig = reactive({
  endpoint: '',
  accessKey: '',
  secretKey: '',
  bucket: ''
})

// 服务状态
const serviceStatus = reactive({
  online: false,
  bucketCount: 0,
  objectCount: 0,
  totalSize: 0
})

// 上次检查时间
const lastCheckTime = ref('--')

// 编辑对话框
const editDialogVisible = ref(false)
const editFormRef = ref(null)
const editForm = reactive({
  endpoint: '',
  accessKey: '',
  secretKey: '',
  bucket: ''
})

// 表单验证规则
const rules = {
  endpoint: [
    { required: true, message: '请输入服务端点', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: '端点必须以 http:// 或 https:// 开头', trigger: 'blur' }
  ],
  accessKey: [
    { required: true, message: '请输入访问密钥', trigger: 'blur' }
  ],
  secretKey: [
    { required: true, message: '请输入密钥', trigger: 'blur' }
  ],
  bucket: [
    { required: true, message: '请输入存储桶名称', trigger: 'blur' }
  ]
}

// 获取 MinIO 配置
const getMinioConfig = async () => {
  loading.value = true
  try {
    const res = await service.get('/api/v1.0/sys/storage/minio/config')
    if (res.code === 10000) {
      Object.assign(minioConfig, res.data)
      checkServiceStatus()
    } else {
      toast('错误', res.message || '获取 MinIO 配置失败', 'error')
    }
  } catch (error) {
    console.error('获取 MinIO 配置失败:', error)
    toast('错误', '获取 MinIO 配置失败', 'error')
  } finally {
    loading.value = false
  }
}

// 检查服务状态
const checkServiceStatus = async () => {
  statusLoading.value = true
  try {
    const res = await service.get('/api/v1.0/sys/storage/minio/status')
    if (res.code === 10000) {
      Object.assign(serviceStatus, res.data)
      // 更新检查时间
      lastCheckTime.value = new Date().toLocaleTimeString()
    } else {
      serviceStatus.online = false
    }
  } catch (error) {
    console.error('检查服务状态失败:', error)
    serviceStatus.online = false
  } finally {
    statusLoading.value = false
  }
}

// 刷新配置
const refreshConfig = () => {
  getMinioConfig()
}

// 处理编辑按钮点击
const handleEdit = () => {
  Object.assign(editForm, minioConfig)
  editDialogVisible.value = true
}

// 提交编辑
const submitEdit = async () => {
  if (!editFormRef.value) return
  
  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const res = await service.post('/api/v1.0/sys/storage/minio/config', editForm)
        if (res.code === 10000) {
          toast('成功', 'MinIO 配置已更新', 'success')
          editDialogVisible.value = false
          getMinioConfig()
        } else {
          toast('错误', res.message || '更新 MinIO 配置失败', 'error')
        }
      } catch (error) {
        console.error('更新 MinIO 配置失败:', error)
        toast('错误', '更新 MinIO 配置失败', 'error')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 掩码显示敏感信息
const maskValue = (value) => {
  if (!value) return '******'
  if (value.length <= 8) return '******'
  return value.substring(0, 4) + '******' + value.substring(value.length - 4)
}

// 获取控制台 URL
const getConsoleUrl = () => {
  if (!minioConfig.endpoint) return ''
  // 假设控制台端口比API端口大1
  return minioConfig.endpoint.replace(':9000', ':9001')
}

// 格式化数字（添加千位分隔符）
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 格式化文件大小
const formatSize = (size) => {
  if (!size) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  let fileSize = size
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }
  
  return `${fileSize.toFixed(2)} ${units[index]}`
}

// 页面加载时获取配置
onMounted(() => {
  getMinioConfig()
})
</script>

<style scoped>
.minio-management-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.title-icon {
  margin-right: 8px;
  font-size: 24px;
  color: var(--el-color-primary);
}

h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.sub-title {
  color: #909399;
  font-size: 14px;
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.secret-value {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.status-overview {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.status-tag {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 500;
}

.status-icon {
  margin-right: 6px;
}

.last-check {
  margin-left: 12px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

.status-details {
  margin-top: 20px;
}

.status-card {
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid var(--el-border-color-light);
  height: 100%;
}

.status-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.status-card-icon .el-icon {
  font-size: 24px;
}

.status-card-content {
  flex-grow: 1;
}

.status-card-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.2;
  margin-bottom: 4px;
}

.status-card-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

/* 深色模式适配 */
html.dark .status-card {
  background-color: var(--el-bg-color);
  border-color: var(--el-border-color-darker);
}

html.dark .status-card-icon {
  background-color: var(--el-color-primary-dark-2);
  color: #ffffff;
}
</style>
