from .scrip_slim import create_app, run_app, TemplateLoader
from pathlib import Path


def get_model():

    import pdfplumber
    import jieba

    class Model:
        def __init__(self):
            pass

        def find_most_overlapping_text(self, input_text, text_list):
            input_words = set(jieba.cut(input_text))
            max_overlap = -1
            best_text = ""

            for text in text_list:
                text_words = set(jieba.cut(text))
                overlap = len(input_words & text_words)
                if overlap > max_overlap:
                    max_overlap = overlap
                    best_text = text

            return best_text, max_overlap

        def _extract_text(self, pdf_paths):
            if isinstance(pdf_paths, str):
                pdf_paths = [pdf_paths]

            context = []
            for path in pdf_paths:
                try:
                    with pdfplumber.open(path) as pdf:
                        for page in pdf.pages:
                            lines = page.extract_text_lines()
                            if lines:
                                context.extend(line['text'] for line in lines)
                except Exception as e:
                    print(e)
                    continue

            return context

        def pipe(self, question: str, files, **kwargs) -> str:
            context = self._extract_text(files)
            # A contextual Q&A model should be used here,
            # but due to the developer's computer limitations,
            # a simple find_most_overlapping_text function is used as a mock.
            text, overlap = self.find_most_overlapping_text(question, context)

            return {"question": question, "answer": text}

    model = Model()

    return model


def inputprocessor(app, request):
    TEMP_DIR = Path(app.config["temp_dir"])

    question = request.form.get('question')

    files = request.files.getlist('file')

    paths = []
    for file in files:
        if file.filename == '':
            continue
        file.save(TEMP_DIR / f'{file.filename}')
        paths.append(TEMP_DIR / f'{file.filename}')

    return {
        "question": question,
        "files": paths
    }


def main(port=8000):

    app = create_app(
        get_model=get_model,
        preprocessor=inputprocessor,
        timeout_minutes=120,
        input_template_config={
            "title": "Question-Answer",
            "fields": [
                {
                    "type": "text",
                    "label": "Question:",
                    "name": "question"
                }, {
                    "type": "multifile",
                    "label": "lib documents",
                    "name": "file",
                    "required":True
                }
            ],
            "history": True
        },
        output_template_config={
            "fields": [{
                "type": "text",
                "label": "Question:",
                "name": "question"
            }, {
                "type": "text",
                "label": "Answer",
                "name": "answer"
            }]
        }
    )
    run_app(app, port)
