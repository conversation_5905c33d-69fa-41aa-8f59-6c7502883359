<template>
    
    <el-col :span="24">
      <el-menu
        :router="true"
        :default-active="$route.path"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
      >
      <el-menu-item index="/data">
          <el-icon><DataLine /></el-icon>
          <span>总览</span>
        </el-menu-item>  

        <el-sub-menu index="my" v-permission="['menu:data:my']">  
          <template #title>
            <el-icon><User /></el-icon>
            <span>我的数据</span>
          </template>
          <el-menu-item-group title="管理">
            <el-menu-item index="/data/my">查看我的数据</el-menu-item>
          </el-menu-item-group>
        </el-sub-menu>
      
        <el-sub-menu index="data">  
          <template #title>
            <el-icon><Box /></el-icon>
            <span>数据列表</span>
          </template>
          <el-menu-item-group title="查看">
            <el-menu-item index="/data/list">查看所有数据</el-menu-item>
          </el-menu-item-group>
        </el-sub-menu>

        <!-- <el-sub-menu index="minio" v-permission="['menu:data:minio']">  
          <template #title>
            <el-icon><Money /></el-icon>
            <span>存储服务管理</span>
          </template>
          <el-menu-item-group title="管理">
            <el-menu-item index="/data/minio">数据存储桶总览</el-menu-item>
            <el-menu-item index="/data/minio/config">数据存储桶配置信息</el-menu-item>
            <el-menu-item index="/data/minio/list">数据存储桶列表</el-menu-item>
          </el-menu-item-group>
        </el-sub-menu> -->
        <el-menu-item index="/">
          <el-icon><IconMenu /></el-icon>
          <span>后台首页</span>
        </el-menu-item>
        
      </el-menu>
    </el-col>
  </template>

<script lang="ts" setup>
import {
  User,
  Menu as IconMenu,
  DataLine,
  Box,
  Money
} from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
</script>

<style scoped>
.el-menu-vertical-demo {
  width: 100%;
  border-right: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  font-family: system-ui, -apple-system, 'PingFang SC', 'Microsoft Yahei UI', 
    'Microsoft Yahei', 'Helvetica Neue', Helvetica, sans-serif;
}

.el-menu {
  height: 100%;
  padding: 6px 0;
  background: var(--el-bg-color);
}

:deep(.el-menu-item), :deep(.el-sub-menu__title) {
  height: 52px;
  line-height: 52px;
  margin: 2px 0;
  padding: 0 20px !important;
  transition: all 0.3s ease;
  font-size: 15px;
  letter-spacing: 0.3px;
  border: 1px solid transparent;
  color: var(--el-text-color-primary);
  font-weight: 400;
  display: flex;
  align-items: center;
  background-color: var(--el-bg-color);
}

:deep(.el-menu-item:hover), :deep(.el-sub-menu__title:hover) {
  background-color: var(--el-color-primary-light-9) !important;
  border-color: var(--el-color-primary-light-7);
}

:deep(.el-menu-item.is-active) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  font-weight: 500;
  text-shadow: 0 0 0.25px currentColor;
  border: 1px solid var(--el-color-primary-light-5);
  box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.1);
}

:deep(.el-sub-menu .el-menu-item) {
  min-width: auto;
  margin: 4px 0;
  height: 48px;
  line-height: 48px;
  padding: 0 36px !important;
  font-size: 14px;
  color: var(--el-text-color-regular);
  background-color: var(--el-bg-color);
}

:deep(.el-menu-item-group__title) {
  padding: 12px 28px 8px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
  font-weight: 400;
  letter-spacing: 0.2px;
  opacity: 0.85;
  line-height: 1.4;
}

:deep(.el-icon) {
  margin-right: 12px;
  font-size: 17px;
  flex-shrink: 0;
  color: var(--el-text-color-regular);
}

:deep(.is-active .el-icon) {
  color: var(--el-color-primary);
}

/* 深色模式特定样式 */
html.dark {
  :deep(.el-menu-item:hover), :deep(.el-sub-menu__title:hover) {
    background-color: var(--el-color-primary-dark-2) !important;
    border-color: var(--el-color-primary-dark-1);
  }

  :deep(.el-menu-item.is-active) {
    background-color: var(--el-color-primary-dark-2);
    border-color: var(--el-color-primary-dark-1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    color: #ffffff;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
  }

  .el-menu {
    background: var(--el-bg-color-overlay);
  }

  :deep(.el-sub-menu.is-opened > .el-sub-menu__title) {
    background-color: var(--el-color-primary-dark-2);
    border-color: var(--el-color-primary-dark-1);
  }
  
  :deep(.is-active .el-icon) {
    color: #ffffff;
  }
  
  :deep(.el-sub-menu .el-menu-item.is-active) {
    background-color: var(--el-color-primary-dark-1);
    color: #ffffff;
    font-weight: 500;
  }
  
  :deep(.el-menu-item:hover), :deep(.el-sub-menu__title:hover) {
    color: #ffffff;
  }
}

/* 添加子菜单展开时的样式 */
:deep(.el-sub-menu.is-opened > .el-sub-menu__title) {
  border-color: var(--el-color-primary-light-7);
  background-color: var(--el-color-primary-light-9);
}

/* 优化菜单组标题样式 */
:deep(.el-menu-item-group__title) {
  padding: 12px 28px 8px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
  font-weight: 400;
  letter-spacing: 0.2px;
  opacity: 0.85;
  line-height: 1.4;
}

/* 图标样式优化 */
:deep(.el-icon) {
  margin-right: 12px;
  font-size: 17px;
  flex-shrink: 0;
}

:deep(.is-active .el-icon) {
  color: var(--el-color-primary);
}

/* 文字容器样式 */
:deep(.el-menu-item span), :deep(.el-sub-menu__title span) {
  display: inline-block;
  vertical-align: middle;
  line-height: normal;
}

/* 子菜单标题箭头位置调整 */
:deep(.el-sub-menu__title .el-sub-menu__icon-arrow) {
  right: 8px;
  margin-top: -6px;
}

/* 确保子菜单内容正确对齐 */
:deep(.el-menu--inline) {
  padding: 4px 0;
}

/* 添加文字平滑效果 */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
