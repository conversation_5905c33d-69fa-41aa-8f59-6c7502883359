<template>
  <div class="user-overview-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><User /></el-icon>
          <h2>用户总览</h2>
        </div>
        <div class="sub-title">查看系统用户和角色的整体统计信息</div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="stat in statistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户角色分布图表 -->
    <el-row>
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><DataLine /></el-icon>
                <span>用户角色分布</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="pieChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { 
  User, 
  UserFilled, 
  Lock,
  CircleCheck,
  DataLine
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'
import * as echarts from 'echarts'

const pageLoading = ref(false)
const pieChartRef = ref(null)
let pieChart = null

// 统计数据
const statistics = ref([
  {
    title: '总用户数',
    value: 0,
    type: 'primary',
    icon: User
  },
  {
    title: '在线用户',
    value: 0,
    type: 'success',
    icon: CircleCheck
  },
  {
    title: '总角色数',
    value: 0,
    type: 'warning',
    icon: Lock
  },
  {
    title: '活跃用户',
    value: 0,
    type: 'info',
    icon: UserFilled
  }
])

// 获取用户统计数据
const fetchStatistics = async () => {
  pageLoading.value = true
  try {
    const res = await service.get('/api/v1.0/sys/user/statistics')
    if (res.code === 10000) {
      statistics.value[0].value = res.data.totalUsers || 0
      statistics.value[1].value = res.data.onlineUsers || 0
      statistics.value[2].value = res.data.totalRoles || 0
      statistics.value[3].value = res.data.activeUsers || 0
      updatePieChart()
    }
  } catch (error) {
    toast('错误', '获取统计数据失败', 'error')
  } finally {
    pageLoading.value = false
  }
}

// 初始化饼图
const initPieChart = () => {
  if (pieChartRef.value) {
    pieChart = echarts.init(pieChartRef.value)
    updatePieChart()
  }
}

// 更新饼图数据
const updatePieChart = () => {
  if (!pieChart) return
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: statistics.value[1].value, 
            name: '在线用户',
            itemStyle: { color: '#67C23A' }
          },
          { 
            value: statistics.value[3].value, 
            name: '活跃用户',
            itemStyle: { color: '#909399' }
          },
          { 
            value: statistics.value[0].value - statistics.value[1].value, 
            name: '离线用户',
            itemStyle: { color: '#E6A23C' }
          }
        ]
      }
    ]
  }
  
  pieChart.setOption(option)
}

onMounted(async () => {
  await fetchStatistics()
  nextTick(() => {
    initPieChart()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    pieChart?.resize()
  })
})

onUnmounted(() => {
  // 销毁图表实例
  pieChart?.dispose()
  
  // 移除事件监听器
  window.removeEventListener('resize', () => {
    pieChart?.resize()
  })
})
</script>

<style scoped>
.user-overview-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.stat-card {
  height: 120px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 8px;
}

.stat-icon.primary { background-color: var(--el-color-primary-light-9); }
.stat-icon.success { background-color: var(--el-color-success-light-9); }
.stat-icon.warning { background-color: var(--el-color-warning-light-9); }
.stat-icon.info { background-color: var(--el-color-info-light-9); }

.stat-info {
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  
  .stat-title {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.chart-card {
  height: 400px;
  overflow: hidden;
}

.chart-container {
  height: 340px;
  padding: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  
  .icon {
    margin-right: 8px;
    font-size: 18px;
    color: var(--el-color-primary);
  }
}

/* 深色模式适配 */
html.dark {
  .stat-card,
  .chart-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style> 