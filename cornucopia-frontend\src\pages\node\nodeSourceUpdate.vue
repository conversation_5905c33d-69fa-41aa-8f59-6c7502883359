<template>
  <div class="update-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Refresh /></el-icon>
          <h2>更新节点资源</h2>
        </div>
        <div class="sub-title">更新所选节点的资源信息</div>
      </div>
      <div class="header-right">
        <el-button 
          @click="$router.push('/node/resource/list')"
          plain
          round
        >
          <el-icon><Back /></el-icon>
          返回资源列表
        </el-button>
      </div>
    </div>

    <div class="main-content" v-loading="loading" element-loading-text="正在更新节点资源...">
      <el-form :model="form" label-width="120px">
        <el-form-item label="选择节点">
          <el-select 
            v-model="selectedNode" 
            placeholder="请选择要更新的节点"
            style="width: 100%"
          >
            <el-option
              v-for="node in nodeList"
              :key="node.id"
              :label="node.name"
              :value="node.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleUpdate"
            :loading="loading"
            :disabled="!selectedNode"
          >
            更新资源信息
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Refresh, Back } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import service from '~/axios'
import { toast } from '~/composables/util'

const router = useRouter()
const loading = ref(false)
const nodeList = ref([])
const selectedNode = ref('')

// 获取节点列表
const fetchNodes = async () => {
  try {
    const res = await service.get('/api/v1.0/sys/nodes')
    if (res.code === 10000) {
      nodeList.value = res.data.nodes
    }
  } catch (error) {
    console.error('获取节点列表失败:', error)
    toast('错误', '获取节点列表失败', 'error')
  }
}

// 更新方法
const handleUpdate = async () => {
  if (!selectedNode.value) {
    toast('警告', '请选择要更新的节点', 'warning')
    return
  }

  try {
    loading.value = true
    const response = await service.put(`/api/v1.0/sys/nodeResource/${selectedNode.value}`, null, {
      timeout: 60000
    })
    
    if (response.code === 10000) {
      toast('成功', '节点资源信息已更新')
      router.push('/node/resource/list')
    } else {
      toast('错误', response.message || '更新失败', 'error')
    }
  } catch (error) {
    console.error('更新节点资源失败:', error)
    if (error.code === 'ECONNABORTED') {
      toast('错误', '请求超时，请稍后重试', 'error')
    } else {
      toast('错误', '更新失败', 'error')
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchNodes()
})
</script>

<style scoped>
.update-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.main-content {
  margin: 20px auto;
  max-width: 600px;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-loading-spinner) {
  .el-loading-text {
    color: var(--el-text-color-primary);
    font-size: 16px;
    margin-top: 20px;
  }
}

/* 深色模式适配 */
html.dark .main-content {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}
</style> 