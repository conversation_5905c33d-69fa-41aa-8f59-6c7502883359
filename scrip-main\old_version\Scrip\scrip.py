from flask import (
    Flask, request,
    jsonify, render_template
)
import importlib.util
import sys
import argparse
import time
import threading
import os
import signal
from werkzeug.utils import secure_filename
import shutil

app = Flask(__name__)


def create_app(port, code_file, service_id, timeout_minutes=30):
    """Initialize the child service application"""
    # Configure upload directory
    upload_folder = os.path.join('/app/temp_uploads', f'service_{service_id}')
    os.makedirs(upload_folder, exist_ok=True)
    app.config['UPLOAD_FOLDER'] = upload_folder

    if not os.path.exists(code_file):
        raise Exception(f"code file do not exist in path {code_file}")

    def load_user_model(model_path):
        """Dynamically load user-provided model"""
        spec = importlib.util.spec_from_file_location("user_model", model_path)
        if not spec:
            raise ImportError(f"Could not load model from {model_path}")
        module = importlib.util.module_from_spec(spec)
        sys.modules["user_model"] = module
        spec.loader.exec_module(module)

        if not hasattr(module, 'get_model'):
            raise AttributeError("Model module must implement 'get_model()'")
        return module.get_model()

    # Load the model
    try:
        print(f"loading code file:{code_file}")
        model = load_user_model(code_file)
        print(f"Service {service_id} started on port {port}")
    except Exception as e:
        print(f"Model loading failed: {str(e)}")
        return None

    def cleanup_resources():
        """Clean up temporary resources"""
        try:
            if os.path.exists(upload_folder):
                shutil.rmtree(upload_folder)
        except Exception as e:
            print(f"Cleanup error: {str(e)}")

    def shutdown_server():
        """Graceful shutdown after timeout"""
        time.sleep(timeout_minutes * 60)
        print(f"Shutting down service {service_id} (timeout reached)")
        cleanup_resources()
        os.kill(os.getpid(), signal.SIGTERM)

    @app.route('/predict', methods=['POST'])
    def predict():
        """Handle prediction requests"""
        if model is None:
            return jsonify({
                "status": "error",
                "message": "Model not loaded"
            }), 500

        if 'file' not in request.files:
            return jsonify({
                "status": "error",
                "message": "No file uploaded"
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "Empty filename"
            }), 400

        filepath = None
        try:
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            predictions = model.predict(filepath)
            return jsonify({
                "status": "success",
                "predictions": predictions,
                "service_id": service_id
            })
        except Exception as e:
            return jsonify({
                "status": "error",
                "message": str(e),
                "service_id": service_id
            }), 500
        finally:
            if filepath and os.path.exists(filepath):
                os.remove(filepath)

    destroy_time = int((time.time() + timeout_minutes * 60 - 30) * 1000)

    @app.route('/')
    def home():
        """Service information page"""
        return render_template(
            'deploy.jinja2',
            destroy_time=destroy_time,
            service_id=service_id
        )

    @app.route('/health')
    def health():
        """Health check endpoint"""
        return jsonify({
            "status": "healthy",
            "service_id": service_id
        }), 200

    # Start shutdown thread
    threading.Thread(target=shutdown_server, daemon=True).start()
    return app


def parse_arguments():
    parser = argparse.ArgumentParser()

    parser.add_argument('--port',
                        type=int,
                        default=int(os.getenv('PORT', '5000')),
                        help='Service port (default: $PORT or 5000)')
    parser.add_argument('--code',
                        default=os.getenv(
                            'CODE', '/app/user_code/user_code.py'),
                        help='Model code path (default: $CODE)')
    parser.add_argument('--service_id',
                        default=os.getenv('SERVICE_ID', 'default'),
                        help='Service ID (default: $SERVICE_ID)')
    parser.add_argument('--timeout',
                        type=int,
                        default=int(os.getenv('TIMEOUT', '30')),
                        help='Timeout in minutes (default: $TIMEOUT or 30)')

    return parser.parse_args()


if __name__ == '__main__':
    args = args = parse_arguments()

    app = create_app(args.port, args.code, args.service_id, args.timeout)
    if app:
        app.run(host='0.0.0.0', port=args.port)
    else:
        sys.exit(1)
