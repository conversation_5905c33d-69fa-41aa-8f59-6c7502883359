<template>
  <div class="minio-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><List /></el-icon>
          <h2>MinIO 存储配置</h2>
        </div>
        <div class="sub-title">管理系统中的 MinIO 存储配置</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <div class="action-buttons">
            <span class="filter-label">筛选类型：</span>
            <el-select
              v-model="filterForm.type"
              placeholder="请选择类型"
              clearable
              @clear="handleFilter"
              @change="handleFilter"
              class="type-select"
            >
              <el-option label="全部" value="" />
              <el-option label="数据集" value="DATASET" />
              <el-option label="模型" value="MODEL" />
            </el-select>
            <el-button type="primary" @click="handleAdd" plain round>
              <el-icon><Plus /></el-icon>新增配置
            </el-button>
            <el-button 
              type="danger" 
              :disabled="!selectedIds.length"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <!-- MinIO 配置表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        border
        stripe
        :default-sort="defaultSort"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        highlight-current-row
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="name" label="配置名称" align="center" min-width="120" />
        <el-table-column prop="endpoint" label="服务地址" align="center" min-width="180" />
        <el-table-column prop="bucket" label="存储桶" align="center" min-width="120" />
        <el-table-column prop="type" label="类型" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'DATASET' ? 'success' : 'warning'" size="small">
              {{ scope.row.type === 'DATASET' ? '数据集' : '模型' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="active" label="状态" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.active ? 'success' : 'danger'" size="small">
              {{ scope.row.active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="timeCreated" label="创建时间" align="center" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" align="center" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  type="info"
                  size="small"
                  @click.stop="handleDetail(scope.row)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="进入桶" placement="top" v-if="scope.row.active">
                <el-button
                  type="success"
                  size="small"
                  @click.stop="handleEnterBucket(scope.row)"
                >
                  <el-icon><Folder /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑配置" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="handleEdit(scope.row)"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除配置" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click.stop="handleDelete(scope.row)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip :content="scope.row.active ? '禁用配置' : '启用配置'" placement="top">
                <el-button
                  :type="scope.row.active ? 'warning' : 'success'"
                  size="small"
                  @click.stop="handleToggleEnable(scope.row)"
                >
                  <el-icon>
                    <component :is="scope.row.active ? 'Lock' : 'Unlock'" />
                  </el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Search, 
  Refresh, 
  Plus, 
  Delete, 
  Edit, 
  View,
  Folder,
  Lock,
  Unlock
} from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import service from '~/axios'

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const selectedIds = ref([])

const filterForm = reactive({
  name: '',
  type: '',
  active: ''
})

// 获取 MinIO 配置列表
const fetchConfigs = async (page = 1) => {
  loading.value = true
  try {
    let url = '/api/v1.0/sys/storage/minio/config'
    const params = {
      page: page,
      size: pageSize.value
    }
    
    // 如果选择了类型，使用类型筛选接口
    if (filterForm.type) {
      url = `/api/v1.0/sys/storage/minio/config/type/${filterForm.type}`
    }
    
    // 添加其他筛选参数
    if (filterForm.name) {
      params.name = filterForm.name
    }
    if (filterForm.active !== '') {
      params.active = filterForm.active
    }
    
    const response = await service.get(url, { params })
    if (response.code === 10000) {
      tableData.value = response.data.configs
      total.value = response.data.pagination.total
      pageSize.value = response.data.pagination.size
    }
  } catch (error) {
    console.error('获取 MinIO 配置列表失败:', error)
    toast('错误', '获取配置列表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 新增配置
const handleAdd = () => {
  router.push('/minio/create')
}

// 编辑配置
const handleEdit = (row) => {
  router.push({
    path: '/minio/update',
    query: { id: row.id }
  })
}

// 删除配置
const handleDelete = async (row) => {
  try {
    await showModal('确定要删除该配置吗？', 'warning', '提示')
    const response = await service.delete(`/api/v1.0/sys/storage/minio/config/${row.id}`)
    
    if (response?.code === 10000) {
      toast('成功', '删除成功')
      fetchConfigs(currentPage.value)
    } else {
      toast('错误', response?.message || '删除失败', 'error')
    }
  } catch (error) {
    console.error('删除配置失败:', error)
    if (error !== 'cancel') {
      toast('错误', '删除失败', 'error')
    }
  }
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.id)
}

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedIds.value.length) {
    toast('警告', '请选择要删除的配置', 'warning')
    return
  }
  
  try {
    await showModal(`确定要删除选中的 ${selectedIds.value.length} 个配置吗？`, 'warning', '提示')
    
    const promises = selectedIds.value.map(id => 
      service.delete(`/api/v1.0/sys/storage/minio/config/${id}`)
    )
    
    await Promise.all(promises)
    toast('成功', '批量删除成功')
    selectedIds.value = []
    fetchConfigs(currentPage.value)
  } catch (error) {
    console.error('批量删除配置失败:', error)
    if (error !== 'cancel') {
      toast('错误', '批量删除失败', 'error')
    }
  }
}

// 页码变化
const handleCurrentChange = (val) => {
  fetchConfigs(val)
}

// 每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchConfigs(1)
}

// 启用/禁用配置
const handleToggleEnable = async (row) => {
  try {
    await showModal(
      `确定要${row.active ? '禁用' : '启用'}配置吗？`,
      'warning',
      '提示'
    )
    
    const res = await service.put(
      `/api/v1.0/sys/storage/minio/config/${row.id}/active`
    )
    
    if (res.code === 10000) {
      row.active = !row.active
      toast(
        '成功', 
        `配置${row.active ? '启用' : '禁用'}成功`, 
        row.active ? 'success' : 'warning'
      )
      fetchConfigs(currentPage.value)
    } else {
      throw new Error(res.message || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', error.message || '操作失败', 'error')
    }
  }
}

// 处理筛选
const handleFilter = () => {
  currentPage.value = 1
  fetchConfigs(1)
}

const formatDateTime = (row) => {
  if (!row.timeCreated) return '-'
  const date = new Date(row.timeCreated)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const sortByTime = (a, b) => {
  const timeA = new Date(a.timeCreated).getTime()
  const timeB = new Date(b.timeCreated).getTime()
  return timeA - timeB
}

const defaultSort = {
  prop: 'timeCreated',
  order: 'descending'
}

// 处理详情按钮点击
const handleDetail = (row) => {
  router.push({
    path: '/minio/detail',
    query: { id: row.id }
  })
}

// 处理进入桶按钮点击
const handleEnterBucket = (row) => {
  // 根据类型跳转到不同的 bucket 列表页面
  const type = row.type === 'DATASET' ? 'datasets' : 'models'
  router.push({
    path: '/minio/bucket',
    query: { type }
  })
}

onMounted(() => {
  fetchConfigs()
})
</script>

<style scoped>
.minio-list-container {
  padding: 20px;
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

:deep(.el-table th) {
  font-weight: bold;
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.action-buttons {
  display: flex;
  align-items: center;
}

.filter-label {
  color: var(--el-text-color-regular);
  font-size: 14px;
  margin-right: 8px;
}

.type-select {
  width: 120px;
  margin-right: 24px;
}

:deep(.type-select .el-input__wrapper) {
  border-radius: 20px;
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
}

/* 调整按钮间距 */
.action-buttons .el-button {
  margin-right: 16px;
}

.action-buttons .el-button:last-child {
  margin-right: 0;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

:deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

.ml-4 {
  margin-left: 4px;
}

/* 删除旧的筛选表单相关样式 */
.filter-form {
  display: none;
}

:deep(.filter-form .el-form-item) {
  display: none;
}

:deep(.filter-form .el-form-item:last-child) {
  display: none;
}

:deep(.filter-form .el-input),
:deep(.filter-form .el-select) {
  display: none;
}

:deep(.filter-form .el-button) {
  display: none;
}

/* 深色模式样式 */
html.dark .filter-form {
  display: none;
}
</style> 