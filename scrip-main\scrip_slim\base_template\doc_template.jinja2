<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="data:,"><!--avoid "GET /favicon.ico"-->
    <title>DOC</title>
    <style>
        :root {
            font-size: 16px;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 75rem;
            margin: 0 auto;
            padding: 1.25rem;
            font-size: 1rem;
        }

        .api-container {
            margin-bottom: 2.5rem;
            border-bottom: 0.0625rem solid #eee;
            padding-bottom: 1.25rem;
        }

        .api-name {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 0.625rem;
        }

        .api-description {
            margin-bottom: 1.25rem;
            color: #7f8c8d;
        }

        .section-title {
            font-size: 1.125rem;
            color: #3498db;
            margin: 0.9375rem 0 0.625rem 0;
            padding-bottom: 0.3125rem;
            border-bottom: 0.0625rem solid #eee;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.25rem;
            font-size: 0.875rem;
        }

        .param-table th,
        .param-table td {
            border: 0.0625rem solid #ddd;
            padding: 0.5rem 0.75rem;
            text-align: left;
        }

        .param-table th {
            background-color: #f2f2f2;
        }

        .param-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .optional {
            color: #7f8c8d;
            font-style: italic;
        }

        @media (max-width: 48rem) {

            :root {
                font-size: 14px;
            }

            body {
                padding: 0.625rem;
            }

            .param-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>

<body>
    <h1>API</h1>

    {% for api_name, api_data in api.items() %}
    <div class="api-container">
        <div class="api-name">{{ api_name }}</div>

        {% if api_data.description %}
        <div class="api-description">{{ api_data.description }}</div>
        {% endif %}

        {% if api_data.input %}
        <div class="section-title">Inputs</div>
        <table class="param-table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                {% for param in api_data.input %}
                <tr>
                    <td>{{ param.name | default("Unknown", true) }}</td>
                    <td>{{ param.type | default("Unknown", true) }}</td>
                    <td>{{ param.description | default("No Description", true) }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}

        {% if api_data.output %}
        <div class="section-title">Outputs</div>
        <table class="param-table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                {% for param in api_data.output %}
                <tr>
                    <td>{{ param.name | default("Unknown", true) }}</td>
                    <td>{{ param.type | default("Unknown", true) }}</td>
                    <td>{{ param.description | default("No Description", true) }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}
    </div>
    {% endfor %}

    <p>
        The automatic documentation is for reference only.
        If you find that the actual type does not match,
        it may be that a customized template has been used.
        Please consult the developer.
    </p>
</body>

</html>