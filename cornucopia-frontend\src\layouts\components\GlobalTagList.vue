<template>
  <div class="tag-list">
    <!-- 导航按钮组 -->
    <div class="nav-buttons">
      <el-button 
        type="primary" 
        size="small" 
        plain 
        class="nav-button"
        @click="goBack"
        :disabled="!canGoBack"
      >
        <el-icon><Back /></el-icon>
        返回
      </el-button>
      
      <el-button 
        type="primary" 
        size="small" 
        plain 
        class="nav-button"
        @click="goForward"
        :disabled="!canGoForward"
      >
        <el-icon><Right /></el-icon>
        前进
      </el-button>
    </div>
    
    <el-scrollbar class="scroll-container">
      <div class="tags-wrapper">
        <el-tag
          v-for="(tag, index) in tags"
          :key="tag.path + JSON.stringify(tag.query || {})"
          :closable="tag.path !== '/'"
          :type="isActive(tag) ? '' : 'info'"
          :effect="isActive(tag) ? 'dark' : 'plain'"
          class="tag-item"
          @click="handleClick(tag)"
          @close="handleClose(tag)"
          :class="{ active: isActive(tag) }"
          size="large"
        >
          <el-icon class="tag-icon" v-if="tag.meta?.icon">
            <component :is="tag.meta.icon" />
          </el-icon>
          {{ tag.title }}
        </el-tag>
      </div>
    </el-scrollbar>

    <div class="tag-actions">
      <el-dropdown trigger="click" @command="handleCommand">
        <el-button type="primary" size="small" plain>
          <el-icon><Operation /></el-icon>
          标签操作
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="closeOthers">
              <el-icon><CircleClose /></el-icon>关闭其他
            </el-dropdown-item>
            <el-dropdown-item command="closeAll">
              <el-icon><Remove /></el-icon>关闭所有
            </el-dropdown-item>
            <el-dropdown-item command="closeLeft">
              <el-icon><Back /></el-icon>关闭左侧
            </el-dropdown-item>
            <el-dropdown-item command="closeRight">
              <el-icon><Right /></el-icon>关闭右侧
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Operation, CircleClose, Remove, Back, Right } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 判断是否可以返回上一页
const canGoBack = ref(false)
// 判断是否可以前进
const canGoForward = ref(false)

// 检查历史记录状态
const checkHistory = () => {
  // 检查是否可以返回
  canGoBack.value = window.history.state?.back !== undefined
  
  // 检查是否可以前进
  canGoForward.value = window.history.state?.forward !== undefined
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 前进到下一页
const goForward = () => {
  router.forward()
}

// 使用localStorage存储标签列表
const getStoredTags = () => {
  try {
    const storedTags = localStorage.getItem('tagList')
    return storedTags ? JSON.parse(storedTags) : [
      {
        title: '首页',
        path: '/',
        meta: { icon: 'House' },
        query: {}
      }
    ]
  } catch (e) {
    console.error('Error parsing stored tags:', e)
    return [
      {
        title: '首页',
        path: '/',
        meta: { icon: 'House' },
        query: {}
      }
    ]
  }
}

// 标签列表
const tags = ref(getStoredTags())

// 保存标签到localStorage
const saveTags = () => {
  localStorage.setItem('tagList', JSON.stringify(tags.value))
}

// 判断标签是否激活
const isActive = (tag) => {
  // 检查路径是否匹配
  if (tag.path !== route.path) return false
  
  // 检查查询参数是否匹配
  const tagQuery = tag.query || {}
  const routeQuery = route.query || {}
  
  // 比较查询参数的键数量
  const tagQueryKeys = Object.keys(tagQuery)
  const routeQueryKeys = Object.keys(routeQuery)
  
  // 如果键数量不同，则不匹配
  if (tagQueryKeys.length !== routeQueryKeys.length) return false
  
  // 检查每个查询参数是否匹配
  for (const key of tagQueryKeys) {
    if (tagQuery[key] !== routeQuery[key]) return false
  }
  
  return true
}

// 添加标签
const addTag = (route) => {
  const { name, path, meta, query } = route
  if (path === '/login') return
  
  const tag = {
    title: meta.title || name,
    path,
    meta,
    query: { ...query } // 保存查询参数
  }
  
  // 检查是否已存在相同路径和查询参数的标签
  const isExist = tags.value.some(item => {
    if (item.path !== path) return false
    
    const itemQuery = item.query || {}
    const tagQuery = tag.query || {}
    
    // 比较查询参数的键数量
    const itemQueryKeys = Object.keys(itemQuery)
    const tagQueryKeys = Object.keys(tagQuery)
    
    if (itemQueryKeys.length !== tagQueryKeys.length) return false
    
    // 检查每个查询参数是否匹配
    for (const key of tagQueryKeys) {
      if (itemQuery[key] !== tagQuery[key]) return false
    }
    
    return true
  })
  
  if (!isExist) {
    // 添加新标签
    tags.value.push(tag)
    
    // 限制标签数量为10个（不包括首页标签）
    const homeTags = tags.value.filter(t => t.path === '/')
    const otherTags = tags.value.filter(t => t.path !== '/')
    
    // 如果非首页标签超过10个，则删除最早的标签
    if (otherTags.length > 10) {
      // 保留最新的10个非首页标签
      const newOtherTags = otherTags.slice(-10)
      // 合并首页标签和最新的10个非首页标签
      tags.value = [...homeTags, ...newOtherTags]
    }
    
    saveTags()
  } else {
    // 如果标签已存在，将其移到最后（最新位置）
    const index = tags.value.findIndex(item => {
      if (item.path !== path) return false
      
      const itemQuery = item.query || {}
      const tagQuery = tag.query || {}
      
      // 比较查询参数的键数量
      const itemQueryKeys = Object.keys(itemQuery)
      const tagQueryKeys = Object.keys(tagQuery)
      
      if (itemQueryKeys.length !== tagQueryKeys.length) return false
      
      // 检查每个查询参数是否匹配
      for (const key of tagQueryKeys) {
        if (itemQuery[key] !== tagQuery[key]) return false
      }
      
      return true
    })
    
    if (index !== -1 && index !== tags.value.length - 1) {
      // 移除标签并添加到末尾
      const tagToMove = tags.value.splice(index, 1)[0]
      tags.value.push(tagToMove)
      saveTags()
    }
  }
}

// 移除标签
const removeTag = (tag) => {
  const index = tags.value.findIndex(item => {
    if (item.path !== tag.path) return false
    
    const itemQuery = item.query || {}
    const tagQuery = tag.query || {}
    
    // 比较查询参数的键数量
    const itemQueryKeys = Object.keys(itemQuery)
    const tagQueryKeys = Object.keys(tagQuery)
    
    if (itemQueryKeys.length !== tagQueryKeys.length) return false
    
    // 检查每个查询参数是否匹配
    for (const key of tagQueryKeys) {
      if (itemQuery[key] !== tagQuery[key]) return false
    }
    
    return true
  })
  
  if (index === -1) return
  
  // 如果关闭的是当前标签,则跳转到前一个标签
  if (isActive(tag) && tags.value.length > 1) {
    const nextTag = tags.value[index + 1] || tags.value[index - 1]
    router.push({
      path: nextTag.path,
      query: nextTag.query || {}
    })
  }
  
  tags.value.splice(index, 1)
  saveTags()
}

// 点击标签
const handleClick = (tag) => {
  // 跳转时包含查询参数
  router.push({
    path: tag.path,
    query: tag.query || {}
  })
}

// 关闭标签
const handleClose = (tag) => {
  removeTag(tag)
}

// 标签操作
const handleCommand = (command) => {
  switch (command) {
    case 'closeOthers':
      tags.value = tags.value.filter(tag => tag.path === '/' || isActive(tag))
      saveTags()
      break
    case 'closeAll':
      tags.value = tags.value.filter(tag => tag.path === '/')
      router.push('/')
      saveTags()
      break
    case 'closeLeft':
      const currentIndex = tags.value.findIndex(tag => isActive(tag))
      tags.value = tags.value.filter((tag, index) => tag.path === '/' || index >= currentIndex)
      saveTags()
      break
    case 'closeRight':
      const curIndex = tags.value.findIndex(tag => isActive(tag))
      tags.value = tags.value.filter((tag, index) => tag.path === '/' || index <= curIndex)
      saveTags()
      break
  }
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    addTag(route)
    // 每次路由变化时检查历史记录状态
    checkHistory()
  },
  { immediate: true }
)

// 监听路由的查询参数变化
watch(
  () => route.query,
  () => {
    addTag(route)
  },
  { deep: true }
)

// 组件挂载时，确保当前路由已添加到标签列表
onMounted(() => {
  addTag(route)
  checkHistory()
  
  // 监听历史记录状态变化
  window.addEventListener('popstate', checkHistory)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('popstate', checkHistory)
})
</script>

<style scoped>
.tag-list {
  display: flex;
  align-items: center;
  padding: 6px 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
}

.nav-buttons {
  display: flex;
  gap: 4px;
  margin-right: 12px;
  flex-shrink: 0;
}

.nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

.scroll-container {
  flex: 1;
  white-space: nowrap;
  padding-right: 16px;
}

.tags-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  margin-right: 4px;
  cursor: pointer;
  height: 30px;
  padding: 0 8px;
  font-size: 12px;
  transition: all 0.3s;
}

.tag-item:hover {
  transform: translateY(-1px);
}

.tag-item.active {
  font-weight: bold;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.tag-icon {
  margin-right: 4px;
  font-size: 13px;
}

.tag-actions {
  margin-left: auto;
  flex-shrink: 0;
}

/* 深色模式适配 */
html.dark {
  .tag-list {
    background: var(--el-bg-color-overlay);
    border-color: var(--el-border-color-darker);
  }

  .tag-item.active {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  }
}
</style> 