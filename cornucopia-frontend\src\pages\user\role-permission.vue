<template>
  <div class="role-permission-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Key /></el-icon>
          <h2>角色权限管理</h2>
        </div>
        <div class="sub-title">管理角色的权限配置</div>
      </div>
      <div class="header-right">
        <el-button 
          @click="$router.push('/user/role/list')"
          plain
          round
        >
          <el-icon><Back /></el-icon>
          返回角色列表
        </el-button>
      </div>
    </div>

    <div class="main-content">
      <el-empty 
        v-if="!hasRoleId" 
        description="未找到角色ID" 
        :image-size="200"
      >
        <el-button type="primary" @click="$router.push('/user/role/list')" round>
          返回角色列表
        </el-button>
      </el-empty>

      <template v-else>
        <el-card class="role-info-card" shadow="hover" v-if="roleData">
          <template #header>
            <div class="card-header">
              <span class="role-name">{{ roleData.name }}</span>
              <el-tag type="info" effect="plain">{{ roleData.description || '暂无描述' }}</el-tag>
            </div>
          </template>
          
          <div class="permissions-container">
            <div class="section-title">
              <el-icon><Setting /></el-icon>
              <span>权限配置</span>
            </div>
            
            <el-form :model="permissionForm" label-width="0">
              <el-form-item>
                <el-checkbox-group v-model="selectedPermissions">
                  <div class="permission-group" v-for="group in permissionGroups" :key="group.type">
                    <div class="group-title">{{ group.label }}</div>
                    <div class="permission-list">
                      <el-checkbox 
                        v-for="permission in group.permissions" 
                        :key="permission.id"
                        :label="permission.id"
                        border
                      >
                        {{ permission.name }}
                        <el-tooltip 
                          v-if="permission.description"
                          :content="permission.description"
                          placement="top"
                        >
                          <el-icon class="info-icon"><InfoFilled /></el-icon>
                        </el-tooltip>
                      </el-checkbox>
                    </div>
                  </div>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item>
                <div class="form-actions">
                  <el-button 
                    type="primary" 
                    @click="handleSave" 
                    :loading="saving"
                    round
                  >
                    <el-icon><Check /></el-icon>保存配置
                  </el-button>
                  <el-button 
                    @click="resetPermissions" 
                    round
                  >
                    <el-icon><RefreshRight /></el-icon>重置
                  </el-button>
                  <el-button 
                    type="danger" 
                    @click="handleClearPermissions"
                    round
                  >
                    <el-icon><Delete /></el-icon>清空权限
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Key, 
  Back, 
  Setting, 
  Check, 
  RefreshRight,
  InfoFilled,
  Delete
} from '@element-plus/icons-vue'
import { toast,showModal } from '~/composables/util'
import service from '~/axios'


const route = useRoute()
const router = useRouter()
const pageLoading = ref(false)
const saving = ref(false)
const hasRoleId = ref(false)
const roleData = ref(null)
const selectedPermissions = ref([])
const originalPermissions = ref([])

// 权限分组
const permissionGroups = ref([
  {
    type: 'user',
    label: '用户管理',
    permissions: []
  },
  {
    type: 'role',
    label: '角色管理',
    permissions: []
  },
  {
    type: 'log',
    label: '日志管理',
    permissions: []
  },
  {
    type: 'node',
    label: '节点管理',
    permissions: []
  },
  {
    type: 'data',
    label: '数据管理',
    permissions: []
  },
  {
    type: 'model',
    label: '模型管理',
    permissions: []
  },
  {
    type: 'menu',
    label: '菜单管理',
    permissions: []
  }
])

// 获取角色信息
const getRoleDetail = async () => {
  try {
    pageLoading.value = true
    const roleId = route.query.id
    const res = await service.get(`/api/v1.0/sys/role/${roleId}`)
    
    if (res.code === 10000) {
      roleData.value = res.data
      // 获取角色的权限
      await getRolePermissions()
    } else {
      throw new Error(res.message || '获取角色详情失败')
    }
  } catch (error) {
    toast('错误', error.message, 'error')
  } finally {
    pageLoading.value = false
  }
}

// 获取所有权限列表
const getAllPermissions = async () => {
  try {
    const res = await service.get('/api/v1.0/sys/permissions')
    if (res.code === 10000) {
      // 清空现有权限列表
      permissionGroups.value.forEach(group => {
        group.permissions = []
      })
      
      // 对权限进行分组
      res.data.forEach(permission => {
        const type = permission.code.split(':')[0]
        const group = permissionGroups.value.find(g => g.type === type)
        if (group) {
          group.permissions.push({
            id: permission.id,
            code: permission.code,
            name: permission.name,
            description: permission.description
          })
        }
      })
    } else {
      throw new Error(res.message || '获取权限列表失败')
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
    toast('错误', '获取权限列表失败', 'error')
  }
}

// 获取角色的权限
const getRolePermissions = async () => {
  try {
    const res = await service.get(`/api/v1.0/sys/role/${route.query.id}/permissions`)
    if (res.code === 10000) {
      selectedPermissions.value = res.data.map(p => p.id)
      originalPermissions.value = [...selectedPermissions.value]
    }
  } catch (error) {
    toast('错误', '获取角色权限失败', 'error')
  }
}

// 保存权限配置
const handleSave = async () => {
  if (!selectedPermissions.value.length) {
    toast('警告', '请至少选择一个权限', 'warning')
    return
  }

  saving.value = true
  try {
    const res = await service.put(`/api/v1.0/sys/role/${route.query.id}/permissions`, {
      permissionIds: selectedPermissions.value
    })
    
    if (res.code === 10000) {
      toast('成功', '权限配置已保存')
      // 更新原始权限列表，用于重置功能
      originalPermissions.value = [...selectedPermissions.value]
    } else {
      throw new Error(res.message || '保存失败')
    }
  } catch (error) {
    toast('错误', error.message || '保存权限配置失败', 'error')
  } finally {
    saving.value = false
  }
}

// 重置权限
const resetPermissions = () => {
  selectedPermissions.value = [...originalPermissions.value]
}

// 清空权限
const handleClearPermissions = async () => {
  try {
    await showModal('确定要清空所有权限吗？', 'warning', '提示')
    const res = await service.delete(`/api/v1.0/sys/role/${route.query.id}/permissions`)
    
    if (res.code === 10000) {
      toast('成功', '权限已清空')
      selectedPermissions.value = []
      originalPermissions.value = []
    } else {
      throw new Error(res.message || '清空失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', error.message || '清空权限失败', 'error')
    }
  }
}

onMounted(() => {
  if (!route.query.id) {
    hasRoleId.value = false
    toast('错误', '未找到角色ID', 'error')
    return
  }
  hasRoleId.value = true
  getRoleDetail()
  getRolePermissions()
  getAllPermissions()
})
</script>

<style scoped>
.role-permission-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.role-info-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .role-name {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.permissions-container {
  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    
    .el-icon {
      color: var(--el-color-primary);
    }
  }
}

.permission-group {
  margin-bottom: 24px;
  
  .group-title {
    margin-bottom: 12px;
    color: #606266;
    font-weight: 500;
  }
  
  .permission-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    
    .el-checkbox {
      margin-right: 0;
    }
  }
}

.info-icon {
  margin-left: 4px;
  font-size: 14px;
  color: #909399;
  cursor: help;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
}
</style> 