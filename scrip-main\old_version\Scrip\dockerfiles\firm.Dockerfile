FROM ubuntu:latest


ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN apt-get update && apt-get install -y --no-install-recommends \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*


RUN pip3 install --no-cache-dir \
    flask \
    requests \
    docker

WORKDIR /app
COPY firm.py .
COPY templates/ ./templates/

EXPOSE 5000
CMD ["python3", "firm.py"]