<template>
  <div class="dataset-overview-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><DataAnalysis /></el-icon>
          <h2>数据集总览</h2>
        </div>
        <div class="sub-title">总览系统中的数据集资源</div>
      </div>
      
      <div class="header-right">
        <el-button 
          type="primary" 
          @click="navigateToDataList"
          plain
          round
        >
          <el-icon><Folder /></el-icon>
          浏览数据集
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="stat in statistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据集类型分布图表 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><PieChart /></el-icon>
                <span>数据集类型分布</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="pieChartRef"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><TrendCharts /></el-icon>
                <span>最近上传数据集</span>
              </div>
            </div>
          </template>
          <div class="recent-datasets">
            <el-table :data="recentDatasets" style="width: 100%" v-if="recentDatasets.length > 0">
              <el-table-column prop="name" label="名称" min-width="120" />
              <el-table-column prop="type" label="类型" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.type === 'FILE' ? 'primary' : 'success'">
                    {{ scope.row.type === 'FILE' ? '文件' : 'URL' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="timeCreated" label="上传时间" width="180">
                <template #default="scope">
                  {{ formatDate(scope.row.timeCreated) }}
                </template>
              </el-table-column>
            </el-table>
            <el-empty description="暂无数据" v-else />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  DataAnalysis, 
  Folder, 
  Document, 
  Link,
  PieChart,
  TrendCharts
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'
import * as echarts from 'echarts'

const router = useRouter()
const pageLoading = ref(false)
const pieChartRef = ref(null)
let pieChart = null
const recentDatasets = ref([])

// 统计数据
const statistics = ref([
  {
    title: '总数据集数',
    value: 0,
    type: 'primary',
    icon: DataAnalysis
  },
  {
    title: '文件类型数据集',
    value: 0,
    type: 'success',
    icon: Document
  },
  {
    title: 'URL类型数据集',
    value: 0,
    type: 'warning',
    icon: Link
  },
  {
    title: '最近上传数据集',
    value: 0,
    type: 'info',
    icon: Folder
  }
])

// 获取数据集统计数据
const fetchStatistics = async () => {
  pageLoading.value = true
  try {
    const res = await service.get('/api/v1.0/sys/dataset/statistics')
    if (res.code === 10000) {
      statistics.value[0].value = res.data.totalDatasets || 0
      statistics.value[1].value = res.data.fileDatasets || 0
      statistics.value[2].value = res.data.urlDatasets || 0
      
      // 最近上传的数据集
      if (res.data.recentDatasets && res.data.recentDatasets.length > 0) {
        recentDatasets.value = res.data.recentDatasets
        statistics.value[3].value = recentDatasets.value.length
      }
      
      updatePieChart()
    }
  } catch (error) {
    toast('错误', '获取统计数据失败', 'error')
  } finally {
    pageLoading.value = false
  }
}

// 初始化饼图
const initPieChart = () => {
  if (pieChartRef.value) {
    pieChart = echarts.init(pieChartRef.value)
    updatePieChart()
  }
}

// 更新饼图数据
const updatePieChart = () => {
  if (!pieChart) return
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: statistics.value[1].value, 
            name: '文件类型数据集',
            itemStyle: { color: '#67C23A' }
          },
          { 
            value: statistics.value[2].value, 
            name: 'URL类型数据集',
            itemStyle: { color: '#E6A23C' }
          }
        ]
      }
    ]
  }
  
  pieChart.setOption(option)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 导航到数据集列表页面
const navigateToDataList = () => {
  router.push('/data/list')
}

onMounted(async () => {
  await fetchStatistics()
  nextTick(() => {
    initPieChart()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    pieChart?.resize()
  })
})

onUnmounted(() => {
  // 销毁图表实例
  pieChart?.dispose()
  
  // 移除事件监听器
  window.removeEventListener('resize', () => {
    pieChart?.resize()
  })
})
</script>

<style scoped>
.dataset-overview-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.stat-card {
  height: 120px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 8px;
}

.stat-icon.primary { background-color: var(--el-color-primary-light-9); }
.stat-icon.success { background-color: var(--el-color-success-light-9); }
.stat-icon.warning { background-color: var(--el-color-warning-light-9); }
.stat-icon.info { background-color: var(--el-color-info-light-9); }
.stat-icon.danger { background-color: var(--el-color-danger-light-9); }

.stat-info {
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  
  .stat-title {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.chart-card {
  height: 400px;
  overflow: hidden;
}

.chart-container {
  height: 340px;
  padding: 10px;
}

.recent-datasets {
  height: 340px;
  padding: 10px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  
  .icon {
    margin-right: 8px;
    font-size: 18px;
    color: var(--el-color-primary);
  }
}

/* 深色模式适配 */
html.dark {
  .stat-card,
  .chart-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style>