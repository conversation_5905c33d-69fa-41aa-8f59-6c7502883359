package ouc.isclab.dataset.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.dataset.entity.DatasetEntity;
import ouc.isclab.dataset.pojo.DatasetInfo;
import ouc.isclab.dataset.repository.DatasetRepository;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.repository.NodeRepository;
import ouc.isclab.storage.pojo.FileItemDTO;
import ouc.isclab.storage.service.MinioService;
import ouc.isclab.dataset.pojo.DatasetDetailDTO;
import ouc.isclab.system.service.UserService;

import java.util.*;

@Slf4j
@Service
public class DatasetService {

    @Autowired
    private DatasetRepository datasetRepository;

    @Autowired
    private NodeRepository nodeRepository;

    @Autowired
    private MinioService minioService;

    @Autowired
    private UserService userService;

    /**
     * 上传文件类型的数据集
     */
    @Transactional
    public DatasetEntity uploadFileDataset(MultipartFile file, DatasetInfo datasetInfo, Long userId) {
        try {
            // 1. 上传文件到MinIO
            FileItemDTO fileItem = minioService.uploadFile("datasets", file, datasetInfo.getDescription());
            
            // 2. 创建数据集实体
            DatasetEntity dataset = new DatasetEntity();
            dataset.setName(datasetInfo.getName());
            dataset.setPath(fileItem.getPath());
            dataset.setType("FILE");
            dataset.setSize(fileItem.getSize());
            dataset.setDescription(datasetInfo.getDescription());
            dataset.setMockData(datasetInfo.getMockData());
            dataset.setCreatorId(userId);
            
            // 3. 设置可用节点
            if (datasetInfo.getNodeIds() != null && !datasetInfo.getNodeIds().isEmpty()) {
                Set<NodeEntity> nodes = new HashSet<>();
                for (Long nodeId : datasetInfo.getNodeIds()) {
                    nodeRepository.findById(nodeId).ifPresent(nodes::add);
                }
                dataset.setAvailableNodes(nodes);
            }
            
            // 4. 保存数据集信息
            return datasetRepository.save(dataset);
            
        } catch (Exception e) {
            log.error("Failed to upload file dataset", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "上传数据集失败: " + e.getMessage());
        }
    }

    /**
     * 上传URL类型的数据集
     */
    @Transactional
    public DatasetEntity uploadUrlDataset(DatasetInfo datasetInfo, Long userId) {
        try {
            // 1. 创建数据集实体
            DatasetEntity dataset = new DatasetEntity();
            dataset.setName(datasetInfo.getName());
            dataset.setPath("url://" + UUID.randomUUID().toString());
            dataset.setType("URL");
            dataset.setUrl(datasetInfo.getUrl());
            dataset.setSize(0L); // URL类型没有实际大小
            dataset.setDescription(datasetInfo.getDescription());
            dataset.setMockData(datasetInfo.getMockData());
            dataset.setCreatorId(userId);
            
            // 2. 设置可用节点
            if (datasetInfo.getNodeIds() != null && !datasetInfo.getNodeIds().isEmpty()) {
                Set<NodeEntity> nodes = new HashSet<>();
                for (Long nodeId : datasetInfo.getNodeIds()) {
                    nodeRepository.findById(nodeId).ifPresent(nodes::add);
                }
                dataset.setAvailableNodes(nodes);
            }
            
            // 3. 保存数据集信息
            return datasetRepository.save(dataset);
            
        } catch (Exception e) {
            log.error("Failed to upload URL dataset", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "上传URL数据集失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有数据集
     */
    public Page<DatasetEntity> getAllDatasets(Pageable pageable) {
        return datasetRepository.findAll(pageable);
    }

    /**
     * 获取用户的数据集
     */
    public Page<DatasetEntity> getUserDatasets(Long userId, Pageable pageable) {
        return datasetRepository.findByCreatorId(userId, pageable);
    }

    /**
     * 根据ID获取数据集
     */
    public DatasetEntity getDatasetById(Long id) {
        return datasetRepository.findById(id)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));
    }

    /**
     * 更新数据集信息
     */
    @Transactional
    public DatasetEntity updateDataset(Long id, DatasetInfo datasetInfo, Long userId) {
        DatasetEntity dataset = datasetRepository.findById(id)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));
        
        // 检查是否是创建者
        if (!dataset.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权修改此数据集");
        }
        
        // 更新基本信息
        dataset.setName(datasetInfo.getName());
        dataset.setDescription(datasetInfo.getDescription());
        dataset.setMockData(datasetInfo.getMockData());
        
        // 如果是URL类型，可以更新URL
        if ("URL".equals(dataset.getType()) && datasetInfo.getUrl() != null) {
            dataset.setUrl(datasetInfo.getUrl());
        }
        
        // 更新可用节点
        if (datasetInfo.getNodeIds() != null) {
            Set<NodeEntity> nodes = new HashSet<>();
            for (Long nodeId : datasetInfo.getNodeIds()) {
                nodeRepository.findById(nodeId).ifPresent(nodes::add);
            }
            dataset.setAvailableNodes(nodes);
        }
        
        return datasetRepository.save(dataset);
    }

    /**
     * 删除数据集
     */
    @Transactional
    public void deleteDataset(Long id, Long userId) {
        DatasetEntity dataset = datasetRepository.findById(id)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));
        
        // 检查是否是创建者
        if (!dataset.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权删除此数据集");
        }
        
        // 如果是文件类型，删除MinIO中的文件
        if ("FILE".equals(dataset.getType())) {
            try {
                minioService.deleteFile("datasets", dataset.getPath());
            } catch (Exception e) {
                log.error("Failed to delete file from MinIO", e);
                // 继续删除数据库记录，即使MinIO删除失败
            }
        }
        
        // 删除数据库记录
        datasetRepository.deleteById(id);
    }

    /**
     * 批量删除数据集
     */
    @Transactional
    public void batchDeleteDatasets(List<Long> ids, Long userId) {
        for (Long id : ids) {
            deleteDataset(id, userId);
        }
    }

    /**
     * 获取数据集统计信息
     */
    public Map<String, Object> getDatasetStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总数据集数量
        long totalDatasets = datasetRepository.count();
        statistics.put("totalDatasets", totalDatasets);
        
        // 文件类型数据集数量
        long fileDatasets = datasetRepository.findAll().stream()
                .filter(d -> "FILE".equals(d.getType()))
                .count();
        statistics.put("fileDatasets", fileDatasets);
        
        // URL类型数据集数量
        long urlDatasets = datasetRepository.findAll().stream()
                .filter(d -> "URL".equals(d.getType()))
                .count();
        statistics.put("urlDatasets", urlDatasets);
        
        // 最近上传的数据集
        List<DatasetEntity> recentDatasets = datasetRepository.findAll().stream()
                .sorted(Comparator.comparing(DatasetEntity::getTimeCreated).reversed())
                .limit(5)
                .toList();
        statistics.put("recentDatasets", recentDatasets);
        
        return statistics;
    }

    /**
     * 检查数据集是否属于用户
     */
    public boolean isDatasetOwner(Long datasetId, Long userId) {
        return datasetRepository.existsByIdAndCreatorId(datasetId, userId);
    }

    /**
     * 获取数据集详情，包括可用节点信息
     */
    public DatasetDetailDTO getDatasetDetail(Long id) {
        DatasetEntity dataset = datasetRepository.findById(id)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));

        String creatorName = userService.findUserById(dataset.getCreatorId()).getUsername();
        
        return DatasetDetailDTO.builder()
                .id(dataset.getId())
                .name(dataset.getName())
                .path(dataset.getPath())
                .type(dataset.getType())
                .url(dataset.getUrl())
                .size(dataset.getSize())
                .description(dataset.getDescription())
                .mockData(dataset.getMockData())
                .creatorId(dataset.getCreatorId())
                .creatorName(creatorName)
                .timeCreated(dataset.getTimeCreated())
                .timeUpdated(dataset.getTimeUpdated())
                .availableNodes(dataset.getAvailableNodes())
                .build();
    }

    /**
     * 获取数据集的分享链接
     */
    public Object getDatasetShareLink(Long id, Integer expirySeconds) {
        DatasetEntity dataset = datasetRepository.findById(id)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));
        
        if ("URL".equals(dataset.getType())) {
            // 如果是URL类型，直接返回存储的URL
            Map<String, Object> result = new HashMap<>();
            result.put("isDirectory", false);
            result.put("name", dataset.getName());
            result.put("url", dataset.getUrl());
            result.put("expiry", null); // 外部URL没有过期时间
            return result;
        } else if ("FILE".equals(dataset.getType())) {
            // 如果是文件类型，通过MinIO服务生成分享链接
            try {
                return minioService.generateShareLink("datasets", dataset.getPath(), expirySeconds);
            } catch (Exception e) {
                log.error("Failed to generate share link", e);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "生成分享链接失败");
            }
        } else {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "未知的数据集类型");
        }
    }
} 