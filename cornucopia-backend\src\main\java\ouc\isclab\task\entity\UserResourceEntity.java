package ouc.isclab.task.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;

/**
 * 用户资源权限实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_USER_RESOURCE", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"user_id", "resource_id"}))
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class UserResourceEntity extends BaseEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId; // 用户ID
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "resource_id", nullable = false)
    private ResourceEntity resource; // 关联的资源
    
    @Column(nullable = false)
    private boolean active = true; // 权限是否有效
} 