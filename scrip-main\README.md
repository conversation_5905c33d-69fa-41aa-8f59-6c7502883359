# Scrip_Slim - Quick Start

## Core Concept
A lightweight framework that lets you serve ML models through a web interface with:
- Automatic form handling

- Customizable UI templates

- Built-in file upload processing

- Clean resource management

  

## Minimal Implementation (3 Components)

### 1. Define Your Model
```python
def get_model():
    class TextProcessor:
        def pipe(self, text_input: str, **kwargs):
            # Your model logic here
            return {"processed_text": text_input.upper()}
    
    return TextProcessor()
```

### 2. Configure Input Form
```python
from scrip_slim import InputTemplateConfig

input_config = (
    InputTemplateConfig()
    .set_title("Text Processing")
    .add_text_field(
        name="text_input",  # Must match pipe() parameter
        label="Enter Text",
        placeholder="Type something...",
        required=True
    )
    .build()
)
```

### 3. Configure Output Display
```python
from scrip_slim import OutputTemplateConfig

output_config = (
    OutputTemplateConfig()
    .set_title("Results")
    .add_text_field(
        name="processed_text",  # Must match return dict key
        label="Processed Output"
    )
    .build()
)
```

### Launch the Service
```python
from scrip_slim import create_app, run_app

app = create_app(
    get_model=get_model,
    input_template_config=input_config,
    output_template_config=output_config
)

run_app(app, port=8000)  # Access at http://localhost:8000
```

## Key Features
| Feature                  | Description                                     |
| ------------------------ | ----------------------------------------------- |
| **Auto Form Processing** | Handles text/number/file inputs → Python types  |
| **20+ Field Types**      | Supports text, files, selects, checkboxes, etc. |
| **Template Control**     | Use built-in templates or bring your own Jinja2 |
| **Self-Cleaning**        | Automatic temp file cleanup                     |

## Advanced Use Cases
##### 1. **File Processing**  

```python
.add_file_field(name="document", accept=".pdf,.docx")
```
Model receives file path: `pipe(document="/tmp/uploaded_file.pdf")`

##### 2. **Custom Templates**  

```python
create_app(output_template_generate=TemplateLoader("custom.jinja2"))
```

##### 3. **Specialized Processors**  

Override `default_preprocessor` for custom request handling.

##### 4. **Extended Operations**  

Set `timeout_minutes=-1` for long-running services.

## Next Steps
1. Try the minimal example

2. Explore field types in `InputTemplateConfig`

3. Customize output displays with `OutputTemplateConfig`

   ... ...



# Examples

````python
from .scrip_slim import create_app, run_app
from pathlib import Path


def get_model():
    from transformers import pipeline
    import re

    class Model:
        def __init__(self):
            self.model = pipeline(
                "text-generation",
                model="distilgpt2"
            )

        def pipe(self, textinput, **kwargs):
            result = self.model(textinput, max_length=100)
            output = result[0]["generated_text"]
            output = re.sub(r'\n\s*\n', '\n\n', output.strip())
            output = re.sub(r'[ \t]+', ' ', output)
            return {
                "output": output
            }

    model = Model()

    return model


def main(port=8000):
    app = create_app(
        get_model=get_model,
        timeout_minutes=120,
        # You can use InputTemplateConfig/OutputTemplateConfig to build the configs
        input_template_config={
            "title": "InputText",
            'fields': [{
                "name": "textinput",  # key of kwarg received by model.pipe
                "label": "input text",
                "type": "text"
            }],
            "history": True
        },
        output_template_config={
            "title": "GeneratedText",
            'fields': [{
                "name": "output",
                "type": "text"
            }]
        }
    )
    run_app(app, port)


````



### NOTE: 

1. The examples in the 'examples' folder need to be manually placed in scrip_slim to be used as a submodule.

2. Now you can use `python -m examples --all` to try all examples (Some examples may require additional data files, and you may need to download them from the Internet)



