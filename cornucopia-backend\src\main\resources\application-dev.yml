server:
  port: 8080
  servlet:
    session:
      timeout: 30m

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************
    username: root
    password: 123456

# actuator配置
management:
  # actuator端口 如果不配置做默认使用server端口
  server:
    port: 8085
  endpoints:
    web:
      exposure:
        # 默认值访问health, info端点  用*可以包含全部端点
        include: "*"
      # 修改访问路径 2.0之前默认是/; 2.0默认是/actuator可以通过这个属性值修改
      base-path: /actuator
  endpoint:
    shutdown:
      enabled: true # 打开shutdown端点
    health:
      show-details: always # 获得健康检查中所有指标的详细信息

# 对象存储服务配置
storage:
  # MinIO自搭建对象存储服务
  minio:
    endpoint: http://10.140.34.208:9000 #存储服务域名
    accessKey: cfCpnt541pIi01ErfZFc
    secretKey: cIHBIYrYy8jsnWPNpdkUeOTJXcSm46aanZ7zjWPU
    bucket: datasets   #存储桶名称

