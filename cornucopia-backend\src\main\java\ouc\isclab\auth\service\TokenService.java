package ouc.isclab.auth.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.auth.entity.TokenEntity;
import ouc.isclab.auth.entity.TokenBlacklistEntity;
import ouc.isclab.auth.repository.TokenRepository;
import ouc.isclab.auth.repository.TokenBlacklistRepository;
import ouc.isclab.auth.util.TokenUtil;

import java.util.Date;
import java.util.Optional;

@Service
public class TokenService {
    @Autowired
    private TokenRepository tokenRepository;
    
    @Autowired
    private TokenBlacklistRepository blacklistRepository;

    @Transactional
    public String createToken(Long userId) {
        // 先删除该用户的所有旧token
        tokenRepository.deleteByUserId(userId);
        
        String token = TokenUtil.generateToken();
        Date expireTime = TokenUtil.generateExpireTime();

        TokenEntity tokenEntity = new TokenEntity();
        tokenEntity.setToken(token);
        tokenEntity.setUserId(userId);
        tokenEntity.setExpireTime(expireTime);

        tokenRepository.save(tokenEntity);
        return token;
    }

    public Optional<Long> validateToken(String token) {
        // 先检查是否在黑名单中
        if (blacklistRepository.existsByTokenAndExpireTimeAfter(token, new Date())) {
            return Optional.empty();
        }
        return tokenRepository.findUserIdByValidToken(token, new Date());
    }

    @Transactional
    public void removeToken(String token) {
        // 将token加入黑名单
        TokenEntity tokenEntity = tokenRepository.findByToken(token)
                .orElse(null);
        if (tokenEntity != null) {
            TokenBlacklistEntity blacklist = new TokenBlacklistEntity();
            blacklist.setToken(token);
            blacklist.setExpireTime(tokenEntity.getExpireTime());
            blacklistRepository.save(blacklist);
        }
        
        // 删除token
        tokenRepository.deleteByToken(token);
    }

    @Transactional
    public String refreshToken(String oldToken) {
        Optional<TokenEntity> tokenOpt = tokenRepository.findByTokenAndExpireTimeAfter(oldToken, new Date());
        if (tokenOpt.isPresent()) {
            TokenEntity token = tokenOpt.get();
            // 生成新token
            String newToken = TokenUtil.generateToken();
            Date newExpireTime = TokenUtil.generateExpireTime();
            
            // 更新token信息
            token.setToken(newToken);
            token.setExpireTime(newExpireTime);
            tokenRepository.save(token);
            
            return newToken;
        }
        return null;
    }

    // 清理过期的token
    @Transactional
    @Scheduled(cron = "0 0 */1 * * *") // 每小时执行一次
    public void cleanExpiredTokens() {
        Date now = new Date();
        tokenRepository.deleteByExpireTimeBefore(now);
    }
} 