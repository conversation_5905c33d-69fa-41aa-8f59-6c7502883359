<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login</title>
    <style>
        :root {
            --color-dark: #333;
            --color-deep: #555;
            --color-saturated: #9e9e9e;
            --color-light: #d3d3d3;
            --color-pale: #f4f4f4;
            --color-font-deep: black;
            --color-font-light: white;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: var(--color-pale);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .login-container {
            background-color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 0 1rem rgba(0, 0, 0, 0.1);
            width: 300px;
        }

        .login-container h1 {
            text-align: center;
            color: var(--color-dark);
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--color-deep);
        }

        .form-group input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--color-light);
            border-radius: 0.25rem;
            box-sizing: border-box;
        }

        .button {
            display: inline-block;
            padding: 0.625rem 1.25rem;
            background-color: var(--color-dark);
            color: var(--color-font-light);
            text-decoration: none;
            border: none;
            border-radius: 0.25rem;
            cursor: pointer;
            width: 100%;
            font-size: 1rem;
        }

        .button:hover {
            background-color: var(--color-deep);
        }

        .error-message {
            color: red;
            margin-top: 1rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>Login</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="button">Login</button>
            <div id="errorMessage" class="error-message"></div>
        </form>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');
            
            try {
                const response = await fetch('/users/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&grant_type=password`
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || 'Login failed');
                }
                
                const data = await response.json();
                localStorage.setItem('access_token', data.access_token);
                console.log(data)
                //debugger
                if(data.role == 'admin'){
                    window.location.href = '/static/users.html';
                }else if(data.role == 'auditor'){
                    window.location.href = '/static/audit.html';
                }else{
                    window.location.href = '/static/task_panel.html';
                }
                
            } catch (error) {
                errorMessage.textContent = error.message;
            }
        });
    </script>
</body>
</html>