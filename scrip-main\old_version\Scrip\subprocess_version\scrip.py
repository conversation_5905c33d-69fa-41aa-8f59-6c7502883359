from flask import Flask, request, jsonify, render_template
import importlib.util
import sys
import argparse
import time
import threading
import os
import signal
from werkzeug.utils import secure_filename
import shutil
from typing import Optional, Dict, Any


def create_app(port: int, code_file: str, service_id: str, timeout_minutes: int = 30) -> Optional[Flask]:
    """Create and configure a Flask application for model serving."""
    app = Flask(__name__)

    # Create unique temporary folder for each service instance
    upload_folder = os.path.join('temp_uploads', f'service_{service_id}')
    os.makedirs(upload_folder, exist_ok=True)
    app.config['UPLOAD_FOLDER'] = upload_folder

    # HTML template path
    HTML_TEMPLATE = "deploy.jinja2"

    def load_user_model(model_path: str) -> Any:
        """Dynamically load user model from given path."""
        module_name = f"user_model_{service_id}"
        spec = importlib.util.spec_from_file_location(module_name, model_path)
        if spec is None:
            raise ImportError(f"Could not load spec from {model_path}")

        user_module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = user_module
        spec.loader.exec_module(user_module)

        if not hasattr(user_module, 'get_model'):
            raise AttributeError("Module must have 'get_model' function")

        return user_module.get_model()

    # Load model
    try:
        model = load_user_model(code_file)
        print(
            f"Model loaded successfully. Service {service_id} running on port {port}")
    except Exception as e:
        print(f"Failed to load model: {str(e)}")
        model = None
        return None

    def cleanup_resources() -> None:
        """Clean up temporary files and folders."""
        try:
            if os.path.exists(code_file):
                os.remove(code_file)
            if os.path.exists(upload_folder):
                shutil.rmtree(upload_folder)
        except Exception as e:
            print(f"Error during cleanup: {str(e)}")

    def shutdown_server() -> None:
        """Shutdown the server after timeout period."""
        time.sleep(timeout_minutes * 60)
        print(
            f"\nTimeout reached ({timeout_minutes} minutes). Shutting down service {service_id}...")
        cleanup_resources()
        os.kill(os.getpid(), signal.SIGTERM)

    @app.route('/predict', methods=['POST'])
    def predict() -> Dict[str, Any]:
        if model is None:
            return jsonify({'error': 'Model not loaded'}), 500

        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        filepath = None
        try:
            # Save uploaded file
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Get predictions from model
            predictions = model.predict(filepath)

            return jsonify({
                'status': 'success',
                'predictions': predictions,
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500
        finally:
            # Clean up temporary file
            if filepath and os.path.exists(filepath):
                os.remove(filepath)

    destroy_time = int((time.time() + timeout_minutes * 60 - 30) * 1000)
    
    @app.route('/')
    def home() -> str:
        return render_template(HTML_TEMPLATE, destroy_time=destroy_time, service_id=service_id)

    @app.route('/health')
    def health_check() -> Dict[str, str]:
        return jsonify({"status": "healthy"}), 200

    # Start shutdown thread
    shutdown_thread = threading.Thread(target=shutdown_server, daemon=True)
    shutdown_thread.start()

    return app


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Model serving application')
    parser.add_argument('--port', type=int, required=True,
                        help='Port to run the service on')
    parser.add_argument('--code', type=str, required=True,
                        help='Path to model code file')
    parser.add_argument('--service_id', type=str,
                        required=True, help='Unique service identifier')
    parser.add_argument('--timeout', type=int, default=30,
                        help='Service timeout in minutes')

    args = parser.parse_args()

    app = create_app(args.port, args.code, args.service_id, args.timeout)
    if app:
        app.run(port=args.port, host='0.0.0.0')
    else:
        print("Failed to load model. Service exiting...")
        sys.exit(1)
