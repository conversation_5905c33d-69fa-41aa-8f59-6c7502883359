package ouc.isclab.task.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.repository.NodeRepository;
import ouc.isclab.node.service.NodeService;
import ouc.isclab.sycee.service.SyceeService;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.system.repository.UserRepository;
import ouc.isclab.task.entity.NodeAccountRequestEntity;
import ouc.isclab.task.entity.NodeAccountRequestEntity.ApprovalStatus;
import ouc.isclab.task.pojo.NodeAccountRequestDTO;
import ouc.isclab.task.repository.NodeAccountRequestRepository;
import ouc.isclab.system.service.UserService;
import ouc.isclab.node.entity.NodeEntity.NodeType;
import ouc.isclab.pyxis.service.PyxisService;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class NodeAccountRequestService {

    @Autowired
    private NodeAccountRequestRepository nodeAccountRequestRepository;
    
    @Autowired
    private NodeRepository nodeRepository;
    
    @Autowired
    private NodeService nodeService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private SyceeService syceeService;

    @Autowired
    private PyxisService pyxisService;

    /**
     * 创建节点账号申请
     */
    @Transactional
    public NodeAccountRequestEntity createRequest(NodeAccountRequestDTO requestDTO, Long applicantId) {
        // 检查节点是否存在
        NodeEntity node = nodeRepository.findById(requestDTO.getNodeId())
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));
        
        // 检查是否已经有针对该节点的申请且未被拒绝
        if (nodeAccountRequestRepository.existsByApplicantIdAndNode_IdAndStatusNot(
                applicantId, requestDTO.getNodeId(), ApprovalStatus.REJECTED)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "您已经有针对该节点的申请");
        }
        
        // 创建申请
        NodeAccountRequestEntity request = new NodeAccountRequestEntity();
        request.setNode(node);
        request.setApplicantId(applicantId);
        
        // 获取申请用户信息
        UserEntity user = userRepository.findById(applicantId)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "申请用户不存在"));
        
        // 生成节点账号信息
        // 用户名格式：用户名+随机uuid<EMAIL>
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        String nodeUsername = user.getUsername() + "_" + uuid + "@cornucopia.ouc";
        request.setUsername(nodeUsername);
        
        // 密码使用用户的MD5密码
        String nodePassword = DigestUtils.md5DigestAsHex(
                user.getPassword().getBytes(StandardCharsets.UTF_8));
        
        request.setNodeUsername(nodeUsername);
        request.setNodePassword(nodePassword);
        request.setReason(requestDTO.getReason());
        request.setStatus(ApprovalStatus.PENDING);
        
        return nodeAccountRequestRepository.save(request);
    }
    
    /**
     * 获取用户的所有节点账号申请
     */
    public Page<NodeAccountRequestEntity> getUserRequests(Long userId, Pageable pageable) {
        return nodeAccountRequestRepository.findByApplicantId(userId, pageable);
    }
    
    /**
     * 获取节点所有者需要审批的申请
     */
    public Page<NodeAccountRequestEntity> getPendingRequestsForNodeOwner(Long nodeOwnerId, Pageable pageable) {
        return nodeAccountRequestRepository.findByNode_CreatorIdAndStatus(
                nodeOwnerId, ApprovalStatus.PENDING, pageable);
    }
    
    /**
     * 审批节点账号申请
     */
    @Transactional
    public NodeAccountRequestEntity approveRequest(Long requestId, boolean approved, String rejectReason, Long approverId) {
        // 获取申请
        NodeAccountRequestEntity request = nodeAccountRequestRepository.findById(requestId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "申请不存在"));
        
        // 检查是否是节点所有者
        if (!nodeService.isNodeOwner(request.getNode().getId(), approverId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "您不是该节点的所有者，无权审批");
        }
        
        // 更新申请状态
        if (approved) {
            Boolean result = false;
            if(request.getNode().getNodeType() == NodeType.Sycee){
                result = syceeService.createNodeAccount(request.getNode().getId(),approverId,request.getNodeUsername(),request.getNodePassword(),request.getNode().getIpAddress(),request.getNode().getPort());
            }else{
                //pyxisService
                result = pyxisService.createPyxisUser(request.getNode().getId(),request.getNodeUsername(),request.getNodePassword());
            }
            if(!result){
                throw new BaseException(ResponseCode.SERVICE_ERROR, "创建节点账号失败");
            }
            request.setStatus(ApprovalStatus.APPROVED);
        } else {
            request.setStatus(ApprovalStatus.REJECTED);
            request.setRejectReason(rejectReason);
        }
        
        request.setApproverId(approverId);
        return nodeAccountRequestRepository.save(request);
    }
    
    /**
     * 检查用户是否有节点的已批准账号
     */
    public boolean hasApprovedNodeAccount(Long userId, Long nodeId) {
        NodeAccountRequestEntity request = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                userId, nodeId, ApprovalStatus.APPROVED);
        return request != null;
    }
    
    /**
     * 获取用户在节点上的账号信息
     */
    public NodeAccountRequestEntity getUserNodeAccount(Long userId, Long nodeId) {
        NodeAccountRequestEntity request = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                userId, nodeId, ApprovalStatus.APPROVED);
        
        if (request == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "您没有该节点的访问权限");
        }
        
        return request;
    }

    /**
     * 获取节点所有者的所有申请
     */
    public Page<NodeAccountRequestEntity> getAllRequestsByNodeOwner(Long nodeOwnerId, Pageable pageable) {
        return nodeAccountRequestRepository.findByNode_CreatorId(nodeOwnerId, pageable);
    }

    /**
     * 按状态获取节点所有者的申请
     */
    public Page<NodeAccountRequestEntity> getRequestsByNodeOwnerAndStatus(Long nodeOwnerId, ApprovalStatus status, Pageable pageable) {
        return nodeAccountRequestRepository.findByNode_CreatorIdAndStatus(nodeOwnerId, status, pageable);
    }

    /**
     * 检查用户是否向节点所有者发起过请求
     */
    public boolean hasRequestFromUserToNodeOwner(Long applicantId, Long nodeOwnerId) {
        // 获取节点所有者拥有的所有节点
        List<Long> ownerNodeIds = nodeRepository.findNodeIdsByCreatorId(nodeOwnerId);
        
        if (ownerNodeIds.isEmpty()) {
            return false;
        }
        
        // 检查申请者是否向这些节点发起过请求
        return nodeAccountRequestRepository.existsByApplicantIdAndNodeIdIn(applicantId, ownerNodeIds);
    }
} 