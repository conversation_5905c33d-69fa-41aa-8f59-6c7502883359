<!DOCTYPE html>
<html>
<head>
    <title>Model Deployment Service</title>
    <style>
        :root {
            --primary-color: #4285f4;
            --secondary-color: #34a853;
            --light-gray: #f5f5f5;
            --dark-gray: #333;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.2;
            color: var(--dark-gray);
            max-width: 900px;
            margin: 0 auto;
            padding: 20px 10px;
            background-color: #f9f9f9;
            min-height: 100vh;
            box-sizing: border-box;
        }
        .header {
            text-align: center;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        .header h1 {
            color: var(--primary-color);
            margin: 5px;
            font-size: 2.5rem;
        }
        .header p {
            font-size: 1.1rem;
            color: #666;
        }
        .upload-container {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 10px;
            margin: 0 auto 10px;
            width: 90%;
            max-width: 800px;
        }
        .upload-container h2 {
            margin-top: 0;
            color: var(--dark-gray);
            text-align: center;
            margin-bottom: 5px;
        }
        .code-editor-container {
            display: flex;
            justify-content: center;
            margin-bottom: 5px;
        }
        .code-editor {
            width: 100%;
            min-height: 300px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            resize: vertical;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
            background-color: #fafafa;
        }
        .button-container {
            display: flex;
            justify-content: center;
        }
        .btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            font-weight: 500;
        }
        .btn:hover {
            background-color: #3367d6;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-message {
            padding: 15px;
            margin: 10px 0;
            border-radius: var(--border-radius);
            display: none;
            text-align: center;
        }
        .status-success {
            background-color: #e6f7ee;
            color: #34a853;
            border: 1px solid #34a853;
        }
        .status-error {
            background-color: #fce8e6;
            color: #d93025;
            border: 1px solid #d93025;
        }
        .status-loading {
            background-color: #fff9e6; 
            color: #f4b400; 
            border: 1px solid #f4b400;
        }
        @media (max-width: 768px) {
            body {
                padding: 20px 15px;
            }
            .upload-container {
                padding: 25px;
                width: 100%;
            }
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI Model Deployment Platform</h1>
    </div>

    <div class="upload-container">
        <h2>Model Code Editor</h2>
        <div id="status-message" class="status-message"></div>
        <form method="POST" action="/upload" id="deploy-form">
            <div class="code-editor-container">
                <textarea class="code-editor" name="code" placeholder="Enter your model code here...">
def get_model():
    # Initializes and returns a model instance for image classification
    #
    # The returned model should have a predict() method with:
    # - Parameters:
    #     * image: Input image (file path)
    # - Returns:
    #     * Dictionary {class_label: confidence_score} where:
    #         - Keys: string class labels (e.g. 'cat', 'dog')
    #         - Values: float confidence scores (0-1)
    #         - Sorted by confidence (highest first)
    #
    # Example:
    #     model = get_model()
    #     prediction = model.predict('cat.jpg')
    #     # Returns {'cat': 0.95, 'dog': 0.05}
    
    # Implementation example (replace with actual model)
    model = YourModelClass()
    model.load_weights('model_weights.pth')
    return model
                </textarea>
            </div>
            <div class="button-container">
                <button type="submit" class="btn">Deploy Model</button>
            </div>
        </form>
    </div>

    <script>
        document.getElementById('deploy-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const statusMessage = document.getElementById('status-message');
            statusMessage.style.display = 'none';
            
            // Disable the button and change its text
            const submitButton = this.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.textContent;
            submitButton.disabled = true;
            submitButton.style.display = 'none'
            submitButton.textContent = 'NOW Deploying ...';
            
            // Show loading message
            statusMessage.className = 'status-message status-loading';
            statusMessage.textContent = 'NOW Deploying ... Please wait a few seconds.';
            statusMessage.style.display = 'block';
            
            try {
                const formData = new FormData(this);
                const response = await fetch(this.action, {
                    method: 'POST',
                    body: new URLSearchParams(formData),
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    if (result.status === 'success') {
                        // Show success message and redirect after delay
                        statusMessage.className = 'status-message status-success';
                        statusMessage.textContent = 'Deployment successful! Redirecting to your service...';
                        statusMessage.style.display = 'block';
                        
                        // Redirect after a short delay
                        setTimeout(() => {
                            window.location.href = result.access_url;
                        }, 2000);
                    } else {
                        // Show error message
                        statusMessage.className = 'status-message status-error';
                        statusMessage.textContent = 'Error: ' + result.message;
                        statusMessage.style.display = 'block';
                    }
                } else {
                    // Show error message
                    statusMessage.className = 'status-message status-error';
                    statusMessage.textContent = result.message || 'Request failed with status ' + response.status;
                    statusMessage.style.display = 'block';
                    
                    // Re-enable the button and restore original text
                    submitButton.disabled = false;
                    submitButton.style.display = 'block'
                    submitButton.textContent = originalButtonText;
                }
            } catch (error) {
                // Show error message
                statusMessage.className = 'status-message status-error';
                statusMessage.textContent = 'An error occurred: ' + error.message;
                statusMessage.style.display = 'block';
                
                // Re-enable the button and restore original text
                submitButton.disabled = false;
                submitButton.style.display = 'block'
                submitButton.textContent = originalButtonText;
            } finally {
                //noop
            }
        });

        // Tab support for code editor
        document.querySelector('.code-editor').addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                e.preventDefault();
                var start = this.selectionStart;
                var end = this.selectionEnd;
                
                this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                this.selectionStart = this.selectionEnd = start + 4;
            }
        });
    </script>
</body>
</html>