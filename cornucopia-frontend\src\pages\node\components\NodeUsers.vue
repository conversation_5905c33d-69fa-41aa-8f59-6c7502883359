<template>
  <div class="code-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><User /></el-icon>
          <h2>用户列表</h2>
        </div>
        <div class="sub-title">查看节点中的用户列表</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <div class="search-wrapper">
            <el-form :inline="true" :model="searchForm" class="search-form">
              <el-form-item>
                <el-input
                  v-model="searchForm.id"
                  placeholder="请输入用户ID"
                  clearable
                  class="search-input"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch" plain round>
                  <el-icon><Search /></el-icon>搜索
                </el-button>
                <el-button @click="resetSearch" round>
                  <el-icon><Refresh /></el-icon>重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="action-wrapper">
            <el-button type="primary" @click="handleCreate" plain round>
              <el-icon><Plus /></el-icon>创建用户
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table 
        v-loading="loading"
        :data="users" 
        stripe 
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        border
      >
        <el-table-column prop="id" label="用户ID" min-width="200" align="center" show-overflow-tooltip />
        <el-table-column prop="name" label="用户名" min-width="120" align="center" />
        <el-table-column prop="email" label="邮箱" min-width="180" align="center" show-overflow-tooltip />
        <el-table-column prop="role" label="角色" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getRoleType(scope.row.role)">
              {{ getRoleLabel(scope.row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="institution" label="机构" min-width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="website" label="网站" min-width="120" align="center" show-overflow-tooltip />
        <el-table-column label="操作" width="280" fixed="right" align="center">
          <template #default="scope">
            <el-button-group class="operation-group">
              <el-tooltip content="刷新" placement="top">
                <el-button type="primary" size="small" @click="handleRefresh(scope.row)">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑" placement="top">
                <el-button type="warning" size="small" @click="handleEdit(scope.row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button type="danger" size="small" @click="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog
      v-model="editDialogVisible"
      title="编辑用户"
      width="500px"
      center
    >
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="用户名" required>
          <el-input v-model="editForm.name" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="角色" required>
          <el-select v-model="editForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="管理员" value="ADMIN" />
            <el-option label="数据科学家" value="DATA_SCIENTIST" />
            <el-option label="数据拥有者" value="DATA_OWNER" />
          </el-select>
        </el-form-item>
        <el-form-item label="机构">
          <el-input v-model="editForm.institution" placeholder="请输入机构名称" />
        </el-form-item>
        <el-form-item label="网站">
          <el-input v-model="editForm.website" placeholder="请输入网站地址" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false" round>取消</el-button>
          <el-button type="primary" @click="confirmEdit" :loading="editLoading" round>
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="createDialogVisible"
      title="创建用户"
      width="500px"
      center
    >
      <el-form :model="createForm" label-width="100px">
        <el-form-item label="邮箱" required>
          <el-input v-model="createForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="用户名" required>
          <el-input v-model="createForm.name" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" required>
          <el-input v-model="createForm.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="确认密码" required>
          <el-input v-model="createForm.password_verify" type="password" placeholder="请再次输入密码" />
        </el-form-item>
        <el-form-item label="角色" required>
          <el-select v-model="createForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="管理员" value="ADMIN" />
            <el-option label="数据科学家" value="DATA_SCIENTIST" />
            <el-option label="数据拥有者" value="DATA_OWNER" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false" round>取消</el-button>
          <el-button type="primary" @click="confirmCreate" :loading="createLoading" round>
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { User, View, Edit, Delete, Refresh, Search, Plus, Key } from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import sycee from '~/sycee.js'
import hljs from 'highlight.js/lib/core'
import python from 'highlight.js/lib/languages/python'
import 'highlight.js/styles/github.css'

const route = useRoute()
const router = useRouter()
const nodeId = ref('')
const users = ref([])
const loading = ref(false)

const searchForm = ref({
  id: ''
})

// 注册 Python 语言
hljs.registerLanguage('python', python)

// 添加新的响应式变量
const codeDialogVisible = ref(false)
const codeContent = ref('')
const highlightedCode = ref('')

const codeLines = computed(() => {
  return codeContent.value.split('\n').length
})

// 获取状态对应的类型
const getStatusType = (status) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'processing':
      return 'warning'
    case 'errored':
      return 'danger'
    case 'created':
      return 'info'
    case 'interrupted':
      return 'danger'
    case 'terminating':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取所有用户
const getUsers = async () => {
  if (!nodeId.value) return
  loading.value = true
  try {
    const res = await sycee(nodeId.value, 'api/user/get_all', {})
    if (res.code === 10000) {
      users.value = res.data
    } else {
      toast("错误", res.data, "error")
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    toast("错误", "获取用户列表失败", "error")
  } finally {
    loading.value = false
  }
}

// 刷新单个用户
const handleRefresh = async (row) => {
  try {
    const res = await sycee(nodeId.value, 'api/user/view', { uid: row.id })
    if (res.code === 10000) {
      const index = users.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        users.value[index] = res.data
      }
      toast('成功', '用户信息已刷新', 'success')
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    toast('错误', '刷新失败', 'error')
  }
}

// 查看用户详情
const handleDetail = (row) => {
  router.push({
    path: '/user/detail',
    query: { id: row.id }
  })
}

// 编辑用户
const handleEdit = (row) => {
  editForm.value = {
    uid: row.id,
    name: row.name,
    role: row.role,
    institution: row.institution || '',
    website: row.website || ''
  }
  editDialogVisible.value = true
}

// 创建用户
const handleCreate = () => {
  createForm.value = {
    email: '',
    name: '',
    password: '',
    password_verify: '',
    role: ''
  }
  createDialogVisible.value = true
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await showModal('确定要删除该用户吗？', 'warning', '提示')
    const res = await sycee(nodeId.value, 'api/user/delete', { uid: row.id })
    if (res.code === 10000) {
      toast('成功', '删除成功')
      getUsers()
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '删除失败', 'error')
    }
  }
}

// 搜索用户
const handleSearch = async () => {
  if (!searchForm.value.id) {
    getUsers()
    return
  }
  
  try {
    const res = await sycee(nodeId.value, 'api/user/view', { uid: searchForm.value.id })
    if (res.code === 10000) {
      users.value = res.data ? [res.data] : []
      if (users.value.length === 0) {
        toast('提示', '未找到相关用户', 'info')
      } else {
        toast('成功', '搜索结果已更新', 'success')
      }
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    toast('错误', '搜索失败', 'error')
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.value.id = ''
  getUsers()
}

// 时间排序
const sortByTime = (a, b) => {
  const timeA = new Date(a.created_time).getTime()
  const timeB = new Date(b.created_time).getTime()
  return timeA - timeB
}

// 在 script 部分添加角色相关的方法
const getRoleType = (role) => {
  switch (role) {
    case 'ADMIN':
      return 'danger'
    case 'DATA_SCIENTIST':
      return 'warning'
    case 'DATA_OWNER':
      return 'success'
    default:
      return 'info'
  }
}

const getRoleLabel = (role) => {
  switch (role) {
    case 'ADMIN':
      return '管理员'
    case 'DATA_SCIENTIST':
      return '数据科学家'
    case 'DATA_OWNER':
      return '数据拥有者'
    default:
      return role
  }
}

// 在 script 部分添加编辑相关的方法
const editDialogVisible = ref(false)
const editLoading = ref(false)
const editForm = ref({
  uid: '',
  name: '',
  role: '',
  institution: '',
  website: ''
})

// 确认编辑
const confirmEdit = async () => {
  if (!editForm.value.name || !editForm.value.role) {
    toast('警告', '请填写必填项', 'warning')
    return
  }

  editLoading.value = true
  try {
    const res = await sycee(nodeId.value, 'api/user/update', editForm.value)
    if (res.code === 10000) {
      toast('成功', '更新成功')
      editDialogVisible.value = false
      getUsers() // 刷新列表
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    toast('错误', '更新失败', 'error')
  } finally {
    editLoading.value = false
  }
}

// 在 script 部分添加创建用户相关的代码
const createDialogVisible = ref(false)
const createLoading = ref(false)
const createForm = ref({
  email: '',
  name: '',
  password: '',
  password_verify: '',
  role: ''
})

// 确认创建
const confirmCreate = async () => {
  if (!createForm.value.email || !createForm.value.name || 
      !createForm.value.password || !createForm.value.password_verify || 
      !createForm.value.role) {
    toast('警告', '请填写所有必填项', 'warning')
    return
  }

  if (createForm.value.password !== createForm.value.password_verify) {
    toast('警告', '两次输入的密码不一致', 'warning')
    return
  }

  createLoading.value = true
  try {
    const res = await sycee(nodeId.value, 'create_user', createForm.value)
    if (res.code === 10000) {
      toast('成功', '创建成功')
      createDialogVisible.value = false
      getUsers() // 刷新列表
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    toast('错误', '创建失败', 'error')
  } finally {
    createLoading.value = false
  }
}

onMounted(() => {
  nodeId.value = route.query.id
  getUsers()
})
</script>

<style>
.code-list-container {
  padding: 20px;
}

.list-card {
  border-radius: 8px;
  margin-bottom: 20px;
}

.request-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.request-container {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.code-list-container .page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.code-list-container .header-left {
  flex-shrink: 0;
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
}

.code-list-container .header-right {
  flex: 1;
  display: flex;
  justify-content: space-between;
  padding-left: 32px;

  .header-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 16px;
    flex-wrap: wrap;
  }
}

.search-wrapper {
  flex: 1;
}

.action-wrapper {
  flex-shrink: 0;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
  :deep(.el-input__wrapper) {
    border-radius: 20px;
    padding: 1px 15px;
    
    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
  min-width: 88px;
}

.reject-container {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.code-dialog :deep(.el-dialog__body) {
  padding: 20px;
  height: 400px;
}

.code-container {
  height: 60vh;
  padding: 20px;
}

.code-wrapper {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  position: relative;
}

.code-scroll-container {
  display: flex;
  width: 100%;
  overflow: auto;
}

.line-numbers {
  padding: 16px 8px;
  background-color: #f0f0f0;
  text-align: right;
  user-select: none;
  position: sticky;
  left: 0;
  z-index: 1;
  border-right: 1px solid #ddd;
  height: fit-content;
  min-height: 100%;
}

.line-numbers::after {
  content: '';
  position: absolute;
  top: 0;
  right: -1px;
  width: 4px;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
}

.line-numbers pre {
  margin: 0;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #999;
  height: 100%;
}

.line-numbers span {
  display: block;
  padding-right: 4px;
}

.code-content {
  margin: 0;
  padding: 16px;
  flex: 1;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  background: transparent;
  min-width: fit-content;
}

.code-content .hljs {
  background: transparent;
  padding: 0;
  white-space: pre;
}

.operation-group {
  display: flex;
  justify-content: center;
  gap: 4px;
}

.operation-group :deep(.el-button) {
  padding: 6px 8px;
}

.operation-group :deep(.el-button + .el-button) {
  margin-left: 0;  /* 覆盖 element-plus 的默认间距 */
}

.result-dialog :deep(.el-dialog__body),
.log-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.result-container,
.log-container {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}
</style>


