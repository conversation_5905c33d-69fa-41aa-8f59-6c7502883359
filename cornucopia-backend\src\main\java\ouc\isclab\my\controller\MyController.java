package ouc.isclab.my.controller;

import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.system.pojo.UserInfo;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.system.service.UserService;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.my.service.MyService;
import ouc.isclab.system.repository.UserRepository;

import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys")
public class MyController {
    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private MyService myService;

    @PostMapping("/my/update")
    public UserEntity updateMyInfo(@CurrentUserId Long userId, @RequestBody Map<String, String> updateInfo) {
        String fullname = updateInfo.get("fullname");
        String email = updateInfo.get("email");
        return myService.updateMyInfo(userId, fullname, email);
    }

    @PostMapping("/my/password")
    public UserEntity updateMyPassword(@CurrentUserId Long userId, @RequestBody Map<String, String> passwordInfo) {
        String oldPassword = passwordInfo.get("oldPassword");
        String newPassword = passwordInfo.get("newPassword");
        
        if (oldPassword == null || newPassword == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "密码不能为空");
        }
        
        return myService.updateMyPassword(userId, oldPassword, newPassword);
    }
}
