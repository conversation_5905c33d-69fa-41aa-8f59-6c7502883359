from .scrip_slim import create_app, run_app
from pathlib import Path


def get_model():
    import os
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

    cache_dir = Path("./models")
    cache_dir.mkdir(parents=True, exist_ok=True)
    os.environ["TRANSFORMERS_CACHE"] = str(cache_dir)

    from transformers import pipeline
    import re

    class Model:
        def __init__(self):
            self.model = pipeline(
                "text-generation",
                model="distilgpt2"
            )

        def pipe(self, textinput, **kwargs):
            result = self.model(textinput, max_length=100)
            output = result[0]["generated_text"]
            output = re.sub(r'\n\s*\n', '\n\n', output.strip())
            output = re.sub(r'[ \t]+', ' ', output)
            return {
                "output": output
            }

    model = Model()

    return model


def main(port=8000):
    app = create_app(
        get_model=get_model,
        timeout_minutes=120,
        input_template_config={
            "title": "InputText",
            'fields': [{
                "name": "textinput",  # key of kwarg received by model.pipe
                "type": "text"
            }],
            "history": True
        },
        output_template_config={
            "title": "GeneratedText",
            'fields': [{
                "name": "output",
                "type": "text"
            }]
        }
    )
    run_app(app, port)
