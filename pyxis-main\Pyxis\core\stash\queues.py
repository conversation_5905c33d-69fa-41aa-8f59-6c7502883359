import asyncio
from asyncio import Queue, Lock
from typing import Callable, Coroutine, Any

import asyncio
from contextlib import contextmanager
from typing import Optional


class AsyncReentrantLock:
    def __init__(self):
        self._lock = asyncio.Lock()
        self._owner: Optional[asyncio.Task] = None
        self._count = 0

    async def acquire(self):
        current_task = asyncio.current_task()
        if self._owner == current_task:
            self._count += 1
            return
        await self._lock.acquire()
        self._owner = current_task
        self._count = 1

    def release(self):
        if self._owner != asyncio.current_task():
            raise RuntimeError("Cannot release un-acquired lock")
        self._count -= 1
        if self._count == 0:
            self._owner = None
            self._lock.release()

    @contextmanager
    async def __call__(self):
        try:
            await self.acquire()
            yield
        finally:
            self.release()

    async def __aenter__(self):
        await self.acquire()
        return self

    async def __aexit__(self, exc_type, exc, tb):
        self.release()


class AsyncFunctionQueue:
    def __init__(self, max_queue_number: int, max_running_number: int):
        self._queue = Queue(maxsize=max_queue_number)
        self._lock = AsyncReentrantLock()
        self._running_number = 0
        self.max_running_number = max_running_number
        self.max_queue_number = max_queue_number

    async def add_function(self, id, func: Callable[..., Coroutine[Any, Any, Any]], *args, **kwargs) -> None:
        async with self._lock:
            if self._queue.empty() and self._running_number < self.max_running_number:
                await self._execute_function(func, args, kwargs)
            else:
                if self._queue.qsize() < self.max_queue_number:
                    await self._queue.put((id, func, args, kwargs))
                else:
                    raise Exception("Queue is full")

    async def notify(self) -> None:
        async with self._lock:
            self._running_number -= 1
            if not self._queue.empty() and self._running_number < self.max_running_number:
                (id, func, args, kwargs) = await self._queue.get()
                await self._execute_function(func, args, kwargs)

    async def _execute_function(self, func: Callable[..., Coroutine[Any, Any, Any]], args, kwargs) -> None:
        async with self._lock:
            self._running_number += 1
            asyncio.create_task(func(*args, **kwargs))

    async def cancel(self, id) -> None:
        async with self._lock:
            found = False
            remaining_items = []

            while not self._queue.empty():
                item = await self._queue.get()
                if item[0] == id:
                    found = True
                else:
                    remaining_items.append(item)
            
            for item in remaining_items:
                await self._queue.put(item)

            return found