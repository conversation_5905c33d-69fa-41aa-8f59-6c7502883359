<template>
  <div class="code-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Timer /></el-icon>
          <h2>作业列表</h2>
        </div>
        <div class="sub-title">查看节点中的作业列表</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.id"
                placeholder="请输入作业ID"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table 
        v-loading="loading"
        :data="jobs" 
        stripe 
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        border
      >
        <el-table-column prop="id" label="作业ID" min-width="200" align="center" show-overflow-tooltip />
        <el-table-column prop="user_code_name" label="代码名称" min-width="200" align="center" show-overflow-tooltip />
        <el-table-column prop="requesting_user" label="请求用户" min-width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" min-width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          prop="creation_time" 
          label="创建时间" 
          min-width="180" 
          align="center" 
          show-overflow-tooltip
          sortable
          :sort-method="sortByTime"
        />
        <el-table-column label="操作" width="320" fixed="right" align="center">
          <template #default="scope">
            <el-button-group class="operation-group">
              <el-tooltip content="刷新" placement="top">
                <el-button type="primary" size="small" @click="handleRefresh(scope.row)">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看源码" placement="top">
                <el-button type="info" size="small" @click="handleViewCode(scope.row)">
                  <el-icon><Document /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看详情" placement="top">
                <el-button type="info" size="small" @click="handleDetail(scope.row)">
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看结果" placement="top">
                <el-button type="success" size="small" @click="handleResult(scope.row)">
                  <el-icon><DataLine /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看日志" placement="top">
                <el-button type="warning" size="small" @click="handleLog(scope.row)">
                  <el-icon><Document /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button type="danger" size="small" @click="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>

  <el-dialog
    v-model="dialogVisible"
    title="作业详情"
    width="55%"
    class="job-dialog"
    center
  >
    <div class="job-container" v-html="jobDescription"></div>
  </el-dialog>

  <el-dialog
    v-model="resultDialogVisible"
    title="作业结果"
    width="55%"
    class="result-dialog"
    center
  >
    <pre class="result-container">{{ jobResult }}</pre>
  </el-dialog>

  <el-dialog
    v-model="logDialogVisible"
    title="作业日志"
    width="55%"
    class="log-dialog"
    center
  >
    <pre class="log-container">{{ jobLog }}</pre>
  </el-dialog>

  <el-dialog
    v-model="codeDialogVisible"
    title="源代码"
    width="55%"
    class="code-dialog"
    center
  >
    <div class="code-container">
      <div class="code-wrapper">
        <div class="code-scroll-container">
          <div class="line-numbers">
            <pre><span v-for="i in codeLines" :key="i">{{ i }}</span></pre>
          </div>
          <pre class="code-content"><code class="python" v-html="highlightedCode"></code></pre>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { Timer, View, DataLine, Document, Delete, Refresh, Search } from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import sycee from '~/sycee.js'
import hljs from 'highlight.js/lib/core'
import python from 'highlight.js/lib/languages/python'
import 'highlight.js/styles/github.css'

const route = useRoute()
const nodeId = ref('')
const jobs = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const resultDialogVisible = ref(false)
const logDialogVisible = ref(false)
const jobDescription = ref('')
const jobResult = ref('')
const jobLog = ref('')

const searchForm = ref({
  id: ''
})

// 注册 Python 语言
hljs.registerLanguage('python', python)

// 添加新的响应式变量
const codeDialogVisible = ref(false)
const codeContent = ref('')
const highlightedCode = ref('')

const codeLines = computed(() => {
  return codeContent.value.split('\n').length
})

// 获取状态对应的类型
const getStatusType = (status) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'processing':
      return 'warning'
    case 'errored':
      return 'danger'
    case 'created':
      return 'info'
    case 'interrupted':
      return 'danger'
    case 'terminating':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取所有作业
const getJobs = async () => {
  if (!nodeId.value) return
  loading.value = true
  try {
    const res = await sycee(nodeId.value, 'api/job/get_all', {})
    if (res.code === 10000) {
      jobs.value = res.data
    } else {
      toast("错误", res.data, "error")
    }
  } catch (error) {
    console.error('获取作业列表失败:', error)
    toast("错误", "获取作业列表失败", "error")
  } finally {
    loading.value = false
  }
}

// 刷新单个作业
const handleRefresh = async (row) => {
  try {
    const res = await sycee(nodeId.value, 'api/job/get', { uid: row.id })
    if (res.code === 10000) {
      const index = jobs.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        jobs.value[index] = res.data
      }
      toast('成功', '作业信息已刷新', 'success')
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    console.error('刷新作业失败:', error)
    toast('错误', '刷新失败', 'error')
  }
}

// 查看作业详情
const handleDetail = async (row) => {
  try {
    const res = await sycee(nodeId.value, 'get_job_html_description', { id: row.id })
    if (res.code === 10000) {
      jobDescription.value = res.data
      dialogVisible.value = true
    } else {
      toast("错误", res.data, "error")
    }
  } catch (error) {
    toast("错误", "获取作业详情失败", "error")
  }
}

// 查看作业结果
const handleResult = async (row) => {
  try {
    const res = await sycee(nodeId.value, 'get_job_result', { id: row.id })
    if (res.code === 10000) {
      jobResult.value = typeof res.data === 'object' ? 
        JSON.stringify(res.data, null, 2) : 
        res.data
      resultDialogVisible.value = true
    } else {
      toast("错误", res.data, "error")
    }
  } catch (error) {
    toast("错误", "获取作业结果失败", "error")
  }
}

// 查看作业日志
const handleLog = async (row) => {
  try {
    const res = await sycee(nodeId.value, 'get_log_by_job_id', { id: row.id })
    if (res.code === 10000) {
      jobLog.value = res.data
      logDialogVisible.value = true
    } else {
      toast("错误", res.data, "error")
    }
  } catch (error) {
    toast("错误", "获取作业日志失败", "error")
  }
}

// 删除作业
const handleDelete = async (row) => {
  try {
    await showModal('确定要删除该作业吗？', 'warning', '提示')
    const res = await sycee(nodeId.value, 'delete_job_by_id', { id: row.id })
    if (res.code === 10000) {
      toast('成功', '删除成功')
      getJobs() // 刷新列表
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '删除失败', 'error')
    }
  }
}

// 搜索作业
const handleSearch = async () => {
  if (!searchForm.value.id) {
    getJobs()
    return
  }
  
  try {
    const res = await sycee(nodeId.value, 'api/job/get', { uid: searchForm.value.id })
    if (res.code === 10000) {
      jobs.value = res.data ? [res.data] : []
      if (jobs.value.length === 0) {
        toast('提示', '未找到相关作业', 'info')
      } else {
        toast('成功', '搜索结果已更新', 'success')
      }
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    console.error('搜索作业失败:', error)
    toast('错误', '搜索失败', 'error')
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.value.id = ''
  getJobs()
}

// 在 script 部分添加排序方法
const sortByTime = (a, b) => {
  const timeA = new Date(a.creation_time).getTime()
  const timeB = new Date(b.creation_time).getTime()
  return timeA - timeB
}

// 查看源码
const handleViewCode = async (row) => {
  try {
    const res = await sycee(nodeId.value, 'get_raw_code_by_id', { id: row.user_code_id })
    if (res.code === 10000) {
      codeContent.value = res.data
      // 预处理代码高亮
      highlightedCode.value = hljs.highlight(res.data, {
        language: 'python'
      }).value
      codeDialogVisible.value = true
    } else {
      toast("错误", res.data, "error")
    }
  } catch (error) {
    toast("错误", "获取源码失败", "error")
  }
}

onMounted(() => {
  nodeId.value = route.query.id
  getJobs()
})
</script>

<style>
.code-list-container {
  padding: 20px;
}

.list-card {
  border-radius: 8px;
  margin-bottom: 20px;
}

.request-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.request-container {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.code-list-container .page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.code-list-container .header-left {
  flex-shrink: 0;
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
}

.code-list-container .header-right {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  padding-left: 32px;

  .header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
  :deep(.el-input__wrapper) {
    border-radius: 20px;
    padding: 1px 15px;
    
    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
  min-width: 88px;
}

.reject-container {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.code-dialog :deep(.el-dialog__body) {
  padding: 20px;
  height: 400px;
}

.code-container {
  height: 60vh;
  padding: 20px;
}

.code-wrapper {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  position: relative;
}

.code-scroll-container {
  display: flex;
  width: 100%;
  overflow: auto;
}

.line-numbers {
  padding: 16px 8px;
  background-color: #f0f0f0;
  text-align: right;
  user-select: none;
  position: sticky;
  left: 0;
  z-index: 1;
  border-right: 1px solid #ddd;
  height: fit-content;
  min-height: 100%;
}

.line-numbers::after {
  content: '';
  position: absolute;
  top: 0;
  right: -1px;
  width: 4px;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
}

.line-numbers pre {
  margin: 0;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #999;
  height: 100%;
}

.line-numbers span {
  display: block;
  padding-right: 4px;
}

.code-content {
  margin: 0;
  padding: 16px;
  flex: 1;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  background: transparent;
  min-width: fit-content;
}

.code-content .hljs {
  background: transparent;
  padding: 0;
  white-space: pre;
}

.operation-group {
  display: flex;
  justify-content: center;
  gap: 4px;
}

.operation-group :deep(.el-button) {
  padding: 6px 8px;
}

.operation-group :deep(.el-button + .el-button) {
  margin-left: 0;  /* 覆盖 element-plus 的默认间距 */
}

.result-dialog :deep(.el-dialog__body),
.log-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.result-container,
.log-container {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}
</style>


