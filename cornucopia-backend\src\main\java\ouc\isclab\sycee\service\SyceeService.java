package ouc.isclab.sycee.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.dataset.service.DatasetService;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.entity.NodeAuthEntity;
import ouc.isclab.node.service.NodeService;
import ouc.isclab.node.repository.NodeAuthRepository;
import ouc.isclab.storage.entity.MinioConfigEntity;
import ouc.isclab.storage.pojo.MinioConfigDTO;
import ouc.isclab.storage.service.MinioConfigService;
import ouc.isclab.sycee.pojo.SyceeInfo;
import ouc.isclab.sycee.pojo.SyceeRequestBody;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.*;

import jakarta.servlet.http.HttpServletRequest;
import ouc.isclab.task.entity.DatasetUsageRequestEntity;
import ouc.isclab.task.entity.NodeAccountRequestEntity;
import ouc.isclab.task.entity.ResourceEntity;
import ouc.isclab.task.repository.NodeAccountRequestRepository;
import ouc.isclab.dataset.entity.DatasetEntity;
import ouc.isclab.dataset.repository.DatasetRepository;
import ouc.isclab.task.repository.ResourceRepository;
import ouc.isclab.task.repository.UserResourceRepository;
import ouc.isclab.task.repository.DatasetUsageRequestRepository;



@Slf4j
@Service
public class SyceeService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private NodeService nodeService;

    @Autowired
    private NodeAuthRepository nodeAuthRepository;

    @Autowired
    private NodeAccountRequestRepository nodeAccountRequestRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MinioConfigService minioConfigService;

    @Autowired
    private DatasetRepository datasetRepository;

    @Autowired
    private ResourceRepository resourceRepository;

    @Autowired
    private UserResourceRepository userResourceRepository;

    @Autowired
    private DatasetUsageRequestRepository datasetUsageRequestRepository;

    /**
     * 处理请求转发
     */
    public JsonNode handleRequest(HttpServletRequest request, SyceeInfo syceeInfo, Long userId) {
        // 验证用户是否是节点的创建者
        if (!nodeService.isNodeOwner(syceeInfo.getNodeId(), userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权访问此节点");
        }

        try {
            // 获取节点信息
            NodeEntity node = nodeService.getNodeById(syceeInfo.getNodeId());
            if (node == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
            }

            // 获取节点认证信息
            NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(syceeInfo.getNodeId());
            if (nodeAuth == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
            }

            // 构建新的请求体
            SyceeRequestBody requestBody = new SyceeRequestBody();
            Map<String, String> user = new HashMap<>();
            user.put("email", nodeAuth.getUsername());
            user.put("password", nodeAuth.getPassword());
//            user.put("datasite_name", node.getName());
            user.put("datasite_name", "d1"); //TODO 先写死
            requestBody.setUser(user);
            requestBody.setArgs(syceeInfo.getArgs());
            requestBody.setCall(syceeInfo.isCall());
            log.info("requestBody: {}", requestBody);

            // 构建基础URL
            String baseUrl = String.format("http://%s", node.getIpAddress() + ":" + node.getPort());

            // 构建目标URL
            String path = request.getRequestURI().replace("/api/v1.0/sycee", "");
            String queryString = request.getQueryString();
            String targetUrl = baseUrl + "/sycee" + path + (queryString != null ? "?" + queryString : "");
            log.info("targetUrl: {}", targetUrl);

            // 复制请求头
            HttpHeaders headers = new HttpHeaders();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                headers.set(headerName, request.getHeader(headerName));
            }
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体，使用新的请求体
            HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求并获取响应
            HttpMethod method = HttpMethod.valueOf(request.getMethod());
            ResponseEntity<String> response = restTemplate.exchange(
                    targetUrl,
                    method,
                    httpEntity,
                    String.class
            );

            log.info("Forwarded {} request to {}, status: {}", method, targetUrl, response.getStatusCode());

            // 将响应体解析为 JsonNode
            return objectMapper.readTree(response.getBody());

        } catch (Exception e) {
            log.error("Error forwarding request", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "转发请求失败: " + e.getMessage());
        }
    }

    /**
     * 处理请求转发并返回响应头
     */
    public Map<String, Object> handleRequestWithHeaders(HttpServletRequest request, SyceeInfo syceeInfo, Long userId) {
        // 记录后端接收到请求的时间
        double backendRequestTime = System.currentTimeMillis() / 1000.0;

        // 验证用户是否是节点的创建者
        if (!nodeService.isNodeOwner(syceeInfo.getNodeId(), userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权访问此节点");
        }

        try {
            // 获取节点信息
            NodeEntity node = nodeService.getNodeById(syceeInfo.getNodeId());
            if (node == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
            }

            // 获取节点认证信息
            NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(syceeInfo.getNodeId());
            if (nodeAuth == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
            }

            // 构建新的请求体
            SyceeRequestBody requestBody = new SyceeRequestBody();
            Map<String, String> user = new HashMap<>();
            user.put("email", nodeAuth.getUsername());
            user.put("password", nodeAuth.getPassword());
            user.put("datasite_url", node.getIpAddress() + ":" + node.getPort());
//            user.put("datasite_name", "d1"); //TODO 先写死
            requestBody.setUser(user);
            requestBody.setArgs(syceeInfo.getArgs());
            requestBody.setCall(syceeInfo.isCall());
            log.info("requestBody: {}", requestBody);

            // 构建基础URL
//            String baseUrl = String.format("http://%s", node.getIpAddress() + ":" + node.getPort());
            String baseUrl = "http://*************:8088";

            // 构建目标URL
            String path = request.getRequestURI().replace("/api/v1.0/sycee", "");
            String queryString = request.getQueryString();
            String targetUrl = baseUrl + "/sycee" + path + (queryString != null ? "?" + queryString : "");
            log.info("targetUrl: {}", targetUrl);

            // 复制请求头
            HttpHeaders headers = new HttpHeaders();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                headers.set(headerName, request.getHeader(headerName));
            }
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 记录后端向sycee发送请求的时间
            double backendToSyceeTime = System.currentTimeMillis() / 1000.0;

            // 使用统一格式的时间戳
            String formattedBackendRequestTime = String.format("%.7f", backendRequestTime);
            String formattedBackendToSyceeTime = String.format("%.7f", backendToSyceeTime);

            headers.set("backend_request_timestamp", formattedBackendRequestTime);
            headers.set("backend_to_sycee_timestamp", formattedBackendToSyceeTime);

            // 创建请求实体，使用新的请求体
            HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求并获取响应
            HttpMethod method = HttpMethod.valueOf(request.getMethod());
            ResponseEntity<String> response = restTemplate.exchange(
                    targetUrl,
                    method,
                    httpEntity,
                    String.class
            );

            // 记录后端从sycee接收到响应的时间
            double syceeToBackendTime = System.currentTimeMillis() / 1000.0;
            String formattedSyceeToBackendTime = String.format("%.7f", syceeToBackendTime);

            log.info("Forwarded {} request to {}, status: {}", method, targetUrl, response.getStatusCode());

            // 提取响应头中的时间戳信息
            Map<String, String> responseHeaders = new HashMap<>();
            // 添加sycee返回的时间戳
            for (String headerName : new String[]{
                    "sycee_request_timestamp",
                    "sycee_response_timestamp",
                    "syft_request_timestamp",
                    "syft_response_timestamp"
            }) {
                if (response.getHeaders().containsKey(headerName)) {
                    responseHeaders.put(headerName, response.getHeaders().getFirst(headerName));
                }
            }

            // 添加我们自己记录的时间戳，使用统一格式
            responseHeaders.put("backend_request_timestamp", formattedBackendRequestTime);
            responseHeaders.put("backend_to_sycee_timestamp", formattedBackendToSyceeTime);
            responseHeaders.put("sycee_to_backend_timestamp", formattedSyceeToBackendTime);

            // 记录后端准备返回前端的时间
            double backendResponseTime = System.currentTimeMillis() / 1000.0;
            String formattedBackendResponseTime = String.format("%.7f", backendResponseTime);
            responseHeaders.put("backend_response_timestamp", formattedBackendResponseTime);

            // 将响应体解析为 JsonNode
            JsonNode responseBody = objectMapper.readTree(response.getBody());

            // 构建包含响应体和头信息的结果
            Map<String, Object> result = new HashMap<>();
            result.put("body", responseBody);
            result.put("headers", responseHeaders);

            return result;

        } catch (Exception e) {
            log.error("Error forwarding request", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "转发请求失败: " + e.getMessage());
        }
    }


    public Boolean createNodeAccount(Long nodeId, Long userId, String username, String password, String ipAddress, int port) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 构建新的请求体
        SyceeRequestBody requestBody = new SyceeRequestBody();
        Map<String, String> user = new HashMap<>();
        user.put("email", nodeAuth.getUsername());
        user.put("password", nodeAuth.getPassword());
        user.put("datasite_url", ipAddress + ":" + port);
        requestBody.setUser(user);
        Map<String, Object> args = new HashMap<>();
        //{"nodeId":"64","args":{"email":"abc","name":"abc","password":"1","password_verify":"1","role":"DATA_SCIENTIST"},"call":true}
        args.put("email", username);
        args.put("name", username);
        args.put("password", password);
        args.put("password_verify", password);
        args.put("role", "DATA_SCIENTIST");
        requestBody.setArgs(args);
        requestBody.setCall(true);

        //   构建基础URL
        String targetUrl = "http://*************:8088/sycee/create_user";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );
        if (response.getStatusCode() != HttpStatus.OK) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "创建节点账号失败");
        }
        return true;
    }

    /**
     * 上传代码字符串到节点
     */
    public Map<String, Object> uploadCodeStr(Long nodeId, Long userId, String func_name, String func_str) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取节点账号请求信息
        NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
        if (nodeAccount == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
        }

        // 构建新的请求体
        SyceeRequestBody requestBody = new SyceeRequestBody();
        Map<String, String> user = new HashMap<>();
        user.put("email", nodeAccount.getNodeUsername());
        user.put("password", nodeAccount.getNodePassword());
        user.put("datasite_url", node.getIpAddress() + ":" + node.getPort());
        requestBody.setUser(user);
        Map<String, Object> args = new HashMap<>();
        args.put("func_name", func_name);
        args.put("func_str", func_str);
        args.put("dataset_getter", new HashMap<>());
        args.put("onmock", false);
        requestBody.setArgs(args);
        requestBody.setCall(true);
        log.info("requestBody: {}", requestBody);

        // 构建基础URL
        String baseUrl = "http://*************:8088";

        // 构建目标URL
        String targetUrl = baseUrl + "/sycee/upload_code_str";
        log.info("targetUrl: {}", targetUrl);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        log.info("上传代码响应状态: {}", response.getStatusCode());

        if (response.getStatusCode() != HttpStatus.OK) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "上传代码失败");
        }

        // 解析响应内容
        Map<String, Object> result = new HashMap<>();
        JsonNode responseBody = null;
        try {
            responseBody = objectMapper.readTree(response.getBody());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        result.put("codeId", responseBody.get("usercode").get("id").asText());
        result.put("requestId", responseBody.get("request").get("id").asText());

        // 提取input_kwargs
        if (responseBody.get("usercode").has("input_kwargs")) {
            List<String> inputKwargs = new ArrayList<>();
            JsonNode kwargsNode = responseBody.get("usercode").get("input_kwargs");
            if (kwargsNode.isArray()) {
                for (JsonNode kwarg : kwargsNode) {
                    inputKwargs.add(kwarg.asText());
                }
            }
            result.put("inputKwargs", inputKwargs);
        }

        return result;
    }

    /**
     * 在节点上批准代码
     */
    public ResponseEntity<String> approveCode(Long nodeId, String requestId) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 构建新的请求体
        SyceeRequestBody requestBody = new SyceeRequestBody();
        Map<String, String> user = new HashMap<>();
        user.put("email", nodeAuth.getUsername());
        user.put("password", nodeAuth.getPassword());
        user.put("datasite_url", node.getIpAddress() + ":" + node.getPort());
        requestBody.setUser(user);
        Map<String, Object> args = new HashMap<>();
        args.put("id", requestId);
        requestBody.setArgs(args);
        requestBody.setCall(true);
        log.info("requestBody: {}", requestBody);

        // 构建基础URL
        String baseUrl = "http://*************:8088";

        // 构建目标URL
        String targetUrl = baseUrl + "/sycee/approve_request_by_id";
        log.info("targetUrl: {}", targetUrl);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        log.info("审批代码响应状态: {}", response.getStatusCode());

        if (response.getStatusCode() != HttpStatus.OK) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "审批代码失败");
        }
        return response;
    }

    /**
     * 在节点上拒绝代码
     */
    public ResponseEntity<String> rejectCode(Long nodeId, String requestId, String reason) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 构建新的请求体
        SyceeRequestBody requestBody = new SyceeRequestBody();
        Map<String, String> user = new HashMap<>();
        user.put("email", nodeAuth.getUsername());
        user.put("password", nodeAuth.getPassword());
        user.put("datasite_url", node.getIpAddress() + ":" + node.getPort());
        requestBody.setUser(user);
        Map<String, Object> args = new HashMap<>();
        args.put("id", requestId);
        args.put("reason", reason);
        requestBody.setArgs(args);
        requestBody.setCall(true);
        log.info("requestBody: {}", requestBody);

        // 构建基础URL
        String baseUrl = "http://*************:8088";

        // 构建目标URL
        String targetUrl = baseUrl + "/sycee/deny_request_by_id";
        log.info("targetUrl: {}", targetUrl);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        log.info("审批代码响应状态: {}", response.getStatusCode());

        if (response.getStatusCode() != HttpStatus.OK) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "审批代码失败");
        }
        return response;
    }

    /**
     * 节点上运行代码   
     */
    public Map<String, Object> runCode(Long nodeId, Long userId, String codeId, Map<String, Object> args) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取节点账号请求信息
        NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
        if (nodeAccount == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
        }

        // 构建新的请求体
        SyceeRequestBody requestBody = new SyceeRequestBody();
        Map<String, String> user = new HashMap<>();
        user.put("email", nodeAccount.getNodeUsername());
        user.put("password", nodeAccount.getNodePassword());
        user.put("datasite_url", node.getIpAddress() + ":" + node.getPort());
        requestBody.setUser(user);
        
        Map<String, Object> requestArgs = new HashMap<>();
        requestArgs.put("id", codeId);
        requestArgs.put("args", args);
        requestBody.setArgs(requestArgs);
        requestBody.setCall(true);
        log.info("requestBody: {}", requestBody);

        // 构建基础URL
        String baseUrl = "http://*************:8088";

        // 构建目标URL
        String targetUrl = baseUrl + "/sycee/run_code";
        log.info("targetUrl: {}", targetUrl);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        log.info("运行代码响应状态: {}", response.getStatusCode());

        if (response.getStatusCode() != HttpStatus.OK) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "运行代码失败");
        }

        // 解析响应内容
        Map<String, Object> result = new HashMap<>();
        JsonNode responseBody = null;
        try {
            responseBody = objectMapper.readTree(response.getBody());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        result.put("jobId", responseBody.get("id").asText());

        return result;
    }

    /**
     * 获取作业日志
     */
    public Map<String, Object> getJobLog(Long nodeId, Long userId, String jobId) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取节点账号请求信息
        NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
        if (nodeAccount == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
        }

        // 构建新的请求体
        SyceeRequestBody requestBody = new SyceeRequestBody();
        Map<String, String> user = new HashMap<>();
        user.put("email", nodeAccount.getNodeUsername());
        user.put("password", nodeAccount.getNodePassword());
        user.put("datasite_url", node.getIpAddress() + ":" + node.getPort());
        requestBody.setUser(user);
        
        Map<String, Object> args = new HashMap<>();
        args.put("id", jobId);
        requestBody.setArgs(args);
        requestBody.setCall(true);
        log.info("requestBody: {}", requestBody);

        // 构建基础URL
        String baseUrl = "http://*************:8088";

        // 构建目标URL
        String targetUrl = baseUrl + "/sycee/get_log_by_job_id";
        log.info("targetUrl: {}", targetUrl);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        log.info("获取作业日志响应状态: {}", response.getStatusCode());

        if (response.getStatusCode() != HttpStatus.OK) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取作业日志失败");
        }

        // 解析响应内容
        try {
            JsonNode responseBody = objectMapper.readTree(response.getBody());
            
            // 创建结果Map
            Map<String, Object> result = new HashMap<>();
            
            result.put("log", responseBody.toString());
            
            
            return result;
        } catch (JsonProcessingException e) {
            log.error("解析作业日志响应失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "解析作业日志响应失败");
        }
    }
    /**
     * 刷新作业状态
     */
    public Map<String, Object> refreshJobStatus(Long nodeId, Long userId, String jobId) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取节点账号请求信息
        NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
        if (nodeAccount == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
        }

        // 构建新的请求体
        SyceeRequestBody requestBody = new SyceeRequestBody();
        Map<String, String> user = new HashMap<>();
        user.put("email", nodeAccount.getNodeUsername());
        user.put("password", nodeAccount.getNodePassword());
        user.put("datasite_url", node.getIpAddress() + ":" + node.getPort());
        requestBody.setUser(user);
        
        Map<String, Object> args = new HashMap<>();
        args.put("uid", jobId);
        requestBody.setArgs(args);
        requestBody.setCall(true);
        log.info("requestBody: {}", requestBody);

        // 构建基础URL
        String baseUrl = "http://*************:8088";

        // 构建目标URL
        String targetUrl = baseUrl + "/sycee/api/job/get";
        log.info("targetUrl: {}", targetUrl);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        log.info("刷新作业响应状态: {}", response.getStatusCode());

        if (response.getStatusCode() != HttpStatus.OK) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "刷新作业失败");
        }

        // 解析响应内容
        try {
            JsonNode responseBody = objectMapper.readTree(response.getBody());
            
            // 创建结果Map
            Map<String, Object> result = new HashMap<>();
            
            // 提取状态信息
            if (responseBody.has("status")) {
                result.put("status", responseBody.get("status").asText());
            } else {
                result.put("status", "unknown");
            }
            
            return result;
        } catch (JsonProcessingException e) {
            log.error("解析作业状态响应失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "解析作业状态响应失败");
        }
    }


    /**
     * 获取作业结果
     */
    public Map<String, Object> getJobResult(Long nodeId, Long userId, String jobId) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取节点账号请求信息
        NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
        if (nodeAccount == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
        }

        // 构建新的请求体
        SyceeRequestBody requestBody = new SyceeRequestBody();
        Map<String, String> user = new HashMap<>();
        user.put("email", nodeAccount.getNodeUsername());
        user.put("password", nodeAccount.getNodePassword());
        user.put("datasite_url", node.getIpAddress() + ":" + node.getPort());
        requestBody.setUser(user);
        
        Map<String, Object> args = new HashMap<>();
        args.put("id", jobId);
        requestBody.setArgs(args);
        requestBody.setCall(true);
        log.info("requestBody: {}", requestBody);

        // 构建基础URL
        String baseUrl = "http://*************:8088";

        // 构建目标URL
        String targetUrl = baseUrl + "/sycee/get_job_result";
        log.info("targetUrl: {}", targetUrl);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        log.info("获取作业结果响应状态: {}", response.getStatusCode());

        if (response.getStatusCode() != HttpStatus.OK) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取作业结果失败");
        }

        // 解析响应内容
        try {
            JsonNode responseBody = objectMapper.readTree(response.getBody());
            // 直接将整个响应体转换为Map返回，明确指定类型参数
            return objectMapper.convertValue(responseBody, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.error("解析作业结果响应失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "解析作业结果响应失败");
        }
    }
    
    /**
     * 上传代码字符串到节点
     */
    public Map<String, Object> uploadCodeStrLocal(Long resourceId, Long userId, String func_name, String func_str, String customModelPath) {
        // 获取资源信息
        ResourceEntity resource = resourceRepository.findById(resourceId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "资源不存在"));
        
        // 验证资源权限
        if (userResourceRepository.findByUserIdAndResource_Id(userId, resourceId) == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权访问此资源");
        }

        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(resource.getNodeId());
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(node.getId());
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取节点账号请求信息
        NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                userId, node.getId(), NodeAccountRequestEntity.ApprovalStatus.APPROVED);
        if (nodeAccount == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
        }

        //判断资源类型
        if (resource.getResourceType().equals("DATASET")){
            // 获取数据集信息
            DatasetEntity dataset = datasetRepository.findById(resource.getResourceId())
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));
        
            // 验证用户是否有权限访问该数据集
            if (!hasDatasetPermission(userId, dataset.getId())) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "无权访问此数据集");
            }
        }

        // 构建新的请求体
        SyceeRequestBody requestBody = new SyceeRequestBody();
        Map<String, String> user = new HashMap<>();
        user.put("email", nodeAccount.getNodeUsername());
        user.put("password", nodeAccount.getNodePassword());
        user.put("datasite_url", node.getIpAddress() + ":" + node.getPort());
        requestBody.setUser(user);
        Map<String, Object> args = new HashMap<>();
        args.put("func_name", func_name);
        args.put("func_str", func_str);
        args.put("dataset_getter", new HashMap<>());
        args.put("onmock", false);

        Map<String, Object> local_args = new HashMap<>();
        
        if (resource.getResourceType().equals("DATASET")){
            MinioConfigDTO dataConfig = minioConfigService.getActiveConfigByType(MinioConfigEntity.ConfigType.DATASET);
            local_args.put("data_endpoint", dataConfig.getEndpoint());
            local_args.put("data_access_key", dataConfig.getAccessKey());
            local_args.put("data_secret_key", dataConfig.getSecretKey());
            local_args.put("data_bucket", dataConfig.getBucket());
            DatasetEntity dataset = datasetRepository.findById(resource.getResourceId())
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));
            local_args.put("data_path", dataset.getPath());
        }

        if (customModelPath!= null){
            MinioConfigDTO modelConfig = minioConfigService.getActiveConfigByType(MinioConfigEntity.ConfigType.MODEL);
            local_args.put("model_endpoint", modelConfig.getEndpoint());
            local_args.put("model_access_key", modelConfig.getAccessKey());
            local_args.put("model_secret_key", modelConfig.getSecretKey());
            local_args.put("model_bucket", modelConfig.getBucket());
            local_args.put("model_path", customModelPath);
        }
        log.info("local_args: {}", local_args);
        
        args.put("local_args", local_args);

        requestBody.setArgs(args);
        requestBody.setCall(true);
        log.info("requestBody: {}", requestBody);

        // 构建基础URL
        String baseUrl = "http://*************:8088";

        // 构建目标URL
        String targetUrl = baseUrl + "/sycee/upload_code_str";
        log.info("targetUrl: {}", targetUrl);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<SyceeRequestBody> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        log.info("上传代码响应状态: {}", response.getStatusCode());

        if (response.getStatusCode() != HttpStatus.OK) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "上传代码失败");
        }

        // 解析响应内容
        Map<String, Object> result = new HashMap<>();
        JsonNode responseBody = null;
        try {
            responseBody = objectMapper.readTree(response.getBody());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        result.put("codeId", responseBody.get("usercode").get("id").asText());
        result.put("requestId", responseBody.get("request").get("id").asText());

        // 提取input_kwargs
        if (responseBody.get("usercode").has("input_kwargs")) {
            List<String> inputKwargs = new ArrayList<>();
            JsonNode kwargsNode = responseBody.get("usercode").get("input_kwargs");
            if (kwargsNode.isArray()) {
                for (JsonNode kwarg : kwargsNode) {
                    inputKwargs.add(kwarg.asText());
                }
            }
            result.put("inputKwargs", inputKwargs);
        }

        return result;
    }

    /**
     * 检查用户是否有权限使用数据集
     */
    public boolean hasDatasetPermission(Long userId, Long datasetId) {
        DatasetEntity dataset = datasetRepository.findById(datasetId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));

        // 如果是数据集创建者，直接有权限
        if (dataset.getCreatorId().equals(userId)) {
            return true;
        }

        // 检查是否有已批准的使用申请
        List<DatasetUsageRequestEntity> approvedRequests = datasetUsageRequestRepository.findByApplicantIdAndStatus(
                userId, DatasetUsageRequestEntity.ApprovalStatus.APPROVED);

        for (DatasetUsageRequestEntity request : approvedRequests) {
            if (request.getDataset().getId().equals(datasetId)) {
                return true;
            }
        }

        return false;
    }

}
