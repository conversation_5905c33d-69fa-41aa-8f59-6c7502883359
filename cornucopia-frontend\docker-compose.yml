version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./cornucopia-frontend
      dockerfile: Dockerfile
    container_name: cornucopia-frontend
    ports:
      - "0.0.0.0:30000:80"  # 将容器的80端口映射到主机的30000端口
    networks:
      - cornucopia-network
    environment:
      - NODE_ENV=production
    depends_on:
      - backend

  # 后端服务
  backend:
    build:
      context: ./cornucopia-backend
      dockerfile: Dockerfile
    container_name: cornucopia-backend
    ports:
      - "8080:8080"  # 将容器的8080端口映射到主机的8080端口
    networks:
      - cornucopia-network
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SERVER_PORT=8080
      - SPRING_DATASOURCE_URL=******************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=123456
      - STORAGE_MINIO_ENDPOINT=http://10.140.34.208:9000
      - STORAGE_MINIO_ACCESSKEY=cfCpnt541pIi01ErfZFc
      - STORAGE_MINIO_SECRETKEY=cIHBIYrYy8jsnWPNpdkUeOTJXcSm46aanZ7zjWPU
      - STORAGE_MINIO_BUCKET=datasets
    volumes:
      - ./logs:/app/logs  # 日志持久化
      - ./user-application.yml:/app/user-application.yml  # 外部配置文件
    depends_on:
      - mysql

  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: cornucopia-mysql
    restart: always
    ports:
      - "13306:3306"  # 将容器的3306端口映射到主机的13306端口
    networks:
      - cornucopia-network
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=cornucopia_db
      - MYSQL_USER=cornucopia
      - MYSQL_PASSWORD=cornucopia
    volumes:
      - mysql-data:/var/lib/mysql  # 数据持久化
      - ./mysql/init:/docker-entrypoint-initdb.d  # 初始化脚本
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

volumes:
  mysql-data:  # 定义数据卷

networks:
  cornucopia-network:
    driver: bridge