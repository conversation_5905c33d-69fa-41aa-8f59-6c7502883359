package ouc.isclab.application.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.application.entity.ApplicationTaskEntity;
import ouc.isclab.application.entity.ApplicationTaskEntity.DeployStatus;
import ouc.isclab.model.entity.ModelEntity;
import ouc.isclab.application.pojo.ApplicationTaskDTO;
import ouc.isclab.application.repository.ApplicationTaskRepository;
import ouc.isclab.model.repository.ModelRepository;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.repository.NodeRepository;
import ouc.isclab.storage.pojo.MinioConfigDTO;
import ouc.isclab.storage.service.MinioConfigService;
import ouc.isclab.storage.service.MinioService;
import ouc.isclab.pyxis.service.PyxisService;
import ouc.isclab.system.service.UserService;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ApplicationService {

    @Autowired
    private ApplicationTaskRepository applicationTaskRepository;
    
    @Autowired
    private ModelRepository modelRepository;
    
    @Autowired
    private NodeRepository nodeRepository;
    
    @Autowired
    private MinioService minioService;

    @Autowired
    private PyxisService pyxisService;
    
    @Autowired
    private MinioConfigService minioConfigService;
    
    @Autowired
    private UserService userService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 创建部署任务
     */
    @Transactional
    public ApplicationTaskEntity createDeployTask(ApplicationTaskDTO taskDTO, Long creatorId) {
        // 验证模型（如果modelId不为空）
        ModelEntity model = null;
        if (taskDTO.getModelId() != null) {
            model = modelRepository.findById(taskDTO.getModelId())
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "模型不存在"));
        }
        
        // 验证节点
        NodeEntity node = nodeRepository.findById(taskDTO.getNodeId())
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));
        
        // 获取创建者信息
        String creatorName;
        try {
            creatorName = userService.findUserById(creatorId).getUsername();
        } catch (Exception e) {
            log.error("获取创建者信息失败", e);
            creatorName = "未知用户";
        }
        
        // 创建部署任务
        ApplicationTaskEntity task = new ApplicationTaskEntity();
        task.setName(taskDTO.getName());
        task.setDescription(taskDTO.getDescription());
        task.setCreatorId(creatorId);
        task.setCreatorName(creatorName);
        task.setModel(model);  // 可以为null
        task.setNode(node);
        task.setModelPath(taskDTO.getModelPath());
        task.setStatus(DeployStatus.PENDING);
        
        // 设置任务ID和保存的文件列表
        if (taskDTO.getTaskId() != null) {
            task.setTaskId(taskDTO.getTaskId());
        }
        if (taskDTO.getSavedFiles() != null) {
            try {
                task.setSavedFiles(objectMapper.writeValueAsString(taskDTO.getSavedFiles()));
            } catch (Exception e) {
                log.error("转换文件列表为JSON失败", e);
                task.setSavedFiles("[]");
            }
        }
        
        return applicationTaskRepository.save(task);
    }

    /**
     * 获取用户的部署任务列表
     */
    public Page<ApplicationTaskEntity> getUserDeployTasks(Long userId, String keyword, Pageable pageable) {
        if (keyword != null && !keyword.trim().isEmpty()) {
            return applicationTaskRepository.findByCreatorIdAndKeyword(userId, keyword.trim(), pageable);
        } else {
            return applicationTaskRepository.findByCreatorId(userId, pageable);
        }
    }
    
    /**
     * 获取部署任务详情
     */
    public ApplicationTaskEntity getDeployTaskDetail(Long taskId, Long userId) {
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "部署任务不存在"));
        
        // 验证是否是任务创建者
        if (!task.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权查看此部署任务");
        }
        
        return task;
    }

    /**
     * 删除部署任务
     */
    @Transactional
    public void deleteDeployTask(Long taskId, Long userId) {
        // 1. 获取任务实体
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "部署任务不存在"));
        
        // 2. 检查权限（只有创建者可以删除）
        if (!task.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权删除此部署任务");
        }
        
        // 3. 检查任务状态（不能删除正在部署的任务）
        if (task.getStatus() == DeployStatus.DEPLOYING) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无法删除正在部署的任务");
        }
        
        // 4. 删除数据库记录
        applicationTaskRepository.delete(task);
    }

    /**
     * 批量删除部署任务
     */
    @Transactional
    public void batchDeleteDeployTasks(List<Long> ids, Long userId) {
        for (Long id : ids) {
            deleteDeployTask(id, userId);
        }
    }

    /**
     * 更新部署任务状态
     */
    @Transactional
    public void updateDeployTaskStatus(Long taskId, DeployStatus status) {
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "部署任务不存在"));
        
        task.setStatus(status);
        applicationTaskRepository.save(task);
    }

    /**
     * 获取节点所有者需要审批的部署任务列表
     */
    public Page<ApplicationTaskEntity> getPendingDeployTasksForNodeOwner(Long nodeOwnerId, DeployStatus status, Pageable pageable) {
        return applicationTaskRepository.findByNode_CreatorIdAndStatus(nodeOwnerId, status, pageable);
    }

    /**
     * 获取节点所有者的所有部署任务
     */
    public Page<ApplicationTaskEntity> getAllDeployTasksByNodeOwner(Long nodeOwnerId, Pageable pageable) {
        return applicationTaskRepository.findByNode_CreatorId(nodeOwnerId, pageable);
    }

    /**
     * 审批部署任务
     */
    @Transactional
    public ApplicationTaskEntity approveDeployTask(Long taskId, boolean approved, String message, Long approverId) {
        // 获取任务实体
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "部署任务不存在"));
        
        // 检查是否是节点所有者
        if (!task.getNode().getCreatorId().equals(approverId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "您不是该节点的所有者，无权审批");
        }
        
        // 更新任务状态
        if (approved) {
            task.setStatus(DeployStatus.APPROVED);
            task.setMessage(message);
            task.setExecuted(false);
            pyxisService.approvePyxisTask(task.getNode().getId(), task.getTaskId(), message, approved);
        } else {
            task.setStatus(DeployStatus.REJECTED);
            task.setMessage(message);
            pyxisService.approvePyxisTask(task.getNode().getId(), task.getTaskId(), message, approved);
        }
        
        task.setApproverId(approverId);
        return applicationTaskRepository.save(task);
    }

    /**
     * 检查用户是否向节点所有者发起过部署任务审批请求
     */
    public boolean hasApprovalRequestFromUserToNodeOwner(Long creatorId, Long nodeOwnerId) {
        // 获取节点所有者拥有的所有节点
        List<Long> ownerNodeIds = nodeRepository.findNodeIdsByCreatorId(nodeOwnerId);
        
        if (ownerNodeIds.isEmpty()) {
            return false;
        }
        
        // 检查用户是否在这些节点上提交过需要审批的部署任务
        return applicationTaskRepository.existsByCreatorIdAndNodeIdInAndStatusIn(
            creatorId, 
            ownerNodeIds, 
            List.of(DeployStatus.PENDING, DeployStatus.APPROVED, DeployStatus.REJECTED)
        );
    }

    /**
     * 执行Pyxis任务
     */
    public Map<String, Object> executePyxisTask(Long taskId, Long userId) {
        // 获取任务实体
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "部署任务不存在"));

        // 检查任务状态
        if (task.getStatus() != DeployStatus.APPROVED) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "任务未被批准，无法执行");
        }
        
        // 检查任务是否已执行过且未重新申请审批
        if (task.getExecuted() != null && task.getExecuted() && task.getStatus() == DeployStatus.APPROVED) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "任务已执行过，请重新申请审批后再执行");
        }

        // 执行Pyxis任务
        Map<String, Object> result = pyxisService.executePyxisTask(task.getNode().getId(), task.getTaskId(), userId);
        
        // 标记任务已执行，但不改变审批状态
        task.setExecuted(true);
        applicationTaskRepository.save(task);
        
        return result;
    }

    /**
     * 停止部署任务
     */
    public Map<String, Object> stopTask(Long taskId, Long userId) {
        // 获取任务实体
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "部署任务不存在"));

        // // 检查任务状态
        // if (task.getStatus() != DeployStatus.APPROVED) {
        //     throw new BaseException(ResponseCode.SERVICE_ERROR, "任务未被批准，无法停止");
        // }

        // 停止Pyxis任务
        Map<String, Object> result = pyxisService.stopPyxisTask(task.getNode().getId(), task.getTaskId(), userId);
        
        // 标记任务已执行操作，但不改变审批状态
        task.setExecuted(true);
        applicationTaskRepository.save(task);
        
        return result;
    }

    /**
     * 强制终止部署任务
     */
    public Map<String, Object> killTask(Long taskId, Long userId) {
        // 获取任务实体
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "部署任务不存在"));

        // // 检查任务状态
        // if (task.getStatus() != DeployStatus.APPROVED) {
        //     throw new BaseException(ResponseCode.SERVICE_ERROR, "任务未被批准，无法终止");
        // }

        // 强制终止Pyxis任务
        Map<String, Object> result = pyxisService.killPyxisTask(task.getNode().getId(), task.getTaskId(), userId);
        
        // 标记任务已执行操作，但不改变审批状态
        task.setExecuted(true);
        applicationTaskRepository.save(task);
        
        return result;
    }

    /**
     * 获取部署任务状态
     */
    public Map<String, Object> getTaskStatus(Long taskId, Long userId) {
        // 获取任务实体
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "部署任务不存在"));

        // // 检查任务状态
        // if (task.getStatus() != DeployStatus.APPROVED) {
        //     throw new BaseException(ResponseCode.SERVICE_ERROR, "任务未被批准，无法获取状态");
        // }

        // 获取Pyxis任务状态（不需要更新任务状态）
        return pyxisService.getPyxisTaskStatus(task.getNode().getId(), task.getTaskId(), userId);
    }

    /**
     * 浏览部署任务文件目录
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param path 要浏览的目录路径，如果为null则浏览根目录
     * @return 目录内容
     */
    public Map<String, Object> browseTaskFiles(Long taskId, Long userId, String path) {
        // 获取任务实体
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "部署任务不存在"));

        // 检查任务状态（不需要检查是否为APPROVED状态，因为浏览文件不需要任务处于特定状态）
        
        // 浏览Pyxis任务文件目录
        return pyxisService.browsePyxisTaskFiles(task.getNode().getId(), task.getTaskId(), userId, path);
    }

    /**
     * 下载任务文件
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param filePath 文件路径
     * @return ResponseEntity<byte[]> 包含文件内容的响应实体
     */
    public ResponseEntity<byte[]> downloadTaskFile(Long taskId, Long userId, String filePath) {
        // 获取任务信息
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "任务不存在"));

        // // 检查任务状态
        // if (!task.getStatus().equals(ModelDeployTaskEntity.TaskStatus.APPROVED)) {
        //     throw new BaseException(ResponseCode.SERVICE_ERROR, "任务未审批，无法下载文件");
        // }

        log.info("下载任务文件: taskId={}, filePath={}, userId={}", taskId, filePath, userId);
        return pyxisService.downloadPyxisTaskFile(task.getNode().getId(), task.getTaskId(), userId, filePath);
    }

    /**
     * 预览任务文本文件
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param filePath 文件路径
     * @return String 文件内容文本
     */
    public String previewTaskFile(Long taskId, Long userId, String filePath) {
        // 获取任务信息
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "任务不存在"));

        log.info("预览任务文件: taskId={}, filePath={}, userId={}", taskId, filePath, userId);
        return pyxisService.previewPyxisTaskFile(task.getNode().getId(), task.getTaskId(), userId, filePath);
    }

    /**
     * 申请重新审批
     */
    public Map<String, Object> requestReapproval(Long taskId, Long userId) {
        // 获取任务实体
        ApplicationTaskEntity task = applicationTaskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "部署任务不存在"));

        // 检查是否是任务的所有者
        if (!task.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "只有任务所有者才能申请重新审批");
        }

        // 更新任务状态为待审批
        task.setStatus(DeployStatus.PENDING);
        task.setExecuted(false);
        applicationTaskRepository.save(task);

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "已成功申请重新审批");
        return result;
    }

    /**
     * 获取应用统计数据
     */
    public Map<String, Object> getApplicationStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 获取总应用数
            long totalApplications = applicationTaskRepository.count();
            statistics.put("totalApplications", totalApplications);

            // 获取运行中应用数（部署中的应用）
            long runningApplications = applicationTaskRepository.countByStatus(DeployStatus.DEPLOYING);
            statistics.put("runningApplications", runningApplications);

            // 获取已停止应用数（已完成的应用）
            long stoppedApplications = applicationTaskRepository.countByStatus(DeployStatus.COMPLETED);
            statistics.put("stoppedApplications", stoppedApplications);

            // 获取失败应用数
            long failedApplications = applicationTaskRepository.countByStatus(DeployStatus.FAILED);
            statistics.put("failedApplications", failedApplications);

            // 生成应用部署趋势数据（最近7天）
            List<Map<String, Object>> appTrend = generateAppTrendData();
            statistics.put("appTrend", appTrend);

            log.info("应用统计数据: {}", statistics);

        } catch (Exception e) {
            log.error("获取应用统计数据失败", e);
            // 返回默认数据
            statistics.put("totalApplications", 0);
            statistics.put("runningApplications", 0);
            statistics.put("stoppedApplications", 0);
            statistics.put("failedApplications", 0);
            statistics.put("appTrend", new ArrayList<>());
        }

        return statistics;
    }

    /**
     * 生成应用部署趋势数据
     */
    private List<Map<String, Object>> generateAppTrendData() {
        List<Map<String, Object>> trendData = new ArrayList<>();

        // 获取最近7天的数据
        Calendar calendar = Calendar.getInstance();
        for (int i = 6; i >= 0; i--) {
            calendar.setTime(new Date());
            calendar.add(Calendar.DAY_OF_MONTH, -i);
            Date startDate = calendar.getTime();

            calendar.add(Calendar.DAY_OF_MONTH, 1);
            Date endDate = calendar.getTime();

            // 查询当天创建的应用数量
            long count = applicationTaskRepository.countByTimeCreatedBetween(startDate, endDate);

            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", new SimpleDateFormat("MM-dd").format(startDate));
            dayData.put("count", count);
            trendData.add(dayData);
        }

        return trendData;
    }

}