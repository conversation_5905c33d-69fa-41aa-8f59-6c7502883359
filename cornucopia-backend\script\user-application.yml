server:
  port: 8080

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************
    username: root
    password: password

  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
      enabled: true

  tomcat:
    uri-encoding: UTF-8
    max-http-form-post-size: -1
    connection-timeout: 200000

  jpa:
    hibernate:
      ddl-auto: update
      show-sql: true
    # 自动创建表采用InnoDB引擎
    database-platform: org.hibernate.dialect.MySQL8Dialect
    # 批量插入数据（未验证）
    properties:
      hibernate:
        #generate_statistics: true
        jdbc:
          batch_size: 5
        order_inserts: true
        enable_lazy_load_no_trans: true # 解决延迟加载问题

    transaction:
      rollback-on-commit-failure: true

mdp:
  file-pool-path: /Users/<USER>/mdp/file-pool
  filter-file-types: jpg, jpeg, png, nc
  video-pool-path: /Users/<USER>/mdp/file-pool/video
