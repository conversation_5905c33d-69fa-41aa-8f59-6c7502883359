from framework.ws import WebSocketServer
from framework.peer import Peer

import time
import asyncio

def handle_message(message):
    print(f"Server received message: {message}")
    return f"Server received message: {message}"
    
def on_connect(websocket):
    print("Client connected to server")

def on_disconnect(websocket):
    print("Client disconnected from server")

# Create and configure the server
server = WebSocketServer(
    host="0.0.0.0",
    port=8000,
    message_callback=handle_message,
    on_connect=on_connect,
    on_disconnect=on_disconnect
)


async def run_server():
    print("register peer")
    peer = Peer()
    resp = peer.register("test_p2p")
    print(resp)
    print("Starting WebSocket server...")
    asyncio.create_task(server.run())
    
    await asyncio.sleep( 5 * 60 )
    
    await server.stop()
    


def main():
    asyncio.run(run_server())
    
        