package ouc.isclab.storage.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.storage.entity.MinioConfigEntity;
import ouc.isclab.storage.pojo.MinioConfigDTO;
import ouc.isclab.storage.pojo.MinioConfigListDTO;
import ouc.isclab.storage.repository.MinioConfigRepository;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MinioConfigService {

    @Autowired
    private MinioConfigRepository minioConfigRepository;

    /**
     * 获取所有配置
     */
    public Page<MinioConfigListDTO> getAllConfigs(Pageable pageable) {
        return minioConfigRepository.findAll(pageable)
                .map(MinioConfigListDTO::fromEntity);
    }

    /**
     * 获取指定类型的配置
     */
    public Page<MinioConfigListDTO> getConfigsByType(MinioConfigEntity.ConfigType type, Pageable pageable) {
        return minioConfigRepository.findByType(type, pageable)
                .map(MinioConfigListDTO::fromEntity);
    }

    /**
     * 获取指定ID的配置
     */
    public MinioConfigDTO getConfigById(Long id) {
        return minioConfigRepository.findById(id)
                .map(MinioConfigDTO::fromEntity)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "配置不存在"));
    }

    /**
     * 创建新配置
     */
    @Transactional
    public MinioConfigDTO createConfig(MinioConfigDTO configDTO) {
        // 如果新配置是活动的，将同类型的其他配置设置为非活动
        if (Boolean.TRUE.equals(configDTO.getActive())) {
            minioConfigRepository.findByTypeAndActiveTrue(configDTO.getType())
                    .ifPresent(config -> {
                        config.setActive(false);
                        minioConfigRepository.save(config);
                    });
        }

        MinioConfigEntity entity = configDTO.toEntity();
        entity = minioConfigRepository.save(entity);
        return MinioConfigDTO.fromEntity(entity);
    }

    /**
     * 更新配置
     */
    @Transactional
    public MinioConfigDTO updateConfig(Long id, MinioConfigDTO configDTO) {
        MinioConfigEntity existingConfig = minioConfigRepository.findById(id)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "配置不存在"));

        // 如果配置类型改变，需要检查新类型是否已有活动配置
        if (!existingConfig.getType().equals(configDTO.getType()) && Boolean.TRUE.equals(configDTO.getActive())) {
            minioConfigRepository.findByTypeAndActiveTrue(configDTO.getType())
                    .ifPresent(config -> {
                        config.setActive(false);
                        minioConfigRepository.save(config);
                    });
        }

        // 如果配置变为活动状态，将同类型的其他配置设置为非活动
        if (Boolean.TRUE.equals(configDTO.getActive()) && !existingConfig.getActive()) {
            minioConfigRepository.findByTypeAndActiveTrue(configDTO.getType())
                    .ifPresent(config -> {
                        if (!config.getId().equals(id)) {
                            config.setActive(false);
                            minioConfigRepository.save(config);
                        }
                    });
        }

        existingConfig.setName(configDTO.getName());
        existingConfig.setEndpoint(configDTO.getEndpoint());
        existingConfig.setAccessKey(configDTO.getAccessKey());
        existingConfig.setSecretKey(configDTO.getSecretKey());
        existingConfig.setBucket(configDTO.getBucket());
        existingConfig.setType(configDTO.getType());
        existingConfig.setActive(configDTO.getActive());

        existingConfig = minioConfigRepository.save(existingConfig);
        return MinioConfigDTO.fromEntity(existingConfig);
    }

    /**
     * 删除配置
     */
    @Transactional
    public void deleteConfig(Long id) {
        if (!minioConfigRepository.existsById(id)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "配置不存在");
        }
        minioConfigRepository.deleteById(id);
    }

    /**
     * 获取指定类型的活动配置
     */
    public MinioConfigDTO getActiveConfigByType(MinioConfigEntity.ConfigType type) {
        return minioConfigRepository.findByTypeAndActiveTrue(type)
                .map(MinioConfigDTO::fromEntity)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "未找到活动配置"));
    }

    /**
     * 切换配置的活动状态
     */
    @Transactional
    public MinioConfigDTO toggleActiveStatus(Long id) {
        MinioConfigEntity config = minioConfigRepository.findById(id)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "配置不存在"));

        // 如果当前配置已经是活动的，直接返回
        if (config.getActive()) {
            return MinioConfigDTO.fromEntity(config);
        }

        // 将同类型的所有活动配置设置为非活动
        List<MinioConfigEntity> activeConfigs = minioConfigRepository.findAll()
                .stream()
                .filter(c -> c.getType().equals(config.getType()) && c.getActive() && !c.getId().equals(id))
                .collect(Collectors.toList());

        for (MinioConfigEntity activeConfig : activeConfigs) {
            activeConfig.setActive(false);
            minioConfigRepository.save(activeConfig);
        }

        // 将当前配置设置为活动
        config.setActive(true);
        return MinioConfigDTO.fromEntity(minioConfigRepository.save(config));
    }

    /**
     * 获取指定类型的活动配置
     */
    public MinioConfigDTO getActiveConfigByBucket(String bucket) {
        MinioConfigEntity.ConfigType type;
        if ("datasets".equals(bucket)) {
            type = MinioConfigEntity.ConfigType.DATASET;
        } else if ("models".equals(bucket)) {
            type = MinioConfigEntity.ConfigType.MODEL;
        } else {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "不支持的存储桶类型");
        }
        return getActiveConfigByType(type);
    }

} 