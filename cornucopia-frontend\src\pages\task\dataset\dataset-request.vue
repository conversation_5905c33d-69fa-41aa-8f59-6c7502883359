<template>
  <div class="dataset-request-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><DataLine /></el-icon>
          <h2>申请数据集使用权限</h2>
        </div>
        <div class="sub-title">申请在特定节点上使用数据集</div>
      </div>
    </div>

    <el-card class="form-card" shadow="hover">
      <el-form :model="requestForm" :rules="rules" ref="requestFormRef" label-width="120px" class="request-form">
        <el-form-item label="数据集" prop="datasetId">
          <el-select 
            v-model="requestForm.datasetId" 
            placeholder="请选择数据集" 
            filterable 
            style="width: 100%"
            :disabled="!!preSelectedDatasetId"
            @change="handleDatasetChange"
          >
            <el-option
              v-for="dataset in datasetOptions"
              :key="dataset.value"
              :label="dataset.label"
              :value="dataset.value"
            >
              <div class="dataset-option">
                <span>{{ dataset.label }}</span>
                <el-tag v-if="isDatasetOwner(dataset.value)" type="success" size="small">我的数据集</el-tag>
              </div>
            </el-option>
          </el-select>
          
          <div v-if="requestForm.datasetId && getDatasetAvailableNodes(requestForm.datasetId).length > 0" class="dataset-nodes-info">
            <el-alert
              title="此数据集可在以下节点上使用"
              type="info"
              :closable="false"
              show-icon
            >
              <div class="available-nodes-list">
                <el-tag 
                  v-for="nodeId in getDatasetAvailableNodes(requestForm.datasetId)" 
                  :key="nodeId"
                  class="node-tag"
                  :type="hasNodeAccount(nodeId) ? 'success' : 'info'"
                >
                  {{ getNodeName(nodeId) }}
                </el-tag>
              </div>
            </el-alert>
          </div>
        </el-form-item>

        <el-form-item label="使用节点" prop="nodeId">
          <el-select 
            v-model="requestForm.nodeId" 
            placeholder="请选择节点" 
            filterable 
            style="width: 100%"
            :disabled="!!preSelectedNodeId"
          >
            <el-option
              v-for="node in filteredNodeOptions"
              :key="node.value"
              :label="node.label"
              :value="node.value"
            >
              <div class="node-option">
                <span>{{ node.label }}</span>
                <div class="node-status-tags">
                  <el-tag v-if="!hasNodeAccount(node.value)" type="warning" size="small">需申请账号</el-tag>
                  <el-tag v-else-if="hasDatasetPermission(requestForm.datasetId, node.value)" type="success" size="small">已有权限</el-tag>
                  <el-tag v-else-if="hasPendingRequest(requestForm.datasetId, node.value)" type="warning" size="small">申请中</el-tag>
                  <el-tag v-else-if="hasRejectedRequest(requestForm.datasetId, node.value)" type="danger" size="small">已拒绝</el-tag>
                </div>
              </div>
            </el-option>
          </el-select>
          
          <div v-if="requestForm.nodeId && !hasNodeAccount(requestForm.nodeId)" class="apply-account-tip">
            <el-alert
              title="您需要先申请该节点的账号"
              type="warning"
              :closable="false"
              show-icon
            >
              <template #default>
                <el-button type="primary" size="small" @click="goToNodeRequest">
                  申请节点账号
                </el-button>
              </template>
            </el-alert>
          </div>
          
          <div v-else-if="requestForm.datasetId && requestForm.nodeId && hasPendingRequest(requestForm.datasetId, requestForm.nodeId)" class="existing-request-tip">
            <el-alert
              title="您已经提交过该数据集在该节点上的使用申请，请等待审批"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>
          
          <div v-else-if="requestForm.datasetId && requestForm.nodeId && hasDatasetPermission(requestForm.datasetId, requestForm.nodeId)" class="existing-permission-tip">
            <el-alert
              title="您已经有该数据集在该节点上的使用权限，无需重复申请"
              type="success"
              :closable="false"
              show-icon
            />
          </div>
          
          <div v-else-if="requestForm.datasetId && requestForm.nodeId && hasRejectedRequest(requestForm.datasetId, requestForm.nodeId)" class="rejected-request-tip">
            <el-alert
              title="您之前的申请已被拒绝，可以重新申请"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <div v-if="getRejectedReason(requestForm.datasetId, requestForm.nodeId)" class="reject-reason">
                  拒绝理由: {{ getRejectedReason(requestForm.datasetId, requestForm.nodeId) }}
                </div>
              </template>
            </el-alert>
          </div>
        </el-form-item>

        <el-form-item label="使用目的" prop="purpose">
          <el-input
            v-model="requestForm.purpose"
            type="textarea"
            :rows="4"
            placeholder="请输入使用目的"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            @click="submitRequest" 
            :loading="submitting" 
            :disabled="isSubmitDisabled"
            round
          >
            <el-icon><Check /></el-icon>提交申请
          </el-button>
          <el-button @click="resetForm" round>
            <el-icon><Refresh /></el-icon>重置
          </el-button>
          <el-button @click="goBack" round>
            <el-icon><Back /></el-icon>返回
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="list-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>我的申请记录</span>
          <el-button type="primary" @click="loadRequests" size="small" plain round>
            <el-icon><Refresh /></el-icon>刷新
          </el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
        highlight-current-row
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column label="数据集" min-width="150" align="center">
          <template #default="scope">
            <el-tag type="primary">{{ scope.row.dataset?.name || '未知数据集' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="节点" min-width="150" align="center">
          <template #default="scope">
            <el-tag type="success">{{ getNodeName(scope.row.nodeId) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="purpose" label="使用目的" min-width="200" show-overflow-tooltip align="center" />
        <el-table-column label="状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          prop="timeCreated" 
          label="申请时间" 
          width="180" 
          align="center"
          sortable
          :formatter="formatDateTime"
        />
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="scope">
            <el-tooltip content="查看详情" placement="top">
              <el-button type="info" size="small" @click="viewRequestDetail(scope.row)">
                <el-icon><View /></el-icon>
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 申请详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="申请详情"
      width="700px"
      destroy-on-close
    >
      <div v-if="currentRequest" class="request-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="申请ID">{{ currentRequest.id }}</el-descriptions-item>
          <el-descriptions-item label="数据集">{{ currentRequest.dataset?.name || '未知数据集' }}</el-descriptions-item>
          <el-descriptions-item label="节点">{{ getNodeName(currentRequest.nodeId) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentRequest.status)">
              {{ getStatusText(currentRequest.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="使用目的">{{ currentRequest.purpose }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime({timeCreated: currentRequest.timeCreated}) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间" v-if="currentRequest.timeUpdated">
            {{ formatDateTime({timeUpdated: currentRequest.timeUpdated}) }}
          </el-descriptions-item>
          <el-descriptions-item label="拒绝理由" v-if="currentRequest.rejectReason">
            {{ currentRequest.rejectReason }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { DataLine, Check, Refresh, Back, View } from '@element-plus/icons-vue';
import service from '~/axios';
import { toast } from '~/composables/util';

const router = useRouter();
const route = useRoute();
const requestFormRef = ref(null);

// 预选参数（从URL参数获取）
const preSelectedDatasetId = computed(() => {
  const datasetId = route.query.datasetId;
  return datasetId ? Number(datasetId) : null;
});

const preSelectedNodeId = computed(() => {
  const nodeId = route.query.nodeId;
  return nodeId ? Number(nodeId) : null;
});

// 重定向路径
const redirectPath = computed(() => {
  return route.query.redirect || '/task/resource/request';
});

// 表单数据
const requestForm = reactive({
  datasetId: null,
  nodeId: null,
  purpose: ''
});

// 表单验证规则
const rules = {
  datasetId: [{ required: true, message: '请选择数据集', trigger: 'change' }],
  nodeId: [{ required: true, message: '请选择节点', trigger: 'change' }],
  purpose: [{ required: false, message: '请输入使用目的', trigger: 'blur' }]
};

// 状态变量
const submitting = ref(false);
const loading = ref(false);
const detailDialogVisible = ref(false);
const currentRequest = ref(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 数据列表
const nodeOptions = ref([]);
const datasetOptions = ref([]);
const tableData = ref([]);
const allRequests = ref([]); // 所有申请记录，用于检查是否已有申请
const nodeAccounts = ref([]); // 用户已有的节点账号

// 计算属性
const nodeOptionsWithAccount = computed(() => {
  return nodeOptions.value.filter(node => hasNodeAccount(node.value));
});

const filteredNodeOptions = computed(() => {
  if (!requestForm.datasetId) return [];
  
  const availableNodeIds = getDatasetAvailableNodes(requestForm.datasetId);
  return nodeOptions.value.filter(node => availableNodeIds.includes(node.value));
});

const isSubmitDisabled = computed(() => {
  if (!requestForm.datasetId || !requestForm.nodeId) return true;
  
  if (!hasNodeAccount(requestForm.nodeId)) return true;
  
  if (hasDatasetPermission(requestForm.datasetId, requestForm.nodeId) || 
      hasPendingRequest(requestForm.datasetId, requestForm.nodeId)) {
    return true;
  }
  
  return false;
});

// 方法
const loadNodes = async () => {
  try {
    const res = await service.get('/api/v1.0/sys/nodes');
    if (res.code === 10000) {
      nodeOptions.value = res.data.nodes.map(node => ({
        value: node.id,
        label: `${node.name} (${node.ipAddress}:${node.port})`,
        data: node
      }));
    } else {
      toast('错误', res.message || '获取节点列表失败', 'error');
    }
  } catch (error) {
    console.error('获取节点列表失败:', error);
    toast('错误', '获取节点列表失败', 'error');
  }
};

const loadDatasets = async () => {
  try {
    const res = await service.get('/api/v1.0/sys/dataset', { params: { size: 1000 } });
    if (res.code === 10000) {
      datasetOptions.value = res.data.datasets.map(dataset => ({
        value: dataset.id,
        label: dataset.name,
        data: dataset
      }));
      
      if (preSelectedDatasetId.value && !requestForm.datasetId) {
        requestForm.datasetId = preSelectedDatasetId.value;
        handleDatasetChange();
      }
      
      if (preSelectedNodeId.value && !requestForm.nodeId) {
        requestForm.nodeId = preSelectedNodeId.value;
      }
    } else {
      toast('错误', res.message || '获取数据集列表失败', 'error');
    }
  } catch (error) {
    console.error('获取数据集列表失败:', error);
    toast('错误', '获取数据集列表失败', 'error');
  }
};

const loadNodeAccounts = async () => {
  try {
    const res = await service.get('/api/v1.0/sys/node/account/requests', { 
      params: { status: 'APPROVED', size: 1000 } 
    });
    if (res.code === 10000) {
      nodeAccounts.value = res.data.requests || [];
    } else {
      toast('错误', res.message || '获取节点账号列表失败', 'error');
    }
  } catch (error) {
    console.error('获取节点账号列表失败:', error);
    toast('错误', '获取节点账号列表失败', 'error');
  }
};

const loadRequests = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value
    };
    
    const res = await service.get('/api/v1.0/sys/dataset/usage/requests', { params });
    if (res.code === 10000) {
      tableData.value = res.data.requests || [];
      total.value = res.data.pagination?.total || 0;
      
      const allRes = await service.get('/api/v1.0/sys/dataset/usage/requests', { params: { size: 1000 } });
      if (allRes.code === 10000) {
        allRequests.value = allRes.data.requests || [];
      }
    } else {
      toast('错误', res.message || '获取申请记录失败', 'error');
    }
  } catch (error) {
    console.error('获取申请记录失败:', error);
    toast('错误', '获取申请记录失败', 'error');
  } finally {
    loading.value = false;
  }
};

const hasNodeAccount = (nodeId) => {
  return nodeAccounts.value.some(req => req.node.id === nodeId);
};

const hasDatasetPermission = (datasetId, nodeId) => {
  if (!datasetId || !nodeId) return false;
  return allRequests.value.some(req => 
    req.dataset.id === datasetId && 
    req.nodeId === nodeId && 
    req.status === 'APPROVED'
  );
};

const hasPendingRequest = (datasetId, nodeId) => {
  if (!datasetId || !nodeId) return false;
  return allRequests.value.some(req => 
    req.dataset.id === datasetId && 
    req.nodeId === nodeId && 
    req.status === 'PENDING'
  );
};

const getNodeName = (nodeId) => {
  const node = nodeOptions.value.find(n => n.value === nodeId);
  return node ? node.label : `节点 ${nodeId}`;
};

const goToNodeRequest = () => {
  router.push({
    path: '/task/node/request',
    query: { 
      nodeId: requestForm.nodeId,
      redirect: route.fullPath
    }
  });
};

const submitRequest = async () => {
  if (!requestFormRef.value) return;
  
  await requestFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    if (!hasNodeAccount(requestForm.nodeId)) {
      toast('警告', '您需要先申请该节点的账号', 'warning');
      return;
    }
    
    if (hasDatasetPermission(requestForm.datasetId, requestForm.nodeId)) {
      toast('提示', '您已经有该数据集在该节点上的使用权限，无需重复申请', 'info');
      return;
    }
    
    if (hasPendingRequest(requestForm.datasetId, requestForm.nodeId)) {
      toast('提示', '您已经提交过该数据集在该节点上的使用申请，请等待审批', 'info');
      return;
    }
    
    submitting.value = true;
    try {
      const res = await service.post('/api/v1.0/sys/dataset/usage/request', {
        datasetId: requestForm.datasetId,
        nodeId: requestForm.nodeId,
        purpose: requestForm.purpose
      });
      
      if (res.code === 10000) {
        toast('成功', '数据集使用申请已提交，请等待审批', 'success');
        resetForm();
        loadRequests();
      } else {
        toast('错误', res.message || '提交申请失败', 'error');
      }
    } catch (error) {
      console.error('提交申请失败:', error);
      toast('错误', error.response?.data?.message || '提交申请失败', 'error');
    } finally {
      submitting.value = false;
    }
  });
};

const viewRequestDetail = (row) => {
  currentRequest.value = row;
  detailDialogVisible.value = true;
};

const resetForm = () => {
  if (requestFormRef.value) {
    requestFormRef.value.resetFields();
  }
};

const goBack = () => {
  router.push(redirectPath.value);
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadRequests();
};

const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadRequests();
};

const formatDateTime = (row, column) => {
  const dateStr = row.timeCreated || row.timeUpdated;
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
};

const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger'
  };
  return statusMap[status] || 'info';
};

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已拒绝'
  };
  return statusMap[status] || status;
};

const getDatasetAvailableNodes = (datasetId) => {
  const dataset = datasetOptions.value.find(d => d.value === datasetId);
  if (!dataset || !dataset.data || !dataset.data.availableNodes) return [];
  
  return dataset.data.availableNodes.map(node => node.id);
};

const isDatasetOwner = (datasetId) => {
  const dataset = datasetOptions.value.find(d => d.value === datasetId);
  if (!dataset || !dataset.data) return false;
  
  const currentUserId = getCurrentUserId();
  return dataset.data.creatorId === currentUserId;
};

const getCurrentUserId = () => {
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  return userInfo.id;
};

const handleDatasetChange = () => {
  if (requestForm.datasetId && requestForm.nodeId) {
    const availableNodeIds = getDatasetAvailableNodes(requestForm.datasetId);
    if (!availableNodeIds.includes(requestForm.nodeId)) {
      requestForm.nodeId = null;
    }
  }
};

const hasRejectedRequest = (datasetId, nodeId) => {
  if (!datasetId || !nodeId) return false;
  return allRequests.value.some(req => 
    req.dataset.id === datasetId && 
    req.nodeId === nodeId && 
    req.status === 'REJECTED'
  );
};

const getRejectedReason = (datasetId, nodeId) => {
  if (!datasetId || !nodeId) return null;
  const request = allRequests.value.find(req => 
    req.dataset.id === datasetId && 
    req.nodeId === nodeId && 
    req.status === 'REJECTED'
  );
  return request ? request.rejectReason : null;
};

onMounted(async () => {
  await Promise.all([
    loadNodes(),
    loadDatasets(),
    loadNodeAccounts()
  ]);
  
  loadRequests();
});
</script>

<style scoped>
.dataset-request-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.form-card {
  margin-bottom: 20px;
}

.request-form {
  max-width: 800px;
  margin: 20px auto;
}

.list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.node-option, .dataset-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.apply-account-tip {
  margin-top: 10px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

.request-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.dataset-nodes-info {
  margin-top: 10px;
}

.available-nodes-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.node-tag {
  margin-right: 5px;
}

.node-status-tags {
  display: flex;
  gap: 5px;
}

.reject-reason {
  margin-top: 8px;
  color: #F56C6C;
  font-size: 12px;
}

.existing-request-tip,
.existing-permission-tip,
.rejected-request-tip {
  margin-top: 10px;
}

/* 深色模式适配 */
html.dark {
  .form-card, .list-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style>