package ouc.isclab.task.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import ouc.isclab.task.entity.TaskEntity;
import ouc.isclab.task.entity.TaskEntity.TaskStatus;
import ouc.isclab.task.entity.CodeEntity;

import java.util.List;

public interface TaskRepository extends JpaRepository<TaskEntity, Long> {

    // 查询用户创建的所有任务
    Page<TaskEntity> findByCreatorId(Long creatorId, Pageable pageable);
    
    // 根据关键词搜索用户的任务
    @Query("SELECT t FROM TaskEntity t WHERE t.creatorId = :creatorId AND (LOWER(t.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<TaskEntity> findByCreatorIdAndKeyword(@Param("creatorId") Long creatorId, @Param("keyword") String keyword, Pageable pageable);

    // 根据状态统计任务数量
    long countByStatus(TaskStatus status);

    // 查询所有任务
    List<TaskEntity> findAll();

    // 添加查询方法
    List<TaskEntity> findByCodesContaining(CodeEntity code);
} 