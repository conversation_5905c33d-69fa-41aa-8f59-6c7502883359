package ouc.isclab.common.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.entity.ServiceLogEntity;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.common.service.ServiceLogService;
import ouc.isclab.common.response.ResponseResult;
import ouc.isclab.common.annotation.RequirePermission;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@BaseResponse
@RestController
@RequestMapping("/api/v1.0")
public class ServiceLogController {

    @Autowired
    private ServiceLogService serviceLogService;

    @RequirePermission("log:view")
    @GetMapping("/logs")
    public Map<String, Object> getLogs(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(required = false) String ip,
            @RequestParam(required = false) String method,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime
    ) {
        // 获取分页数据
        List<ServiceLogEntity> logs = serviceLogService.findLogs(page, size, ip, method, startTime, endTime);
        long total = serviceLogService.countLogs(ip, method, startTime, endTime);
        
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("data", logs);
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("total", total);
        pagination.put("page", page);
        pagination.put("size", size);
        result.put("pagination", pagination);
        
        return result;
    }

    @RequirePermission("log:delete")
    @DeleteMapping("/logs")
    public int deleteLogs(
            @RequestParam(required = false) String ip,
            @RequestParam(required = false) String method,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime
    ) {
        if (ip == null && method == null && startTime == null && endTime == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "请至少选择一种删除方法");
        }
        return serviceLogService.deleteLogs(ip, method, startTime, endTime);
    }

}


