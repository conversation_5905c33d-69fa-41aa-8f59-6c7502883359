<template>
  <div class="data-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Folder /></el-icon>
          <h2>数据集管理</h2>
        </div>
        <div class="sub-title">管理系统中的数据集文件</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.name"
                placeholder="请输入数据集名称"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
        highlight-current-row
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="name" label="数据集名称" min-width="200" show-overflow-tooltip align="center">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center; cursor: pointer;">
              <el-icon class="file-icon">
                <Link v-if="scope.row.type === 'URL'" />
                <Document v-else />
              </el-icon>
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'URL' ? 'success' : 'primary'">
              {{ scope.row.type === 'URL' ? 'URL' : '文件' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="120" align="center">
          <template #default="scope">
            {{ formatFileSize(scope.row.size) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip align="center" />
        <el-table-column 
          prop="timeCreated" 
          label="创建时间" 
          width="180" 
          align="center"
          sortable
          :formatter="formatDateTime"
        />
        <el-table-column label="操作" width="100" fixed="right" align="center">
          <template #default="scope">
            <el-button-group>
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleViewDetail(scope.row)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="数据集详情"
      width="700px"
    >
      <div v-if="currentDataset" class="dataset-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="数据集名称">{{ currentDataset.name }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="currentDataset.type === 'URL' ? 'success' : 'primary'">
              {{ currentDataset.type === 'URL' ? 'URL' : '文件' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="大小">{{ formatFileSize(currentDataset.size) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ new Date(currentDataset.timeCreated).toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ new Date(currentDataset.timeUpdated).toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="创建者">{{ currentDataset.creatorName }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ currentDataset.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="URL" v-if="currentDataset.type === 'URL'">
            <el-link type="primary" :href="currentDataset.url" target="_blank">{{ currentDataset.url }}</el-link>
          </el-descriptions-item>
          <el-descriptions-item label="可用节点">
            <div v-if="currentDataset.availableNodes && currentDataset.availableNodes.length">
              <el-tag 
                v-for="node in currentDataset.availableNodes" 
                :key="node.id"
                class="node-tag"
              >
                {{ node.name }}
              </el-tag>
            </div>
            <span v-else>没有节点可用</span>
          </el-descriptions-item>
          <el-descriptions-item label="示例数据" v-if="currentDataset.mockData">
            <pre class="mock-data-pre">{{ currentDataset.mockData }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Search, Folder, FolderAdd, Upload, Download, Delete, Share, Refresh, Document, Link, View } from '@element-plus/icons-vue';
import service from '~/axios';
import { toast, showModal } from '~/composables/util';

const router = useRouter();
const route = useRoute();

// 数据加载状态
const loading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 表格数据
const tableData = ref([]);
const selectedDatasets = ref([]);
const nodeOptions = ref([]);

// 搜索表单
const searchForm = reactive({
  name: '',
});

// 详情对话框
const detailDialogVisible = ref(false);
const currentDataset = ref(null);

// 加载数据集列表
const loadDatasets = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
    };
    
    if (searchForm.name) {
      params.name = searchForm.name;
    }
    
    const res = await service.get('/api/v1.0/sys/dataset', { params });
    if (res.code === 10000) {
      tableData.value = res.data.datasets || [];
      total.value = res.data.pagination?.total || 0;
    } else {
      toast('错误', res.message || '获取数据集列表失败', 'error');
    }
  } catch (error) {
    console.error('获取数据集列表失败:', error);
    toast('错误', '获取数据集列表失败', 'error');
  } finally {
    loading.value = false;
  }
};

// 加载节点列表
const loadNodes = async () => {
  try {
    const res = await service.get('/api/v1.0/sys/nodes');
    if (res.code === 10000) {
      nodeOptions.value = res.data.nodes.map(node => ({
        value: node.id,
        label: node.name
      }));
    } else {
      toast('错误', res.message || '获取节点列表失败', 'error');
    }
  } catch (error) {
    console.error('获取节点列表失败:', error);
    toast('错误', '获取节点列表失败', 'error');
  }
};

// 查看数据集详情
const handleViewDetail = async (row) => {
  try {
    const res = await service.get(`/api/v1.0/sys/dataset/${row.id}`);
    
    if (res.code === 10000) {
      currentDataset.value = res.data;
      detailDialogVisible.value = true;
    } else {
      toast('错误', res.message || '获取数据集详情失败', 'error');
    }
  } catch (error) {
    console.error('获取数据集详情失败:', error);
    toast('错误', '获取数据集详情失败', 'error');
  }
};

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedDatasets.value = selection;
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadDatasets();
};

// 重置搜索
const resetSearch = () => {
  searchForm.name = '';
  currentPage.value = 1;
  loadDatasets();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadDatasets();
};

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadDatasets();
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let index = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(2)} ${units[index]}`;
};

// 格式化日期时间
const formatDateTime = (row, column) => {
  if (!row || !row.timeCreated) return '';
  const date = new Date(row.timeCreated);
  return date.toLocaleString();
};

// 页面加载时执行
onMounted(() => {
  loadDatasets();
  loadNodes();
});
</script>

<style scoped>
.data-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
}

.list-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

.file-icon {
  margin-right: 5px;
  font-size: 18px;
}

.mock-data-pre {
  white-space: pre-wrap;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.node-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.dataset-detail {
  max-height: 60vh;
  overflow-y: auto;
}

/* 深色模式适配 */
html.dark {
  .list-card {
    background-color: var(--el-bg-color-overlay);
  }
  
  .mock-data-pre {
    background-color: var(--el-bg-color);
  }
}
</style> 