
import asyncio
from typing import Optional, Callable, Any, Set

import inspect

import websockets
from websockets.server import WebSocketServerProtocol
from websockets.exceptions import ConnectionClosed


def async_wapper(func):
    if func is None:
        return None

    if not callable(func):
        raise TypeError(f"Object {func} is not callable")

    if inspect.iscoroutinefunction(func):
        return func

    async def wrapped(*args, **kwargs):
        return func(*args, **kwargs)

    return wrapped


class WebSocketServer:
    """A production-ready WebSocket server implementation using the websockets library.

    This server implements the standard WebSocket protocol (RFC 6455) for full-duplex
    communication between clients and server. It supports:
    - Async message handling
    - Connection state tracking
    - Customizable message and event callbacks
    - Graceful shutdown handling

    Typical Usage:
        >>> async def message_handler(message: str) -> str:
        ...     print(f"Received: {message}")
        ...     return f"Processed: {message}"
        >>> server = WebSocketServer(message_callback=message_handler)
        >>> asyncio.get_event_loop().run_until_complete(server.run())

    Note:
    - Requires Python 3.7+ and the websockets package
    """

    def __init__(
        self,
        host: str = '0.0.0.0',
        port: int = 8000,
        message_callback: Optional[Callable[[str], Any]] = None,
        on_connect: Optional[Callable[[WebSocketServerProtocol], None]] = None,
        on_disconnect: Optional[Callable[[
            WebSocketServerProtocol], None]] = None,
        on_error: Optional[Callable[[Exception], None]] = None,
        max_connections: int = 100,
    ):
        """Initialize the WebSocket server with configuration and handlers.

        Args:
            host: Network interface to bind to. Use '0.0.0.0' for all available
                  interfaces or '127.0.0.1' for local-only. Default: '0.0.0.0'.
            port: TCP port to listen on. Default: 8765 (IANA-registered for WS).
            message_callback: Optional callable that processes incoming messages.
                            Should accept message (str/bytes) and return response.
                            If None, echoes received messages.
            on_connect: Optional callable triggered when new client connects.
                       Receives the WebSocketProtocol object.
            on_disconnect: Optional callable triggered when client disconnects.
                          Receives the WebSocketProtocol object.
            on_error: Optional callable for handling server-level exceptions.
            max_connections: Maximum simultaneous connections allowed.
                             Default: 100.
        """
        self.host = host
        self.port = port
        self.message_callback = async_wapper(
            message_callback) or self._default_message_handler
        self.on_connect = async_wapper(on_connect)
        self.on_disconnect = async_wapper(on_disconnect)
        self.on_error = async_wapper(on_error)
        self.max_connections = max_connections
        self.active_connections: Set[WebSocketServerProtocol] = set()
        self.server: Optional[websockets.server.Serve] = None

    async def _default_message_handler(self, message: str) -> str:
        """Default message handler that echoes received messages."""
        return f"Echo: {message}"

    async def _connection_handler(self, websocket: WebSocketServerProtocol):
        """Main handler for each WebSocket connection lifecycle."""
        if len(self.active_connections) >= self.max_connections:
            await websocket.close(code=1008, reason="Server at capacity")
            return

        self.active_connections.add(websocket)
        if self.on_connect:
            await self.on_connect(websocket)

        try:
            async for message in websocket:
                try:
                    response = await self.message_callback(message)
                    if response is not None:
                        await websocket.send(response)
                except Exception as e:
                    if self.on_error:
                        await self.on_error(e)
                    await websocket.close(code=1011, reason=str(e))
        except ConnectionClosed:
            pass
        finally:
            self.active_connections.remove(websocket)
            if self.on_disconnect:
                await self.on_disconnect(websocket)

    async def run(self):
        """Start the WebSocket server and begin accepting connections.

        This runs indefinitely until the server is stopped or encounters
        a fatal error. Typically run within an asyncio event loop.

        Raises:
            RuntimeError: If server is already running
            websockets.WebSocketException: For protocol-level errors
        """
        if self.server is not None:
            raise RuntimeError("Server is already running")

        self.server = await websockets.serve(
            self._connection_handler,
            self.host,
            self.port,
            ping_interval=20,  # Keepalive ping interval (seconds)
            ping_timeout=60,   # Timeout waiting for pong
            max_size=2**20,    # 1MB max message size
        )

        print(f"WebSocket server started on ws://{self.host}:{self.port}")
        await self.server.wait_closed()

    async def stop(self):
        """Gracefully shutdown the server and all active connections.

        Closes all client connections with status code 1001 (going away)
        before stopping the server.
        """
        if self.server:
            for connection in self.active_connections.copy():
                await connection.close(code=1001, reason="Server shutdown")
            self.server.close()
            await self.server.wait_closed()
            self.server = None

    def get_connection_count(self) -> int:
        """Return the current number of active connections.

        Returns:
            int: Count of currently connected clients
        """
        return len(self.active_connections)


class WebSocketClient:
    """A WebSocket client with callback-based message handling.

    This client provides asynchronous WebSocket communication with configurable
    callbacks for connection events and message processing.
    """

    def __init__(
        self,
        uri: str,
        message_callback: Callable[[Any], None],
        on_open: Optional[Callable[[], None]] = None,
        on_close: Optional[Callable[[], None]] = None,
        on_error: Optional[Callable[[Exception], None]] = None,
    ):
        """Initialize the WebSocket client with callback handlers.

        Args:
            uri: WebSocket server URI (e.g., 'ws://localhost:8080')
            message_callback: Callback for processing received messages
            on_open: Optional callback when connection is established
            on_close: Optional callback when connection is closed
            on_error: Optional callback for connection errors
        """
        self.uri = uri
        self.connection: Optional[websockets.WebSocketClientProtocol] = None
        self._running = False

        self.message_callback = message_callback
        self.on_open = on_open
        self.on_close = on_close
        self.on_error = on_error

    async def connect(self) -> bool:
        """Establish WebSocket connection and start message listener.

        Returns:
            bool: True if connection succeeded, False otherwise
        """
        try:
            self.connection = await websockets.connect(
                self.uri,
                ping_interval=None,
            )
            self._running = True

            if self.on_open:
                self.on_open()

            asyncio.create_task(self._listen())
            return True

        except Exception as e:
            if self.on_error:
                self.on_error(e)
            return False

    async def _listen(self):
        """Internal message listener coroutine.

        Continuously receives messages and invokes the message callback.
        """
        while self._running:
            try:
                message = await self.connection.recv()
                self.message_callback(message)

            except ConnectionClosed:
                self._running = False
                if self.on_close:
                    self.on_close()
                break

            except Exception as e:
                self._running = False
                if self.on_error:
                    self.on_error(e)
                break

    async def send(self, message: Any) -> bool:
        """Send a message through the WebSocket connection.

        Args:
            message: Data to send (str or bytes)

        Returns:
            bool: True if message was sent successfully, False otherwise
        """
        if not self._running or not self.connection:
            if self.on_error:
                self.on_error(Exception("Connection not established"))
            return False

        try:
            await self.connection.send(message)
            return True
        except Exception as e:
            self._running = False
            if self.on_error:
                self.on_error(e)
            return False

    async def close(self):
        """Close the WebSocket connection."""
        if self._running and self.connection:
            self._running = False
            await self.connection.close()

    def is_connected(self) -> bool:
        """Check if the connection is currently active.

        Returns:
            bool: True if connected, False otherwise
        """
        return self._running and self.connection and self.connection.open
