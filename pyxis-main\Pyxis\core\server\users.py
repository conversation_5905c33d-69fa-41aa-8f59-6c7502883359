from typing import List
import uuid
import os

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from passlib.context import CryptContext
from datetime import datetime, timedelta, timezone
from jose import JWTError, jwt
from pydantic import BaseModel

from ..stash.models import SessionLocal, User

users = APIRouter(prefix="/users")

SECRET_KEY = os.getenv("SECRET_KEY", "pyxis-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="users/token")


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    user_id: str = None
    role: str = None


class UserCreate(BaseModel):
    name: str
    password: str
    role: str  # 'admin', 'user', 'auditor'


class UserResponse(BaseModel):
    user_id: str
    name: str
    role: str


class UserUpdate(BaseModel):
    name: str = None
    password: str = None
    role: str = None  # 'admin', 'user', 'auditor'


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("user_id")
        if user_id is None:
            raise credentials_exception
        token_data = TokenData(user_id=user_id, role=payload.get("role"))
    except JWTError:
        raise credentials_exception

    user = db.query(User).filter(User.user_id == token_data.user_id).first()
    if user is None:
        raise credentials_exception
    return user


def require_role(role: str):
    async def role_checker(user: User = Depends(get_current_user)):
        if user.role != role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Require {role} role"
            )
        return user
    return role_checker


@users.post("/", response_model=UserResponse)
async def create_user(user: UserCreate, db: Session = Depends(get_db),
                      current_user: User = Depends(require_role("admin"))):
    db_user = db.query(User).filter(User.name == user.name).first()
    if db_user:
        raise HTTPException(
            status_code=400, detail="Username already registered")

    user_id = str(uuid.uuid4())
    hashed_password = get_password_hash(user.password)
    db_user = User(
        user_id=user_id,
        name=user.name,
        password=hashed_password,
        role=user.role
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


@users.post("/token")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(),
                                 db: Session = Depends(get_db)):
    user = db.query(User).filter(User.name == form_data.username).first()
    if not user or not verify_password(form_data.password, user.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.name, "user_id": user.user_id, "role": user.role},
        expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer", "role": user.role, "name": user.name}


@users.get("/me", response_model=UserResponse)
async def read_users_me(current_user: User = Depends(get_current_user)):
    return current_user


@users.get("/", response_model=List[UserResponse])
async def get_all_users(db: Session = Depends(get_db),
                        current_user: User = Depends(require_role("admin"))):
    users = db.query(User).all()
    return users


@users.delete("/{user_id}", response_model=UserResponse)
async def delete_user(user_id: str, db: Session = Depends(get_db),
                      current_user: User = Depends(require_role("admin"))):
    db_user = db.query(User).filter(User.user_id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    if db_user.user_id == current_user.user_id:
        raise HTTPException(status_code=400, detail="Cannot delete yourself")

    db.delete(db_user)
    db.commit()
    return db_user


@users.put("/{user_id}", response_model=UserResponse)
async def update_user(user_id: str, user_update: UserUpdate, db: Session = Depends(get_db),
                      current_user: User = Depends(require_role("admin"))):
    db_user = db.query(User).filter(User.user_id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    if user_update.name is not None:
        existing_user = db.query(User).filter(
            User.name == user_update.name,
            User.user_id != user_id
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=400, detail="Username already taken")
        db_user.name = user_update.name

    if user_update.password is not None:
        db_user.password = get_password_hash(user_update.password)

    if user_update.role is not None:
        db_user.role = user_update.role

    db.commit()
    db.refresh(db_user)
    return db_user
