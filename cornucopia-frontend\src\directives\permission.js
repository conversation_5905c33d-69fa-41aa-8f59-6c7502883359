import store from '~/store'

// 这个函数检查元素是否应该显示
function checkPermission(el, binding) {
    const { value } = binding  // 获取指令的值
    const permissions = Array.isArray(value) ? value : [value]
    const hasPermission = store.getters.hasAnyPermission(permissions)

    if (!hasPermission) {
        el.parentNode?.removeChild(el)  // 如果没有权限，移除元素
    }
}

// 导出指令配置对象
export default {
    // 当指令绑定的元素被挂载到 DOM 中时调用
    mounted(el, binding) {
        checkPermission(el, binding)
    },
    // 当指令的值更新时调用
    updated(el, binding) {
        checkPermission(el, binding)
    }
} 