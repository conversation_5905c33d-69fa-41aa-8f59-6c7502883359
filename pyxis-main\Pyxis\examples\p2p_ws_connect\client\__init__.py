from framework.peer import Peer
from framework.ws import WebSocketClient
import asyncio

def handle_message(message):
    print(f"Client received: {message}")
    
def on_open():
    print("Connected to server")

def on_close():
    print("Disconnected from server")

def on_error(e):
    print(f"Error occurred: {e}")

async def ws_connect(target_url):
    # Create the client
    client = WebSocketClient(
        uri=target_url,
        message_callback=handle_message,
        on_open=on_open,
        on_close=on_close,
        on_error=on_error
    )

    # Connect to the server
    if await client.connect():
        print("Connection established")
        
        try:
            # Send some messages
            for i in range(5):
                message = f"Hello, server! Message #{i+1}"
                print(f"Sending: {message}")
                await client.send(message)
                await asyncio.sleep(1)
                
        finally:
            # Close the connection when done
            await client.close()
    else:
        print("Failed to connect to server")
        

def main():
    peer = Peer()
    uri = peer.get_target_url("test_p2p").get("access_url")
    print(f"uri:{uri}")
    uri = "ws://"+uri
    asyncio.run(ws_connect(uri))