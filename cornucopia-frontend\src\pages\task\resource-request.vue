<template>
  <div class="resource-request-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Connection /></el-icon>
          <h2>资源申请中心</h2>
        </div>
        <div class="sub-title">为计算任务申请节点和数据集资源</div>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 节点资源申请 -->
      <el-col :span="12">
        <el-card class="resource-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>节点资源</span>
              <el-button type="primary" size="small" @click="loadNodeAccounts" plain round>
                <el-icon><Refresh /></el-icon>刷新
              </el-button>
            </div>
          </template>
          
          <el-table
            v-loading="loadingNodes"
            :data="nodeOptions"
            style="width: 100%"
            border
            stripe
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          >
            <el-table-column prop="label" label="节点名称" min-width="180" show-overflow-tooltip /> 
            <el-table-column label="节点类型" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.data.nodeType === 'Sycee' ? 'success' : 'primary'">
                  {{ scope.row.data.nodeType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag v-if="hasNodeAccount(scope.row.value)" type="success">已有账号</el-tag>
                <el-tag v-else-if="getNodeAccountStatus(scope.row.value) === 'PENDING'" type="warning">申请中</el-tag>
                <el-tag v-else-if="getNodeAccountStatus(scope.row.value) === 'REJECTED'" type="danger">已拒绝</el-tag>
                <el-tag v-else type="info">未申请</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button 
                  v-if="!hasNodeAccount(scope.row.value) && getNodeAccountStatus(scope.row.value) !== 'PENDING'"
                  type="primary" 
                  size="small" 
                  @click="goToNodeRequest(scope.row.value)"
                >
                  申请
                </el-button>
                <el-button 
                  v-else-if="getNodeAccountStatus(scope.row.value) === 'REJECTED'"
                  type="primary" 
                  size="small" 
                  @click="goToNodeRequest(scope.row.value)"
                >
                  重新申请
                </el-button>
                <el-button 
                  v-else
                  type="info" 
                  size="small" 
                  @click="viewNodeAccountDetail(scope.row.value)"
                >
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <!-- 数据集资源申请 -->
      <el-col :span="12">
        <el-card class="resource-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>数据集资源</span>
              <div>
                <el-select 
                  v-model="selectedNodeForDataset" 
                  placeholder="选择节点查看数据集权限" 
                  style="width: 220px; margin-right: 10px"
                  :disabled="!hasAnyNodeAccount"
                >
                  <el-option
                    v-for="node in nodeAccountsOptions"
                    :key="node.value"
                    :label="node.label"
                    :value="node.value"
                  />
                </el-select>
                <el-button type="primary" size="small" @click="loadDatasetPermissions" plain round>
                  <el-icon><Refresh /></el-icon>刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <div v-if="!selectedNodeForDataset" class="empty-state">
            <el-empty description="请先选择一个已有账号的节点" />
          </div>
          
          <el-table
            v-else
            v-loading="loadingDatasets"
            :data="datasetOptions"
            style="width: 100%"
            border
            stripe
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          >
            <el-table-column prop="label" label="数据集名称" min-width="180" show-overflow-tooltip />
            <el-table-column label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag v-if="canUseDataset(scope.row.value, selectedNodeForDataset)" type="success">可用</el-tag>
                <el-tag v-else-if="getDatasetPermissionStatus(scope.row.value, selectedNodeForDataset) === 'PENDING'" type="warning">申请中</el-tag>
                <el-tag v-else-if="getDatasetPermissionStatus(scope.row.value, selectedNodeForDataset) === 'REJECTED'" type="danger">已拒绝</el-tag>
                <el-tag v-else type="info">未申请</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button 
                  v-if="!canUseDataset(scope.row.value, selectedNodeForDataset) && 
                        getDatasetPermissionStatus(scope.row.value, selectedNodeForDataset) !== 'PENDING'"
                  type="primary" 
                  size="small" 
                  @click="goToDatasetRequest(scope.row.value, selectedNodeForDataset)"
                >
                  申请
                </el-button>
                <el-button 
                  v-else-if="getDatasetPermissionStatus(scope.row.value, selectedNodeForDataset) === 'REJECTED'"
                  type="primary" 
                  size="small" 
                  @click="goToDatasetRequest(scope.row.value, selectedNodeForDataset)"
                >
                  重新申请
                </el-button>
                <el-button 
                  v-else
                  type="info" 
                  size="small" 
                  @click="viewDatasetPermissionDetail(scope.row.value, selectedNodeForDataset)"
                >
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 节点账号详情对话框 -->
    <el-dialog
      v-model="nodeAccountDialogVisible"
      title="节点账号详情"
      width="600px"
      destroy-on-close
    >
      <div v-if="currentNodeAccount" class="account-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="节点">{{ getNodeName(currentNodeAccount.node?.id) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentNodeAccount.status)">
              {{ getStatusText(currentNodeAccount.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请理由">{{ currentNodeAccount.reason }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime(currentNodeAccount.timeCreated) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间" v-if="currentNodeAccount.timeUpdated">
            {{ formatDateTime(currentNodeAccount.timeUpdated) }}
          </el-descriptions-item>
          <el-descriptions-item label="拒绝理由" v-if="currentNodeAccount.rejectReason">
            {{ currentNodeAccount.rejectReason }}
          </el-descriptions-item>
          <el-descriptions-item label="节点用户名" v-if="currentNodeAccount.nodeUsername">
            {{ currentNodeAccount.nodeUsername }}
          </el-descriptions-item>
          <el-descriptions-item label="节点密码" v-if="currentNodeAccount.nodePassword">
            {{ currentNodeAccount.nodePassword }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    
    <!-- 数据集权限详情对话框 -->
    <el-dialog
      v-model="datasetPermissionDialogVisible"
      title="数据集权限详情"
      width="600px"
      destroy-on-close
    >
      <div v-if="currentDatasetPermission" class="permission-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="数据集">{{ getDatasetName(currentDatasetPermission.dataset?.id) }}</el-descriptions-item>
          <el-descriptions-item label="节点">{{ getNodeName(currentDatasetPermission.nodeId) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentDatasetPermission.status)">
              {{ getStatusText(currentDatasetPermission.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="使用目的">{{ currentDatasetPermission.purpose }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime(currentDatasetPermission.timeCreated) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间" v-if="currentDatasetPermission.timeUpdated">
            {{ formatDateTime(currentDatasetPermission.timeUpdated) }}
          </el-descriptions-item>
          <el-descriptions-item label="拒绝理由" v-if="currentDatasetPermission.rejectReason">
            {{ currentDatasetPermission.rejectReason }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Connection, Refresh, View } from '@element-plus/icons-vue';
import service from '~/axios';
import { toast } from '~/composables/util';

const router = useRouter();

// 状态变量
const loadingNodes = ref(false);
const loadingDatasets = ref(false);
const nodeAccountDialogVisible = ref(false);
const datasetPermissionDialogVisible = ref(false);
const currentNodeAccount = ref(null);
const currentDatasetPermission = ref(null);
const selectedNodeForDataset = ref(null);

// 数据列表
const nodeOptions = ref([]);
const datasetOptions = ref([]);
const nodeAccounts = ref([]); // 用户已有的节点账号
const datasetPermissions = ref([]); // 用户已有的数据集使用权限

// 计算属性
const nodeAccountsOptions = computed(() => {
  return nodeOptions.value.filter(node => 
    nodeAccounts.value.some(account => 
      account.node?.id === node.value && account.status === 'APPROVED'
    )
  );
});

const hasAnyNodeAccount = computed(() => {
  return nodeAccountsOptions.value.length > 0;
});

// 方法
const loadNodes = async () => {
  loadingNodes.value = true;
  try {
    const res = await service.get('/api/v1.0/sys/nodes');
    if (res.code === 10000) {
      nodeOptions.value = res.data.nodes.map(node => ({
        value: node.id,
        label: `${node.name} (${node.ipAddress}:${node.port})`,
        data: node
      }));
    } else {
      toast('错误', res.message || '获取节点列表失败', 'error');
    }
  } catch (error) {
    console.error('获取节点列表失败:', error);
    toast('错误', '获取节点列表失败', 'error');
  } finally {
    loadingNodes.value = false;
  }
};

const loadDatasets = async (nodeId) => {
  if (!nodeId) {
    datasetOptions.value = [];
    return;
  }
  
  loadingDatasets.value = true;
  try {
    const res = await service.get(`/api/v1.0/sys/node/${nodeId}/datasets`, { params: { size: 1000 } });
    if (res.code === 10000) {
      datasetOptions.value = res.data.datasets.map(dataset => ({
        value: dataset.id,
        label: dataset.name,
        data: dataset
      }));
    } else {
      toast('错误', res.message || '获取数据集列表失败', 'error');
    }
  } catch (error) {
    console.error('获取数据集列表失败:', error);
    toast('错误', '获取数据集列表失败', 'error');
  } finally {
    loadingDatasets.value = false;
  }
};

const loadNodeAccounts = async () => {
  loadingNodes.value = true;
  try {
    const res = await service.get('/api/v1.0/sys/node/account/requests', { 
      params: { size: 1000 } 
    });
    if (res.code === 10000) {
      nodeAccounts.value = res.data.requests || [];
      
      // 如果当前选择的节点不在已有账号列表中，重置选择
      if (selectedNodeForDataset.value && !hasNodeAccount(selectedNodeForDataset.value)) {
        selectedNodeForDataset.value = null;
      }
      
      // 如果有已批准的节点账号但未选择节点，自动选择第一个
      if (!selectedNodeForDataset.value && nodeAccountsOptions.value.length > 0) {
        selectedNodeForDataset.value = nodeAccountsOptions.value[0].value;
      }
    } else {
      toast('错误', res.message || '获取节点账号列表失败', 'error');
    }
  } catch (error) {
    console.error('获取节点账号列表失败:', error);
    toast('错误', '获取节点账号列表失败', 'error');
  } finally {
    loadingNodes.value = false;
  }
};

const loadDatasetPermissions = async () => {
  loadingDatasets.value = true;
  try {
    const res = await service.get('/api/v1.0/sys/dataset/usage/requests', { 
      params: { size: 1000 } 
    });
    if (res.code === 10000) {
      datasetPermissions.value = res.data.requests || [];
    } else {
      toast('错误', res.message || '获取数据集权限列表失败', 'error');
    }
  } catch (error) {
    console.error('获取数据集权限列表失败:', error);
    toast('错误', '获取数据集权限列表失败', 'error');
  } finally {
    loadingDatasets.value = false;
  }
};

const hasNodeAccount = (nodeId) => {
  return nodeAccounts.value.some(account => account.node?.id === nodeId && account.status === 'APPROVED');
};

const canUseDataset = (datasetId, nodeId) => {
  return datasetPermissions.value.some(
    permission => permission.dataset?.id === datasetId && permission.nodeId === nodeId && permission.status === 'APPROVED'
  );
};

const getNodeName = (nodeId) => {
  const node = nodeOptions.value.find(n => n.value === nodeId);
  return node ? node.label : '未知节点';
};

const getDatasetName = (datasetId) => {
  const dataset = datasetOptions.value.find(d => d.value === datasetId);
  return dataset ? dataset.label : '未知数据集';
};

const goToNodeRequest = (nodeId) => {
  router.push({
    path: '/task/node/request',
    query: { nodeId, redirect: '/task/resource/request' }
  });
};

const goToDatasetRequest = (datasetId, nodeId) => {
  router.push({
    path: '/task/dataset/request',
    query: { datasetId, nodeId, redirect: '/task/resource/request' }
  });
};

// 添加获取节点账号状态的函数
const getNodeAccountStatus = (nodeId) => {
  const account = nodeAccounts.value.find(acc => acc.node?.id === nodeId);
  return account ? account.status : null;
};

// 添加获取数据集权限状态的函数
const getDatasetPermissionStatus = (datasetId, nodeId) => {
  const permission = datasetPermissions.value.find(
    perm => perm.dataset?.id === datasetId && perm.nodeId === nodeId
  );
  return permission ? permission.status : null;
};

// 添加获取状态类型的函数
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger'
  };
  return statusMap[status] || 'info';
};

// 添加获取状态文本的函数
const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已拒绝'
  };
  return statusMap[status] || status;
};

// 查看节点账号详情
const viewNodeAccountDetail = (nodeId) => {
  const account = nodeAccounts.value.find(acc => acc.node?.id === nodeId);
  if (account) {
    currentNodeAccount.value = account;
    nodeAccountDialogVisible.value = true;
  }
};

// 查看数据集权限详情
const viewDatasetPermissionDetail = (datasetId, nodeId) => {
  const permission = datasetPermissions.value.find(
    perm => perm.dataset?.id === datasetId && perm.nodeId === nodeId
  );
  if (permission) {
    currentDatasetPermission.value = permission;
    datasetPermissionDialogVisible.value = true;
  }
};

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
};

// 添加 watch 监听选择的节点变化
watch(selectedNodeForDataset, (newNodeId) => {
  if (newNodeId) {
    loadDatasets(newNodeId);
  } else {
    datasetOptions.value = [];
  }
});

onMounted(async () => {
  // 并行加载数据
  await Promise.all([
    loadNodes(),
    loadNodeAccounts(),
    loadDatasetPermissions()
  ]);
  // 不需要在这里加载数据集，因为会在选择节点后自动加载
});
</script>

<style scoped>
.resource-request-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.resource-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  padding: 40px 0;
}

.account-detail, .permission-detail {
  max-height: 60vh;
  overflow-y: auto;
}

/* 深色模式适配 */
html.dark {
  .resource-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style> 