<template>
  <div class="node-request-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Connection /></el-icon>
          <h2>申请节点账号</h2>
        </div>
        <div class="sub-title">申请使用计算节点的账号</div>
      </div>
    </div>

    <el-card class="form-card" shadow="hover">
      <el-form :model="requestForm" :rules="rules" ref="requestFormRef" label-width="120px" class="request-form">
        <el-form-item label="节点" prop="nodeId">
          <el-select 
            v-model="requestForm.nodeId" 
            placeholder="请选择节点" 
            filterable 
            style="width: 100%"
            :disabled="!!preSelectedNodeId"
          >
            <el-option
              v-for="node in nodeOptions"
              :key="node.value"
              :label="node.label"
              :value="node.value"
            >
              <div class="node-option">
                <span>{{ node.label }}</span>
                <el-tag v-if="hasNodeAccount(node.value)" type="success" size="small">已有账号</el-tag>
                <el-tag v-else-if="hasPendingRequest(node.value)" type="warning" size="small">申请中</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="申请理由" prop="reason">
          <el-input
            v-model="requestForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入申请理由"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitRequest" :loading="submitting" round>
            <el-icon><Check /></el-icon>提交申请
          </el-button>
          <el-button @click="resetForm" round>
            <el-icon><Refresh /></el-icon>重置
          </el-button>
          <el-button @click="goBack" round>
            <el-icon><Back /></el-icon>返回
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="list-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>我的申请记录</span>
          <el-button type="primary" @click="loadRequests" size="small" plain round>
            <el-icon><Refresh /></el-icon>刷新
          </el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
        highlight-current-row
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column label="节点" min-width="150" align="center">
          <template #default="scope">
            <div>
              <el-tag :type="scope.row.node?.nodeType === 'Sycee' ? 'success' : 'primary'" style="margin-right: 4px">
                {{ scope.row.node?.nodeType }}
              </el-tag>
              <el-tag>{{ scope.row.node?.name || '未知节点' }} ({{ scope.row.node?.ipAddress }}:{{ scope.row.node?.port }})</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="申请理由" min-width="200" show-overflow-tooltip align="center" />
        <el-table-column label="状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          prop="timeCreated" 
          label="申请时间" 
          width="180" 
          align="center"
          sortable
          :formatter="formatDateTime"
        />
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="scope">
            <el-tooltip content="查看详情" placement="top">
              <el-button type="info" size="small" @click="viewRequestDetail(scope.row)">
                <el-icon><View /></el-icon>
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 申请详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="申请详情"
      width="700px"
      destroy-on-close
    >
      <div v-if="currentRequest" class="request-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="申请ID">{{ currentRequest.id }}</el-descriptions-item>
          <el-descriptions-item label="节点">
            {{ currentRequest.node?.name || '未知节点' }} ({{ currentRequest.node?.ipAddress }}:{{ currentRequest.node?.port }})
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentRequest.status)">
              {{ getStatusText(currentRequest.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请理由">{{ currentRequest.reason }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime({timeCreated: currentRequest.timeCreated}) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间" v-if="currentRequest.timeUpdated">
            {{ formatDateTime({timeUpdated: currentRequest.timeUpdated}) }}
          </el-descriptions-item>
          <el-descriptions-item label="拒绝理由" v-if="currentRequest.rejectReason">
            {{ currentRequest.rejectReason }}
          </el-descriptions-item>
          <el-descriptions-item label="节点用户名" v-if="currentRequest.nodeUsername">
            {{ currentRequest.nodeUsername }}
          </el-descriptions-item>
          <el-descriptions-item label="节点密码" v-if="currentRequest.nodePassword">
            {{ currentRequest.nodePassword }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Connection, Check, Refresh, Back, View } from '@element-plus/icons-vue';
import service from '~/axios';
import { toast } from '~/composables/util';

const router = useRouter();
const route = useRoute();
const requestFormRef = ref(null);

// 预选节点ID（从URL参数获取）
const preSelectedNodeId = computed(() => {
  const nodeId = route.query.nodeId;
  return nodeId ? Number(nodeId) : null;
});

// 重定向路径
const redirectPath = computed(() => {
  return route.query.redirect || '/task/resource/request';
});

// 表单数据
const requestForm = reactive({
  nodeId: null,
  reason: ''
});

// 表单验证规则
const rules = {
  nodeId: [{ required: true, message: '请选择节点', trigger: 'change' }],
  reason: [{ required: false, message: '请输入申请理由', trigger: 'blur' }]
};

// 状态变量
const submitting = ref(false);
const loading = ref(false);
const detailDialogVisible = ref(false);
const currentRequest = ref(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 数据列表
const nodeOptions = ref([]);
const tableData = ref([]);
const allRequests = ref([]); // 所有申请记录，用于检查是否已有申请

// 方法
const loadNodes = async () => {
  try {
    const res = await service.get('/api/v1.0/sys/nodes');
    if (res.code === 10000) {
      nodeOptions.value = res.data.nodes.map(node => ({
        value: node.id,
        label: `${node.name} (${node.ipAddress}:${node.port})`,
        data: node
      }));
      
      // 如果有预选节点，设置表单值
      if (preSelectedNodeId.value && !requestForm.nodeId) {
        requestForm.nodeId = preSelectedNodeId.value;
      }
    } else {
      toast('错误', res.message || '获取节点列表失败', 'error');
    }
  } catch (error) {
    console.error('获取节点列表失败:', error);
    toast('错误', '获取节点列表失败', 'error');
  }
};

const loadRequests = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value
    };
    
    const res = await service.get('/api/v1.0/sys/node/account/requests', { params });
    if (res.code === 10000) {
      tableData.value = res.data.requests || [];
      total.value = res.data.pagination?.total || 0;
      
      // 加载所有申请记录（不分页）
      const allRes = await service.get('/api/v1.0/sys/node/account/requests', { params: { size: 1000 } });
      if (allRes.code === 10000) {
        allRequests.value = allRes.data.requests || [];
      }
    } else {
      toast('错误', res.message || '获取申请记录失败', 'error');
    }
  } catch (error) {
    console.error('获取申请记录失败:', error);
    toast('错误', '获取申请记录失败', 'error');
  } finally {
    loading.value = false;
  }
};

const hasNodeAccount = (nodeId) => {
  return allRequests.value.some(req => req.node.id === nodeId && req.status === 'APPROVED');
};

const hasPendingRequest = (nodeId) => {
  return allRequests.value.some(req => req.node.id === nodeId && req.status === 'PENDING');
};

const submitRequest = async () => {
  if (!requestFormRef.value) return;
  
  await requestFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    // 检查是否已有账号
    if (hasNodeAccount(requestForm.nodeId)) {
      toast('提示', '您已经有该节点的账号，无需重复申请', 'info');
      return;
    }
    
    // 检查是否有待审批的申请
    if (hasPendingRequest(requestForm.nodeId)) {
      toast('提示', '您已经提交过该节点的账号申请，请等待审批', 'info');
      return;
    }
    
    submitting.value = true;
    try {
      const res = await service.post('/api/v1.0/sys/node/account/request', {
        nodeId: requestForm.nodeId,
        reason: requestForm.reason
      });
      
      if (res.code === 10000) {
        toast('成功', '节点账号申请已提交，请等待审批', 'success');
        resetForm();
        loadRequests(); // 刷新列表
      } else {
        toast('错误', res.message || '提交申请失败', 'error');
      }
    } catch (error) {
      console.error('提交申请失败:', error);
      toast('错误', error.response?.data?.message || '提交申请失败', 'error');
    } finally {
      submitting.value = false;
    }
  });
};

const viewRequestDetail = (row) => {
  currentRequest.value = row;
  detailDialogVisible.value = true;
};

const resetForm = () => {
  if (requestFormRef.value) {
    requestFormRef.value.resetFields();
  }
};

const goBack = () => {
  router.push(redirectPath.value);
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadRequests();
};

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadRequests();
};

// 格式化日期时间
const formatDateTime = (row, column) => {
  const dateStr = row.timeCreated || row.timeUpdated;
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
};

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已拒绝'
  };
  return statusMap[status] || status;
};

onMounted(async () => {
  await loadNodes();
  loadRequests();
});
</script>

<style scoped>
.node-request-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.form-card {
  margin-bottom: 20px;
}

.request-form {
  max-width: 800px;
  margin: 20px auto;
}

.list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.node-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

.request-detail {
  max-height: 60vh;
  overflow-y: auto;
}

/* 深色模式适配 */
html.dark {
  .form-card, .list-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style>