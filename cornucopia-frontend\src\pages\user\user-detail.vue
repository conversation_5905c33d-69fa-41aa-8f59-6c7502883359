<template>
  <div class="user-detail-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><User /></el-icon>
          <h2>用户详情</h2>
        </div>
        <div class="sub-title">查看用户的详细信息</div>
      </div>
      <div class="header-right">
        <el-button 
          @click="$router.push('/user/list')"
          plain
          round
        >
          <el-icon><Back /></el-icon>
          返回用户列表
        </el-button>
      </div>
    </div>

    <div class="main-content">
      <el-empty 
        v-if="!hasUserId" 
        description="未找到用户ID" 
        :image-size="200"
      >
        <el-button type="primary" @click="$router.push('/user/list')" round>
          返回用户列表
        </el-button>
      </el-empty>

      <div v-else>
        <div class="user-info" v-if="userData">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户名">
              {{ userData.username }}
            </el-descriptions-item>
            <el-descriptions-item label="姓名">
              {{ userData.fullname || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ userData.email || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag 
                :type="userData.enable ? 'success' : 'danger'"
                effect="dark"
              >
                {{ userData.enable ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ userData.timeCreated }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ userData.timeUpdated }}
            </el-descriptions-item>
            <el-descriptions-item label="角色" :span="2">
              <template v-if="userData.roles && userData.roles.length">
                <el-tag 
                  v-for="role in userData.roles" 
                  :key="role.id"
                  class="role-tag"
                  type="primary"
                  effect="plain"
                  @click="handleRoleClick(role)"
                  style="cursor: pointer"
                >
                  {{ role.name }}
                </el-tag>
              </template>
              <span v-else>暂无角色</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { User, Back } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const route = useRoute()
const router = useRouter()
const pageLoading = ref(false)
const hasUserId = ref(false)
const userData = ref(null)

const getUserDetail = async () => {
  try {
    pageLoading.value = true
    const userId = route.query.id
    const res = await service.get(`/api/v1.0/sys/user/${userId}`)
    
    if (res.code === 10000) {
      userData.value = res.data
    } else {
      throw new Error(res.message || '获取用户详情失败')
    }
  } catch (error) {
    toast('错误', error.message, 'error')
  } finally {
    pageLoading.value = false
  }
}

const handleRoleClick = (role) => {
  router.push({
    path: '/user/role/detail',
    query: { id: role.id }
  })
}

onMounted(() => {
  if (route.query.id) {
    hasUserId.value = true
    getUserDetail()
  } else {
    hasUserId.value = false
    toast('错误', '未找到用户ID', 'error')
  }
})
</script>

<style scoped>
.user-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

:deep(.el-empty) {
  padding: 40px 0;
}

.user-info {
  margin: 20px auto;
  width: 100%;
  max-width: 800px;
}

.role-tag {
  margin-right: 8px;
  margin-bottom: 4px;
  transition: all 0.3s;
}

.role-tag:hover {
  transform: translateY(-1px);
  opacity: 0.8;
}
</style>

