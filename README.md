# MinIO模型部署应用

这个应用程序从MinIO服务器读取预训练的食物分类模型，并使用pyxis框架部署为Web服务。

## 功能特点

- 从MinIO加载预训练PyTorch模型
- 通过Web界面上传图片进行食物分类
- 展示分类结果和各类别概率
- 30分钟自动关闭服务

## 依赖项

```
torch
torchvision
PIL (Pillow)
minio
```

## 环境配置

1. 确保安装了必要的依赖项：
   ```bash
   pip install torch torchvision pillow minio
   ```

2. 确保MinIO服务器可访问，且包含预训练模型文件

## 运行应用

直接运行python脚本：

```bash
python minio_model_deploy.py
```

默认情况下，应用将在8000端口启动，您可以在浏览器中访问：
```
http://localhost:8000
```

## 自定义配置

如需修改配置，可以编辑`minio_model_deploy.py`文件：

- MinIO连接参数（服务器地址、访问凭证）
- 模型存储位置（桶名、文件名）
- 超时时间设置
- 监听端口

## 应用架构

- `FoodCNN`: 食物分类CNN模型定义
- `FoodClassifier`: 模型加载和预测类
- `get_model()`: 返回模型实例的函数
- `main()`: 设置和启动pyxis应用

## 使用说明

1. 启动应用后，打开浏览器访问服务
2. 上传食物图片（支持常见图片格式）
3. 点击提交，查看分类结果
4. 结果页面将显示预测类别、置信度和所有类别的概率表

## 注意事项

- 由于使用了pyxis框架，服务将在30分钟后自动关闭
- 应用需要连接到指定的MinIO服务器获取模型
- 模型仅支持10种食物类别的分类 