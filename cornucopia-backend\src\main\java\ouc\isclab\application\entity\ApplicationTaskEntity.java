package ouc.isclab.application.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.model.entity.ModelEntity;

/**
 * 应用部署任务实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_APPLICATION_TASK")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class ApplicationTaskEntity extends BaseEntity {

    @Column(nullable = false)
    private String name; // 任务名称
    
    @Lob
    @Column(columnDefinition = "text")
    private String description; // 任务描述
    
    @Column(name = "creator_id", nullable = false)
    private Long creatorId; // 创建者ID

    @Column(name = "creator_name", length = 100)
    private String creatorName; // 创建者名称

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "model_id", nullable = true)
    private ModelEntity model; // 关联的模型

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "node_id", nullable = false)
    private NodeEntity node; // 部署节点

    @Column(nullable = true)
    private String modelPath; // 模型文件路径

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DeployStatus status; // 部署状态

    @Column
    private String message; // 审批消息

    @Column(name = "approver_id")
    private Long approverId; // 审批人ID

    @Column(name = "task_id")
    private String taskId; // 部署任务ID

    @Column(name = "saved_files", columnDefinition = "text")
    private String savedFiles; // 保存的文件列表，以JSON字符串形式存储

    @Column
    private Boolean executed = false; // 标记任务是否已执行过

    public enum DeployStatus {
        PENDING,    // 待审批
        APPROVED,   // 已批准
        REJECTED,   // 已拒绝
        DEPLOYING,  // 部署中
        COMPLETED,  // 已完成
        FAILED      // 失败
    }
} 