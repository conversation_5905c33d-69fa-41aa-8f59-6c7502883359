
from .scrip_slim import create_app, run_app, TemplateLoader
from pathlib import Path

import pandas as pd
from prophet import Prophet
import os
from io import String<PERSON>
from matplotlib import pyplot as plt
import matplotlib
matplotlib.use('Agg')


class ProphetModelManager:
    def __init__(self, default_model_path='./models/prophet_model.json'):
        self.default_model_path = os.path.normpath(default_model_path)
        self.default_model_path = os.path.abspath(self.default_model_path)
        os.makedirs(os.path.dirname(self.default_model_path), exist_ok=True)

    def _prepare_data(self, df):
        df = df.rename(columns={'Month': 'ds', 'Passengers': 'y'})
        df['ds'] = pd.to_datetime(df['ds']).dt.tz_localize(None)
        return df

    def get_model(self, train_csv_path='./models/airline-passengers.csv', **model_params):

        if train_csv_path is None:
            raise ValueError("no csv to train")

        return self._train(train_csv_path, **model_params)

    def _train(self, train_csv_path, **model_params):
        print(
            f"start training...")

        df = pd.read_csv(train_csv_path)
        df = df.rename(columns={'Month': 'ds', 'Passengers': 'y'})

        model = Prophet(
            seasonality_mode=model_params.get(
                'seasonality_mode', 'multiplicative'),
            yearly_seasonality=model_params.get('yearly_seasonity', True),
            daily_seasonality=model_params.get('daily_seasonality', False),
            weekly_seasonality=model_params.get('weekly_seasonality', False)
        )

        model.fit(df)
        return model


def make_predictions(model, predict_csv_path, periods=12):

    df = pd.read_csv(predict_csv_path)
    df = df.rename(columns={'Month': 'ds', 'Passengers': 'y'})

    future = model.make_future_dataframe(periods=periods, freq='ME')

    forecast = model.predict(future)

    return df, forecast


def generate_forecast_svg(history_df, forecast_df):

    fig, ax = plt.subplots(figsize=(12, 6))

    history_dates = pd.to_datetime(history_df['ds']).dt.tz_localize(None)
    forecast_dates = pd.to_datetime(forecast_df['ds']).dt.tz_localize(None)

    ax.plot(history_dates, history_df['y'],
            'o', color='#1f77b4', markersize=5, alpha=0.7, label='Actual Data')

    ax.plot(forecast_dates, forecast_df['yhat'],
            color='#ff7f0e', linewidth=2, label='Forecast')

    ax.fill_between(forecast_dates,
                    forecast_df['yhat_lower'], forecast_df['yhat_upper'],
                    color='gray', alpha=0.2, label='Confidence Interval')

    ax.set_title('AirPassengers Forecast\n', fontsize=14, pad=20)
    ax.set_xlabel('Date', fontsize=12, labelpad=10)
    ax.set_ylabel('Passengers', fontsize=12, labelpad=10)
    ax.grid(True, linestyle=':', alpha=0.6)
    ax.legend(loc='upper left')

    svg_buffer = StringIO()
    fig.savefig(svg_buffer, format='svg', bbox_inches='tight', dpi=100)
    plt.close(fig)
    svg_str = svg_buffer.getvalue()
    svg_str = svg_str.replace(
        '<svg ', '<svg style="width:100%;height:auto;" ', 1)
    return svg_str


def get_model():
    class Model:
        def __init__(self):
            self.model = ProphetModelManager().get_model()

        def pipe(self, history):
            svg = generate_forecast_svg(*make_predictions(self.model, history))
            return {"forecast": svg}

    return Model()


def main(port=8000):

    base_path = Path(__file__).parent

    app = create_app(
        get_model=get_model,
        input_template_config={
            "title": "Time Series",
            "fields": [{
                "label": "History csv",
                "type": "file",
                "name": "history",
                "required":True,
                "description": "upload csv file"
            }]
        },
        output_template_config={
            "fields": [{
                "type": "svg",
                "name": "forecast",
                "description": "the forecast svg"
            }]
        },
        timeout_minutes=120,
        error_traceback=True
    )
    run_app(app, port)
