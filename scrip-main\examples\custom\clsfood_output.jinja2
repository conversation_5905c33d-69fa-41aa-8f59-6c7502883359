<style>
    /* Table styles */
    .results table {
        width: 100%;
        border-collapse: collapse;
        margin: var(--space-sm) 0;
        font-size: var(--font-size-sm);
    }

    .results th {
        background-color: var(--primary-color);
        color: white;
        text-align: left;
        padding: var(--space-xs) var(--space-sm);
    }

    .results td {
        padding: var(--space-xs) var(--space-sm);
        border-bottom: 1px solid var(--light-gray);
    }

    .results tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    /* Progress bar styles */
    .progress-container {
        width: 100%;
        background-color: var(--light-gray);
        border-radius: var(--radius-sm);
        margin: var(--space-xs) 0;
        position: relative;
        height: 1.5rem;
    }

    .progress-bar {
        height: 100%;
        border-radius: var(--radius-sm);
        background-color: var(--primary-color);
        position: relative;
        transition: width 0.3s ease;
    }

    .progress-text {
        position: absolute;
        z-index: 2;
        color: var(--dark-gray);
        width: 100%;
        text-align: center;
        line-height: 1.5rem;
        font-size: var(--font-size-sm);
        font-weight: 500;
    }
</style>

{% if result %}
<div class="results">
    <h2>Results</h2>
    {% if result is mapping %}
    <table>
        <thead>
            <tr>
                <th>Class</th>
                <th>Probability</th>
            </tr>
        </thead>
        <tbody>
            {% for class_name, prob in result.items() %}
            <tr>
                <td>
                    {{ class_name }}
                </td>
                <td>
                    <div class="progress-container">
                        <span class="progress-text">{{ "%.2f"|format(prob * 100) }}%</span>
                        <div class="progress-bar" style="width: {{ prob * 100 }}%">
                        </div>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="result-value">
        <pre>{{ result }}</pre>
    </div>
    {% endif %}
</div>
{% endif %}