<template>
  <div class="data-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Folder /></el-icon>
          <h2>数据集管理</h2>
        </div>
        <div class="sub-title">管理系统中的数据集文件</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.name"
                placeholder="请输入文件名"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="action-buttons">
            <el-button type="primary" plain round @click="showUploadDialog">
              <el-icon><Upload /></el-icon>上传文件
            </el-button>
            <el-button type="success" plain round @click="showCreateFolderDialog">
              <el-icon><FolderAdd /></el-icon>新建文件夹
            </el-button>
            <el-button 
              type="danger" 
              :disabled="!selectedFiles.length"
              @click="handleBatchDelete"
              plain
              round
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加面包屑导航 -->
    <div class="breadcrumb-container">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/data/minio/list', query: { directory: '' } }">
          <el-icon><Folder /></el-icon> 根目录
        </el-breadcrumb-item>
        <template v-for="(part, index) in currentPathParts" :key="index">
          <el-breadcrumb-item 
            :to="{ path: '/data/minio/list', query: { directory: getPathUpTo(index) } }"
          >
            {{ part }}
          </el-breadcrumb-item>
        </template>
      </el-breadcrumb>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
        highlight-current-row
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="name" label="文件名" min-width="200" show-overflow-tooltip align="center">
          <template #default="scope">
            <div 
              style="display: flex; align-items: center; justify-content: center; cursor: pointer;"
              @click="scope.row.isDirectory ? navigateToDirectory(scope.row.directory) : null"
            >
              <el-icon class="file-icon">
                <Folder v-if="scope.row.isDirectory" />
                <Document v-else />
              </el-icon>
              <span>{{ getDisplayName(scope.row) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="120" align="center">
          <template #default="scope">
            {{ formatFileSize(scope.row.size) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip align="center" />
        <el-table-column 
          prop="lastModified" 
          label="修改时间" 
          width="180" 
          align="center"
          sortable
          :formatter="formatDateTime"
        />
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <el-button-group v-if="!scope.row.isDirectory">
              <el-tooltip content="下载文件" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleDownload(scope.row)"
                >
                  <el-icon><Download /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="分享文件" placement="top">
                <el-button
                  type="success"
                  size="small"
                  @click="handleShare(scope.row)"
                >
                  <el-icon><Share /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除文件" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(scope.row)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
            <el-button-group v-else>
              <el-tooltip content="打开文件夹" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click="navigateToDirectory(scope.row.directory)"
                >
                  <el-icon><Folder /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="分享文件夹" placement="top">
                <el-button
                  type="success"
                  size="small"
                  @click="handleShare(scope.row)"
                >
                  <el-icon><Share /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除文件夹" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(scope.row)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 上传文件对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传文件"
      width="500px"
    >
      <el-form :model="uploadForm" label-width="80px">
        <el-form-item label="文件">
          <div class="upload-type-switch">
            <el-radio-group v-model="uploadForm.uploadType">
              <el-radio label="file">上传文件</el-radio>
              <el-radio label="directory">上传文件夹</el-radio>
            </el-radio-group>
          </div>
          
          <template v-if="uploadForm.uploadType === 'file'">
            <el-upload
              class="upload-in-dialog"
              action="/api/api/v1.0/sys/file/datasets/upload"
              :headers="uploadHeaders"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeUpload"
              :data="uploadData"
              :auto-upload="false"
              :limit="1"
              ref="uploadRef"
            >
              <el-button type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持任意类型文件上传
                </div>
              </template>
            </el-upload>
          </template>
          
          <template v-else>
            <div class="upload-in-dialog">
              <el-button type="primary" @click="triggerDirectorySelect">选择文件夹</el-button>
              <input 
                type="file" 
                ref="directoryInputRef" 
                @change="handleDirectoryInputChange"
                webkitdirectory 
                directory
                style="display: none;"
              />
              <div class="el-upload__tip">
                支持任意类型文件夹上传
              </div>
              
              <div v-if="selectedFolderInfo.name" class="selected-folder-info">
                <el-card shadow="hover" class="folder-info-card">
                  <template #header>
                    <div class="folder-info-header">
                      <el-icon><Folder /></el-icon>
                      <span>已选择文件夹</span>
                    </div>
                  </template>
                  <div class="folder-info-content">
                    <div class="info-item">
                      <span class="info-label">文件夹名称:</span>
                      <span class="info-value">{{ selectedFolderInfo.name }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">文件数量:</span>
                      <span class="info-value">{{ selectedFolderInfo.fileCount }} 个文件</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">总大小:</span>
                      <span class="info-value" :class="{ 'size-warning': selectedFolderInfo.exceededSize }">
                        {{ formatFileSize(selectedFolderInfo.totalSize) }}
                        <el-icon v-if="selectedFolderInfo.exceededSize" class="warning-icon"><WarningFilled /></el-icon>
                      </span>
                    </div>
                    
                    <!-- 超大文件夹警告 -->
                    <div v-if="selectedFolderInfo.exceededSize" class="size-warning-message">
                      <el-alert
                        title="文件夹超过大小限制(500MB)"
                        type="warning"
                        description="上传大文件夹可能会导致浏览器卡顿或失败"
                        :closable="false"
                        show-icon
                      />
                    </div>
                    
                    <!-- 上传进度条 -->
                    <div v-if="uploadForm.uploadProgress > 0 && uploadForm.uploadProgress < 100" class="upload-progress">
                      <div class="info-label">上传进度:</div>
                      <el-progress 
                        :percentage="uploadForm.uploadProgress" 
                        :format="percent => `${percent}%`"
                        :stroke-width="10"
                        status="success"
                      />
                    </div>
                  </div>
                </el-card>
                
                <div v-if="directoryFileList.length > 0" class="directory-files-preview">
                  <el-collapse>
                    <el-collapse-item title="查看文件列表" name="1">
                      <el-table :data="directoryFileList.slice(0, 10)" size="small" style="width: 100%">
                        <el-table-column prop="name" label="文件名" show-overflow-tooltip />
                        <el-table-column label="大小" width="100" align="right">
                          <template #default="scope">
                            {{ formatFileSize(scope.row.size) }}
                          </template>
                        </el-table-column>
                      </el-table>
                      <div v-if="directoryFileList.length > 10" class="more-files-hint">
                        仅显示前10个文件，共 {{ directoryFileList.length }} 个文件
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
              
              <div v-else class="directory-upload-tip">
                <el-empty description="请选择要上传的文件夹" :image-size="80" />
              </div>
            </div>
          </template>
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="uploadForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入文件描述（可选）"
          ></el-input>
        </el-form-item>
        <el-form-item label="当前目录">
          <el-input 
            v-model="currentDirectory" 
            disabled
            placeholder="根目录"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload">上传</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建文件夹对话框 -->
    <el-dialog
      v-model="createFolderDialogVisible"
      title="新建文件夹"
      width="500px"
    >
      <el-form :model="folderForm" label-width="80px" :rules="folderRules" ref="folderFormRef">
        <el-form-item label="文件夹名" prop="name">
          <el-input 
            v-model="folderForm.name" 
            placeholder="请输入文件夹名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="folderForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入文件夹描述（可选）"
          ></el-input>
        </el-form-item>
        <el-form-item label="当前目录">
          <el-input 
            v-model="currentDirectory" 
            disabled
            placeholder="根目录"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createFolderDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCreateFolder">创建</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分享链接对话框 -->
    <el-dialog
      v-model="shareDialogVisible"
      :title="currentShareFile?.isDirectory ? '分享文件夹' : '分享文件'"
      width="500px"
    >
      <div class="share-dialog-content">
        <el-form :model="shareForm" label-width="80px" class="share-form">
          <el-form-item label="有效期">
            <el-select v-model="shareForm.expiry" placeholder="请选择有效期" @change="handleShareExpiry">
              <el-option label="1小时" :value="3600" />
              <el-option label="6小时" :value="21600" />
              <el-option label="12小时" :value="43200" />
              <el-option label="1天" :value="86400" />
              <el-option label="7天" :value="604800" />
              <el-option label="30天" :value="2592000" />
            </el-select>
          </el-form-item>
        </el-form>
        
        <template v-if="currentShareFile?.isDirectory && shareFiles.length > 0">
          <div class="directory-share-info">
            <p>此文件夹包含 {{ shareFiles.length }} 个文件的分享链接</p>
            <el-collapse>
              <el-collapse-item title="查看所有文件链接" name="1">
                <el-table :data="shareFiles" style="width: 100%" size="small">
                  <el-table-column prop="name" label="文件名" />
                  <el-table-column label="链接" width="120">
                    <template #default="scope">
                      <el-button size="small" @click="copySpecificUrl(scope.row.url)">
                        复制链接
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </div>
        </template>
        <template v-else>
          <el-input
            v-model="shareUrl"
            readonly
            :autoselect="true"
          >
            <template #append>
              <el-button @click="copyShareUrl">复制</el-button>
            </template>
          </el-input>
        </template>
        
        <div class="share-info">
          链接有效期：{{ shareExpiry }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Search, 
  Refresh, 
  Upload, 
  Download,
  Delete, 
  Document,
  Folder,
  FolderAdd,
  Share,
  WarningFilled
} from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import service from '~/axios'
import { getToken } from '~/composables/auth'

const route = useRoute()
const router = useRouter()
const currentDirectory = ref('')
const currentPathParts = computed(() => {
  if (!currentDirectory.value) return []
  return currentDirectory.value.split('/').filter(Boolean)
})

// 获取路径的某一部分
const getPathUpTo = (index) => {
  return currentPathParts.value.slice(0, index + 1).join('/')
}

// 获取显示名称（去掉UUID前缀）
const getDisplayName = (file) => {
  if (file.isDirectory) {
    // 对于目录，显示最后一级目录名
    const parts = file.directory.split('/')
    return parts[parts.length - 1]
  } else {
    // 对于文件，尝试去掉UUID前缀
    const name = file.name
    const match = name.match(/^[a-f0-9-]+_(.+)$/)
    return match ? match[1] : name
  }
}

// 导航到指定目录
const navigateToDirectory = (directory) => {
  router.push({
    path: '/data/minio/list',
    query: { directory }
  })
}

const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const selectedFiles = ref([])
const shareDialogVisible = ref(false)
const shareUrl = ref('')
const shareExpiry = ref('24小时') // 默认值
const currentShareFile = ref(null) // 当前分享的文件
const shareFiles = ref([])

// 上传相关
const uploadDialogVisible = ref(false)
const uploadRef = ref(null)
const directoryInputRef = ref(null)
const directoryFileList = ref([])
const uploadForm = ref({
  description: '',
  uploadType: 'file', // 默认为文件上传，可选 'file' 或 'directory'
  uploadProgress: 0   // 添加上传进度属性
})

// 创建文件夹相关
const createFolderDialogVisible = ref(false)
const folderFormRef = ref(null)
const folderForm = ref({
  name: '',
  description: ''
})

// 文件夹名称验证规则
const folderRules = {
  name: [
    { required: true, message: '请输入文件夹名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
    { pattern: /^[^\\/:*?"<>|]+$/, message: '文件夹名称不能包含特殊字符 \\ / : * ? " < > |', trigger: 'blur' }
  ]
}

const searchForm = ref({
  name: ''
})

// 添加上传请求头，包含 token
const uploadHeaders = {
  token: getToken()
}

// 上传数据，会随表单一起提交
const uploadData = computed(() => {
  return {
    description: uploadForm.value.description || '',
    directory: currentDirectory.value || ''
  }
})

// 定义常量
const MAX_FOLDER_SIZE = 500 * 1024 * 1024 // 500MB

// 处理文件夹大小检查
const checkFolderSize = (size) => {
  if (size > MAX_FOLDER_SIZE) {
    return {
      valid: false,
      message: `文件夹大小(${(size / (1024 * 1024)).toFixed(2)}MB)超过500MB限制，请选择更小的文件夹`
    }
  }
  return { valid: true }
}

// 显示上传对话框
const showUploadDialog = () => {
  // 重置表单数据
  uploadForm.value = {
    description: '',
    uploadType: 'file',  // 默认为文件上传模式
    uploadProgress: 0   // 重置上传进度
  }
  
  // 清空文件夹选择状态
  directoryFileList.value = []
  selectedFolderInfo.value = { 
    name: '', 
    fileCount: 0, 
    totalSize: 0,
    exceededSize: false
  }
  
  // 显示对话框
  uploadDialogVisible.value = true
  
  // 确保所有DOM元素重置
  nextTick(() => {
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
  })
}

// 显示创建文件夹对话框
const showCreateFolderDialog = () => {
  folderForm.value.name = ''
  folderForm.value.description = ''
  createFolderDialogVisible.value = true
}

// 提交创建文件夹
const submitCreateFolder = async () => {
  if (!folderFormRef.value) return
  
  await folderFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 构建完整的目录路径
        let fullPath = folderForm.value.name
        if (currentDirectory.value) {
          fullPath = `${currentDirectory.value}/${folderForm.value.name}`
        }
        
        const response = await service.post('/api/v1.0/sys/file/datasets/createDirectory', null, {
          params: {
            directory: fullPath,
            description: folderForm.value.description || ''
          }
        })
        
        if (response.code === 10000) {
          toast('成功', '文件夹创建成功', 'success')
          createFolderDialogVisible.value = false
          fetchData(currentPage.value)
        } else {
          toast('错误', response.message || '创建文件夹失败', 'error')
        }
      } catch (error) {
        console.error('创建文件夹失败:', error)
        toast('错误', '创建文件夹失败', 'error')
      }
    }
  })
}

// 提交上传
const submitUpload = async () => {
  if (uploadForm.value.uploadType === 'file') {
    // 文件上传
    if (uploadRef.value) {
      uploadRef.value.submit()
    }
  } else {
    // 文件夹上传
    if (directoryFileList.value.length === 0) {
      toast('警告', '请选择需要上传的文件夹', 'warning')
      return
    }
    
    // 强制检查文件夹大小限制
    const totalSize = directoryFileList.value.reduce((sum, file) => sum + file.size, 0)
    const sizeCheck = checkFolderSize(totalSize)
    if (!sizeCheck.valid) {
      // 弹出确认对话框，让用户确认是否继续
      const result = await showModal(
        '文件夹过大',
        `${sizeCheck.message}，是否仍然尝试上传？
        (注意：上传大文件夹可能会导致浏览器卡顿或失败)`,
        'warning'
      )
      
      if (!result) {
        return // 用户取消上传
      }
    }
    
    try {
      loading.value = true
      // 重置上传进度
      uploadForm.value.uploadProgress = 0
      
      // 创建一个FormData对象
      const formData = new FormData()
      
      // 添加描述和目录信息
      formData.append('description', uploadForm.value.description || '')
      formData.append('directory', currentDirectory.value || '')
      
      // 添加所有文件，保留相对路径结构
      const relativePaths = []
      
      directoryFileList.value.forEach(fileItem => {
        // 添加文件到FormData
        formData.append('files', fileItem.raw)
        
        // 准备相对路径
        const fullPath = fileItem.name  // 形如 "folder1/subfolder/file.txt"
        relativePaths.push(fullPath)
      })
      
      // 将相对路径列表添加到FormData
      relativePaths.forEach(path => {
        formData.append('relativePaths', path)
      })
      
      // 发送请求到后端
      const response = await service.post('/api/v1.0/sys/file/datasets/upload-folder', formData, {
        headers: {
          ...uploadHeaders,
          'Content-Type': 'multipart/form-data'
        },
        // 添加上传进度处理
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            uploadForm.value.uploadProgress = percentCompleted
            console.log(`上传进度: ${percentCompleted}%`)
          }
        }
      })
      
      if (response.code === 10000) {
        uploadForm.value.uploadProgress = 100
        toast('成功', '文件夹上传成功', 'success')
        uploadDialogVisible.value = false
        fetchData(currentPage.value)
      } else {
        toast('错误', response.message || '文件夹上传失败', 'error')
      }
    } catch (error) {
      console.error('文件夹上传失败:', error)
      toast('错误', '文件夹上传失败: ' + (error.message || '未知错误'), 'error')
    } finally {
      loading.value = false
    }
  }
}

// 获取文件列表
const fetchData = async (page = 1) => {
  loading.value = true
  try {
    const response = await service.get('/api/v1.0/sys/file/datasets/list', {
      params: {
        page,
        size: pageSize.value,
        directory: currentDirectory.value,
        name: searchForm.value.name
      }
    })
    if (response.code === 10000) {
      // 处理文件和目录
      const files = response.data.files || []
      
      // 创建目录映射，用于识别唯一目录
      const dirMap = new Map()
      
      // 首先处理明确的目录项
      files.forEach(file => {
        if (file.isDirectory === true) {
          // 这是一个目录，直接添加到目录映射
          const dirName = file.name
          dirMap.set(dirName, {
            ...file,
            isDirectory: true
          })
        }
      })
      
      // 处理文件和嵌套目录
      files.forEach(file => {
        // 跳过已处理的目录
        if (file.isDirectory === true) return
        
        // 检查文件是否在当前目录下
        const isInCurrentDir = !currentDirectory.value 
          ? !file.directory || file.directory === '' 
          : file.directory === currentDirectory.value
        
        // 如果文件在当前目录下，直接添加为文件
        if (isInCurrentDir) {
          return // 这些文件会在后面处理
        }
        
        // 处理子目录
        if (file.path && file.path.includes('/')) {
          // 获取文件路径
          const pathParts = file.path.split('/')
          
          // 如果当前目录为空，则第一部分是目录
          // 如果当前目录不为空，则检查路径是否以当前目录开头
          if (!currentDirectory.value) {
            // 根目录下，第一部分是目录名
            const dirName = pathParts[0]
            if (dirName && !dirMap.has(dirName)) {
              dirMap.set(dirName, {
                name: dirName,
                path: dirName,
                directory: dirName,
                isDirectory: true,
                size: 0,
                lastModified: file.lastModified
              })
            }
          } else {
            // 在子目录中，检查路径是否以当前目录开头
            if (file.path.startsWith(currentDirectory.value + '/')) {
              // 获取当前目录之后的下一级目录
              const relativePath = file.path.substring(currentDirectory.value.length + 1)
              const nextDirName = relativePath.split('/')[0]
              
              if (nextDirName && !dirMap.has(nextDirName)) {
                const fullPath = `${currentDirectory.value}/${nextDirName}`
                dirMap.set(nextDirName, {
                  name: nextDirName,
                  path: fullPath,
                  directory: fullPath,
                  isDirectory: true,
                  size: 0,
                  lastModified: file.lastModified
                })
              }
            }
          }
        }
      })
      
      // 过滤出当前目录下的文件
      const currentFiles = files.filter(file => {
        // 跳过目录项
        if (file.isDirectory === true) return false
        
        if (!currentDirectory.value) {
          // 根目录：显示没有目录信息的文件
          return !file.directory || file.directory === ''
        } else {
          // 子目录：显示目录正好匹配当前目录的文件
          return file.directory === currentDirectory.value
        }
      }).map(file => ({
        ...file,
        isDirectory: false
      }))
      
      // 合并目录和文件
      tableData.value = [
        ...Array.from(dirMap.values()),
        ...currentFiles
      ]
      
      // 对文件和目录进行排序，目录在前
      tableData.value.sort((a, b) => {
        if (a.isDirectory && !b.isDirectory) return -1
        if (!a.isDirectory && b.isDirectory) return 1
        return a.name.localeCompare(b.name) // 同类型按名称排序
      })
      
      total.value = response.data.pagination.total
      pageSize.value = response.data.pagination.size
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    toast('错误', '获取文件列表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 上传前检查
const beforeUpload = (file) => {
  return true
}

// 上传成功
const handleUploadSuccess = (response) => {
  if (response.code === 10000) {
    toast('成功', '文件上传成功', 'success')
    uploadDialogVisible.value = false
    fetchData(currentPage.value)
  } else {
    toast('错误', response.message || '上传失败', 'error')
  }
}

// 上传失败
const handleUploadError = (error) => {
  toast('错误', '文件上传失败', 'error')
}

// 下载文件
const handleDownload = async (row) => {
  try {
    // 修改为使用查询参数传递路径
    const response = await service.get(`/api/v1.0/sys/file/datasets/download`, {
      params: {
        path: row.path
      },
      responseType: 'blob'
    })
    
    // 创建 Blob URL
    const blob = new Blob([response])
    const url = window.URL.createObjectURL(blob)
    
    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = row.name
    document.body.appendChild(link)
    link.click()
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }, 100)
  } catch (error) {
    console.error('下载文件失败:', error)
    toast('错误', '下载文件失败', 'error')
  }
}

// 分享表单
const shareForm = ref({
  expiry: 86400 // 默认1天
})

// 处理有效期变更
const handleShareExpiry = async () => {
  if (!currentShareFile.value) return
  
  try {
    // 显示加载状态
    loading.value = true
    
    // 修改请求方式，使用查询参数传递路径而不是URL路径
    const response = await service.get(`/api/v1.0/sys/file/datasets/share`, {
      params: {
        path: currentShareFile.value.path,
        expiry: shareForm.value.expiry
      }
    })
    
    if (response.code === 10000) {
      // 处理返回的特殊格式
      let urlData = response.data.url
      
      // 如果返回的是字符串形式的对象，需要提取实际的URL
      if (typeof urlData === 'string' && urlData.includes('url=')) {
        // 提取 url= 后面的完整URL，包括所有查询参数
        const urlRegex = /url=(http[^,}]+)/
        const match = urlData.match(urlRegex)
        if (match && match[1]) {
          shareUrl.value = match[1]
        } else {
          shareUrl.value = urlData
        }
      } else {
        // 其他情况，直接使用返回值
        shareUrl.value = urlData
      }
      
      // 设置有效期
      if (response.data.expiry) {
        const hours = Math.floor(response.data.expiry / 3600)
        if (hours >= 24) {
          const days = Math.floor(hours / 24)
          shareExpiry.value = `${days}天`
        } else {
          shareExpiry.value = `${hours}小时`
        }
      } else {
        shareExpiry.value = '24小时' // 默认值
      }
      
      // 显示成功提示
      toast('成功', '已更新分享链接', 'success')
    } else {
      toast('错误', response.message || '生成分享链接失败', 'error')
    }
  } catch (error) {
    console.error('分享文件失败:', error)
    // 添加更详细的错误信息
    if (error.response) {
      console.error('错误响应数据:', error.response.data)
      console.error('错误状态码:', error.response.status)
    }
    toast('错误', '生成分享链接失败', 'error')
  } finally {
    // 关闭加载状态
    loading.value = false
  }
}

// 复制特定URL
const copySpecificUrl = async (url) => {
  try {
    await navigator.clipboard.writeText(url)
    toast('成功', '链接已复制到剪贴板', 'success')
  } catch (error) {
    toast('错误', '复制失败', 'error')
  }
}

// 修改 handleShare 方法，添加对文件夹的处理
const handleShare = async (row) => {
  // 保存当前分享的文件或文件夹
  currentShareFile.value = row
  
  // 重置为默认有效期
  shareForm.value.expiry = 86400 // 默认1天
  shareFiles.value = [] // 清空之前的文件列表
  
  try {
    // 显示加载状态
    loading.value = true
    
    // 使用查询参数传递路径
    const response = await service.get(`/api/v1.0/sys/file/datasets/share`, {
      params: {
        path: row.isDirectory ? row.directory : row.path,
        expiry: shareForm.value.expiry
      }
    })
    
    if (response.code === 10000) {
      // 处理返回的数据
      let responseData = response.data;
      
      // 检查是否是字符串形式的对象
      if (typeof responseData.url === 'string' && responseData.url.includes('isDirectory=true')) {
        // 尝试解析字符串化的对象
        try {
          // 提取文件列表
          const filesMatch = responseData.url.match(/files=\[(.*?)\]/);
          if (filesMatch && filesMatch[1]) {
            const filesStr = filesMatch[1];
            // 解析文件列表
            const fileItems = [];
            const fileRegex = /{name=(.*?), url=(.*?)}/g;
            let match;
            while ((match = fileRegex.exec(filesStr)) !== null) {
              fileItems.push({
                name: match[1],
                url: match[2]
              });
            }
            shareFiles.value = fileItems;
          }
          
          // 设置为目录模式
          currentShareFile.value = {
            ...currentShareFile.value,
            isDirectory: true
          };
          
          // 如果有文件，设置第一个文件的链接作为示例
          if (shareFiles.value.length > 0) {
            shareUrl.value = shareFiles.value[0].url;
          } else {
            shareUrl.value = '文件夹为空';
          }
        } catch (parseError) {
          console.error('解析目录分享数据失败:', parseError);
          shareUrl.value = '无法解析目录分享数据';
        }
      } else if (responseData.isDirectory === true) {
        // 标准JSON格式的目录响应
        shareFiles.value = responseData.files || [];
        
        // 如果有文件，设置第一个文件的链接作为示例
        if (shareFiles.value.length > 0) {
          shareUrl.value = shareFiles.value[0].url;
        } else {
          shareUrl.value = '文件夹为空';
        }
      } else {
        // 处理单个文件的链接
        let urlData = responseData.url;
        
        // 如果返回的是字符串形式的对象，需要提取实际的URL
        if (typeof urlData === 'string' && urlData.includes('url=')) {
          const urlRegex = /url=(http[^,}]+)/;
          const match = urlData.match(urlRegex);
          if (match && match[1]) {
            shareUrl.value = match[1];
          } else {
            shareUrl.value = urlData;
          }
        } else {
          // 其他情况，直接使用返回值
          shareUrl.value = urlData;
        }
      }
      
      // 设置有效期
      if (responseData.expiry) {
        const hours = Math.floor(responseData.expiry / 3600);
        if (hours >= 24) {
          const days = Math.floor(hours / 24);
          shareExpiry.value = `${days}天`;
        } else {
          shareExpiry.value = `${hours}小时`;
        }
      } else {
        shareExpiry.value = '24小时'; // 默认值
      }
      
      // 显示分享对话框
      shareDialogVisible.value = true;
    } else {
      toast('错误', response.message || '生成分享链接失败', 'error');
    }
  } catch (error) {
    console.error('分享失败:', error);
    if (error.response) {
      console.error('错误响应数据:', error.response.data);
      console.error('错误状态码:', error.response.status);
    }
    toast('错误', '生成分享链接失败', 'error');
  } finally {
    // 关闭加载状态
    loading.value = false;
  }
}

// 复制分享链接
const copyShareUrl = async () => {
  try {
    await navigator.clipboard.writeText(shareUrl.value)
    toast('成功', '链接已复制到剪贴板', 'success')
  } catch (error) {
    toast('错误', '复制失败', 'error')
  }
}

// 删除文件
const handleDelete = async (row) => {
  try {
    await showModal('确定要删除该文件吗？', 'warning', '提示')
    // 修改为使用查询参数传递路径
    const response = await service.delete(`/api/v1.0/sys/file/datasets`, {
      params: {
        path: row.path
      }
    })
    if (response.code === 10000) {
      toast('成功', '删除成功')
      fetchData(currentPage.value)
    } else {
      toast('错误', response.message || '删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '删除失败', 'error')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedFiles.value.length) {
    toast('警告', '请选择要删除的文件', 'warning')
    return
  }
  
  try {
    await showModal(`确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`, 'warning', '提示')
    // 修改为使用查询参数传递路径
    const deletePromises = selectedFiles.value.map(file => 
      service.delete(`/api/v1.0/sys/file/datasets`, {
        params: {
          path: file.path
        }
      })
    )
    await Promise.all(deletePromises)
    toast('成功', '批量删除成功')
    selectedFiles.value = []
    fetchData(currentPage.value)
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '批量删除失败', 'error')
    }
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value.name = ''
  currentPage.value = 1
  fetchData()
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedFiles.value = selection
}

// 页码变化
const handleCurrentChange = (val) => {
  fetchData(val)
}

// 每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData(1)
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(2)} ${units[index]}`
}

// 格式化日期时间
const formatDateTime = (row, column) => {
  const value = row[column.property]
  if (!value) return '-'
  return new Date(value).toLocaleString()
}

// 监听路由变化
watch(() => route.query.directory, (newDirectory) => {
  currentDirectory.value = newDirectory || ''
  fetchData(1)
}, { immediate: true })

onMounted(() => {
  // 从路由中获取当前目录
  currentDirectory.value = route.query.directory || ''
  fetchData()
})

// 处理文件夹选择
const triggerDirectorySelect = () => {
  if (directoryInputRef.value) {
    directoryInputRef.value.click()
  }
}

// 处理文件夹输入变化
const handleDirectoryInputChange = (event) => {
  const files = event.target.files
  if (!files || files.length === 0) return
  
  // 将FileList转换为数组
  const fileArray = Array.from(files)
  
  // 获取所有文件路径的共同前缀（根文件夹名称）
  const paths = fileArray.map(file => file.webkitRelativePath)
  const rootFolder = paths[0].split('/')[0]
  
  // 计算总大小
  const totalSize = fileArray.reduce((sum, file) => sum + file.size, 0)
  
  // 检查文件夹大小是否超过限制
  const sizeCheck = checkFolderSize(totalSize)
  if (!sizeCheck.valid) {
    toast('警告', sizeCheck.message, 'warning')
    // 不阻止用户继续操作，但显示警告
  }
  
  // 更新选择的文件夹信息
  selectedFolderInfo.value = {
    name: rootFolder,
    fileCount: fileArray.length,
    totalSize: totalSize,
    exceededSize: !sizeCheck.valid
  }
  
  // 更新文件列表，并按路径排序
  directoryFileList.value = fileArray.map(file => {
    return {
      name: file.webkitRelativePath,
      size: file.size,
      raw: file
    }
  }).sort((a, b) => a.name.localeCompare(b.name))
  
  console.log(`选择的文件夹: ${rootFolder}，包含 ${fileArray.length} 个文件，总大小: ${formatFileSize(totalSize)}`)
  
  // 提示用户
  toast('成功', `已选择文件夹: ${rootFolder}，包含 ${fileArray.length} 个文件`, 'success')
}

// 处理文件夹信息
const selectedFolderInfo = ref({
  name: '',
  fileCount: 0,
  totalSize: 0,
  exceededSize: false
})

// 处理文件夹信息变化
const handleDirectoryInfoChange = () => {
  const files = directoryFileList.value
  selectedFolderInfo.value.fileCount = files.length
  selectedFolderInfo.value.totalSize = files.reduce((total, file) => total + file.size, 0)
  selectedFolderInfo.value.name = directoryFileList.value[0]?.name.split('/')[0] || ''
}

// 处理文件夹内文件移除
const handleDirectoryFileRemove = (file, fileList) => {
  directoryFileList.value = fileList
}
</script>

<style scoped>
.data-list-container {
  padding: 20px;
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

:deep(.el-table th) {
  font-weight: bold;
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
}

:deep(.el-button--primary) {
  --el-button-hover-bg-color: var(--el-color-primary-light-3);
  --el-button-hover-border-color: var(--el-color-primary-light-3);
  --el-button-active-bg-color: var(--el-color-primary-dark-2);
  --el-button-active-border-color: var(--el-color-primary-dark-2);
}

:deep(.el-button--default) {
  --el-button-hover-bg-color: var(--el-color-primary-light-9);
  --el-button-hover-border-color: var(--el-color-primary-light-7);
  --el-button-hover-text-color: var(--el-color-primary);
}

:deep(.el-button--danger) {
  --el-button-hover-bg-color: var(--el-color-danger-light-3);
  --el-button-hover-border-color: var(--el-color-danger-light-3);
  --el-button-active-bg-color: var(--el-color-danger-dark-2);
  --el-button-active-border-color: var(--el-color-danger-dark-2);
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

:deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

.ml-4 {
  margin-left: 4px;
}

.file-icon {
  margin-right: 8px;
  vertical-align: middle;
}

.upload-in-dialog {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.share-dialog-content {
  .share-info {
    margin-top: 12px;
    color: #909399;
    font-size: 14px;
  }
}

/* 文件类型标签样式 */
:deep(.el-tag) {
  /* 移除 text-transform: uppercase; */
}

.share-form {
  margin-bottom: 16px;
}

.breadcrumb-container {
  margin-bottom: 16px;
  padding: 8px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

:deep(.el-breadcrumb__item) {
  display: flex;
  align-items: center;
}

:deep(.el-breadcrumb__inner) {
  display: flex;
  align-items: center;
}

:deep(.el-breadcrumb__inner .el-icon) {
  margin-right: 4px;
}

.directory-share-info {
  margin-bottom: 16px;
}

.directory-share-info p {
  margin-bottom: 8px;
  color: #606266;
}

:deep(.el-collapse) {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

:deep(.el-collapse-item__header) {
  color: #409eff;
  font-weight: 500;
}

:deep(.el-table--small) {
  font-size: 12px;
}

/* 添加新样式 */
.folder-icon {
  color: #e6a23c;
  margin-right: 5px;
}

.folder-add-icon {
  color: #67c23a;
  margin-right: 5px;
}

.upload-type-switch {
  margin-bottom: 16px;
}

:deep(.el-upload-list) {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 12px;
}

:deep(.el-radio-group) {
  margin-bottom: 10px;
}

:deep(.el-upload-list__item) {
  transition: all 0.3s;
}

:deep(.el-upload-list__item:hover) {
  background-color: var(--el-fill-color-light);
}

/* 文件夹选择器样式 */
.directory-uploader {
  position: relative;
  display: inline-block;
}

.directory-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 1;
}

:deep(.el-upload-list--directory) {
  max-height: 300px;
  overflow-y: auto;
}

.directory-upload-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.directory-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.directory-upload-tip {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

.directory-files-preview {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.selected-folder-info {
  margin: 12px 0;
}

.more-files-hint {
  text-align: center;
  padding: 8px;
  color: #909399;
  font-size: 13px;
  background-color: #f5f7fa;
}

.small-text {
  font-size: 12px;
  color: #909399;
}

.directory-upload-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.directory-button-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.select-folder-button {
  height: 36px;
  padding: 0 20px;
}

.upload-tip-text {
  text-align: center;
  color: #909399;
  padding: 8px;
}

.folder-info-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.folder-info-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.folder-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.folder-info-header .el-icon {
  margin-right: 8px;
  font-size: 24px;
  color: var(--el-color-primary);
}

.folder-info-content {
  padding: 12px;
}

.info-item {
  margin-bottom: 8px;
}

.info-label {
  font-weight: bold;
}

.info-value {
  margin-left: 8px;
}

/* 文件夹上传相关样式 */
.folder-upload-section {
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 选择文件夹按钮区域 */
.folder-select-area {
  width: 100%;
  text-align: center;
  padding: 20px 0;
}

.folder-select-btn {
  width: 180px;
  height: 40px;
  border-radius: 20px;
  transition: all 0.3s;
}

.folder-select-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.folder-select-btn .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.folder-select-tip {
  font-size: 13px;
  color: #909399;
  margin-top: 12px;
}

/* 文件夹信息卡片 */
.folder-selected-area {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  animation: fade-in 0.3s ease-in-out;
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.folder-info-card {
  width: 100%;
  border-radius: 8px;
  transition: all 0.3s;
}

.folder-info-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.folder-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #409EFF;
}

.folder-header .el-icon {
  font-size: 18px;
}

.folder-info-list {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.folder-info-item {
  display: flex;
  align-items: center;
  line-height: 1.5;
}

.info-label {
  width: 80px;
  font-size: 14px;
  color: #606266;
  flex-shrink: 0;
}

.info-value {
  font-weight: 500;
  color: #303133;
  word-break: break-all;
}

/* 文件列表预览 */
.folder-files-preview {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.more-files-tip {
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

/* 空状态 */
.folder-empty-state {
  width: 100%;
  padding: 30px 0;
  text-align: center;
}

.folder-size-limit {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

/* 文件夹上传容器 - 与上传文件风格一致 */
.folder-upload-container {
  width: 100%;
  text-align: left;
}

.folder-upload-container .el-button {
  margin-bottom: 10px;
}

.folder-upload-container .el-upload__tip {
  line-height: 1.5;
  color: #909399;
  font-size: 13px;
  margin-bottom: 20px;
}

/* 文件夹信息部分 */
.folder-info-section {
  margin-top: 20px;
  width: 100%;
}

.folder-info-card {
  margin-bottom: 20px;
  width: 100%;
}

.folder-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.folder-header .el-icon {
  color: #409EFF;
}

.folder-info-list {
  padding: 0;
}

.folder-info-item {
  display: flex;
  margin-bottom: 8px;
  line-height: 1.5;
}

.folder-info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 80px;
  color: #606266;
  font-size: 14px;
}

.info-value {
  flex: 1;
  word-break: break-all;
}

/* 文件列表部分 */
.folder-files-list {
  margin-top: 16px;
  width: 100%;
}

.folder-files-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 10px;
  font-weight: 500;
}

.more-files-hint {
  text-align: center;
  font-size: 12px;
  color: #909399;
  padding: 8px 0;
  background-color: #f5f7fa;
  margin-top: 8px;
}

.size-warning {
  color: #E6A23C;
  font-weight: bold;
}

.warning-icon {
  color: #E6A23C;
  margin-left: 5px;
  font-size: 16px;
}

.size-warning-message {
  margin-top: 10px;
  margin-bottom: 10px;
}

.upload-progress {
  margin-top: 16px;
}
</style> 