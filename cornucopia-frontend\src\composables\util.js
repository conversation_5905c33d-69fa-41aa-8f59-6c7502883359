import { ElNotification,ElMessageBox } from 'element-plus'
import nprogress from 'nprogress'
// 消息提示
export function toast(title = "请求成功",message,type = "success",dangerouslyUseHTMLString = false){
    ElNotification({
        title,
        message,
        type,
        dangerouslyUseHTMLString,
        duration:3000
    })
}

// 显示全屏loading
export function showFullLoading(){
  nprogress.start()
}

// 隐藏全屏loading
export function hideFullLoading(){
  nprogress.done()
}

export function showModal(
    content = "提示内容",
    type = "warning",
    title = "提示"
) {
    return ElMessageBox.confirm(
        content,
        title,
        {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type,
            center: true,
            roundButton: true,
            customClass: 'my-message-box',
            dangerouslyUseHTMLString: true,
            customStyle: {
                width: '420px',
                borderRadius: '12px',
                padding: '24px 20px'
            },
            buttonSize: 'large',
            showClose: true,
            draggable: true,
            lockScroll: true,
            confirmButtonClass: 'confirm-button',
            cancelButtonClass: 'cancel-button'
        }
    )
}
