from pathlib import Path
import shutil
from typing import Optional, Union


def get_default_extract_dir(archive_path: Union[str, Path]) -> Path:
    """Get the default extraction directory (same as archive's directory).

    Args:
        archive_path: Path to the archive file.

    Returns:
        Path: Path to the directory containing the archive.
    """
    archive_path = Path(archive_path)
    return archive_path.resolve().parent


def ensure_directory_exists(dir_path: Union[str, Path]) -> None:
    """Ensure directory exists, create if it doesn't.

    Args:
        dir_path: Path to the directory to check/create.
    """
    Path(dir_path).mkdir(parents=True, exist_ok=True)


def extract_archive(archive_path: Union[str, Path], extract_dir: Union[str, Path]) -> None:
    """Basic archive extraction functionality.

    Args:
        archive_path: Path to the archive file.
        extract_dir: Directory where to extract the archive.
    """
    shutil.unpack_archive(str(archive_path), str(extract_dir))


def find_extracted_root_dir(extract_dir: Union[str, Path], archive_name: str) -> Optional[Path]:
    """Find the root directory of extracted contents.

    Looks for common directory naming patterns after extraction.

    Args:
        extract_dir: Directory where archive was extracted.
        archive_name: Name of the original archive file.

    Returns:
        Optional[Path]: Path to the root extraction directory if found, else None.
    """
    extract_dir = Path(extract_dir)
    archive_stem = Path(archive_name).stem

    # Possible root directory naming variants
    possible_roots = [
        extract_dir / archive_stem,  # Without extension
        extract_dir / archive_name.replace('.tar.gz', '').replace('.zip', ''),
    ]

    # Check for single subdirectory in extract directory
    items_in_extract_dir = list(extract_dir.iterdir())
    if len(items_in_extract_dir) == 1:
        single_item = items_in_extract_dir[0]
        if single_item.is_dir():
            possible_roots.append(single_item)

    # Return first valid directory found
    for possible_root in possible_roots:
        if possible_root.is_dir():
            return possible_root
    return None


def flatten_directory(target_dir: Union[str, Path], parent_dir: Union[str, Path]) -> None:
    """Move all contents from target directory to parent directory.

    Args:
        target_dir: Directory whose contents should be moved.
        parent_dir: Destination directory for the contents.

    Raises:
        OSError: If file operations fail.
    """
    target_dir = Path(target_dir)
    parent_dir = Path(parent_dir)

    for item in target_dir.iterdir():
        dst = parent_dir / item.name

        # Handle existing items
        if dst.exists():
            if dst.is_dir():
                shutil.rmtree(dst)
            else:
                dst.unlink()

        shutil.move(str(item), str(dst))

    # Remove now-empty directory
    shutil.rmtree(target_dir)


def remove_file_if_exists(file_path: Union[str, Path]) -> None:
    """Safely remove a file if it exists.

    Args:
        file_path: Path to the file to be removed.
    """
    file_path = Path(file_path)
    if file_path.exists():
        file_path.unlink()


def unpack_archive_with_flatten(
    archive_path: Union[str, Path],
    extract_dir: Optional[Union[str, Path]] = None,
    flatten_single_dir: bool = False,
    remove_archive: bool = False,
) -> Path:
    """Extract archive with optional directory flattening.

    Extracts an archive and optionally flattens directory structure if the archive
    contains a single directory wrapping all files.

    Args:
        archive_path: Path to the archive file.
        extract_dir: Directory to extract to. If None, uses archive's directory.
        flatten_single_dir: If True, flattens single-directory extraction.
        remove_archive: If True, deletes the archive after extraction.

    Returns:
        Path: Path to the directory where contents were extracted.

    Raises:
        ValueError: If archive_path doesn't exist.
        shutil.ReadError: If extraction fails.
    """
    archive_path = Path(archive_path)

    # 1. Determine extraction directory
    if extract_dir is None:
        extract_dir = get_default_extract_dir(archive_path)
    else:
        extract_dir = Path(extract_dir)

    ensure_directory_exists(extract_dir)

    # 2. Perform extraction
    extract_archive(archive_path, extract_dir)

    # 3. Handle directory flattening
    if flatten_single_dir:
        extracted_root = find_extracted_root_dir(
            extract_dir, archive_path.name)

        if extracted_root:
            # Only flatten if root isn't the extract_dir itself
            if extracted_root.resolve() != extract_dir.resolve():
                flatten_directory(extracted_root, extract_dir)

    # 4. Clean up archive if requested
    if remove_archive:
        remove_file_if_exists(archive_path)

    return extract_dir


def is_archive_supported(filepath: Union[str, Path]) -> bool:
    """Check if a file is an archive format supported by shutil.unpack_archive.

    Args:
        filepath: Path to the file to be checked.

    Returns:
        bool: True if the file is a supported archive format, False otherwise.
    """
    filepath = Path(filepath)

    if not filepath.suffix:
        return False

    ext_combinations = set()
    for i in range(1, len(filepath.suffixes) + 1):
        ext_combinations.add(
            "".join(filepath.suffixes[-i:]).lstrip('.').lower())

    # Get all supported formats from shutil
    supported_formats = shutil.get_unpack_formats()

    # Check if extension matches any supported format
    for fmt in supported_formats:
        if fmt[0] in ext_combinations:
            return True

    return False
