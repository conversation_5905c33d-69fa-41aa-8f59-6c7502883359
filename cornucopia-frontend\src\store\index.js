import { createStore } from 'vuex'
import { login,getinfo,register } from '~/api/manager'
import {
    setToken,
    removeToken
} from '~/composables/auth'
const store = createStore({
    state() {
        return {
            // 用户信息
            user: {
                userId: null,
                username: '',
                fullname: null,
                email: null,
                enable: true,
                roles: []
            }
        }
    },
    mutations: {
        // 记录用户信息
        SET_USERINFO(state, user){
            state.user = {
                userId: user.userId,
                username: user.username,
                fullname: user.fullname,
                email: user.email,
                enable: user.enable,
                roles: user.roles || []
            }
        }
    },
    actions:{
        // 登录
        login({ commit }, { username,password }){
            return new Promise((resolve,reject)=>{
                login(username,password).then(res=>{
                    if(res.code === 10001) { 
                        // 保存token
                        setToken(res.data.token)
                        // 保存用户信息
                        commit("SET_USERINFO", res.data.userInfo)
                    }
                    resolve(res)
                }).catch(err=>reject(err))
            })
        },
        // 获取当前登录用户信息
        getinfo({ commit }){
            return new Promise((resolve,reject)=>{
                getinfo().then(res=>{
                    if(res.code === 10000) {
                        commit("SET_USERINFO", res.data)
                        resolve(res.data)
                    } else {
                        reject(new Error(res.message || '获取用户信息失败'))
                    }
                }).catch(err=>reject(err))
            })
        },
        // 退出登录
        logout({ commit }){
            // 移除cookie里的token
            removeToken()
            // 清除当前用户状态 vuex
            commit("SET_USERINFO", {
                userId: null,
                username: '',
                fullname: null,
                email: null,
                enable: true,
                roles: []
            })
        },
        // 注册
        register({ commit }, { username, password,roleId }){
            return new Promise((resolve,reject)=>{
                register(username, password,roleId).then(res=>{
                    resolve(res)
                }).catch(err=>reject(err))
            })
        }
    },
    getters: {
        // 获取用户权限列表
        userPermissions: (state) => {
            const permissions = new Set()
            state.user.roles?.forEach(role => {
                role.permissions?.forEach(permission => {
                    permissions.add(permission)
                })
            })
            return Array.from(permissions)
        },
        // 获取用户角色列表
        userRoles: (state) => {
            return state.user.roles?.map(role => role.name) || []
        },
        // 检查是否拥有某个权限
        hasPermission: (state) => (permission) => {
            const permissions = new Set()
            state.user.roles?.forEach(role => {
                role.permissions?.forEach(perm => {
                    permissions.add(perm)
                })
            })
            return permissions.has(permission)
        },
        // 检查是否拥有任意一个权限
        hasAnyPermission: (state) => (permissionList) => {
            const permissions = new Set()
            state.user.roles?.forEach(role => {
                role.permissions?.forEach(perm => {
                    permissions.add(perm)
                })
            })
            return permissionList.some(permission => permissions.has(permission))
        },
        // 检查是否拥有所有权限
        hasAllPermissions: (state) => (permissionList) => {
            const permissions = new Set()
            state.user.roles?.forEach(role => {
                role.permissions?.forEach(perm => {
                    permissions.add(perm)
                })
            })
            return permissionList.every(permission => permissions.has(permission))
        }
    }
})

export default store