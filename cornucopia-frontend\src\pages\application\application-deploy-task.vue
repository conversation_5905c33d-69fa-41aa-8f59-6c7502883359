<template>
  <div class="task-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><List /></el-icon>
          <h2>我的应用部署任务</h2>
        </div>
        <div class="sub-title">管理您创建的所有模型部署任务</div>
      </div>
      <div class="header-right">
        <el-button
          type="danger"
          size="default"
          :disabled="selectedIds.length === 0"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon> 批量删除
        </el-button>
        <el-button type="primary" @click="goToCreateTask" round>
          <el-icon><Plus /></el-icon>
          创建任务
        </el-button>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <!-- 任务表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="任务ID" align="center" width="80" />
        <el-table-column prop="name" label="任务名称" align="center" min-width="180">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center; cursor: pointer;">
              <el-icon class="file-icon" style="margin-right: 5px;">
                <Box />
              </el-icon>
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" align="center" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="审核状态" align="center" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="creatorName" label="创建者" align="center" width="120" /> -->
        <el-table-column prop="createTime" label="创建时间" align="center" width="180" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" align="center" width="180" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-tooltip content="查看任务详情" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="viewTaskDetail(scope.row)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="刷新任务状态" placement="top">
                <el-button
                  type="info"
                  size="small"
                  @click.stop="refreshTaskStatus(scope.row)">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除任务" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click.stop="handleDelete(scope.row)"
                  :disabled="scope.row.status === 'RUNNING'"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
            <!-- 任务操作按钮 -->
            <div class="container-actions">
              <el-button-group class="mt-2">
                <el-tooltip content="启动任务" placement="top">
                  <el-button
                    type="success"
                    size="small"
                    @click.stop="startTask(scope.row)"
                    :disabled="isTaskRunning(scope.row)"
                  >
                    <el-icon><VideoPlay /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="停止任务" placement="top">
                  <el-button
                    type="warning"
                    size="small"
                    @click.stop="stopTask(scope.row)"
                    :disabled="!isTaskRunning(scope.row)"
                  >
                    <el-icon><VideoPause /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="杀死任务" placement="top">
                  <el-button
                    type="danger"
                    size="small"
                    @click.stop="killTask(scope.row)"
                    :disabled="!isTaskRunning(scope.row)"
                  >
                    <el-icon><CircleClose /></el-icon>
                  </el-button>
                </el-tooltip>
                <!-- 添加服务按钮 -->
                <el-tooltip content="访问服务" placement="top">
                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="openService(scope.row)"
                    :disabled="!isServiceAvailable(scope.row)"
                  >
                    <el-icon><Link /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="申请重新审批" placement="top">
                  <el-button
                    type="info"
                    size="small"
                    @click.stop="requestReapproval(scope.row)"
                    :disabled="scope.row.status === 'PENDING'"
                  >
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </el-tooltip>
              </el-button-group>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
    
    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="taskDetailVisible"
      title="任务详情"
      width="700px"
      destroy-on-close
    >
      <div v-loading="taskDetailLoading">
        <el-descriptions v-if="taskDetail" :column="1" border>
          <el-descriptions-item label="任务ID">{{ taskDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskDetail.name }}</el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getStatusType(taskDetail.status)">{{ getStatusText(taskDetail.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(taskDetail.timeCreated) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(taskDetail.timeUpdated) }}</el-descriptions-item>
          <el-descriptions-item label="创建者">用户{{ taskDetail.creatorId }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ taskDetail.description || '无' }}</el-descriptions-item>
          
          <!-- 模型信息 -->
          <el-descriptions-item label="模型信息">
            <div v-if="taskDetail.model">
              <p><strong>模型ID：</strong>{{ taskDetail.model.id }}</p>
              <p><strong>模型名称：</strong>{{ taskDetail.model.name }}</p>
              <p><strong>模型描述：</strong>{{ taskDetail.model.description || '无' }}</p>
              <p><strong>模型路径：</strong>{{ taskDetail.modelPath }}</p>
            </div>
            <span v-else>无</span>
          </el-descriptions-item>

          <!-- 节点信息 -->
          <el-descriptions-item label="部署节点">
            <div v-if="taskDetail.node">
              <p><strong>节点ID：</strong>{{ taskDetail.node.id }}</p>
              <p><strong>节点名称：</strong>{{ taskDetail.node.name }}</p>
              <p><strong>节点地址：</strong>{{ taskDetail.node.ipAddress }}:{{ taskDetail.node.port }}</p>
              <p><strong>节点类型：</strong>{{ taskDetail.node.nodeType }}</p>
              <p><strong>节点描述：</strong>{{ taskDetail.node.description || '无' }}</p>
            </div>
            <span v-else>无</span>
          </el-descriptions-item>

          <!-- 部署信息 -->
          <el-descriptions-item label="部署任务ID">{{ taskDetail.taskId }}</el-descriptions-item>
          
          <!-- 保存的文件 -->
          <el-descriptions-item label="保存的文件">
            <div v-if="Array.isArray(taskDetail.savedFiles) && taskDetail.savedFiles.length">
              <el-tag 
                v-for="file in taskDetail.savedFiles" 
                :key="file"
                class="file-tag"
                type="info"
              >
                {{ file }}
              </el-tag>
            </div>
            <span v-else>无</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="taskDetailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加代码详情对话框 -->
    <el-dialog
      v-model="codeDetailVisible"
      :title="`代码详情: ${codeDetail?.funcName || ''}`"
      width="800px"
      destroy-on-close
    >
      <div v-loading="codeDetailLoading">
        <el-descriptions v-if="codeDetail" :column="1" border>
          <el-descriptions-item label="代码ID">{{ codeDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="函数名称">{{ codeDetail.funcName }}</el-descriptions-item>
          <el-descriptions-item label="所属节点">
            <el-tag type="info" size="small">
              {{ codeDetail.node?.name || '未知节点' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getCodeStatusType(codeDetail.status)">
              {{ getCodeStatusText(codeDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(codeDetail.timeCreated) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(codeDetail.timeUpdated) }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ codeDetail.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="代码内容">
            <div class="code-content">
              <pre><code>{{ codeDetail.codeContent }}</code></pre>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="codeDetailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 状态详情对话框 -->
    <el-dialog
      v-model="statusDetailVisible"
      title="任务状态详情"
      width="550px"
      destroy-on-close
    >
      <div v-loading="statusDetailLoading" class="status-detail-custom">
        <template v-if="statusDetail">
          <!-- 状态信息 -->
          <div class="status-header">
            <div class="status-title">运行状态</div>
            <el-tag :type="getContainerStatusType(statusDetail.status)" class="status-tag">
              {{ getContainerStatusText(statusDetail.status) }}
            </el-tag>
          </div>
          
          <!-- 基本信息 -->
          <div class="info-section">
            <div class="info-group">
              <div class="info-item">
                <span class="info-label">任务ID：</span>
                <span class="info-value">{{ statusDetail.task_id }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">容器ID：</span>
                <el-tooltip :content="statusDetail.container_id" placement="top">
                  <span class="info-value">{{ ellipsis(statusDetail.container_id, 12) }}</span>
                </el-tooltip>
              </div>
            </div>
          </div>
          
          <!-- 运行信息 -->
          <div class="info-section">
            <div class="section-title">运行信息</div>
            <div class="info-group">
              <div class="info-item">
                <span class="info-label">停止码：</span>
                <span class="info-value">
                  <el-tag :type="getExitCodeType(statusDetail.exit_code)" v-if="statusDetail.exit_code !== null">
                    {{ statusDetail.exit_code }}
                  </el-tag>
                  <span v-else>未停止</span>
                </span>
              </div>
              <div class="info-item">
                <span class="info-label">错误信息：</span>
                <span class="info-value error-text" v-if="statusDetail.error">{{ statusDetail.error }}</span>
                <span class="info-value" v-else>无</span>
              </div>
            </div>
            
            <!-- 任务操作按钮 -->
            <div class="container-action-group">
              <el-button-group>
                <el-button 
                  type="success" 
                  size="small" 
                  @click="startTask({id: currentRowId})"
                  :disabled="statusDetail.status === 'running'"
                >
                  <el-icon><VideoPlay /></el-icon> 启动
                </el-button>
                <el-button 
                  type="warning" 
                  size="small" 
                  @click="stopTask({id: currentRowId})"
                  :disabled="statusDetail.status !== 'running'"
                >
                  <el-icon><VideoPause /></el-icon> 停止
                </el-button>
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="killTask({id: currentRowId})"
                  :disabled="statusDetail.status !== 'running'"
                >
                  <el-icon><CircleClose /></el-icon> 杀死
                </el-button>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="openServiceFromDetail()"
                  :disabled="statusDetail.status !== 'running' || !canAccessService()"
                >
                  <el-icon><Link /></el-icon> 访问服务
                </el-button>
                <el-button 
                  type="info" 
                  size="small" 
                  @click="requestReapproval({id: currentRowId.value})"
                >
                  <el-icon><Refresh /></el-icon> 申请重新审批
                </el-button>
              </el-button-group>
            </div>
          </div>
          
          <!-- 时间信息 -->
          <div class="info-section">
            <div class="section-title">时间信息</div>
            <div class="time-grid">
              <div class="time-item">
                <div class="time-label">创建时间</div>
                <div class="time-value">{{ formatDateTime(statusDetail.created_time) }}</div>
              </div>
              <div class="time-item">
                <div class="time-label">启动时间</div>
                <div class="time-value">{{ formatDateTime(statusDetail.start_time) }}</div>
              </div>
              <div class="time-item">
                <div class="time-label">停止时间</div>
                <div class="time-value">{{ formatDateTime(statusDetail.stopped_at) }}</div>
              </div>
              <div class="time-item">
                <div class="time-label">最后更新</div>
                <div class="time-value">{{ formatDateTime(statusDetail.last_update) }}</div>
              </div>
            </div>
          </div>
          
          <!-- 文件浏览与下载 -->
          <div class="info-section">
            <div class="section-title">文件管理</div>
            <div class="file-actions">
              <el-button type="primary" @click="browseFiles({id: currentRowId})" :disabled="!statusDetail.container_id">
                <el-icon><Folder /></el-icon> 浏览文件
              </el-button>
              <el-button type="success" @click="downloadFiles({id: currentRowId})" :disabled="!statusDetail.container_id">
                <el-icon><Download /></el-icon> 下载文件
              </el-button>
            </div>
            
            <!-- 文件列表 -->
            <div class="file-list" v-if="fileList.length > 0">
              <div class="section-subtitle">文件列表</div>
              <el-table :data="fileList" size="small" border style="width: 100%">
                <el-table-column prop="name" label="文件名" />
                <el-table-column prop="size" label="大小" width="100" />
                <el-table-column label="修改时间" width="180">
                  <template #default="scope">
                    {{ formatFileTime(scope.row.modified) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" align="center">
                  <template #default="scope">
                    <el-button size="small" type="primary" @click="downloadFile(scope.row)">
                      <el-icon><Download /></el-icon>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </template>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="statusDetailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 文件浏览对话框 -->
    <el-dialog
      v-model="fileBrowserVisible"
      title="文件浏览器"
      width="700px"
      destroy-on-close
    >
      <div v-loading="fileBrowserLoading" class="file-browser">
        <div class="file-browser-header">
          <div class="current-path">{{ currentPath || '/' }}</div>
          <el-button size="small" @click="navigateUp" :disabled="currentPath === ''">
            <el-icon><Back /></el-icon> 返回上级
          </el-button>
        </div>
        
        <el-table :data="browserFiles" size="small" border style="width: 100%">
          <el-table-column prop="name" label="名称">
            <template #default="scope">
              <div class="file-item" @click="navigateToFile(scope.row)">
                <el-icon class="file-icon">
                  <Folder v-if="scope.row.type === 'directory'" />
                  <Document v-else />
                </el-icon>
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" width="100" />
          <el-table-column label="修改时间" width="180">
            <template #default="scope">
              {{ formatFileTime(scope.row.modified) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button-group>
                <el-button 
                  size="small" 
                  type="primary" 
                  @click.stop="downloadBrowserFile(scope.row)"
                  :disabled="scope.row.type === 'directory'"
                >
                  <el-icon><Download /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  @click.stop="previewFile(scope.row)"
                  :disabled="scope.row.type === 'directory' || !isTextFile(scope.row.name)"
                >
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="filePreviewVisible"
      :title="`文件预览: ${previewFileName}`"
      width="700px"
      destroy-on-close
    >
      <div v-loading="filePreviewLoading" class="file-preview">
        <div class="preview-actions">
          <el-button type="primary" @click="downloadPreviewFile">
            <el-icon><Download /></el-icon> 下载文件
          </el-button>
          <el-button type="info" @click="copyPreviewContent">
            <el-icon><DocumentCopy /></el-icon> 复制内容
          </el-button>
        </div>
        <div class="preview-content">
          <pre>{{ filePreviewContent }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  List,
  Box,
  Document,
  View,
  Plus,
  Delete,
  Refresh,
  VideoPlay,
  VideoPause,
  CircleClose,
  Download,
  Folder,
  Back,
  Link,
  DocumentCopy, // 添加复制图标
  ZoomIn,      // 添加预览图标
} from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import service from '~/axios'
import { ElLoading } from 'element-plus'

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 任务详情相关
const taskDetailVisible = ref(false)
const taskDetailLoading = ref(false)
const taskDetail = ref(null)

// 代码详情相关
const codeDetailVisible = ref(false)
const codeDetailLoading = ref(false)
const codeDetail = ref(null)

// 状态详情相关
const statusDetailVisible = ref(false)
const statusDetailLoading = ref(false)
const statusDetail = ref(null)

// 添加选中行的数据
const selectedIds = ref([])

// 文件管理相关
const fileList = ref([])
const fileBrowserVisible = ref(false)
const fileBrowserLoading = ref(false)
const browserFiles = ref([])
const currentPath = ref('')

// 文件预览相关
const filePreviewVisible = ref(false)
const filePreviewLoading = ref(false)
const filePreviewContent = ref('')
const previewFileName = ref('')

// 当前选中的行ID
const currentRowId = ref(null)

// 获取任务列表
const fetchTasks = async (page = 1) => {
  loading.value = true
  try {
    const response = await service.get('/api/v1.0/sys/application/tasks', {
      params: {
        page: page,
        size: pageSize.value
      }
    })
    if (response.code === 10000) {
      tableData.value = response.data.tasks || []
      tableData.value.forEach(task => {
        task.createTime = task.timeCreated
        task.updateTime = task.timeUpdated
        // 强化 savedFiles 解析
        if (task.savedFiles) {
          try {
            if (typeof task.savedFiles === 'string' && task.savedFiles.trim().startsWith('[')) {
              task.savedFiles = JSON.parse(task.savedFiles)
            }
            if (!Array.isArray(task.savedFiles)) {
              task.savedFiles = []
            }
          } catch (e) {
            task.savedFiles = []
          }
        } else {
          task.savedFiles = []
        }
      })
      total.value = response.data.pagination.total
      pageSize.value = response.data.pagination.size
    }
  } catch (error) {
    console.error('获取部署任务列表失败:', error)
    toast('错误', '获取部署任务列表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',    // 待审批
    'APPROVED': 'success',   // 已批准
    'REJECTED': 'danger',    // 已拒绝
    'DEPLOYING': 'info',     // 部署中
    'COMPLETED': 'success',  // 已完成
    'FAILED': 'danger'       // 失败
  };
  return statusMap[status] || 'info';
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已拒绝',
    'DEPLOYING': '部署中',
    'COMPLETED': '已完成',
    'finished': '已结束',
    'running': '运行中',
    'created': '已创建',
    'exited': '已停止',
    'killed': '已杀死',
    'kill_failed': '杀死失败',
  };
  return statusMap[status] || status;
}

// 格式化日期时间
const formatDateTime = (val) => {
  if (!val) return '无'
  const date = typeof val === 'string' ? new Date(val.replace(/-/g, '/')) : new Date(val)
  if (isNaN(date.getTime())) return val
  return date.toLocaleString('zh-CN', { hour12: false })
}

// 格式化文件修改时间（处理科学计数法格式的时间戳）
const formatFileTime = (timestamp) => {
  if (!timestamp) return '无'
  
  try {
    // 处理科学计数法格式的时间戳 (1.7474714980392427E9)
    const milliseconds = parseFloat(timestamp) * 1000
    if (!isNaN(milliseconds)) {
      const date = new Date(milliseconds)
      if (!isNaN(date.getTime())) {
        return date.toLocaleString('zh-CN', { hour12: false })
      }
    }
    
    // 如果不是时间戳格式，尝试普通日期转换
    return formatDateTime(timestamp)
  } catch (e) {
    console.error('文件时间格式化错误:', e, timestamp)
    return String(timestamp)
  }
}

// 查看任务详情
const viewTaskDetail = async (row) => {
  taskDetailVisible.value = true
  taskDetailLoading.value = true
  currentRowId.value = row.id // 保存当前行的ID
  try {
    const response = await service.get(`/api/v1.0/sys/application/task/${row.id}`)
    if (response.code === 10000) {
      const detail = response.data
      // 解析 savedFiles
      if (detail.savedFiles) {
        try {
          if (typeof detail.savedFiles === 'string' && detail.savedFiles.trim().startsWith('[')) {
            detail.savedFiles = JSON.parse(detail.savedFiles)
          }
          if (!Array.isArray(detail.savedFiles)) {
            detail.savedFiles = []
          }
        } catch (e) {
          detail.savedFiles = []
        }
      } else {
        detail.savedFiles = []
      }
      taskDetail.value = detail
    } else {
      toast('错误', response.message || '获取任务详情失败', 'error')
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    toast('错误', '获取任务详情失败', 'error')
  } finally {
    taskDetailLoading.value = false
  }
}

// 获取代码状态类型标签样式
const getCodeStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取代码状态文本
const getCodeStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已拒绝'
  }
  return statusMap[status] || status
}


// 跳转到创建任务页面
const goToCreateTask = () => {
  router.push('/application/deploy')
}

// 页码变化
const handleCurrentChange = (val) => {
  fetchTasks(val)
}

// 每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchTasks(1)
}

// 添加处理选择变化的方法
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.id)
}

// 添加删除方法
const handleDelete = async (row) => {
  try {
    await showModal(`确定要删除部署任务 "${row.name}" 吗？`, 'warning', '提示')
    const response = await service.delete(`/api/v1.0/sys/application/task/${row.id}`)
    
    if (response.code === 10000) {
      toast('成功', '删除成功')
      fetchTasks(currentPage.value)
    } else {
      toast('错误', response.message || '删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error)
      toast('错误', '删除失败', 'error')
    }
  }
}

// 添加批量删除方法
const handleBatchDelete = async () => {
  if (selectedIds.value.length === 0) {
    toast('警告', '请选择要删除的任务', 'warning')
    return
  }
  
  try {
    await showModal(`确定要删除选中的 ${selectedIds.value.length} 个任务吗？`, 'warning', '提示')
    
    const response = await service.delete('/api/v1.0/sys/application/tasks', {
      data: selectedIds.value
    })
    
    if (response.code === 10000) {
      toast('成功', '批量删除成功')
      selectedIds.value = []
      fetchTasks(currentPage.value)
    } else {
      toast('错误', response.message || '批量删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除任务失败:', error)
      toast('错误', '批量删除失败', 'error')
    }
  }
}

// 判断任务是否在运行
const isTaskRunning = (row) => {
  // 检查任务状态
  return row.status === 'running' || row.containerStatus === 'running'
}

// 获取任务状态类型
const getContainerStatusType = (status) => {
  const statusMap = {
    'created': 'info',
    'running': 'success',
    'exited': 'warning',
    'killed': 'danger',
    'kill_failed': 'danger',
    'dead': 'danger',
    'paused': 'info',
    'cancelled': 'info',
    'not_found': 'warning',
    'stopped': 'warning',
    'finished': 'success',
    'launching': 'info',
    'building image': 'info',
    'launching container': 'info',
    'launch_failed': 'danger',
    'queued': 'info',
    'error': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取任务状态文本
const getContainerStatusText = (status) => {
  const statusMap = {
    'created': '已创建',
    'running': '运行中',
    'exited': '已停止',
    'killed': '已杀死',
    'kill_failed': '杀死失败',
    'dead': '已终止',
    'paused': '已暂停',
    'cancelled': '已取消',
    'not_found': '未找到',
    'stopped': '已停止',
    'finished': '已完成',
    'launching': '启动中',
    'building image': '构建镜像中',
    'launching container': '启动容器中',
    'launch_failed': '启动失败',
    'queued': '队列中',
    'error': '错误'
  }
  return statusMap[status] || status
}

// 获取退出码类型
const getExitCodeType = (code) => {
  if (code === null) return 'info'
  if (code === 0) return 'success'
  if (code === 137) return 'warning' // SIGKILL
  return 'danger'
}

// 刷新任务状态
const refreshTaskStatus = async (row) => {
  statusDetailLoading.value = true
  statusDetailVisible.value = true
  currentRowId.value = row.id // 保存当前行的ID
  
  try {
    const response = await service.get(`/api/v1.0/sys/application/status/${row.id}`)
    
    if (response.code === 10000) {
      statusDetail.value = response.data
      // 不再更新表格中的状态
      // Object.assign(row, {
      //   status: response.data.status,
      //   updateTime: response.data.last_update
      // })
      toast('成功', '任务状态已刷新', 'success')
    } else {
      toast('错误', response.message || '刷新任务状态失败', 'error')
    }
  } catch (error) {
    console.error('刷新任务状态失败:', error)
    toast('错误', '刷新任务状态失败', 'error')
  } finally {
    statusDetailLoading.value = false
  }
}

// 容器ID省略显示
const ellipsis = (str, len = 8) => {
  if (!str) return '无'
  if (str.length <= len * 2) return str
  return str.slice(0, len) + '...' + str.slice(-len)
}

// 启动任务
const startTask = async (row) => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: '正在启动任务...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    const response = await service.post(`/api/v1.0/sys/application/execute/${row.id}`)
    
    if (response.code === 10000) {
      toast('成功', '任务已启动', 'success')
      // 刷新状态
      await refreshTaskStatus(row)
    } else {
      toast('错误', response.message || '启动任务失败', 'error')
    }
    
    loading.close()
  } catch (error) {
    console.error('启动任务失败:', error)
    toast('错误', '启动任务失败', 'error')
  }
}

// 停止任务
const stopTask = async (row) => {
  try {
    await showModal('确定要停止此任务吗？', 'warning', '提示')
    
    const loading = ElLoading.service({
      lock: true,
      text: '正在停止任务...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    const response = await service.post(`/api/v1.0/sys/application/stop/${row.id}`)
    
    if (response.code === 10000) {
      toast('成功', '任务已停止', 'success')
      // 刷新状态
      await refreshTaskStatus(row)
    } else {
      toast('错误', response.message || '停止任务失败', 'error')
    }
    
    loading.close()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止任务失败:', error)
      toast('错误', '停止任务失败', 'error')
    }
  }
}

// 杀死任务
const killTask = async (row) => {
  try {
    await showModal('确定要强制杀死此任务吗？这可能导致数据丢失！', 'warning', '警告')
    
    const loading = ElLoading.service({
      lock: true,
      text: '正在杀死任务...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    const response = await service.post(`/api/v1.0/sys/application/kill/${row.id}`)
    
    if (response.code === 10000) {
      toast('成功', '任务已杀死', 'success')
      // 刷新状态
      await refreshTaskStatus(row)
    } else {
      toast('错误', response.message || '杀死任务失败', 'error')
    }
    
    loading.close()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('杀死任务失败:', error)
      toast('错误', '杀死任务失败', 'error')
    }
  }
}

// 浏览文件
const browseFiles = async (row) => {
  fileBrowserVisible.value = true
  fileBrowserLoading.value = true
  currentPath.value = ''
  
  try {
    await fetchDirectoryContents('')
  } catch (error) {
    console.error('获取文件列表失败:', error)
    toast('错误', '获取文件列表失败', 'error')
  } finally {
    fileBrowserLoading.value = false
  }
}

// 获取目录内容
const fetchDirectoryContents = async (path) => {
  fileBrowserLoading.value = true
  try {
    const response = await service.get(`/api/v1.0/sys/application/browse/${currentRowId.value}`, {
      params: { path: path || '' }
    })
    
    if (response.code === 10000) {
      // 处理API返回的数据结构
      const data = response.data
      if (data.children && Array.isArray(data.children)) {
        browserFiles.value = data.children
      } else if (data.type === 'directory' && data.children && Array.isArray(data.children)) {
        browserFiles.value = data.children
      } else {
        browserFiles.value = []
      }
      currentPath.value = path
    } else {
      toast('错误', response.message || '获取目录内容失败', 'error')
    }
  } catch (error) {
    console.error('获取目录内容失败:', error)
    toast('错误', '获取目录内容失败', 'error')
  } finally {
    fileBrowserLoading.value = false
  }
}

// 导航到文件或目录
const navigateToFile = (file) => {
  if (file.type === 'directory') {
    const newPath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name)
    fetchDirectoryContents(newPath)
  } else {
    // 判断是否是文本文件
    if (isTextFile(file.name)) {
      previewFile(file)
    } else {
      downloadBrowserFile(file)
    }
  }
}

// 判断是否是文本文件
const isTextFile = (filename) => {
  const textExtensions = ['.txt', '.py', '.js', '.html', '.css', '.json', '.md', '.xml', '.csv', '.log', '.ini', '.conf', '.sh', '.bat', '.yaml', '.yml', '.env']
  const ext = '.' + filename.split('.').pop().toLowerCase()
  return textExtensions.includes(ext)
}

// 预览文件
const previewFile = async (file) => {
  filePreviewLoading.value = true
  filePreviewVisible.value = true
  previewFileName.value = file.name
  
  try {
    const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name)
    
    // 注意：需要在后端实现该接口，返回文件内容文本
    // GET /api/v1.0/sys/model/deploy/preview/:taskId?filePath=xxxx
    const response = await service.get(`/api/v1.0/sys/application/preview/${currentRowId.value}`, {
      params: { filePath },
      responseType: 'text'
    })
    
    filePreviewContent.value = response
  } catch (error) {
    console.error('预览文件失败:', error)
    toast('错误', `预览文件失败: ${error.message}`, 'error')
    filePreviewContent.value = '文件内容加载失败'
  } finally {
    filePreviewLoading.value = false
  }
}

// 下载当前预览的文件
const downloadPreviewFile = () => {
  const filePath = currentPath.value ? `${currentPath.value}/${previewFileName.value}` : previewFileName.value
  downloadSpecificFile(currentRowId.value, filePath)
}

// 复制预览内容到剪贴板
const copyPreviewContent = async () => {
  try {
    // 确保内容是字符串格式
    let contentToCopy = filePreviewContent.value
    if (typeof contentToCopy === 'object') {
      contentToCopy = JSON.stringify(contentToCopy, null, 2)
    } else if (contentToCopy === null || contentToCopy === undefined) {
      contentToCopy = ''
    } else {
      contentToCopy = String(contentToCopy)
    }

    await navigator.clipboard.writeText(contentToCopy)
    toast('成功', '内容已复制到剪贴板', 'success')
  } catch (error) {
    console.error('复制失败:', error)
    toast('错误', '复制失败', 'error')
  }
}

// 导航到上级目录
const navigateUp = () => {
  if (!currentPath.value) return
  
  const pathParts = currentPath.value.split('/')
  pathParts.pop()
  const newPath = pathParts.join('/')
  fetchDirectoryContents(newPath)
}

// 下载文件
const downloadBrowserFile = (file) => {
  if (file.type === 'directory') return
  
  const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name)
  downloadSpecificFile(currentRowId.value, filePath)
}

// 下载文件列表
const downloadFiles = async (row) => {
  fileList.value = []
  fileBrowserLoading.value = true
  
  try {
    const response = await service.get(`/api/v1.0/sys/application/browse/${row.id}`)
    
    if (response.code === 10000) {
      // 处理API返回的数据结构
      const data = response.data
      if (data.children && Array.isArray(data.children)) {
        // 收集所有非目录文件
        const collectFiles = (items) => {
          let result = []
          for (const item of items) {
            if (item.type === 'file') {
              result.push({
                name: item.name,
                path: item.path,
                size: item.size
              })
            } else if (item.type === 'directory' && item.children) {
              result = result.concat(collectFiles(item.children))
            }
          }
          return result
        }
        
        fileList.value = collectFiles(data.children)
      } else {
        fileList.value = []
      }
    } else {
      toast('错误', response.message || '获取文件列表失败', 'error')
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    toast('错误', '获取文件列表失败', 'error')
  } finally {
    fileBrowserLoading.value = false
  }
}

// 下载特定文件
const downloadFile = (file) => {
  const filePath = file.path || file.name
  downloadSpecificFile(currentRowId.value, filePath)
}

// 下载文件的通用方法
const downloadSpecificFile = async (taskId, filePath) => {
  try {
    // 显示加载动画
    const loading = ElLoading.service({
      lock: true,
      text: '正在下载文件...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    // 获取文件名
    const filename = filePath.split('/').pop() || 'downloaded_file'
    
    // 使用service发送请求
    const response = await service.get(`/api/v1.0/sys/application/download/${taskId}`, {
      params: { filePath },
      responseType: 'blob'
    })
    
    // 创建 Blob URL
    const blob = new Blob([response])
    const url = window.URL.createObjectURL(blob)
    
    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }, 100)
    
    // 关闭加载动画
    loading.close()
    
    toast('成功', `文件 ${filename} 下载成功`, 'success')
  } catch (error) {
    console.error('下载文件失败:', error)
    toast('错误', `下载文件失败: ${error.message}`, 'error')
  }
}

// 打开服务链接
const openService = (row) => {
  if (!row || !row.node) return
  
  // 从任务对象中获取服务链接所需信息
  const ipAddress = row.node.ipAddress
  const port = row.node.port
  const taskId = row.taskId
  
  // 检查必要信息是否存在
  if (!ipAddress || !port || !taskId) {
    toast('错误', '无法获取服务信息', 'error')
    return
  }
  
  // 构建服务URL - 直接使用taskId作为服务路径
  const serviceUrl = `http://${ipAddress}:${port}/service/${taskId}/`
  
  // 在新窗口打开链接
  window.open(serviceUrl, '_blank')
}

// 从详情对话框打开服务
const openServiceFromDetail = () => {
  // 获取当前选中的任务
  const row = tableData.value.find(item => item.id === currentRowId.value)
  if (row && row.node && row.taskId) {
    openService(row)
  } else if (taskDetail.value && taskDetail.value.node && taskDetail.value.taskId) {
    // 使用任务详情的数据
    const ipAddress = taskDetail.value.node.ipAddress
    const port = taskDetail.value.node.port
    const taskId = taskDetail.value.taskId
    
    // 构建服务URL - 直接使用taskId作为服务路径
    const serviceUrl = `http://${ipAddress}:${port}/service/${taskId}/`
    
    // 在新窗口打开链接
    window.open(serviceUrl, '_blank')
  } else {
    toast('错误', '无法获取服务信息', 'error')
  }
}

// 检查服务是否可用
const isServiceAvailable = (row) => {
  return isTaskRunning(row) && row.node && row.node.ipAddress && row.node.port && row.taskId
}

// 检查当前选中的任务是否可以访问服务
const canAccessService = () => {
  // 首先检查表格中的任务数据
  const row = tableData.value.find(item => item.id === currentRowId.value)
  if (row && row.node && row.taskId) {
    return true
  }
  
  // 如果表格中没有数据，检查任务详情
  if (taskDetail.value && taskDetail.value.node && taskDetail.value.taskId) {
    return true
  }
  
  return false
}

// 申请重新审批
const requestReapproval = async (row) => {
  try {
    await showModal('确定要申请重新审批吗？', 'warning', '提示')
    
    const response = await service.post(`/api/v1.0/sys/application/reapproval/${row.id}`)
    
    if (response.code === 10000) {
      toast('成功', '申请已提交', 'success')
      // 刷新状态
      await refreshTaskStatus(row)
    } else {
      toast('错误', response.message || '申请失败', 'error')
    }
  } catch (error) {
    console.error('申请重新审批失败:', error)
    toast('错误', '申请重新审批失败', 'error')
  }
}

onMounted(() => {
  fetchTasks()
})
</script>

<style scoped>
.task-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

:deep(.el-table th) {
  font-weight: bold;
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

:deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

.file-icon {
  margin-right: 5px;
  font-size: 16px;
}

.code-tag {
  margin-right: 8px;
  margin-bottom: 5px;
  transition: all 0.3s;
}

.code-tag:hover {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

/* 添加代码内容样式 */
.code-content {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
}

.code-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
}

/* 深色模式下的代码内容样式 */
html.dark .code-content {
  background-color: #1e1e1e;
  color: #e6e6e6;
}

.header-right {
  display: flex;
  align-items: center;
  margin-left: 20px;
  
  .el-button {
    font-size: 15px;
    padding: 10px 20px;
    margin-left: 10px;
  }
  
  .el-icon {
    margin-right: 5px;
    font-size: 16px;
  }
}

.header-left {
  flex: 1;
  /* 其他现有样式... */
}

.file-tag {
  margin: 2px;
  font-family: monospace;
}

:deep(.el-descriptions__label) {
  width: 120px;
  font-weight: bold;
}

:deep(.el-descriptions__content) {
  padding: 12px 20px;
}

:deep(.el-descriptions__content p) {
  margin: 4px 0;
}

/* 状态详情弹窗样式 */
.status-detail-custom {
  padding: 10px 0;
  color: var(--el-text-color-primary);
}

.status-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.status-title {
  font-size: 16px;
  font-weight: bold;
}

.status-tag {
  font-size: 16px;
  font-weight: bold;
  padding: 6px 12px;
}

.info-section {
  margin-bottom: 20px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  padding: 15px;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--el-text-color-secondary);
  position: relative;
  padding-left: 10px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  height: 14px;
  width: 3px;
  background-color: var(--el-color-primary);
  border-radius: 1px;
}

.info-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  width: 80px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
}

.info-value {
  word-break: break-all;
}

.error-text {
  color: var(--el-color-danger);
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.time-item {
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.time-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.time-value {
  font-weight: 500;
}

/* 深色模式样式适配 */
html.dark .info-section {
  background-color: var(--el-bg-color);
}

html.dark .time-item {
  background-color: var(--el-bg-color-overlay);
}

/* 任务操作相关样式 */
.container-actions {
  margin-top: 8px;
}

.container-action-group {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

/* 文件浏览相关样式 */
.file-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.file-list {
  margin-top: 15px;
}

.section-subtitle {
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--el-text-color-secondary);
}

.file-browser-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.current-path {
  font-family: monospace;
  font-weight: 500;
  padding: 4px 8px;
  background-color: var(--el-fill-color);
  border-radius: 4px;
}

.file-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.file-item:hover {
  color: var(--el-color-primary);
}

.file-icon {
  margin-right: 8px;
}

.mt-2 {
  margin-top: 8px;
}

/* 文件预览相关样式 */
.file-preview {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
}

.preview-actions {
  margin-bottom: 10px;
}

.preview-content {
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
}

/* 深色模式下的文件预览样式 */
html.dark .file-preview {
  background-color: #1e1e1e;
  color: #e6e6e6;
}
</style> 