package ouc.isclab.node.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;
import ouc.isclab.dataset.entity.DatasetEntity;

import java.util.HashSet;
import java.util.Set;

/**
 * @desc 节点实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_NODE",
        uniqueConstraints = @UniqueConstraint(columnNames = {"ipAddress", "port"})) // 添加复合唯一约束
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class NodeEntity extends BaseEntity {

    @Column(nullable = false)
    private String name; // 节点名称

    @Column(nullable = false)
    private String ipAddress; // 节点IP地址

    @Column(nullable = false)
    private int port; // 节点端口

    @Lob
    @Column(columnDefinition = "text")
    private String description; // 节点描述

    @Column(name = "creator_id", nullable = false)
    private Long creatorId; // 节点创建者ID

    @Column(name = "node_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private NodeType nodeType; // 节点类型

    @ManyToMany(mappedBy = "availableNodes", fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<DatasetEntity> datasets = new HashSet<>();

    public enum NodeType {
        Sycee,  // Sycee类型节点
        Pyxis   // Pyxis类型节点
    }

    public Set<DatasetEntity> getDatasets() {
        return datasets;
    }

    public void setDatasets(Set<DatasetEntity> datasets) {
        this.datasets = datasets;
    }

}
