package ouc.isclab.model.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import ouc.isclab.model.entity.ModelEntity;
import ouc.isclab.model.entity.ModelEntity.ModelStatus;
import ouc.isclab.task.entity.TaskEntity;

import java.util.List;

public interface ModelRepository extends JpaRepository<ModelEntity, Long> {

    // 查询用户创建的所有模型
    Page<ModelEntity> findByCreatorId(Long creatorId, Pageable pageable);
    
    // 根据关键词搜索用户的模型
    @Query("SELECT m FROM ModelEntity m WHERE m.creatorId = :creatorId AND (m.name LIKE CONCAT('%', :keyword, '%') OR m.description LIKE CONCAT('%', :keyword, '%'))")
    Page<ModelEntity> findByCreatorIdAndKeyword(@Param("creatorId") Long creatorId, @Param("keyword") String keyword, Pageable pageable);

    // 根据任务ID查询模型
    List<ModelEntity> findByTaskId(Long taskId);

    // 根据状态统计模型数量
    long countByStatus(ModelStatus status);
} 