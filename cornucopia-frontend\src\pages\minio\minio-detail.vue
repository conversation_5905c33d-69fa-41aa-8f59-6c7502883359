<template>
  <div class="minio-detail-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><View /></el-icon>
          <h2>MinIO 配置详情</h2>
        </div>
        <div class="sub-title">查看 MinIO 存储配置的详细信息</div>
      </div>
      
      <div class="header-right">
        <el-button type="primary" @click="handleBack" plain round>
          <el-icon><ArrowLeft /></el-icon>返回列表
        </el-button>
      </div>
    </div>

    <el-empty 
      v-if="!route.query.id" 
      description="未找到配置ID" 
      :image-size="200"
    >
      <el-button type="primary" @click="$router.push('/minio/list')" round>
        返回配置列表
      </el-button>
    </el-empty>

    <el-card v-else class="detail-card" shadow="hover" v-loading="loading">
      <div class="detail-content">
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="配置名称">{{ detail.name }}</el-descriptions-item>
            <el-descriptions-item label="服务地址">{{ detail.endpoint }}</el-descriptions-item>
            <el-descriptions-item label="存储桶">{{ detail.bucket }}</el-descriptions-item>
            <el-descriptions-item label="类型">
              <el-tag :type="detail.type === 'DATASET' ? 'success' : 'warning'" size="small">
                {{ detail.type === 'DATASET' ? '数据集' : '模型' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="detail.active ? 'success' : 'danger'" size="small">
                {{ detail.active ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDateTime(detail.timeCreated) }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h3 class="section-title">认证信息</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="Access Key">{{ detail.accessKey }}</el-descriptions-item>
            <el-descriptions-item label="Secret Key">{{ detail.secretKey }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { View, ArrowLeft } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const detail = ref({})


// 获取配置详情
const fetchDetail = async () => {
  loading.value = true
  try {
    const response = await service.get(`/api/v1.0/sys/storage/minio/config/${route.query.id}`)
    if (response.code === 10000) {
      detail.value = response.data
    }
  } catch (error) {
    console.error('获取配置详情失败:', error)
    toast('错误', '获取配置详情失败', 'error')
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatDateTime = (date) => {
  if (!date) return '-'
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}


// 返回列表
const handleBack = () => {
  router.push('/minio/list')
}

onMounted(() => {
  if (route.query.id) {
    fetchDetail()
  } else {
    toast('错误', '缺少配置ID', 'error')
  }
})
</script>

<style scoped>
.minio-detail-container {
  padding: 20px;
}

.detail-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.detail-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.detail-content {
  padding: 20px;
}

.detail-section {
  margin-bottom: 30px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  margin: 0 0 16px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  width: 120px;
  font-weight: 500;
}

:deep(.el-descriptions__content) {
  color: var(--el-text-color-regular);
}

/* 深色模式样式 */
html.dark .detail-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .detail-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

html.dark .section-title {
  color: var(--el-text-color-primary);
}
</style> 