package ouc.isclab.auth.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.auth.repository.UserAuthRepository;

@Service
public class UserAuthService {
    @Autowired
    private UserAuthRepository userAuthRepository;

    public UserEntity validate(String username, String password) {
        UserEntity userEntity = userAuthRepository.findByUsername(username);
        
        // 检查用户是否存在
        if (userEntity == null) {
            throw new BaseException(ResponseCode.LOGIN_FAILED, "用户名未注册");
        }
        
        // 检查用户状态
        if (!userEntity.isEnable()) {
            throw new BaseException(ResponseCode.LOGIN_FAILED, "账号已被禁用，请联系管理员");
        }
        
        // 验证密码
        if (userEntity.getPassword().equals(password)) {
            return userEntity;
        }
        
        return null;
    }

}
