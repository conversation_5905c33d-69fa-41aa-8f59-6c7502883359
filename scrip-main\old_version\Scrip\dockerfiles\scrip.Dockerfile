FROM ubuntu:latest


ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone


RUN apt-get update && apt-get install -y --no-install-recommends \
    python3 \
    python3-pip \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*


RUN pip3 install --no-cache-dir  \
    torch \
    torchvision \
    flask \
    werkzeug \
    numpy \
    pillow \
    minio

WORKDIR /app
COPY scrip.py .
COPY templates/ ./templates/

ENV PYTHONUNBUFFERED=1
ENV OMP_NUM_THREADS=1  

EXPOSE 5000

ENTRYPOINT ["python3", "scrip.py"]