import { 
    createRouter,
    createWebHashHistory
} from 'vue-router'

import Home from "~/layouts/home.vue";
import Index from '~/pages/index.vue'
import Login from '~/pages/login.vue'
import NotFound from '~/pages/404.vue'
import Register from '~/pages/register.vue'
import Node from '~/layouts/node.vue'
import NodeIndex from '~/pages/node/index.vue'
import NodeList from '~/pages/node/node-list.vue'
import NodeRegister from '~/pages/node/node-register.vue'
import NodeUpdate from '~/pages/node/node-update.vue'
import NodeDetail from '~/pages/node/node-detail.vue'
import NodeResourceList from '~/pages/node/nodeResource-list.vue'
import NodeResourceDetail from '~/pages/node/nodeResource-detail.vue'
import NodeResourceUpdate from '~/pages/node/nodeResource-update.vue'
import NodePyxis from '~/pages/node/node-mangement-pyxis.vue'
import User from '~/layouts/user.vue'
import UserIndex from '~/pages/user/index.vue'
import UserList from '~/pages/user/user-list.vue'
import UserCreate from '~/pages/user/user-create.vue'
import UserUpdate from '~/pages/user/user-update.vue'
import RoleList from '~/pages/user/role-list.vue'
import RoleCreate from '~/pages/user/role-create.vue'
import RoleUpdate from '~/pages/user/role-update.vue'
import RoleDetail from '~/pages/user/role-detail.vue'
import UserDetail from '~/pages/user/user-detail.vue'
import My from '~/pages/my.vue'
import MyUpdate from '~/pages/my/update.vue'
import MyPassword from '~/pages/my/password.vue'
import LogList from '~/pages/log/log-list.vue'
import RolePermission from '~/pages/user/role-permission.vue'
import RoleAssign from '~/pages/user/role-assign.vue'
import NodeSycee from '~/pages/node/node-mangement.vue'
import NodeMy from '~/pages/node/node-my.vue'
import Data from '~/layouts/data.vue'
import MinioIndex from '~/pages/data/minio-index.vue'
import MinioList from '~/pages/data/minio-list.vue'  
import MinioConfigList from '~/pages/minio/minio-list.vue'
import MinioConfigCreate from '~/pages/minio/minio-config-create.vue'
import MinioConfigUpdate from '~/pages/minio/minio-config-update.vue'
import MinioConfigDetail from '~/pages/minio/minio-detail.vue'
import DataList from '~/pages/data/data-list.vue'
import DataMy from '~/pages/data/data-my.vue'
import DataIndex from '~/pages/data/index.vue'
import Task from '~/layouts/task.vue'
import TaskMy from '~/pages/task/task-my.vue'
import TaskCreate from '~/pages/task/task-create.vue'
import NodePending from '~/pages/task/node/node-pending.vue'
import NodeRequest from '~/pages/task/node/node-request.vue'
import DatasetPending from '~/pages/task/dataset/dataset-pending.vue'
import DatasetRequest from '~/pages/task/dataset/dataset-request.vue'
import ResourceRequest from '~/pages/task/resource-request.vue'
import ResourceMy from '~/pages/task/resource-my.vue'
import CodePending from '~/pages/task/code/code-pending.vue'
import CodeMy from '~/pages/task/code/code-my.vue'
import TaskIndex from '~/pages/task/index.vue'
import Model from '~/layouts/model.vue'
import ModelIndex from '~/pages/model/index.vue'
import ModelMy from '~/pages/model/model-my.vue'
import ModelFolder from '~/pages/model/model-folder.vue'
import MinioConfig from '~/pages/data/minio-mangement.vue'
import Minio from '~/layouts/minio.vue'
import MinioBucketList from '~/pages/minio/minio-bucket-list.vue'
// import ModelDeploy from '~/pages/model/model-deploy.vue'
// import ModelDeployTask from '~/pages/model/model-deploy-task.vue'
// import ModelDeployPending from '~/pages/model/model-deploy-pending.vue'
import Application from '~/layouts/application.vue'
import ApplicationIndex from '~/pages/application/index.vue'
import ApplicationDeploy from '~/pages/application/application-deploy.vue'
import ApplicationDeployTask from '~/pages/application/application-deploy-task.vue'
import ApplicationDeployPending from '~/pages/application/application-deploy-pending.vue'

const routes = [
    {
        path:"/",
        component:Home,
        // 子路由
        children:[{
            path:"",
            component:Index,
            meta:{
                title:"后台首页"
            }
        },
        {
            path:"my",
            component:My,
            meta:{
                title:"我的"
            }
        },
        {
            path:"my/update",
            component:MyUpdate,
            meta:{
                title:"修改个人信息"
            }
        },
        {
            path:"my/password",
            component:MyPassword,
            meta:{
                title:"修改密码"
            }
        },
        {
            path:"logs",
            component:LogList,
            meta:{
                title:"系统日志",
                permissions: ['menu:log']
            }
        },]
    },
    {
        path:"/minio",
        component:Minio,
        meta: {
            title: "存储服务配置",
            permissions: ['menu:data:minio']
        },
        children: [
            {
                path: "",
                component: MinioIndex,
                meta: {
                    title: "MinIO 总览"
                }
            },
            {
                path: "list",
                component: MinioConfigList,
                meta: {
                    title: "MinIO 配置列表"
                }
            },
            {
                path: "bucket",
                component: MinioBucketList,
                meta: {
                    title: "MinIO 桶列表"
                }
            },
            {
                path: "create",
                component: MinioConfigCreate,
                meta: {
                    title: "创建 MinIO 配置"
                }
            },
            {
                path: "update",
                component: MinioConfigUpdate,
                meta: {
                    title: "编辑 MinIO 配置"
                }
            },
            {
                path: "detail",
                component: MinioConfigDetail,
                meta: {
                    title: "MinIO 配置详情"
                }
            }
        ]
    },
    {
        path:"/node",
        component:Node,
        meta:{
            permissions: ['menu:node']
        },
        // 子路由
        children:[{
            path:"",
            component:NodeIndex,
            meta:{
                title:"节点管理"
            }
        },
        {
            path:"list",
            component:NodeList,
            meta:{
                title:"查看所有节点"
            }
        },
        {
            path:"my",
            component:NodeMy,
            meta:{
                permissions: ['menu:node:my'],
                title:"我的节点"
            }
        },
        {
            path:"sycee",
            component:NodeSycee,
            meta:{
                permissions: ['menu:node:my'],
                title:"操作节点"
            }
        },
        {
            path:"pyxis",
            component:NodePyxis,
            meta:{
                permissions: ['menu:node:my'],
                title:"Pyxis节点管理"
            }
        },
        {
            path:"register",
            component:NodeRegister,
            meta:{
                permissions: ['menu:node:my'],
                title:"注册节点"
            }
        },
        {
            path:"update",
            component:NodeUpdate,
            meta:{
                permissions: ['menu:node:my'],
                title:"更新节点资料"
            }
        },
        {
            path:"detail",
            component:NodeDetail,
            meta:{
                title:"节点详情"
            }
        },
        {
            path:"resource/list",
            component:NodeResourceList,
            meta:{
                title:"节点资源列表"
            }
        },
        {
            path:"resource/detail",
            component:NodeResourceDetail,
            meta:{
                title:"节点资源详情"
            }
        },
        {
            path:"resource/update",
            component:NodeResourceUpdate,
            meta:{
                title:"更新节点资源"
            }   
        }
        
    ]
    },
{
    path:"/login",
    component:Login,
    meta:{
        title:"登录页"
    }
},{ 
    path: '/:pathMatch(.*)*', 
    name: 'NotFound', 
    component: NotFound 
},
{
    path:"/register",
    component: Register,
    meta:{
        title:"注册页"
    }
},
{
    path: "/user",
    component: User,
    meta:{
        permissions: ['menu:user']
    },
    children: [
        {
            path: "",
            component: UserIndex,
            meta: {
                title: "用户总览"
            }
        },
        {
            path: "list",
            component: UserList,
            meta: {
                title: "用户列表"
            }
        },
        {
            path: "create",
            component: UserCreate,
            meta: {
                title: "创建用户"
            }
        },
        {
            path: "update",
            component: UserUpdate,
            meta: {
                title: "更新用户"
            }
        },
        {
            path: "role/list",
            component: RoleList,
            meta: {
                title: "角色列表"
            }
        },
        {
            path: "role/create",
            component: RoleCreate,
            meta: {
                title: "创建角色"
            }
        },
        {
            path: "role/update",
            component: RoleUpdate,
            meta: {
                title: "更新角色"
            }
        },
        {
            path: "role/detail",
            component: RoleDetail,
            meta: {
                title: "角色详情"
            }
        },
        {
            path: "detail",
            component: UserDetail,
            meta: {
                title: "用户详情"
            }
        },
        {
            path: "role/permission",
            component: RolePermission,
            meta: {
                title: "角色权限管理"
            }
        },
        {
            path: "role/assign",
            component: RoleAssign,
            meta: {
                title: "分配角色"
            }
        }
    ]
},
{
    path: "/data",
    component: Data,
    meta: {
        permissions: ['menu:data']
    },
    children: [
        {
            path: "",
            component: DataIndex,
            meta: {
                title: "数据集总览"
            }
        },
        {
            path: "my",
            component: DataMy,
            meta: {
                permissions: ['menu:data:my'],
                title: "我的数据集"
            }
        },
        {
            path: "list",
            component: DataList,
            meta: {
                title: "数据集列表"
            }
        },
        {
            path: "minio",   
            component: MinioIndex,
            meta: {
                permissions: ['menu:data:minio'],
                title: "MinIO总览"
            }
        },
        {
            path: "minio/config",
            component: MinioConfig,
            meta: {
                permissions: ['menu:data:minio'],
                title: "MinIO配置信息"
            }
        },
        {
            path: "minio/list",
            component: MinioList,
            meta: {
                permissions: ['menu:data:minio'],
                title: "MinIO存储列表"
            }
        }   
    ]
},
{
    path: "/application",
    component: Application,
    meta: {
        title:"应用管理"
    },
    children: [
        {
            path: "",
            component: ApplicationIndex,
            meta: {
                title: "应用总览"
            }
        },
        {
            path: "deploy",
            component: ApplicationDeploy,
            meta: {
                title: "应用部署"
            }
        },   
        {
            path: "deploy/task",
            component: ApplicationDeployTask,
            meta: {
                title: "应用部署任务"
            }
        },
        {
            path: "deploy/pending",
            component: ApplicationDeployPending,
            meta: {
                title: "应用部署审批"
            }
        }   
    ],
},
{
    path: "/model",
    component: Model,
    meta: {
        title:"模型管理"
    },
    children: [
        {
            path: "",
            component: ModelIndex,
            meta: {
                title: "模型总览"
            }
        },
        {
            path: "my",
            component: ModelMy,
            meta: {
                title: "我的模型"
            }
        },
        {
            path: "folder",
            component: ModelFolder,
            meta: {
                title: "模型文件夹"
            }
        },
        // {
        //     path: "deploy",
        //     component: ModelDeploy,
        //     meta: {
        //         title: "模型部署"
        //     }
        // },   
        // {
        //     path: "deploy/task",
        //     component: ModelDeployTask,
        //     meta: {
        //         title: "模型部署任务"
        //     }
        // },
        // {
        //     path: "deploy/pending",
        //     component: ModelDeployPending,
        //     meta: {
        //         title: "模型部署审批"
        //     }
        // }   
    ],
},
{
    path: "/task",
    component: Task,
    meta:{
        title:"任务管理",
        permissions: ['menu:task']
    },
    children: [
        {
            path: "",
            component: TaskIndex,
            meta: {
                title: "任务总览"
            }
        },
        {
            path: "my",
            component: TaskMy,
            meta: {
                title: "我的任务"
            }
        },
        {
            path: "create",
            component: TaskCreate,
            meta: {
                title: "创建任务"
            }
        },
        {
            path: "resource/request",
            component: ResourceRequest,
            meta: {
                title: "资源申请"
            }
        },
        {
            path: "resource/my",
            component: ResourceMy,
            meta: {
                title: "我的资源"
            }
        },
        {
            path: "node/pending",
            component: NodePending,
            meta: {
                permissions: ['menu:task:approveNode'],
                title: "节点审批"
            }
        },
        {
            path: "code/pending",
            component: CodePending,
            meta: {
                permissions: ['menu:task:approveNode'],
                title: "代码审批"
            }
        },
        {
            path: "code/my",
            component: CodeMy,
            meta: {
                title: "我的代码"
            }
        },
        {
            path: "dataset/pending",
            component: DatasetPending,
            meta: {
                permissions: ['menu:task:approveData'],
                title: "数据集审批"
            }
        },
        {
            path: "dataset/request",
            component: DatasetRequest,
            meta: {
                title: "数据集使用申请"
            }
        },
        {
            path: "node/request",
            component: NodeRequest,
            meta: {
                title: "节点使用申请"
            }
        }           
    ]
}

]

const router = createRouter({
    history:createWebHashHistory(),
    routes
})

export default router