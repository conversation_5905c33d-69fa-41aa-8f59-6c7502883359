<template>
    <el-container class="task-container">
        <el-header>
            <f-header/>
        </el-header>
        <el-container>
            <el-aside width="200px">
                <TaskMenu></TaskMenu>
            </el-aside>
            <el-container class="right-container">
                <div class="tag-list-container">
                    <el-card class="tag-list-card" shadow="hover">
                        <GlobalTagList/>
                    </el-card>
                </div>
                <el-main>
                    <el-card class="main-card" shadow="hover">
                        <router-view></router-view>
                    </el-card>
                </el-main>
            </el-container>
        </el-container>
        <el-footer>
            <f-footer/>
        </el-footer>    
    </el-container>
</template>

<script setup>
import FHeader from './components/FHeader.vue';
import TaskMenu from './components/TaskMenu.vue';
import GlobalTagList from './components/GlobalTagList.vue';
import FFooter from './components/FFooter.vue';
</script>

<style scoped>
.task-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

.el-header {
    padding: 0 !important;
    width: 100%;
    height: auto !important;
    flex-shrink: 0;
    z-index: 1000;
}

.el-footer {
    flex-shrink: 0;
    padding: 0 !important;
    height: 60px !important;
    width: 100% !important;
}

.el-container:nth-child(2) {
    flex: 1;
    overflow: hidden;
    min-height: 0;
    position: relative;
}

.right-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    min-height: 0;
}

.el-aside {
    border-right: 1px solid var(--el-border-color-light);
    background-color: var(--el-bg-color-overlay);
    transition: width 0.3s;
    overflow-y: auto;
    height: 100%;
    flex-shrink: 0;
}

.el-main {
    overflow-y: auto;
    padding: 20px;
    padding-bottom: 20px;
    background-color: var(--el-bg-color-page, #f2f3f5);
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

.main-card {
    height: 100%;
    margin-bottom: 0;
    border-radius: 8px;
    border: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.main-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
    flex: 1;
}

/* 深色模式下的卡片样式 */
html.dark .main-card {
    background-color: var(--el-bg-color-overlay);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .main-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.tag-list-container {
    padding: 12px 20px 0;
    background-color: var(--el-bg-color-page, #f2f3f5);
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.tag-list-card {
    margin-bottom: 0;
    border-radius: 8px;
    border: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.tag-list-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.tag-list-card .el-card__body) {
    padding: 12px 16px;
}

/* 深色模式下的卡片样式 */
html.dark .tag-list-card {
    background-color: var(--el-bg-color-overlay);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .tag-list-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
</style>