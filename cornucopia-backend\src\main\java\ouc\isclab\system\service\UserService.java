package ouc.isclab.system.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.system.entity.RoleEntity;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.system.entity.UserRoleEntity;
import ouc.isclab.system.entity.PermissionEntity;
import ouc.isclab.system.entity.RolePermissionEntity;
import ouc.isclab.system.repository.UserRepository;
import ouc.isclab.system.repository.RoleRepository;
import ouc.isclab.system.repository.UserRoleRepository;
import ouc.isclab.system.repository.PermissionRepository;
import ouc.isclab.system.repository.RolePermissionRepository;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRoleRepository userRoleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private RolePermissionRepository rolePermissionRepository;

    /**
     * 创建用户
     */
    @Transactional
    public UserEntity createUser(String username, String fullname, String email, String password, Long roleId) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(username)) {
            throw new BaseException(ResponseCode.REGISTER_FAILED, "用户名已存在");
        }

        // 创建用户实体
        UserEntity userEntity = new UserEntity();
        userEntity.setUsername(username);
        userEntity.setFullname(fullname);
        userEntity.setEmail(email);
        userEntity.setPassword(password);
        userEntity.setEnable(true); // 默认启用

        // 保存用户信息
        UserEntity savedUser = userRepository.save(userEntity);

        try {
            // 获取角色，如果指定角色不存在则使用默认角色（ID=1）
            RoleEntity roleEntity;
            if (roleId != null) {
                roleEntity = roleRepository.getRoleEntityById(roleId);
                if (roleEntity == null) {
                    roleEntity = roleRepository.getRoleEntityById(1L);
                    if (roleEntity == null) {
                        throw new BaseException(ResponseCode.SERVICE_ERROR,"系统错误：默认角色未初始化");
                    }
                }
            } else {
                roleEntity = roleRepository.getRoleEntityById(1L);
                if (roleEntity == null) {
                    throw new BaseException(ResponseCode.SERVICE_ERROR,"系统错误：默认角色未初始化");
                }
            }

            // 设置用户角色
            Set<RoleEntity> roles = new HashSet<>();
            roles.add(roleEntity);
            savedUser.setRoles(roles);

            // 保存用户角色关联
            UserRoleEntity userRoleEntity = new UserRoleEntity();
            userRoleEntity.setUserId(savedUser.getId());
            userRoleEntity.setRoleId(roleEntity.getId());
            userRoleRepository.save(userRoleEntity);

            return savedUser;
        } catch (Exception e) {
            throw new BaseException(ResponseCode.SERVICE_ERROR,"用户创建失败：" + e.getMessage());
        }
    }

    /**
     * 列出所有用户
     */
    public Page<UserEntity> listUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    /**
     * 根据用户名查用户
     */
    public UserEntity findUserByUsername(String username) { 
        return userRepository.getUserEntityByUsername(username);
    }

    /**
     * 删除用户
     */
    @Transactional
    public void deleteUserById(Long id) {
        UserEntity userEntity = userRepository.getUserEntityById(id);
        if (userEntity != null) {
            // 先删除用户角色关联
            userRoleRepository.deleteByUserId(id);
            // 再删除用户
            userRepository.deleteById(id);
        }
    }

    /**
     * 修改用户基本信息
     */
    public UserEntity updateUserById(Long id, String username, String fullname, String email, boolean enable) {
        UserEntity userEntity = userRepository.getUserEntityById(id);

        userEntity.setUsername(username);
        userEntity.setFullname(fullname);
        userEntity.setEmail(email);
        userEntity.setEnable(enable);

        return userRepository.save(userEntity);
    }

    /**
     * 修改用户密码
     */
    public UserEntity updatePwdById(Long id, String password, String newPassword) throws Exception {
        UserEntity userEntity = userRepository.getUserEntityById(id);
        if (userEntity.getPassword().equals(password)){
            userEntity.setPassword(newPassword);
        } else {
            throw new BaseException(ResponseCode.SERVICE_ERROR,"修改密码失败，原密码错误");
        }
        return userRepository.save(userEntity);
    }

    /**
     * 修改用户角色
     */
    @Transactional
    public UserEntity updateUserRoles(Long userId, List<Long> roleIds) {
        UserEntity user = userRepository.getUserEntityById(userId);
        if (user == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "用户不存在");
        }

        // 删除原有的所有角色关联
        userRoleRepository.deleteByUserId(userId);

        Set<RoleEntity> roles = new HashSet<>();
        for (Long roleId : roleIds) {
            // 使用getRoleEntityById获取角色
            RoleEntity role = roleRepository.getRoleEntityById(roleId);
            if (role == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "角色不存在: " + roleId);
            }
            roles.add(role);

            // 创建新的用户角色关联
            UserRoleEntity userRoleEntity = new UserRoleEntity();
            userRoleEntity.setUserId(userId);
            userRoleEntity.setRoleId(roleId);
            userRoleRepository.save(userRoleEntity);
        }

        user.setRoles(roles);
        return userRepository.save(user);
    }

    /**
     * 获取用户统计数据
     */
    public Map<String, Object> getUserStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 获取总用户数
            long totalUsers = userRepository.count();
            log.info("Total users: {}", totalUsers);
            statistics.put("totalUsers", totalUsers);
            
            // 获取在线用户数
            long onlineUsers = userRepository.countByOnlineTrue();
            log.info("Online users: {}", onlineUsers);
            statistics.put("onlineUsers", onlineUsers);
            
            // 获取活跃用户数 (最近7天有登录的用户)
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -7);
            Date sevenDaysAgo = calendar.getTime();
            long activeUsers = userRepository.countByLastLoginAfter(sevenDaysAgo);
            log.info("Active users: {}", activeUsers);
            statistics.put("activeUsers", activeUsers);
            
            // 获取角色总数
            long totalRoles = roleRepository.count();
            log.info("Total roles: {}", totalRoles);
            statistics.put("totalRoles", totalRoles);
            
            return statistics;
        } catch (Exception e) {
            log.error("Error getting user statistics", e);
            throw e;
        }
    }

    // 用户登录时调用此方法更新状态
    @Transactional
    public void updateUserLoginStatus(String username, boolean online) {
        UserEntity user = userRepository.getUserEntityByUsername(username);
        if (user != null) {
            user.setOnline(online);
            user.setLastLogin(new Date());
            userRepository.save(user);
        }
    }

    /**
     * 批量删除用户
     */
    @Transactional
    public void deleteUsersByIds(List<Long> ids) {
        for (Long id : ids) {
            UserEntity userEntity = userRepository.getUserEntityById(id);
            if (userEntity != null) {
                // 先删除用户角色关联
                userRoleRepository.deleteByUserId(id);
                // 再删除用户
                userRepository.deleteById(id);
            }
        }
    }

    /**
     * 根据ID查询用户
     */
    public UserEntity findUserById(Long id) {
        return userRepository.getUserEntityById(id);
    }

    /**
     * 根据ID列表批量查询用户
     */
    public List<UserEntity> findUsersByIds(List<Long> ids) {
        List<UserEntity> users = new ArrayList<>();
        for (Long id : ids) {
            UserEntity user = userRepository.getUserEntityById(id);
            if (user != null) {
                users.add(user);
            }
        }
        return users;
    }

    /**
     * 更新用户启用状态
     */
    @Transactional
    public UserEntity updateUserEnable(Long id, boolean enable) {
        UserEntity user = userRepository.getUserEntityById(id);
        if (user == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR,"用户不存在");
        }
        user.setEnable(enable);
        return userRepository.save(user);
    }

    /**
     * 检查用户是否拥有所有指定权限
     */
    public boolean hasAllPermissions(Long userId, String[] permissions) {
        // 获取用户的所有角色
        List<UserRoleEntity> userRoles = userRoleRepository.findByUserId(userId);
        if (userRoles.isEmpty()) {
            return false;
        }

        // 获取这些角色的所有权限
        Set<String> userPermissions = new HashSet<>();
        for (UserRoleEntity userRole : userRoles) {
            List<RolePermissionEntity> rolePermissions = rolePermissionRepository.findByRoleId(userRole.getRoleId());
            for (RolePermissionEntity rolePermission : rolePermissions) {
                PermissionEntity permission = permissionRepository.findById(rolePermission.getPermissionId())
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "权限不存在"));
                userPermissions.add(permission.getCode());
            }
        }

        // 检查是否包含所有需要的权限
        return Arrays.stream(permissions).allMatch(userPermissions::contains);
    }

    /**
     * 检查用户是否拥有任意指定权限
     */
    public boolean hasAnyPermission(Long userId, String[] permissions) {
        // 获取用户的所有角色
        List<UserRoleEntity> userRoles = userRoleRepository.findByUserId(userId);
        if (userRoles.isEmpty()) {
            return false;
        }

        // 获取这些角色的所有权限
        Set<String> userPermissions = new HashSet<>();
        for (UserRoleEntity userRole : userRoles) {
            List<RolePermissionEntity> rolePermissions = rolePermissionRepository.findByRoleId(userRole.getRoleId());
            for (RolePermissionEntity rolePermission : rolePermissions) {
                PermissionEntity permission = permissionRepository.findById(rolePermission.getPermissionId())
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "权限不存在"));
                userPermissions.add(permission.getCode());
            }
        }

        // 检查是否包含任意需要的权限
        return Arrays.stream(permissions).anyMatch(userPermissions::contains);
    }
}
