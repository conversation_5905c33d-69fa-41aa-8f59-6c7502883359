import io
import torch
from PIL import Image
from flask import Flask, request, jsonify, render_template_string
from torchvision import transforms
import torch.nn as nn
import torch.nn.functional as F
from minio import Minio
from minio.error import S3Error
import threading
import time
import os

# Flask应用配置
app = Flask(__name__)

# MinIO配置
MINIO_ENDPOINT = '10.140.34.208:9000'
MINIO_ACCESS_KEY = 'eaZ2nCveMAYIc0yoeVU2'
MINIO_SECRET_KEY = 'ouc.edu.cn'
MINIO_SECURE = False
MODEL_BUCKET = 'models'
MODEL_NAME = 'food_classifier_best.pt'

# 分类类别
CLASSES = ['apple_pie', 'bibimbap', 'caesar_salad', 'donuts', 'fried_rice',
           'hamburger', 'ice_cream', 'pizza', 'ramen', 'steak']
IMG_SIZE = 224
DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

# 初始化MinIO客户端
minio_client = Minio(
    MINIO_ENDPOINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)

# 图像预处理
transform = transforms.Compose([
    transforms.Resize((IMG_SIZE, IMG_SIZE)),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])


class FoodCNN(nn.Module):
    def __init__(self, num_classes):
        super(FoodCNN, self).__init__()
        self.features = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),

            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),

            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),

            nn.Conv2d(256, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),

            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
        )

        self.classifier = nn.Sequential(
            nn.Linear(512 * 7 * 7, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(4096, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(4096, num_classes),
        )

    def forward(self, x):
        x = self.features(x)
        x = torch.flatten(x, 1)
        x = self.classifier(x)
        return x


def load_model():
    """从MinIO加载模型"""
    try:
        # 从MinIO获取模型
        response = minio_client.get_object(MODEL_BUCKET, MODEL_NAME)
        model_data = io.BytesIO(response.data)

        # 加载模型
        model = FoodCNN(len(CLASSES)).to(DEVICE)
        model.load_state_dict(torch.load(model_data, map_location=DEVICE))
        model.eval()

        print("模型加载成功")
        return model
    except S3Error as e:
        print(f"加载模型失败: {e}")
        return None
    finally:
        response.close()
        response.release_conn()


# 全局模型变量
model = load_model()

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>食物分类器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .countdown {
            text-align: center;
            padding: 10px;
            background-color: #f8d7da;
            color: #721c24;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .upload-container {
            border: 2px dashed #ccc;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            margin-bottom: 20px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .upload-container:hover {
            border-color: #aaa;
        }
        #image-preview {
            max-width: 100%;
            max-height: 300px;
            margin-top: 10px;
            display: none;
        }
        #file-input {
            display: none;
        }
        .result-container {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 5px;
            background-color: #f9f9f9;
            display: none;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            margin-top: 10px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s;
        }
        .class-probabilities {
            margin-top: 15px;
        }
        .class-row {
            display: flex;
            margin-bottom: 5px;
            align-items: center;
        }
        .class-name {
            width: 120px;
            font-weight: bold;
        }
        .class-bar-container {
            flex-grow: 1;
            margin-left: 10px;
            margin-right: 10px;
        }
        .class-percent {
            width: 60px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="countdown">
        <div>服务将在 <span id="countdown-timer">30:00</span> 后自动关闭</div>
    </div>
    
    <div class="upload-container" onclick="document.getElementById('file-input').click()">
        <div>点击或拖拽图片到此处上传</div>
        <input type="file" id="file-input" accept="image/*" onchange="handleFileSelect(event)">
        <img id="image-preview" src="#" alt="预览图片">
    </div>
    
    <div class="result-container" id="result-container">
        <h3>预测结果</h3>
        <div id="prediction-result"></div>
        <div class="progress-bar">
            <div class="progress" id="confidence-bar"></div>
        </div>
        <div id="confidence-text"></div>
        
        <div class="class-probabilities" id="class-probabilities"></div>
    </div>

    <script>
        // 倒计时功能
        function updateCountdown() {
            const endTime = {{ destroy_time }};
            const now = new Date().getTime();
            const distance = endTime - now;
            
            if (distance < 0) {
                document.getElementById('countdown-timer').innerHTML = "服务已关闭";
                return;
            }
            
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            
            document.getElementById('countdown-timer').innerHTML = 
                minutes.toString().padStart(2, '0') + ":" + seconds.toString().padStart(2, '0');
            
            setTimeout(updateCountdown, 1000);
        }
        
        // 页面加载时启动倒计时
        window.onload = function() {
            updateCountdown();
        };
        
        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file || !file.type.match('image.*')) {
                alert('请选择有效的图片文件');
                return;
            }
            
            if (file.size > 5 * 1024 * 1024) {
                alert('图片大小不能超过5MB');
                return;
            }
            
            const uploadArea = document.getElementById('upload-area');
            const imagePreview = document.getElementById('image-preview');
            const loadingIndicator = document.getElementById('loading');
            
            // 显示图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                imagePreview.style.display = 'block';
                
                // 显示加载指示器
                loadingIndicator.style.display = 'flex';
                
                // 上传文件到服务器
                uploadFile(file);
            };
            reader.readAsDataURL(file);
        }

        // 上传文件到服务器
        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            const loadingIndicator = document.getElementById('loading');
            
            fetch('service/{{ service_id }}/predict', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('服务器响应异常');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                // 隐藏加载指示器
                loadingIndicator.style.display = 'none';
                
                // 显示结果容器
                const resultContainer = document.getElementById('result-container');
                resultContainer.style.display = 'block';
                
                // 处理返回的预测结果字典
                const predictions = data.predictions;
                
                // 将字典转换为数组并排序
                const sortedPredictions = Object.entries(predictions)
                    .map(([className, confidence]) => ({ className, confidence }))
                    .sort((a, b) => b.confidence - a.confidence);
                
                // 获取最高置信度的类别
                const topPrediction = sortedPredictions[0];
                
                // 显示预测结果
                document.getElementById('prediction-result').innerHTML = 
                    `最可能类别: <strong>${topPrediction.className}</strong> (置信度: ${(topPrediction.confidence * 100).toFixed(2)}%)`;
                
                // 显示所有类别的概率
                const classProbsContainer = document.getElementById('class-probabilities');
                classProbsContainer.innerHTML = '';
                
                sortedPredictions.forEach((prediction, index) => {
                    const probPercent = (prediction.confidence * 100).toFixed(2);
                    const isTopClass = index === 0;
                    
                    const probItem = document.createElement('div');
                    probItem.className = 'progress-container' + (isTopClass ? ' top-class' : '');
                    
                    probItem.innerHTML = `
                        <div class="progress-label">${prediction.className}</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${probPercent}%"></div>
                        </div>
                        <div class="progress-value">${probPercent}%</div>
                    `;
                    
                    classProbsContainer.appendChild(probItem);
                });
                
                // 滚动到结果区域
                resultContainer.scrollIntoView({ behavior: 'smooth' });
            })
            .catch(error => {
                loadingIndicator.style.display = 'none';
                alert('预测失败: ' + error.message);
                console.error('Error:', error);
            });
        }
        
        // 拖放功能
        document.addEventListener('DOMContentLoaded', function() {
            const uploadContainer = document.querySelector('.upload-container');
            
            uploadContainer.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.style.borderColor = '#4CAF50';
            });
            
            uploadContainer.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.style.borderColor = '#ccc';
            });
            
            uploadContainer.addEventListener('drop', function(e) {
                e.preventDefault();
                this.style.borderColor = '#ccc';
                
                if (e.dataTransfer.files.length) {
                    const fileInput = document.getElementById('file-input');
                    fileInput.files = e.dataTransfer.files;
                    handleFileSelect({ target: fileInput });
                }
            });
        });
    </script>
</body>
</html>
"""


destroy_time = time.time() + 60 * 30
destroy_time = int(destroy_time * 1000)
@app.route('/')
def home():
    return render_template_string(HTML_TEMPLATE, destroy_time=destroy_time)


@app.route('/predict', methods=['POST'])
def predict():
    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if file and model:
        try:
            # 读取图像数据
            image_bytes = file.read()
            img = Image.open(io.BytesIO(image_bytes)).convert('RGB')

            # 预处理图像
            img_tensor = transform(img).unsqueeze(0).to(DEVICE)

            # 预测
            with torch.no_grad():
                outputs = model(img_tensor)
                probs = torch.softmax(outputs, dim=1)
                conf, pred = torch.max(probs, 1)

                # 获取所有类别的概率
                class_probs = probs.squeeze().cpu().numpy()

                return jsonify({
                    'class': CLASSES[pred.item()],
                    'class_index': pred.item(),
                    'class_labels': CLASSES,
                    'class_probabilities': class_probs.tolist(),
                    'confidence': conf.item()
                })

        except Exception as e:
            return jsonify({'error': f'Error processing image: {str(e)}'}), 500

    return jsonify({'error': 'Model not loaded'}), 500


def shutdown_server():
    time.sleep(60 * 30)  # 30分钟后关闭
    print("30分钟已到,正在关闭服务器...")
    os._exit(0)


if __name__ == '__main__':
    # 检查模型是否加载成功
    if model is None:
        print("无法加载模型，服务启动失败")
        exit(1)
    # 启动定时关闭线程
    shutdown_thread = threading.Thread(target=shutdown_server)
    shutdown_thread.daemon = True
    shutdown_thread.start()

    # 启动Flask应用
    app.run(host='0.0.0.0', port=5000, debug=False)
