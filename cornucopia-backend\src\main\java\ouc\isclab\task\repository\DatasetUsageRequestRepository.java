package ouc.isclab.task.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import ouc.isclab.task.entity.DatasetUsageRequestEntity;
import ouc.isclab.task.entity.DatasetUsageRequestEntity.ApprovalStatus;

import java.util.List;

public interface DatasetUsageRequestRepository extends JpaRepository<DatasetUsageRequestEntity, Long> {
    // 查询用户的所有申请
    Page<DatasetUsageRequestEntity> findByApplicantId(Long applicantId, Pageable pageable);
    
    // 查询数据集所有者需要审批的申请
    Page<DatasetUsageRequestEntity> findByDataset_CreatorIdAndStatus(Long datasetOwnerId, ApprovalStatus status, Pageable pageable);
    
    // 检查用户是否已经有针对该数据集的申请
    boolean existsByApplicantIdAndDataset_IdAndStatusNot(Long applicantId, Long datasetId, ApprovalStatus status);
    
    // 查询用户已批准的数据集申请
    List<DatasetUsageRequestEntity> findByApplicantIdAndStatus(Long applicantId, ApprovalStatus status);

    // 检查用户是否已经有针对特定节点上特定数据集的申请
    boolean existsByApplicantIdAndDataset_IdAndNodeIdAndStatusNot(
        Long applicantId, Long datasetId, Long nodeId, ApprovalStatus status);

    // 查询数据集所有者的所有申请
    Page<DatasetUsageRequestEntity> findByDataset_CreatorId(Long datasetOwnerId, Pageable pageable);

    boolean existsByApplicantIdAndDatasetIdIn(Long applicantId, List<Long> datasetIds);
} 