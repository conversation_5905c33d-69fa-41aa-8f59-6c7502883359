<template>
    <el-row class="login-container">
        <el-col :lg="16" :md="12" class="left">
            <div>
                <div class="cornucopia-title">Cornucopia</div>
                <div class="main-title">联邦学习与共享计算平台</div>
                <div class="description">致力于数据安全技术研究与创新应用</div>
            </div>
        </el-col>
        <el-col :lg="8" :md="12" class="right">
            <h2 class="title">欢迎回来</h2>
            <div>
                <span class="line"></span>
                <span>账号密码登录</span>
                <span class="line"></span>
            </div>
            <el-form ref="formRef" :rules="rules" :model="form" class="w-[400px]">
                <el-form-item prop="username" class="form-item">
                    <el-input v-model="form.username" placeholder="请输入用户名" :size="large">
                        <template #prefix>
                            <el-icon><user /></el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item prop="password" class="form-item">
                    <el-input type="password" v-model="form.password" placeholder="请输入密码" show-password :size="large">
                        <template #prefix>
                            <el-icon><lock /></el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button round color="#626aef" class="w-[400px]" type="primary" @click="onSubmit" :loading="loading" :size="large">登 录</el-button>
                </el-form-item>
            </el-form>
            <div class="flex items-center justify-center mt-4">
                <router-link to="/register" class="text-gray-600 hover:text-indigo-500">
                    还没有账号？立即注册
                </router-link>
            </div>
            <div class="copyright" style="color:#666">
                2025 @海尔海大数据安全联合开发实验室 青岛
            </div>
        </el-col>
    </el-row>
</template>

<script setup>
import { ref,reactive,onMounted,onBeforeUnmount } from 'vue'
import { toast } from '~/composables/util'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

const store = useStore()
const router = useRouter()

// do not use same name with ref
const form = reactive({
  username:"",
  password:""
})

const rules = {
    username:[
        { 
            required: true, 
            message: '用户名不能为空', 
            trigger: 'blur' 
        },
    ],
    password:[
        { 
            required: true, 
            message: '密码不能为空', 
            trigger: 'blur' 
        },
    ]
}

const formRef = ref(null)
const loading = ref(false)
const onSubmit = () => {
    formRef.value.validate((valid)=>{
        if(!valid){
            return false
        }
        loading.value = true
        
        // 直接使用登录接口，后端会设置Cookie
        store.dispatch("login",form).then(res=>{
            // 判断是否登录成功
            if(res.code === 10001) {
                toast("请求成功","登录成功")
                router.push("/")
            } else {
                // 登录失败，显示错误信息
                toast("错误", res.message || "登录失败", "error")
            }
        }).catch(err=>{
            // 处理网络错误等异常情况
            toast("错误","登录失败,请检查用户名和密码", "error")
        }).finally(()=>{
            loading.value = false
        })
    })
}

// 监听回车事件
function onKeyUp(e){
    if(e.key == "Enter") onSubmit()
}

// 添加键盘监听
onMounted(()=>{
    document.addEventListener("keyup",onKeyUp)
})
// 移除键盘监听
onBeforeUnmount(()=>{
    document.removeEventListener("keyup",onKeyUp)
})

</script>

<style scoped>
.login-container{
    @apply min-h-screen bg-indigo-500;
}
.login-container .left,.login-container .right{
    @apply flex items-center justify-center;
}
.login-container .right{
    @apply bg-light-50 flex-col;
}
.left>div>div:first-child{
    @apply font-bold text-7xl text-light-50 mb-4;
}
.left>div>div:nth-child(2){
    @apply font-bold text-5xl text-light-50 mb-6;
}
.left>div>div:nth-child(3){
    @apply text-gray-200 text-3xl;
}
.right .title{
    @apply font-bold text-5xl text-gray-800 mb-2 mt-16;
}
.right>div{
    @apply flex items-center justify-center my-8 text-gray-300 space-x-4;
}
.right .line{
    @apply h-[1px] w-28 bg-gray-200;
}
.copyright {
    @apply text-lg mt-12 opacity-80;
}
.right .form-item {
    @apply mb-6;
}
.right :deep(.el-input__wrapper) {
    @apply h-[50px] text-lg;
}
.right :deep(.el-button) {
    @apply h-[50px] text-lg;
}
.right :deep(.el-input__prefix-inner) {
    @apply text-xl;
}
</style>