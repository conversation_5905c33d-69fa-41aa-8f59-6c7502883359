import requests
from typing import Dict, Any
from pathlib import Path


def launch_task(path: str, url: str = "pyxis-core:8000") -> Dict[str, Any]:
    """
    Client function to call the /task endpoint with either:
    - All files in a folder (preserving subfolder structure, excluding root folder), or
    - A single file (synchronous version)

    Args:
        path: Path to either a folder or a single file to upload
        url: Target pyxis_core url

    Returns:
        Dictionary containing the response from the server (task_id, container_id, status)

    Raises:
        FileNotFoundError: If the specified path doesn't exist
        ValueError: If no files are found in folder
        requests.HTTPError: If the server returns an error status code
        Exception: For other unexpected errors
    """
    try:
        input_path = Path(path).resolve()

        # Verify path exists
        if not input_path.exists():
            raise FileNotFoundError(f"Path not found: {input_path}")

        files_to_upload = []

        if input_path.is_file():
            # Handle single file case
            with open(input_path, "rb") as f:
                files_to_upload.append(
                    ("files", (input_path.name, f.read(), "application/octet-stream"))
                )
        elif input_path.is_dir():
            # Handle folder case
            for file_path in input_path.rglob('*'):
                if file_path.is_file():
                    # Get relative path (excluding the root folder name)
                    rel_path = file_path.relative_to(input_path)

                    with open(file_path, "rb") as f:
                        # Include the relative path in the filename
                        files_to_upload.append(
                            ("files", (str(rel_path), f.read(), "application/octet-stream"))
                        )

            if not files_to_upload:
                raise ValueError(f"No files found in folder: {input_path}")
        else:
            raise ValueError(f"Path is neither a file nor a directory: {input_path}")

        # Make the request (synchronous)
        response = requests.post(
            f"http://{url}/task",
            files=files_to_upload
        )
        response.raise_for_status()
        return response.json()

    except requests.HTTPError as e:
        error_detail = e.response.json().get("detail", str(e))
        raise requests.HTTPError(
            f"Server returned error: {error_detail}",
            request=e.request,
            response=e.response
        )
    except Exception as e:
        # Handle other unexpected errors
        raise Exception(f"Failed to launch task: {str(e)}") from e