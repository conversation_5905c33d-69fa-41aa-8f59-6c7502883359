package ouc.isclab.storage.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import ouc.isclab.storage.entity.MinioConfigEntity;

import java.util.List;
import java.util.Optional;

public interface MinioConfigRepository extends JpaRepository<MinioConfigEntity, Long> {

    /**
     * 查找指定类型的活动配置
     */
    Optional<MinioConfigEntity> findByTypeAndActiveTrue(MinioConfigEntity.ConfigType type);

    /**
     * 查找指定类型的所有配置
     */
    Page<MinioConfigEntity> findByType(MinioConfigEntity.ConfigType type, Pageable pageable);

    /**
     * 查找所有配置
     */
    Page<MinioConfigEntity> findAll(Pageable pageable);

} 