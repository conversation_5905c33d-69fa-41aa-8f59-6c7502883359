<template>
  <div class="node-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Monitor /></el-icon>
          <h2>节点列表</h2>
        </div>
        <div class="sub-title">查看联邦学习网络中的节点列表</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.name"
                placeholder="请输入节点名称"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="action-buttons">
            <el-button type="primary" @click="handleAdd" plain round>
              <el-icon><Plus /></el-icon>添加节点
            </el-button>
            <el-button 
              type="danger" 
              :disabled="!selectedIds.length"
              @click="handleBatchDelete"
              round
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table 
        v-loading="loading"
        :data="tableData" 
        stripe 
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        border
        highlight-current-row
        @selection-change="handleSelectionChange"
        :default-sort="defaultSort"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column 
          prop="id" 
          label="ID" 
          width="70" 
          align="center"
          sortable
          :sort-method="sortById"
        />
        <el-table-column prop="name" label="节点名称" width="180" show-overflow-tooltip />
        <el-table-column prop="nodeType" label="节点类型" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.nodeType === 'Sycee' ? 'success' : 'primary'">
              {{ scope.row.nodeType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ipAddress" label="IP地址" width="180" show-overflow-tooltip />
        <el-table-column prop="port" label="端口" width="100" align="center" />
        <el-table-column prop="ipPortCombo" label="连接地址" width="200" show-overflow-tooltip>
          <template #default="scope">
            <el-tag size="small">{{ scope.row.ipAddress }}:{{ scope.row.port }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column 
          prop="timeCreated" 
          label="创建时间" 
          width="180" 
          show-overflow-tooltip
          sortable
          :formatter="formatDateTime"
          :sort-method="sortByTime"
        />
        <el-table-column 
          prop="timeUpdated" 
          label="更新时间" 
          width="180" 
          show-overflow-tooltip
          sortable
          :formatter="formatDateTime"
          :sort-method="sortByTime"
        />
        <el-table-column label="操作" width="240" fixed="right" align="center">
          <template #default="scope">
            <el-button-group>
              <el-tooltip content="查看详情" placement="top">
                <el-button type="info" size="small" @click.stop="handleDetail(scope.row)">
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑节点" placement="top">
                <el-button type="primary" size="small" @click.stop="handleEdit(scope.row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除节点" placement="top">
                <el-button type="danger" size="small" @click.stop="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          v-model:current-page="currentPage"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  Plus, 
  Edit, 
  Delete, 
  Monitor,
  View,
  Search,
  Refresh
} from '@element-plus/icons-vue'
import service from '~/axios'
import { useRouter } from 'vue-router'
import { toast, showModal } from '~/composables/util'

const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const router = useRouter()

// 添加搜索表单
const searchForm = ref({
  name: ''
})

// 添加选中ID数组
const selectedIds = ref([])

// 添加 loading 状态
const loading = ref(false)

const fetchNodes = async (page = 1) => {
  loading.value = true
  try {
    const response = await service.get('/api/v1.0/sys/nodes', {
      params: {
        page: page,
        size: pageSize.value,
        name: searchForm.value.name || undefined
      }
    })
    if (response.code === 10000) {
      tableData.value = response.data.nodes
      total.value = response.data.pagination.total
      pageSize.value = response.data.pagination.size
    }
  } catch (error) {
    console.error('获取节点列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchNodes(page)
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchNodes()
}

const handleSearch = async () => {
  loading.value = true
  try {
    if (!searchForm.value.name) {
      fetchNodes(currentPage.value)
      return
    }
    
    const response = await service.get(`/api/v1.0/sys/node/search/${searchForm.value.name}`)
    if (response.code === 10000) {
      if (response.data === null) {
        tableData.value = []
        total.value = 0
        currentPage.value = 1
        toast('提示', '未找到匹配的节点', 'info')
      } else {
        tableData.value = [response.data]
        total.value = 1
        currentPage.value = 1
      }
    } else {
      toast('错误', response.message || '搜索失败', 'error')
    }
  } catch (error) {
    console.error('搜索节点失败:', error)
    toast('错误', '搜索失败', 'error')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.value = {
    name: ''
  }
  fetchNodes(1)
}

const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.id)
}

const handleBatchDelete = async () => {
  if (!selectedIds.value.length) return
  
  try {
    await showModal(`确定要删除选中的 ${selectedIds.value.length} 个节点吗？`, 'warning', '提示')
    const queryParams = selectedIds.value.map(id => `ids=${id}`).join('&')
    const response = await service.delete(`/api/v1.0/sys/node/batch?${queryParams}`)
    
    if (response.code === 10000) {
      toast('成功', '批量删除成功')
      selectedIds.value = []
      fetchNodes(currentPage.value)
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '批量删除失败', 'error')
    }
  }
}

const handleDetail = (row) => {
  router.push({
    path: '/node/detail',
    query: { id: row.id }
  })
}

const handleAdd = () => {
  router.push('/node/register')
}

const handleEdit = (row) => {
  router.push({
    path: '/node/update',
    query: {
      id: row.id
    }
  })
}

const handleDelete = (row) => {
  showModal(
    `确定要删除节点 ${row.name} 吗？`,
    'warning',
    '警告'
  ).then(async () => {
    try {
      const response = await service.delete(`/api/v1.0/sys/node/${row.id}`)
      if (response.code === 10000) {
        toast('删除成功')
        // 重新加载数据
        fetchNodes(currentPage.value)
      } else {
        toast('删除失败', response.message, 'error')
      }
    } catch (error) {
      console.error('删除节点失败:', error)
      toast('删除失败', error.message, 'error')
    }
  }).catch(() => {
    toast('已取消操作', '', 'info')
  })
}

const formatDateTime = (row) => {
  if (!row.timeCreated) return '-'
  const date = new Date(row.timeCreated)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const sortByTime = (a, b) => {
  const timeA = new Date(a.timeCreated).getTime()
  const timeB = new Date(b.timeCreated).getTime()
  return timeA - timeB
}

const sortById = (a, b) => {
  return a.id - b.id
}

const defaultSort = {
  prop: 'timeUpdated',
  order: 'descending'
}

onMounted(() => {
  fetchNodes()
})
</script>

<style scoped>
.node-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  flex-shrink: 0;
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
}

:deep(.el-button--primary) {
  --el-button-hover-bg-color: var(--el-color-primary-light-3);
  --el-button-hover-border-color: var(--el-color-primary-light-3);
  --el-button-active-bg-color: var(--el-color-primary-dark-2);
  --el-button-active-border-color: var(--el-color-primary-dark-2);
}

:deep(.el-button--default) {
  --el-button-hover-bg-color: var(--el-color-primary-light-9);
  --el-button-hover-border-color: var(--el-color-primary-light-7);
  --el-button-hover-text-color: var(--el-color-primary);
}

:deep(.el-button--danger) {
  --el-button-hover-bg-color: var(--el-color-danger-light-3);
  --el-button-hover-border-color: var(--el-color-danger-light-3);
  --el-button-active-bg-color: var(--el-color-danger-dark-2);
  --el-button-active-border-color: var(--el-color-danger-dark-2);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

:deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}
</style>
  