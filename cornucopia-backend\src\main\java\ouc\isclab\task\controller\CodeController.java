package ouc.isclab.task.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.task.pojo.CodeUploadDTO;
import ouc.isclab.task.pojo.CodeDTO;
import ouc.isclab.task.service.CodeService;
import ouc.isclab.task.entity.CodeEntity;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.system.service.UserService;
import ouc.isclab.system.pojo.UserInfo;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.ResponseEntity;
import org.springframework.http.MediaType;



import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@BaseResponse
@RequestMapping("/api/v1.0/sys/code")
public class CodeController {

    @Autowired
    private CodeService codeService;

    @Autowired
    private UserService userService;

    /**
     * 上传代码字符串
     */
    @PostMapping("/upload")
    public Map<String, Object> uploadCodeString(
            @RequestBody CodeUploadDTO codeUploadDTO,
            @CurrentUserId Long userId
    ) {
        if (!codeService.isSycee(codeUploadDTO.getNodeId())) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点类型错误");
        }
        log.info("上传代码字符串: nodeId={}, funcName={}, userId={}", 
                codeUploadDTO.getNodeId(), codeUploadDTO.getFuncName(), userId);
        log.info("自定义模型路径: {}", codeUploadDTO.getCustomModelPath());
        Map<String, Object> result = codeService.uploadCodeString(
                codeUploadDTO.getNodeId(),
                codeUploadDTO.getResourceId(),
                codeUploadDTO.getFuncName(), 
                codeUploadDTO.getCodeContent(),
                codeUploadDTO.getDescription(),
                userId,
                codeUploadDTO.getCustomModelPath()
        );
        return result;
    }

    /**
     * 部署模型
     */
    @PostMapping("/uploadfile")
    public Map<String, Object> uploadFile(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam("nodeId") Long nodeId,
            @RequestParam(value = "folder_name", required = false) String folderName,
            @CurrentUserId Long userId) {
        if (codeService.isSycee(nodeId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点类型错误");
        }
        log.info("部署模型: 节点ID={}, 用户ID={}, 文件夹名称={}", nodeId, userId, folderName);
        return codeService.uploadFile(nodeId, userId, files, folderName);
    }   

    /**
     * 获取用户上传的代码列表
     */
    @GetMapping("/list")
    public Map<String, Object> getUserCodes(
            @RequestParam(value = "nodeId", required = false) Long nodeId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @CurrentUserId Long userId
    ) {
        log.info("获取用户代码列表: nodeId={}, userId={}", nodeId, userId);
        return codeService.getUserCodes(nodeId, userId, page, size);
    }

    /**
     * 获取代码详情
     */
    @GetMapping("/{codeId}")
    public CodeDTO getCodeDetail(
            @PathVariable Long codeId,
            @CurrentUserId Long userId
    ) {
        log.info("获取代码详情: codeId={}, userId={}", codeId, userId);
        return codeService.getCodeDetail(codeId, userId);
    }

    /**
     * 审批代码
     */
    @PostMapping("/approve/{codeId}")
    public CodeDTO approveCode(
            @PathVariable Long codeId,
            @RequestParam boolean approved,
            @RequestParam(required = false) String rejectReason,
            @CurrentUserId Long userId
    ) {
        log.info("审批代码: codeId={}, approved={}, userId={}", codeId, approved, userId);
        if (codeService.isSyceeCode(codeId)) {
            return codeService.approveCode(codeId, approved, rejectReason, userId);
        }else{
            return codeService.approveCodePyxis(codeId, approved, rejectReason, userId);
        }
    }

    /**
     * 获取节点所有者需要审批的代码列表
     */
    @GetMapping("/examine")
    public Map<String, Object> getCodeForExamine(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @CurrentUserId Long userId
    ) {
        log.info("获取节点所有者代码审批列表: status={}, userId={}", status, userId);
        
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.ASC, "id"));
        
        Map<String, Object> result;
        if (status != null && !status.isEmpty()) {
            // 如果提供了状态参数，按状态过滤
            CodeEntity.ApprovalStatus approvalStatus = 
                CodeEntity.ApprovalStatus.valueOf(status);
            result = codeService.getCodesByNodeOwnerAndStatus(userId, approvalStatus, pageable);
        } else {
            // 否则获取所有代码
            result = codeService.getAllCodesByNodeOwner(userId, pageable);
        }
        
        return result;
    }

    /**
     * 运行代码
     */
    @PostMapping("/run/{codeId}")
    public Map<String, Object> runCode(
            @PathVariable Long codeId,
            @RequestBody Map<String, Object> args,
            @CurrentUserId Long userId
    ) {
        if (!codeService.isSyceeCode(codeId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点类型错误");
        }
        log.info("运行代码: codeId={}, args={}, userId={}", codeId, args, userId);
        return codeService.runCode(codeId, args, userId);
    }

    /**
     * 获取代码运行结果
     */
    @GetMapping("/result/{codeId}")
    public Map<String, Object> getCodeResult(
            @PathVariable Long codeId,
            @CurrentUserId Long userId
    ) {
        if (!codeService.isSyceeCode(codeId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点类型错误");
        }
        log.info("获取代码运行结果: codeId={}, userId={}", codeId, userId);
        return codeService.getCodeResult(codeId, userId);
    }

    /**
     * 获取代码运行日志
     */
    @GetMapping("/log/{codeId}")
    public Map<String, Object> getCodeLog(
            @PathVariable Long codeId,
            @CurrentUserId Long userId
    ) {
        if (!codeService.isSyceeCode(codeId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点类型错误");
        }
        log.info("获取代码运行日志: codeId={}, userId={}", codeId, userId);
        return codeService.getCodeLog(codeId, userId);
    }

    /**
     * 删除代码
     */
    @DeleteMapping("/{codeId}")
    public void deleteCode(
            @PathVariable Long codeId,
            @CurrentUserId Long userId
    ) {
        log.info("删除代码: codeId={}, userId={}", codeId, userId);
        codeService.deleteCode(codeId, userId);
    }

    /**
     * 批量删除代码
     */
    @DeleteMapping("/batch")
    public void batchDeleteCodes(
            @RequestParam List<Long> ids,
            @CurrentUserId Long userId
    ) {
        log.info("批量删除代码: ids={}, userId={}", ids, userId);
        codeService.batchDeleteCodes(ids, userId);
    }

    /**
     * 获取申请代码审批的用户信息
     */
    @GetMapping("/approval/user/{userId}")
    public Object getUserInfo(@PathVariable Long userId, @CurrentUserId Long currentUserId) {
        // 验证该用户是否向当前用户发起过代码审批请求
        boolean hasRequestToCurrentUser = codeService.hasApprovalRequestFromUserToNodeOwner(userId, currentUserId);
        
        if (!hasRequestToCurrentUser) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权查看该用户信息");
        }
        
        UserEntity userEntity = userService.findUserById(userId);
        return UserInfo.fromEntity(userEntity);
    }

    /**
     * 刷新代码执行状态
     */
    @GetMapping("/refresh/{codeId}")
    public Map<String, Object> refreshCodeStatus(@PathVariable Long codeId, @CurrentUserId Long userId) {
        if (!codeService.isSyceeCode(codeId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点类型错误");
        }
        log.info("刷新代码执行状态: codeId={}, userId={}", codeId, userId);
        return codeService.refreshCodeStatus(codeId, userId);
    }

    /**
     * 执行Pyxis任务
     */
    @PostMapping("/execute/{codeId}")
    public Map<String, Object> executePyxisTask(
        @PathVariable Long codeId,
        @CurrentUserId Long userId) {
        log.info("执行Pyxis任务: codeId={}, userId={}", codeId, userId);
        return codeService.executePyxisTask(codeId, userId);
    }   

    /**
     * 获取Pyxis任务状态
     */
    @GetMapping("/status/{codeId}")
    public Map<String, Object> getTaskStatus(
        @PathVariable Long codeId,
        @CurrentUserId Long userId) {
        log.info("获取Pyxis任务状态: codeId={}, userId={}", codeId, userId);
        return codeService.getTaskStatus(codeId, userId);
    }

    /**
     * 停止Pyxis任务
     */
    @PostMapping("/stop/{codeId}")
    public Map<String, Object> stopTask(
        @PathVariable Long codeId,
        @CurrentUserId Long userId) {
        log.info("停止Pyxis任务: codeId={}, userId={}", codeId, userId);
        return codeService.stopTask(codeId, userId);
    }

    /**
     * 终止Pyxis任务 
     */
    @PostMapping("/kill/{codeId}")
    public Map<String, Object> killTask(
        @PathVariable Long codeId,
        @CurrentUserId Long userId) {
        log.info("终止Pyxis任务: codeId={}, userId={}", codeId, userId); 
        return codeService.killTask(codeId, userId);
    }

    /**
     * 浏览Pyxis任务文件目录
     */
    @GetMapping("/browse/{codeId}")
    public Map<String, Object> browseTaskFiles(
        @PathVariable Long codeId,
        @RequestParam(required = false) String path,
        @CurrentUserId Long userId) {
        log.info("浏览Pyxis任务文件目录: codeId={}, path={}, userId={}", codeId, path, userId);
        return codeService.browseTaskFiles(codeId, userId, path);
    }

    /**
     * 下载Pyxis任务文件
     * @param codeId 代码ID
     * @param filePath 文件路径
     * @param userId 当前用户ID
     * @return ResponseEntity<byte[]> 文件内容
     */
    @GetMapping("/download/{codeId}")
    public ResponseEntity<byte[]> downloadTaskFile(
            @PathVariable Long codeId,
            @RequestParam String filePath,
            @CurrentUserId Long userId) {
        log.info("下载Pyxis任务文件请求: codeId={}, filePath={}, userId={}", codeId, filePath, userId);
        return codeService.downloadTaskFile(codeId, userId, filePath);
    }

    /**
     * 预览Pyxis任务文本文件
     * @param codeId 代码ID
     * @param filePath 文件路径
     * @param userId 当前用户ID
     * @return String 文件内容文本
     */
    @GetMapping("/preview/{codeId}")
    @ResponseBody
    public ResponseEntity<String> previewTaskFile(
            @PathVariable Long codeId,
            @RequestParam String filePath,
            @CurrentUserId Long userId) {
        log.info("预览Pyxis任务文件请求: codeId={}, filePath={}, userId={}", codeId, filePath, userId);
        String content = codeService.previewTaskFile(codeId, userId, filePath);
        return ResponseEntity.ok()
                .contentType(MediaType.TEXT_PLAIN)
                .body(content);
    }

    /**
     * 申请代码重新审批
     * @param codeId 代码ID
     * @param userId 当前用户ID
     * @return 申请结果
     */
    @PostMapping("/reapproval/{codeId}")
    public Map<String, Object> requestReapproval(
            @PathVariable Long codeId,
            @CurrentUserId Long userId) {
        log.info("申请代码重新审批: codeId={}, userId={}", codeId, userId);
        return codeService.requestReapproval(codeId, userId);
    }

}
