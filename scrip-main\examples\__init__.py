import argparse
import time
from . import fields_examples
from . import food_classification
from . import question_answer
from . import text_generation
from . import video_detection
from . import time_series

import threading

_module_map = {
    'fields_examples': fields_examples,
    'food_classification': food_classification,
    'question_answer': question_answer,
    'text_generation': text_generation,
    'video_detection': video_detection,
    'time_series':time_series
}


def start_all_examples(start_port=8000):
    examples = [fields_examples, food_classification,
                question_answer, text_generation, video_detection]
    try:
        port = start_port
        for key in _module_map:
            def start_server():
                _module_map[key].main(port)
            threading.Thread(target=start_server, daemon=True).start()
            port += 1
    except Exception as e:
        print(e)
    except KeyboardInterrupt:
        print("close servers")


def start_specific_example(module_name: str, port: int = 8000):

    if module_name not in _module_map:
        raise ValueError(
            f"unknow module: {module_name}. usable modules: {list(_module_map.keys())}")

    module = _module_map[module_name]

    try:
        thread = threading.Thread(
            target=module.main, args=(port,), daemon=True)
        thread.start()
        print(f"start {module_name} server, port: {port}")
        return thread
    except Exception as e:
        print(e)
    except KeyboardInterrupt:
        print("close servers")


def parse_args():
    parser = argparse.ArgumentParser(description="launch example server")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--all', action='store_true', help='launch all')
    group.add_argument('--module', choices=list(_module_map.keys()),
                       help='module name to launch')
    parser.add_argument('--port', type=int, default=8000,
                        help='start port (default: 8000)')
    return parser.parse_args()


def main():
    args = parse_args()

    try:
        if args.all:
            print("start all servers...")
            start_all_examples(args.port)
        else:
            start_specific_example(args.module, args.port)

        while True:
            time.sleep(5)
    except KeyboardInterrupt:
        print("shutting down...")
    except Exception as e:
        print(e)
