package ouc.isclab.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.Date;

/**
 * 服务日志记录
 */
@Data
@Entity
@Table(name = "SYS_SERVICE_LOG")
@EntityListeners(AuditingEntityListener.class)
public class ServiceLogEntity {
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    String clientIp; // 客户端IP

    int clientPort; // 客户端端口

    String httpMethod; // HTTP方法

    @Lob
    @Column(columnDefinition = "text")
    String requestUri; // 请求URI

    long duration; // 花费时间

    @JsonFormat(shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @CreatedDate
    @Column(updatable = false, nullable = false)
    private Date timeCreated;
}
