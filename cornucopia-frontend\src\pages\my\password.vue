<template>
  <div class="password-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Lock /></el-icon>
          <h2>修改密码</h2>
        </div>
        <div class="sub-title">修改您的登录密码</div>
      </div>
    </div>

    <el-card class="form-card" shadow="hover">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="password-form"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input 
            v-model="form.oldPassword"
            type="password" 
            placeholder="请输入原密码"
            show-password
            size="large"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="form.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
            size="large"
          >
            <template #prefix>
              <el-icon><Key /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input 
            v-model="form.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
            size="large"
          >
            <template #prefix>
              <el-icon><Key /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item class="form-buttons">
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :loading="submitting"
            size="large"
            round
          >
            <el-icon><Check /></el-icon>确认修改
          </el-button>
          <el-button 
            @click="handleCancel"
            size="large"
            round
          >
            <el-icon><Close /></el-icon>取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Lock, Key, Check, Close } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const router = useRouter()
const formRef = ref(null)
const submitting = ref(false)

const form = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else {
    if (form.value.confirmPassword !== '') {
      formRef.value?.validateField('confirmPassword')
    }
    callback()
  }
}

const validatePass2 = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== form.value.newPassword) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

const rules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    // { min: 6, max: 20, message: '密码长度在6-20个字符之间', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, validator: validatePass, trigger: 'blur' },
    // { min: 6, max: 20, message: '密码长度在6-20个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validatePass2, trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const res = await service.post('/api/v1.0/sys/my/password', {
      oldPassword: form.value.oldPassword,
      newPassword: form.value.newPassword
    })
    
    if (res.code === 10000) {
      toast('成功', '密码修改成功,请重新登录', 'success')
      // 退出登录
      router.push('/login')
    } else {
      toast('修改失败', res.message, 'error')
    }
  } catch (error) {
    if (error.message) {
      toast('错误', error.message, 'error')
    }
  } finally {
    submitting.value = false
  }
}

// 取消
const handleCancel = () => {
  router.back()
}
</script>

<style scoped>
.password-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.title-icon {
  margin-right: 8px;
  font-size: 24px;
  color: var(--el-color-primary);
}

h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.sub-title {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.form-card {
  max-width: 600px;
  margin: 0 auto;
}

.password-form {
  padding: 20px 0;
}

.form-buttons {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style> 