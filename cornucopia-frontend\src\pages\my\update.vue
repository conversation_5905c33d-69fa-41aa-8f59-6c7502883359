<template>
  <div class="my-update-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Edit /></el-icon>
          <h2>编辑个人信息</h2>
        </div>
        <div class="sub-title">修改您的个人基本信息</div>
      </div>
    </div>

    <el-card class="form-card" shadow="hover" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="update-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="form.username" 
            disabled
            size="large"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="姓名" prop="fullname">
          <el-input 
            v-model="form.fullname" 
            placeholder="请输入姓名"
            size="large"
          >
            <template #prefix>
              <el-icon><UserFilled /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input 
            v-model="form.email" 
            placeholder="请输入邮箱"
            size="large"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item class="form-buttons">
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :loading="submitting"
            size="large"
            round
          >
            <el-icon><Check /></el-icon>保存修改
          </el-button>
          <el-button 
            @click="handleCancel"
            size="large"
            round
          >
            <el-icon><Close /></el-icon>取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Edit, User, UserFilled, Message, Check, Close } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const router = useRouter()
const formRef = ref(null)
const loading = ref(false)
const submitting = ref(false)

const form = ref({
  username: '',
  fullname: '',
  email: ''
})

const rules = {
  fullname: [
    { max: 50, message: '姓名长度不能超过50个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
    { max: 100, message: '邮箱长度不能超过100个字符', trigger: 'blur' }
  ]
}

// 获取用户信息
const getUserInfo = async () => {
  try {
    loading.value = true
    const res = await service.get('/api/v1.0/sys/user')
    if (res.code === 10000) {
      const { username, fullname, email } = res.data
      form.value = { username, fullname, email }
    } else {
      toast('获取用户信息失败', res.data.message, 'error')
    }
  } catch (error) {
    toast('获取用户信息失败', error.message, 'error')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const res = await service.post('/api/v1.0/sys/my/update', {
      fullname: form.value.fullname,
      email: form.value.email
    })
    
    if (res.code === 10000) {
      toast('成功', '个人信息更新成功', 'success')
      router.push('/my')
    } else {
      toast('更新失败', res.message, 'error')
    }
  } catch (error) {
    if (error.message) {
      toast('错误', error.message, 'error')
    }
  } finally {
    submitting.value = false
  }
}

// 取消
const handleCancel = () => {
  router.back()
}

onMounted(() => {
  getUserInfo()
})
</script>

<style scoped>
.my-update-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.title-icon {
  margin-right: 8px;
  font-size: 24px;
  color: var(--el-color-primary);
}

h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.sub-title {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.form-card {
  max-width: 600px;
  margin: 0 auto;
}

.update-form {
  padding: 20px 0;
}

.form-buttons {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style> 