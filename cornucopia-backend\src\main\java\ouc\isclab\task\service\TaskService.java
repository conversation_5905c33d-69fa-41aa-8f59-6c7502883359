package ouc.isclab.task.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.dataset.entity.DatasetEntity;
import ouc.isclab.dataset.repository.DatasetRepository;
import ouc.isclab.model.repository.ModelRepository;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.repository.NodeRepository;
import ouc.isclab.storage.pojo.MinioConfigDTO;
import ouc.isclab.storage.service.MinioConfigService;
import ouc.isclab.sycee.service.SyceeService;
import ouc.isclab.task.entity.NodeAccountRequestEntity;
import ouc.isclab.task.entity.TaskEntity;
import ouc.isclab.task.entity.TaskEntity.TaskStatus;
import ouc.isclab.task.pojo.TaskDTO;
import ouc.isclab.task.repository.TaskRepository;
import ouc.isclab.task.entity.CodeEntity;
import ouc.isclab.task.repository.CodeRepository;
import ouc.isclab.storage.service.MinioService;
import org.springframework.beans.factory.annotation.Value;
import ouc.isclab.model.entity.ModelEntity;
import ouc.isclab.model.service.ModelService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaskService {

    @Autowired
    private TaskRepository taskRepository;
    
    @Autowired
    private CodeRepository codeRepository;
    
    @Autowired
    private NodeRepository nodeRepository;
    
    @Autowired
    private DatasetRepository datasetRepository;
    
    @Autowired
    private NodeAccountRequestService nodeAccountRequestService;
    
    @Autowired
    private DatasetUsageRequestService datasetUsageRequestService;
    
    @Autowired
    private SyceeService syceeService;
    
    @Autowired
    private MinioService minioService;

    @Autowired
    private MinioConfigService minioConfigService;
    
    @Autowired
    private ModelService modelService;
    @Autowired
    private ModelRepository modelRepository;

    /**
     * 创建任务
     */
    @Transactional
    public TaskEntity createTask(TaskDTO taskDTO, Long creatorId) {
        // 验证代码
        Set<CodeEntity> codes = new HashSet<>();
        if (taskDTO.getCodeIds() != null && !taskDTO.getCodeIds().isEmpty()) {
            for (Long codeId : taskDTO.getCodeIds()) {
                CodeEntity code = codeRepository.findById(codeId)
                        .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在: " + codeId));

                // 验证代码是否属于该用户
                if (!code.getCreatorId().equals(creatorId)) {
                    throw new BaseException(ResponseCode.SERVICE_ERROR, 
                            "无权使用他人的代码: " + code.getFuncName());
                }
                
                codes.add(code);
            }
        }
        
        // 创建任务
        TaskEntity task = new TaskEntity();
        task.setName(taskDTO.getName());
        task.setDescription(taskDTO.getDescription());
        task.setCreatorId(creatorId);
        task.setCodes(codes);
        task.setStatus(TaskStatus.CREATED);
        
        // 保存任务以获取ID
        task = taskRepository.save(task);
        

        if (taskDTO.getCustomModelPath() != null && !taskDTO.getCustomModelPath().isEmpty()) {
            // 使用自定义路径
            String modelPath = createCustomModelFolder(task, taskDTO.getCustomModelPath());
            task.setModelPath(modelPath);
            // 创建关联的模型
            createModelForTask(task, creatorId);
        }
        
        return taskRepository.save(task);
    }
    
    /**
     * 创建模型文件夹
     */
    private String createModelFolder(TaskEntity task) {
        try {
            // 生成文件夹路径：userId/taskId/
            String folderPath = "userId-" + task.getCreatorId() + "taskId-" + task.getId() ;
            
            // 使用MinIO创建文件夹
            MinioConfigDTO config = minioConfigService.getActiveConfigByBucket("models");
            minioService.updateMinioClient(config);
            minioService.createDirectory("models", folderPath, "任务" + task.getName() + "的模型文件夹");
            
            return folderPath;
        } catch (Exception e) {
            log.error("创建模型文件夹失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "创建模型文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 创建自定义模型文件夹
     */
    private String createCustomModelFolder(TaskEntity task, String customPath) {
        try {
            // 生成文件夹路径：customPath/
            String folderPath = customPath;
            
            // 使用MinIO创建文件夹
            MinioConfigDTO config = minioConfigService.getActiveConfigByBucket("models");
            minioService.updateMinioClient(config);
            minioService.createDirectory("models", folderPath, "任务" + task.getName() + "的模型文件夹");
            
            return folderPath;
        } catch (Exception e) {
            log.error("创建自定义模型文件夹失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "创建自定义模型文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 为任务创建关联的模型
     */
    private void createModelForTask(TaskEntity task, Long creatorId) {
        ModelEntity model = new ModelEntity();
        model.setName(task.getName() + "的模型");
        model.setDescription("由任务" + task.getName() + "创建的模型");
        model.setCreatorId(creatorId);
        model.setTask(task);
        model.setModelPath(task.getModelPath());
        model.setStatus(ModelEntity.ModelStatus.CREATED);
        
        modelRepository.save(model);
    }

    /**
     * 获取用户的任务列表
     */
    public Page<TaskEntity> getUserTasks(Long userId, String keyword, Pageable pageable) {
        if (keyword != null && !keyword.trim().isEmpty()) {
            return taskRepository.findByCreatorIdAndKeyword(userId, keyword.trim(), pageable);
        } else {
            return taskRepository.findByCreatorId(userId, pageable);
        }
    }
    
    /**
     * 获取任务详情
     */
    public TaskEntity getTaskDetail(Long taskId, Long userId) {
        TaskEntity task = taskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "任务不存在"));
        
        // 验证是否是任务创建者
        if (!task.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权查看此任务");
        }
        
        return task;
    }

    /**
     * 删除任务
     */
    @Transactional
    public void deleteTask(Long taskId, Long userId) {
        // 1. 获取任务实体
        TaskEntity task = taskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "任务不存在"));
        
        // 2. 检查权限（只有创建者可以删除）
        if (!task.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权删除此任务");
        }
        
        // 3. 检查任务状态（不能删除正在运行的任务）
        if (task.getStatus() == TaskEntity.TaskStatus.RUNNING) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无法删除正在运行的任务");
        }
        
        // 4. 解除与代码的关联
        task.getCodes().clear();
        
        // 5. 删除数据库记录
        taskRepository.delete(task);
    }

    /**
     * 批量删除任务
     */
    @Transactional
    public void batchDeleteTasks(List<Long> ids, Long userId) {
        for (Long id : ids) {
            deleteTask(id, userId);
        }
    }

    /**
     * 获取任务统计数据
     */
    public Map<String, Object> getTaskStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取所有任务
        List<TaskEntity> allTasks = taskRepository.findAll();
        
        // 计算基本统计数据
        int totalTasks = allTasks.size();
        int completedTasks = countTasksByStatus(allTasks, TaskStatus.COMPLETED);
        int runningTasks = countTasksByStatus(allTasks, TaskStatus.RUNNING);
        int failedTasks = countTasksByStatus(allTasks, TaskStatus.FAILED);
        
        // 添加基本统计数据到结果
        result.put("totalTasks", totalTasks);
        result.put("completedTasks", completedTasks);
        result.put("runningTasks", runningTasks);
        result.put("failedTasks", failedTasks);
        
        // 计算状态分布
        List<Map<String, Object>> statusDistribution = calculateStatusDistribution(allTasks);
        result.put("statusDistribution", statusDistribution);
        
        // 计算任务创建趋势（最近7天）
        List<Map<String, Object>> taskTrend = calculateTaskTrend(allTasks);
        result.put("taskTrend", taskTrend);
        
        return result;
    }

    /**
     * 统计指定状态的任务数量
     */
    private int countTasksByStatus(List<TaskEntity> tasks, TaskStatus status) {
        return (int) tasks.stream()
                .filter(task -> status.equals(task.getStatus()))
                .count();
    }

    /**
     * 计算任务状态分布
     */
    private List<Map<String, Object>> calculateStatusDistribution(List<TaskEntity> tasks) {
        Map<TaskStatus, Long> statusCounts = tasks.stream()
                .collect(Collectors.groupingBy(TaskEntity::getStatus, Collectors.counting()));
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (Map.Entry<TaskStatus, Long> entry : statusCounts.entrySet()) {
            Map<String, Object> item = new HashMap<>();
            item.put("status", entry.getKey().name());
            item.put("count", entry.getValue());
            result.add(item);
        }
        
        return result;
    }

    /**
     * 计算最近7天的任务创建趋势
     */
    private List<Map<String, Object>> calculateTaskTrend(List<TaskEntity> tasks) {
        // 获取最近7天的日期
        LocalDate today = LocalDate.now();
        List<LocalDate> last7Days = new ArrayList<>();
        for (int i = 6; i >= 0; i--) {
            last7Days.add(today.minusDays(i));
        }
        
        // 格式化日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        
        // 按日期分组统计任务数量
        Map<LocalDate, Long> taskCountByDate = tasks.stream()
                .filter(task -> task.getTimeCreated() != null)
                .collect(Collectors.groupingBy(
                        task -> task.getTimeCreated().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        Collectors.counting()
                ));
        
        // 构建结果
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (LocalDate date : last7Days) {
            Map<String, Object> item = new HashMap<>();
            item.put("date", date.format(formatter));
            item.put("count", taskCountByDate.getOrDefault(date, 0L));
            result.add(item);
        }
        
        return result;
    }

    /**
     * 更新任务状态
     * 根据关联代码的状态更新任务状态
     */
    @Transactional
    public void updateTaskStatus(Long taskId) {
        TaskEntity task = taskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "任务不存在"));
        
        // 如果任务没有关联代码，则不更新状态
        if (task.getCodes() == null || task.getCodes().isEmpty()) {
            return;
        }
        
        boolean hasRunningCode = false;
        boolean allCodesCompleted = true;
        boolean hasFailedCode = false;
        
        for (CodeEntity code : task.getCodes()) {
            if (code.getStatus() == CodeEntity.ApprovalStatus.RUNNING) {
                hasRunningCode = true;
                allCodesCompleted = false;
                break;
            } else if (code.getStatus() == CodeEntity.ApprovalStatus.ERROR) {
                hasFailedCode = true;
                allCodesCompleted = false;
            } else if (code.getStatus() != CodeEntity.ApprovalStatus.COMPLETED) {
                allCodesCompleted = false;
            }
        }
        
        // 更新任务状态
        if (hasRunningCode) {
            task.setStatus(TaskStatus.RUNNING);
        } else if (allCodesCompleted) {
            task.setStatus(TaskStatus.COMPLETED);
        } else if (hasFailedCode) {
            task.setStatus(TaskStatus.FAILED);
        }
        
        taskRepository.save(task);
    }

    /**
     * 刷新任务状态
     * 刷新任务关联的所有代码状态，并更新任务状态
     */
    @Transactional
    public TaskEntity refreshTaskStatus(Long taskId, Long userId) {
        // 1. 获取任务实体
        TaskEntity task = taskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "任务不存在"));
        
        // 2. 检查权限（只有创建者可以刷新）
        if (!task.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权刷新此任务状态");
        }
        
        // 3. 如果任务没有关联代码，则不更新状态
        if (task.getCodes() == null || task.getCodes().isEmpty()) {
            return task;
        }
        
        // 4. 刷新每个代码的状态
        for (CodeEntity code : task.getCodes()) {
            if (code.getSyceeJobId() != null && !code.getSyceeJobId().isEmpty()) {
                try {
                    // 调用Sycee服务获取结果
                    Map<String, Object> syceeResult = syceeService.refreshJobStatus(
                            code.getNode().getId(),
                            userId,
                            code.getSyceeJobId()
                    );
                    
                    // 检查Sycee服务返回结果
                    if (syceeResult != null && syceeResult.containsKey("status")) {
                        // 更新代码状态
                        String jobStatus = syceeResult.get("status").toString();
                        if (jobStatus.equals("completed")) {
                            code.setStatus(CodeEntity.ApprovalStatus.COMPLETED);
                        } else if (jobStatus.equals("errored") || jobStatus.equals("FAILED")) {
                            code.setStatus(CodeEntity.ApprovalStatus.ERROR);
                        } else if (jobStatus.equals("processing")) {
                            code.setStatus(CodeEntity.ApprovalStatus.RUNNING);
                        }
                        codeRepository.save(code);
                    }
                } catch (Exception e) {
                    log.error("刷新代码状态失败: codeId={}, error={}", code.getId(), e.getMessage());
                    // 继续处理下一个代码，不中断流程
                }
            }
        }
        
        // 5. 更新任务状态
        boolean hasRunningCode = false;
        boolean allCodesCompleted = true;
        boolean hasFailedCode = false;
        
        for (CodeEntity code : task.getCodes()) {
            if (code.getStatus() == CodeEntity.ApprovalStatus.RUNNING) {
                hasRunningCode = true;
                allCodesCompleted = false;
                break;
            } else if (code.getStatus() == CodeEntity.ApprovalStatus.ERROR) {
                hasFailedCode = true;
                allCodesCompleted = false;
            } else if (code.getStatus() != CodeEntity.ApprovalStatus.COMPLETED) {
                allCodesCompleted = false;
            }
        }
        
        // 更新任务状态
        if (hasRunningCode) {
            task.setStatus(TaskEntity.TaskStatus.RUNNING);
        } else if (allCodesCompleted) {
            task.setStatus(TaskEntity.TaskStatus.COMPLETED);
        } else if (hasFailedCode) {
            task.setStatus(TaskEntity.TaskStatus.FAILED);
        }
        
        return taskRepository.save(task);
    }
} 