<!DOCTYPE html>
<html>

<head>
    <title>Classifier - Service {{ service_id[:8] }}</title>
    <style>
        :root {
            --primary-color: #4285f4;
            --secondary-color: #34a853;
            --error-color: #ea4335;
            --warning-color: #fbbc05;
            --light-gray: #f5f5f5;
            --dark-gray: #333;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--dark-gray);
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }

        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }

        .service-title {
            color: var(--primary-color);
            margin: 0;
        }

        .service-id {
            background-color: var(--light-gray);
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
        }

        .countdown {
            text-align: center;
            padding: 12px;
            background-color: #fff8e1;
            color: #ff8f00;
            border-radius: 6px;
            margin-bottom: 15px;
            font-weight: 500;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            margin-bottom: 15px;
            background: white;
            transition: all 0.3s;
            position: relative;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background-color: #f8f9fa;
        }

        .upload-area.active {
            border-color: var(--secondary-color);
            background-color: #e8f5e9;
        }

        .upload-text {
            font-size: 18px;
            margin-bottom: 10px;
        }

        #image-preview {
            max-width: 100%;
            max-height: 300px;
            margin-top: 20px;
            border-radius: 4px;
            display: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        #file-input {
            display: none;
        }

        .result-container {
            margin-top: 30px;
            padding: 25px;
            border-radius: 8px;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .result-title {
            margin-top: 0;
            color: var(--primary-color);
        }

        .prediction-result {
            font-size: 18px;
            margin-bottom: 15px;
        }

        .confidence-meter {
            margin: 20px 0;
        }

        .progress-container {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .progress-label {
            width: 120px;
            font-weight: 500;
        }

        .progress-bar {
            flex-grow: 1;
            height: 20px;
            background-color: var(--light-gray);
            border-radius: 10px;
            overflow: hidden;
            margin: 0 15px;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--primary-color);
            width: 0%;
            transition: width 0.5s ease-out;
        }

        .progress-fill.top-prediction {
            background-color: var(--secondary-color);
        }

        .progress-value {
            width: 60px;
            text-align: right;
            font-family: monospace;
        }

        .loading {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 10;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div class="service-header">
        <h1 class="service-title">Classification Service</h1>
        <div class="service-id">ID: {{ service_id[:8] }}...</div>
    </div>

    <div class="countdown">
        <div>Service will automatically shut down in <span id="countdown-timer">30:00</span></div>
    </div>

    <div class="upload-area" id="upload-area">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Analyzing image...</div>
        </div>
        <div class="upload-text">Click or drag image here to upload</div>
        <input title="file" type="file" id="file-input" accept="image/*">
        <img id="image-preview" alt="Image preview">
    </div>

    <div class="result-container" id="result-container">
        <h2 class="result-title">Prediction Results</h2>
        <div class="prediction-result" id="prediction-result"></div>

        <div class="confidence-meter">
            <h3>Classification Confidence</h3>
            <div id="class-probabilities"></div>
        </div>
    </div>

    <script>
        // Countdown functionality
        const destroyTime = {{ destroy_time }};

        function updateCountdown() {
            const now = new Date().getTime()
            const distance = destroyTime - now

            if (distance < 0) {
                document.getElementById('countdown-timer').textContent = "00:00"
                document.querySelector('.countdown').style.backgroundColor = '#ffebee'
                document.querySelector('.countdown').style.color = "var('--error-color')"
                document.querySelector('.countdown').textContent = "Service has expired, please redeploy"
                return
            }

            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
            const seconds = Math.floor((distance % (1000 * 60)) / 1000)

            document.getElementById('countdown-timer').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`

            setTimeout(updateCountdown, 1000)
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function () {
            updateCountdown()

            // Initialize drag and drop
            const uploadArea = document.getElementById('upload-area')
            const fileInput = document.getElementById('file-input')
            const imagePreview = document.getElementById('image-preview')
            const loadingIndicator = document.getElementById('loading')

            // Click to upload
            uploadArea.addEventListener('click', function () {
                fileInput.click()
            })

            // Handle file selection
            fileInput.addEventListener('change', handleFileSelect)

            // Drag and drop functionality
            uploadArea.addEventListener('dragover', function (e) {
                e.preventDefault()
                this.classList.add('active')
            })

            uploadArea.addEventListener('dragleave', function () {
                this.classList.remove('active')
            })

            uploadArea.addEventListener('drop', function (e) {
                e.preventDefault()
                this.classList.remove('active')

                if (e.dataTransfer.files.length) {
                    fileInput.files = e.dataTransfer.files
                    handleFileSelect({ target: fileInput })
                }
            })
        })

        // Handle file selection
        function handleFileSelect(event) {
            const file = event.target.files[0]
            if (!file || !file.type.match('image.*')) {
                alert('Please select a valid image file')
                return
            }

            if (file.size > 5 * 1024 * 1024) {
                alert('Image size must be less than 5MB')
                return
            }

            const uploadArea = document.getElementById('upload-area')
            const imagePreview = document.getElementById('image-preview')
            const loadingIndicator = document.getElementById('loading')

            // Show image preview
            const reader = new FileReader()
            reader.onload = function (e) {
                imagePreview.src = e.target.result
                imagePreview.style.display = 'block'

                // Show loading indicator
                loadingIndicator.style.display = 'flex'

                // Upload file to server
                uploadFile(file)
            }
            reader.readAsDataURL(file)
        }

        // Upload file to server
        function uploadFile(file) {
            const formData = new FormData()
            formData.append('file', file)
            const loadingIndicator = document.getElementById('loading')

            fetch('predict', {
                method: 'POST',
                body: formData
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Server response error')
                    }
                    return response.json()
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error)
                    }

                    // Hide loading indicator
                    loadingIndicator.style.display = 'none'

                    // Show results container
                    const resultContainer = document.getElementById('result-container')
                    resultContainer.style.display = 'block'

                    // Process prediction results
                    const predictions = data.predictions

                    // Convert to array and sort
                    const sortedPredictions = Object.entries(predictions)
                        .map(([className, confidence]) => ({ className, confidence }))
                        .sort((a, b) => b.confidence - a.confidence)

                    // Get top prediction
                    const topPrediction = sortedPredictions[0]

                    // Display prediction result
                    document.getElementById('prediction-result').innerHTML =
                        `Most likely class: <strong>${topPrediction.className}</strong> (confidence: ${(topPrediction.confidence * 100).toFixed(2)}%)`

                    // Display all class probabilities
                    const classProbsContainer = document.getElementById('class-probabilities')
                    classProbsContainer.innerHTML = ''

                    sortedPredictions.forEach((prediction, index) => {
                        const probPercent = (prediction.confidence * 100).toFixed(2)
                        const isTopClass = index === 0

                        const probItem = document.createElement('div')
                        probItem.className = 'progress-container'

                        probItem.innerHTML = `
                        <div class="progress-label">${prediction.className}</div>
                        <div class="progress-bar">
                            <div class="progress-fill ${isTopClass ? 'top-prediction' : ''}" style="width: ${probPercent}%"></div>
                        </div>
                        <div class="progress-value">${probPercent}%</div>
                    `

                        classProbsContainer.appendChild(probItem)
                    })

                })
                .catch(error => {
                    loadingIndicator.style.display = 'none'
                    alert('Prediction failed: ' + error.message)
                    console.error('Error:', error)
                })
        }
    </script>
</body>

</html>