package ouc.isclab.dataset.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import ouc.isclab.dataset.entity.DatasetEntity;
import ouc.isclab.node.entity.NodeEntity;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface DatasetRepository extends JpaRepository<DatasetEntity, Long> {
    // 根据创建者ID查询数据集
    Page<DatasetEntity> findByCreatorId(Long creatorId, Pageable pageable);
    
    // 根据名称模糊查询
    List<DatasetEntity> findByNameContaining(String name);
    
    // 统计用户创建的数据集数量
    long countByCreatorId(Long creatorId);
    
    // 检查数据集是否属于某个用户
    boolean existsByIdAndCreatorId(Long datasetId, Long creatorId);

    // 根据节点查询关联的数据集
    Page<DatasetEntity> findByAvailableNodesContaining(NodeEntity node, Pageable pageable);

    /**
     * 查找用户创建的所有数据集ID
     */
    @Query("SELECT d.id FROM DatasetEntity d WHERE d.creatorId = :creatorId")
    List<Long> findDatasetIdsByCreatorId(@Param("creatorId") Long creatorId);
} 