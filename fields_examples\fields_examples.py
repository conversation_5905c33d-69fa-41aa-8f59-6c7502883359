from .scrip_slim import create_app, run_app
from .scrip_slim import InputTemplateConfig, OutputTemplateConfig
import base64


def get_model():
    class Model:
        def __init__(self):
            pass

        def image_to_base64(self, file_path):
            with open(file_path, "rb") as image_file:
                encoded_string = base64.b64encode(
                    image_file.read()).decode('utf-8')
            return encoded_string

        def pipe(self, **kwargs):
            res = {
                "input": kwargs,
            }

            if kwargs.get('avatar'):
                res['avatar'] = f"data:image/jpeg;base64,{self.image_to_base64(kwargs.get('avatar'))}"

            if kwargs.get('interests'):
                interests = kwargs.get('interests')
                if isinstance(interests, str):
                    interests = [interests]
                res['interests'] = interests

            if kwargs.get('website'):
                res['website'] = '<a href=' + \
                    kwargs.get('website')+'>PersonalWebsite</a>'

            res['tableA'] = [[1, 3, 5], [2, 4, 6]]
            res['tableB'] = [{'d': 7, "e": 9, "f": 11},
                             {'d': 10, "e": 12, "f": 14}]
            return res

    model = Model()

    return model


def main(port=8000):
    app = create_app(
        get_model=get_model,
        timeout_minutes=120,
        input_template_config=(
            InputTemplateConfig()
            .set_title("Input Fields examples")
            # Basic input types
            .add_text_field(
                name="username",
                label="Username",
                placeholder="Please enter 3-20 characters",
                value="Default user"
            )
            .add_number_field(
                name="age",
                label="Age",
                placeholder="18-99",
                value=25
            )
            .add_email_field(
                name="email",
                label="Email",
                placeholder="<EMAIL>",
                required=True
            )
            .add_password_field(
                name="password",
                label="Password",
                placeholder="At least 8 characters"
            )
            # File upload types
            .add_image_field(
                name="avatar",
                label="Profile Avatar",
                placeholder="Upload your profile photo"
            )
            .add_file_field(
                name="singlefile",
            )
            .add_multi_file_field(
                name="documents",
                label="Document upload",
                help_text="Multiple files allowed, max 10MB"
            )
            # img display
            .add_image_display(
                label="img_display",
                # src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgNTAiIHdpZHRoPSIxMDAiIGhlaWdodD0iNTAiPgogIDxwYXRoIGQ9Ik0xMCw0MCBRNTAsMTAgOTAsNDAiIHN0cm9rZT0iIzM0OThkYiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIiAvPgo8L3N2Zz4="
                src="static/1.png" # if use static file
            )
            # Selection types
            .add_select_field(
                name="education",
                label="Education",
                options=[
                    {"value": "high_school", "label": "High School"},
                    {"value": "college", "label": "College", "selected": True},
                    {"value": "bachelor", "label": "Bachelor"},
                    {"value": "master", "label": "Master"},
                    {"value": "phd", "label": "PhD"}
                ]
            )
            # Radio button group
            .add_radio_field(
                name="gender",
                label="Gender",
                options=[
                    {"value": "male", "label": "Male"},
                    {"value": "female", "label": "Female", "checked": True},
                    {"value": "other", "label": "Other"}
                ]
            )
            # Multiple checkbox
            .add_checkbox_group(
                name="interests",
                label="Interests",
                options=[
                    {"value": "sports", "label": "Sports", "checked": True},
                    {"value": "music", "label": "Music"},
                    {"value": "reading", "label": "Reading"},
                    {"value": "travel", "label": "Travel"}
                ]
            )
            # Single checkbox
            .add_checkbox_field(
                name="agree_terms",
                label="I have read and agree to the terms",
                value="agreed",
                checked=False,
                required=True
            )
            # Date/time types
            .add_date_field(
                name="birthday",
                label="Birthday",
                value="1990-01-01"
            )
            .add_datetime_field(
                name="meeting_time",
                label="Meeting time"
            )
            # Other HTML5 input types
            .add_color_field(
                name="theme_color",
                label="Theme color",
                value="#4285f4"
            )
            .add_range_field(
                name="volume",
                label="Volume control",
                min=0,
                max=100,
                step=5,
                value=50
            )
            .add_url_field(
                name="website",
                label="Personal website",
                placeholder="https://example.com"
            )
            .add_phone_field(
                name="phone",
                label="Phone number",
                pattern="[0-9]{11}"
            )
            .add_html_content(
                content="<a href='https://www.bing.com'>test html display</a>"
            )
            .build()
        ),

        output_template_config=(
            OutputTemplateConfig()
            .set_title("Output Fields examples")
            .add_json_field(
                name="input",
                label="json"
            )
            .add_image_field(
                name="avatar",
                label="img"
            )
            .add_list_field(
                name="interests",
                label="list"
            )
            .add_html_field(
                name="website",
                label="html"
            )
            .add_table_field(
                name="tableA",
                headers=["a", "b", "c"],
                label="tableA"
            )
            .add_table_field(
                name="tableB",
                headers=["d", "e", "f"],
                label="tableB"
            )
            .build()
        )

    )
    run_app(app, port)
