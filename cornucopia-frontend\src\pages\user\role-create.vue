<template>
  <div class="role-create-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Plus /></el-icon>
          <h2>创建角色</h2>
        </div>
        <div class="sub-title">创建新的系统角色</div>
      </div>
    </div>

    <el-card class="box-card">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="role-form"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入角色名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入角色描述"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit">创建</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Plus } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const router = useRouter()
const formRef = ref(null)

// 表单数据
const form = ref({
  name: '',
  description: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 使用 URLSearchParams 构造请求参数
        const params = new URLSearchParams()
        Object.keys(form.value).forEach(key => {
          params.append(key, form.value[key])
        })
        
        const res = await service.post('/api/v1.0/sys/role', params)
        if (res.code === 10000) {
          toast('成功', '角色创建成功')
          router.push('/user/role/list')
        } else {
          // 处理非10000的返回码
          toast('错误', res.message || '角色创建失败', 'error')
        }
      } catch (error) {
        // 处理所有异常情况
        toast('错误', error.response?.data?.message || '角色创建失败', 'error')
      }
    }
  })
}

// 取消
const handleCancel = () => {
  router.back()
}
</script>

<style scoped>
.role-create-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  
  .icon {
    margin-right: 8px;
    font-size: 18px;
    color: var(--el-color-primary);
  }
}

.role-form {
  max-width: 600px;
  margin: 0 auto;
  padding-top: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}
</style> 