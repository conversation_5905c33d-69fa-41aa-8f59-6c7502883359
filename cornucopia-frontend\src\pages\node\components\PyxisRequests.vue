<template>
  <div class="request-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Message /></el-icon>
          <h2>待审批请求</h2>
        </div>
        <div class="sub-title">查看并管理待审批的任务请求</div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table 
        v-loading="loading"
        :data="requests" 
        stripe 
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        border
      >
        <el-table-column prop="task_id" label="任务ID" min-width="220" align="center" show-overflow-tooltip />
        <el-table-column prop="owner_id" label="所有者ID" min-width="220" align="center" show-overflow-tooltip />
        <el-table-column prop="audit_message" label="审批消息" min-width="180" align="center" show-overflow-tooltip />
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <el-button-group class="operation-group">
              <el-tooltip content="批准" placement="top">
                <el-button type="success" size="small" @click="handleApprove(scope.row)">
                  <el-icon><Check /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="拒绝" placement="top">
                <el-button type="danger" size="small" @click="handleReject(scope.row)">
                  <el-icon><Close /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看详情" placement="top">
                <el-button type="primary" size="small" @click="handleViewDetail(scope.row)">
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 拒绝对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="拒绝请求"
      width="500px"
      center
    >
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝理由" required>
          <el-input 
            v-model="rejectForm.message" 
            type="textarea" 
            :rows="4"
            placeholder="请输入拒绝理由" 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false" round>取消</el-button>
          <el-button type="primary" @click="confirmReject" :loading="rejectLoading" round>
            确认拒绝
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="请求详情"
      width="900px"
      center
    >
      <div v-loading="detailLoading" class="detail-container">
        <el-tabs type="border-card">
          <!-- 基本信息标签页 -->
          <el-tab-pane label="基本信息">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="任务ID">{{ currentDetail.task_id }}</el-descriptions-item>
              <el-descriptions-item label="所有者ID">{{ currentDetail.owner_id }}</el-descriptions-item>
              <el-descriptions-item label="审批消息">{{ currentDetail.audit_message }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          
          <!-- 文件浏览标签页 -->
          <el-tab-pane label="文件浏览">
            <div class="file-browser-panel">
              <div class="current-path">
                <div class="path-nav">
                  <el-button 
                    v-if="currentPath" 
                    size="small" 
                    @click="navigateToParent"
                    type="primary"
                    plain
                  >
                    <el-icon><ArrowUp /></el-icon> 返回上级
                  </el-button>
                  <span>当前路径: {{ currentPath || '/' }}</span>
                  <el-button 
                    size="small" 
                    @click="refreshFiles" 
                    type="info"
                    plain
                  >
                    <el-icon><RefreshRight /></el-icon> 刷新
                  </el-button>
                </div>
              </div>
              
              <el-empty v-if="files.length === 0" description="暂无文件" />
              
              <!-- 文件列表 -->
              <el-table v-else :data="files" style="width: 100%" border>
                <el-table-column prop="name" label="文件名" min-width="220">
                  <template #default="scope">
                    <div class="file-item" @click="handleFileClick(scope.row)">
                      <el-icon v-if="scope.row.is_dir" class="file-icon"><Folder /></el-icon>
                      <el-icon v-else class="file-icon"><Document /></el-icon>
                      <span>{{ scope.row.name }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="size" label="大小" width="120" align="right">
                  <template #default="scope">
                    {{ formatSize(scope.row.size) }}
                  </template>
                </el-table-column>
                <el-table-column prop="last_modified" label="修改时间" width="180" align="center">
                  <template #default="scope">
                    {{ formatDate(scope.row.last_modified) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template #default="scope">
                    <div class="file-actions">
                      <el-button
                        v-if="!scope.row.is_dir"
                        type="primary"
                        size="small"
                        @click.stop="handleDownload(scope.row)"
                        plain
                      >
                        下载
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false" round>关闭</el-button>
          <el-button-group>
            <el-button type="success" @click="handleApprove(currentDetail)" round>批准</el-button>
            <el-button type="danger" @click="handleReject(currentDetail)" round>拒绝</el-button>
          </el-button-group>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { 
  Message, View, Check, Close, 
  Folder, Document, ArrowUp, RefreshRight
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'
import { ElLoading } from 'element-plus'

const route = useRoute()
const nodeId = ref('')
const requests = ref([])
const loading = ref(false)

// 文件浏览相关
const files = ref([])
const filesLoading = ref(false)
const currentPath = ref('')

// 获取待审批请求列表
const getRequests = async () => {
  if (!nodeId.value) return
  loading.value = true
  try {
    const res = await service.get(`/api/v1.0/pyxis/${nodeId.value}/audit/pending`)
    if (res.code === 10000 && res.data) {
      // 处理嵌套的数据结构
      const requestsData = res.data.data || res.data
      
      if (Array.isArray(requestsData)) {
        requests.value = requestsData
      } else {
        console.error('无效的请求数据格式', requestsData)
        toast("错误", "无效的数据格式", "error")
      }
    } else {
      toast("错误", res.message || "获取请求列表失败", "error")
    }
  } catch (error) {
    console.error('获取请求列表失败:', error)
    toast("错误", "获取请求列表失败", "error")
  } finally {
    loading.value = false
  }
}

// 批准请求
const handleApprove = async (row) => {
  try {
    const payload = {
      task_id: row.task_id,
      approve: true,
      message: "已批准"
    }
    
    const res = await service.post(`/api/v1.0/pyxis/${nodeId.value}/audit/approve`, payload)
    
    if (res.code === 10000) {
      toast('成功', '请求已批准', 'success')
      getRequests() // 刷新列表
    } else {
      toast('错误', res.message || '批准失败', 'error')
    }
  } catch (error) {
    console.error('批准请求失败:', error)
    toast('错误', '批准失败', 'error')
  }
}

// 拒绝请求对话框
const rejectDialogVisible = ref(false)
const rejectLoading = ref(false)
const rejectForm = ref({
  task_id: '',
  message: ''
})

// 显示拒绝对话框
const handleReject = (row) => {
  rejectForm.value = {
    task_id: row.task_id,
    message: ''
  }
  rejectDialogVisible.value = true
}

// 确认拒绝
const confirmReject = async () => {
  if (!rejectForm.value.message) {
    toast('警告', '请填写拒绝理由', 'warning')
    return
  }

  rejectLoading.value = true
  try {
    const payload = {
      task_id: rejectForm.value.task_id,
      approve: false,
      message: rejectForm.value.message
    }
    
    const res = await service.post(`/api/v1.0/pyxis/${nodeId.value}/audit/approve`, payload)
    
    if (res.code === 10000) {
      toast('成功', '请求已拒绝', 'success')
      rejectDialogVisible.value = false
      getRequests() // 刷新列表
    } else {
      toast('错误', res.message || '拒绝失败', 'error')
    }
  } catch (error) {
    console.error('拒绝请求失败:', error)
    toast('错误', '拒绝失败', 'error')
  } finally {
    rejectLoading.value = false
  }
}

// 详情对话框
const detailDialogVisible = ref(false)
const detailLoading = ref(false)
const currentDetail = ref({})

// 查看详情
const handleViewDetail = async (row) => {
  currentDetail.value = { ...row }
  detailDialogVisible.value = true
  currentPath.value = ''
  
  // 加载文件
  await loadFiles()
}

// 加载文件
const loadFiles = async (path = '') => {
  if (!currentDetail.value.task_id) return
  
  filesLoading.value = true
  try {
    // 确保路径格式正确
    let apiPath = `/api/v1.0/pyxis/${nodeId.value}/task/${currentDetail.value.task_id}/browse/`
    
    if (path) {
      // 添加路径，不对斜杠进行编码
      apiPath += path.split('/').map(segment => encodeURIComponent(segment)).join('/') + '/'
    }
    
    const res = await service.get(apiPath)
    
    if (res.code === 10000 && res.data) {
      // 处理嵌套的目录结构
      let filesList = []
      
      // 检查返回数据格式
      if (res.data.type === 'directory' && res.data.children) {
        // 新的API格式，包含嵌套目录结构
        filesList = res.data.children.map(item => {
          // 处理时间戳，确保正确转换
          let lastModified = null
          if (item.modified) {
            try {
              const ts = parseFloat(item.modified)
              // 判断是秒级还是毫秒级时间戳
              lastModified = ts > 10000000000 ? new Date(ts) : new Date(ts * 1000)
              // 检查是否有效
              if (isNaN(lastModified.getTime()) || lastModified.getFullYear() === 1970) {
                lastModified = null
              }
            } catch (e) {
              console.error('时间戳解析错误:', item.modified)
              lastModified = null
            }
          }
          
          return {
            name: item.name,
            is_dir: item.type === 'directory',
            size: item.size || 0,
            last_modified: lastModified,
            path: item.path,
            extension: item.extension,
            content: item.content
          }
        })
      } else if (Array.isArray(res.data)) {
        // 旧的API格式，直接是文件数组
        filesList = res.data
      }
      
      // 排序：目录在前，文件在后
      filesList.sort((a, b) => {
        if (a.is_dir && !b.is_dir) return -1
        if (!a.is_dir && b.is_dir) return 1
        return a.name.localeCompare(b.name)
      })
      
      files.value = filesList
      currentPath.value = path
    } else {
      toast("错误", res.message || "获取文件列表失败", "error")
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    toast("错误", "获取文件列表失败", "error")
  } finally {
    filesLoading.value = false
  }
}

// 刷新文件列表
const refreshFiles = () => {
  loadFiles(currentPath.value)
}

// 处理文件点击
const handleFileClick = (file) => {
  if (file.is_dir) {
    // 如果是目录，则进入该目录
    if (file.path) {
      // 如果有完整路径，直接使用
      loadFiles(file.path)
    } else {
      // 否则拼接路径
      const newPath = currentPath.value ? `${currentPath.value}/${file.name}` : file.name
      loadFiles(newPath)
    }
  } else {
    // 如果是文件，可以预览或下载
    handleDownload(file)
  }
}

// 返回上级目录
const navigateToParent = () => {
  if (!currentPath.value) return
  
  // 获取父目录路径
  const pathParts = currentPath.value.split('/')
  pathParts.pop()
  const parentPath = pathParts.join('/')
  
  // 加载父目录
  loadFiles(parentPath)
}

// 格式化文件大小
const formatSize = (size) => {
  // 检查size是否有效
  if (!size || isNaN(parseFloat(size))) return '-'
  
  // 尝试提取数字部分
  let numericSize = size
  if (typeof size === 'string') {
    // 如果是形如 "1.60 KB" 的字符串，提取数字部分
    const match = size.match(/^([\d.]+)/)
    if (match) {
      numericSize = parseFloat(match[1])
      
      // 根据单位调整大小
      if (size.includes('KB')) numericSize *= 1024
      else if (size.includes('MB')) numericSize *= 1024 * 1024
      else if (size.includes('GB')) numericSize *= 1024 * 1024 * 1024
    } else {
      return size // 如果无法解析，直接返回原始值
    }
  }
  
  // 格式化大小
  const bytes = parseFloat(numericSize)
  if (isNaN(bytes)) return '-'
  
  if (bytes < 1024) return bytes.toFixed(0) + ' B'
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB'
  if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB'
  return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '-'
  
  try {
    // 检查是否是有效的科学计数法格式时间戳
    let date
    if (typeof timestamp === 'number' || /^[\d.]+(?:e[+-]?\d+)?$/i.test(String(timestamp))) {
      // 尝试解析科学计数法表示的时间戳
      const ts = parseFloat(timestamp)
      
      // 检查时间戳范围判断是秒级还是毫秒级
      if (ts > 10000000000) { // 毫秒级时间戳
        date = new Date(ts)
      } else { // 秒级时间戳
        date = new Date(ts * 1000)
      }
      
      // 如果时间戳生成的日期无效或者是1970年，返回'-'
      if (isNaN(date.getTime()) || date.getFullYear() === 1970) {
        // 打印调试信息
        console.log('无效的时间戳或1970年:', timestamp, ts, date.toString())
        return '-'
      }
      
      // 有效的日期
      return date.toLocaleString()
    } else {
      // 尝试作为标准日期格式解析
      date = new Date(timestamp)
      
      if (isNaN(date.getTime())) {
        return '-'
      }
      
      return date.toLocaleString()
    }
  } catch (error) {
    console.error('日期格式化错误:', error, timestamp)
    return '-'
  }
}

// 处理文件下载
const handleDownload = async (file) => {
  if (!currentDetail.value.task_id) return
  
  // 使用文件对象上的path属性，如果存在的话
  const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name)
  
  try {
    // 显示加载动画
    const loading = ElLoading.service({
      lock: true,
      text: '正在下载文件...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    // 获取文件名
    const filename = file.name || filePath.split('/').pop() || 'downloaded_file'
    
    // 构建正确的下载路径 - 根据后端controller的路径结构
    // 注意: 后端使用的是 /download/{nodeId}/** 的路径模式
    let downloadUrl = `/api/v1.0/pyxis/download/${nodeId.value}`
    
    // 添加任务ID和文件路径
    // 需要确保任务ID和文件路径都被正确包含在URL中
    const taskPart = `/task/${currentDetail.value.task_id}`
    const filePart = `/download/${filePath}` 
    
    // 组合完整URL
    const fullUrl = `${downloadUrl}${taskPart}${filePart}`
    
    console.log('下载URL:', fullUrl)
    
    // 直接使用路径方式发送请求，不使用查询参数
    const response = await service.get(fullUrl, {
      responseType: 'blob',
    })
    
    // 创建下载链接
    const blob = new Blob([response])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }, 100)
    
    loading.close()
    toast("成功", "文件下载成功", "success")
  } catch (error) {
    console.error('文件下载失败:', error)
    toast("错误", "文件下载失败: " + (error.message || '未知错误'), "error")
  }
}

onMounted(() => {
  nodeId.value = route.query.id
  getRequests()
})
</script>

<style scoped>
.request-list-container {
  padding: 20px;
}

.list-card {
  border-radius: 8px;
  margin-bottom: 20px;
}

.detail-container {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.request-list-container .page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.request-list-container .header-left {
  flex-shrink: 0;
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.operation-group {
  display: flex;
  justify-content: center;
  gap: 4px;
}

.operation-group :deep(.el-button) {
  padding: 6px 8px;
}

.operation-group :deep(.el-button + .el-button) {
  margin-left: 0;  /* 覆盖 element-plus 的默认间距 */
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
  min-width: 88px;
}

.file-browser-panel {
  padding: 20px;
}

.current-path {
  margin-bottom: 20px;
}

.path-nav {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.file-item:hover {
  color: var(--el-color-primary);
}

.file-icon {
  font-size: 20px;
  color: var(--el-color-primary);
}

.file-actions {
  display: flex;
  justify-content: flex-end;
  gap: 4px;
}

/* 增加文件浏览区域样式 */
:deep(.el-tabs--border-card) {
  box-shadow: none;
}

.file-browser-panel {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.current-path {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
}

:deep(.el-tabs__content) {
  height: 500px;
  overflow-y: auto;
}

:deep(.el-tab-pane) {
  height: 100%;
}

:deep(.el-descriptions) {
  padding: 16px;
}

.detail-container {
  min-height: 500px;
}
</style>


