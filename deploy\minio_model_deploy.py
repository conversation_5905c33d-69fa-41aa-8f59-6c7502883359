from .scrip_slim import create_app, run_app
from .scrip_slim import InputTemplateConfig, OutputTemplateConfig
import io
import torch
from PIL import Image
from torchvision import transforms
import torch.nn as nn
from minio import Minio
import os
from pathlib import Path
import shutil

# 配置参数
CLASSES = ['apple_pie', 'baby_back_ribs', 'baklava', 'beef_carpaccio', 'beef_tartare', 
          'beet_salad', 'beignets', 'bibimbap', 'bread_pudding', 'breakfast_burrito', 
          'bruschetta', 'caesar_salad', 'cannoli', 'caprese_salad', 'carrot_cake', 
          'ceviche', 'cheese_plate', 'cheesecake', 'chicken_curry', 'chicken_quesadilla', 
          'chicken_wings', 'chocolate_cake', 'chocolate_mousse', 'churros', 'clam_chowder', 
          'club_sandwich', 'crab_cakes', 'creme_brulee', 'croque_madame', 'cup_cakes', 
          'deviled_eggs', 'donuts', 'dumplings', 'edamame', 'eggs_benedict', 
          'escargots', 'falafel', 'filet_mignon', 'fish_and_chips', 'foie_gras', 
          'french_fries', 'french_onion_soup', 'french_toast', 'fried_calamari', 'fried_rice', 
          'frozen_yogurt', 'garlic_bread', 'gnocchi', 'greek_salad', 'grilled_cheese_sandwich', 
          'grilled_salmon', 'guacamole', 'gyoza', 'hamburger', 'hot_and_sour_soup', 
          'hot_dog', 'huevos_rancheros', 'hummus', 'ice_cream', 'lasagna', 
          'lobster_bisque', 'lobster_roll_sandwich', 'macaroni_and_cheese', 'macarons', 'miso_soup', 
          'mussels', 'nachos', 'omelette', 'onion_rings', 'oysters', 
          'pad_thai', 'paella', 'pancakes', 'panna_cotta', 'peking_duck', 
          'pho', 'pizza', 'pork_chop', 'poutine', 'prime_rib', 
          'pulled_pork_sandwich', 'ramen', 'ravioli', 'red_velvet_cake', 'risotto', 
          'samosa', 'sashimi', 'scallops', 'seaweed_salad', 'shrimp_and_grits', 
          'spaghetti_bolognese', 'spaghetti_carbonara', 'spring_rolls', 'steak', 'strawberry_shortcake', 
          'sushi', 'tacos', 'takoyaki', 'tiramisu', 'tuna_tartare','waffles']
IMG_SIZE = 224
DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

# MinIO配置
MINIO_ENDPOINT = '10.140.34.208:9000'
MINIO_ACCESS_KEY = 'eaZ2nCveMAYIc0yoeVU2'
MINIO_SECRET_KEY = 'ouc.edu.cn'
MINIO_SECURE = False
MODEL_BUCKET = 'models'
MODEL_NAME = '101food_classifier_final.pt'

# 模型定义
class FoodCNN(nn.Module):
    def __init__(self, num_classes):
        super(FoodCNN, self).__init__()
        self.features = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),

            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),

            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),

            nn.Conv2d(256, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),

            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
        )

        self.classifier = nn.Sequential(
            nn.Linear(512 * 7 * 7, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(4096, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(4096, num_classes),
        )
    
    def forward(self, x):
        x = self.features(x)
        x = torch.flatten(x, 1)
        x = self.classifier(x)
        return x

# 模型加载类
class FoodClassifier:
    def __init__(self):
        self.minio_client = Minio(
            MINIO_ENDPOINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=MINIO_SECURE
        )
        self.transform = transforms.Compose([
            transforms.Resize((IMG_SIZE, IMG_SIZE)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
        self.classes = CLASSES
        self.model = self._load_model()
        
    def _load_model(self):
        try:
            print("正在从MinIO加载模型...")
            response = self.minio_client.get_object(MODEL_BUCKET, MODEL_NAME)
            model_data = io.BytesIO(response.data)
            model = FoodCNN(len(self.classes)).to(DEVICE)
            model.load_state_dict(torch.load(model_data, map_location=DEVICE))
            model.eval()
            print("模型加载成功！")
            return model
        except Exception as e:
            print(f"模型加载失败: {str(e)}")
            raise RuntimeError(f"模型加载失败: {str(e)}")
        
    def pipe(self, **kwargs):
        """接收处理图像并返回预测结果"""
        try:
            # 从kwargs获取图像文件路径
            image_path = kwargs.get('image')
            if not image_path:
                return {"error": "未提供图像文件"}
            
            # 处理图像
            img = Image.open(image_path).convert('RGB')
            img_tensor = self.transform(img).unsqueeze(0).to(DEVICE)
            
            # 预测
            with torch.no_grad():
                outputs = self.model(img_tensor)
                probs = torch.softmax(outputs, dim=1)
                conf, pred = torch.max(probs, 1)
            
            # 准备结果字典
            results = {
                "prediction": self.classes[pred.item()],
                "confidence": float(conf.item()),
                "all_probabilities": {}
            }
            
            # 添加所有类别的概率
            for i, class_name in enumerate(self.classes):
                results["all_probabilities"][class_name] = float(probs.squeeze()[i])
            
            # 对结果按照概率降序排序
            sorted_probs = sorted(
                [(cls, prob) for cls, prob in results["all_probabilities"].items()],
                key=lambda x: x[1],
                reverse=True
            )
            
            # 格式化结果为表格展示
            table_data = [[cls, f"{prob:.4f}"] for cls, prob in sorted_probs[:5]]
            
            return {
                "prediction": results["prediction"],
                "confidence": f"{results['confidence']:.4f}",
                "probabilities_table": table_data,
                "original_image": str(image_path)
            }
            
        except Exception as e:
            print(f"预测失败: {str(e)}")
            return {"error": f"预测失败: {str(e)}"}

def get_model():
    """返回食物分类模型实例
    
    符合pyxis框架要求的模型获取函数，返回一个带有pipe方法的模型实例
    """
    try:
        return FoodClassifier()
    except Exception as e:
        print(f"无法初始化模型: {str(e)}")
        raise RuntimeError(f"无法初始化模型: {str(e)}")

def setup_static_dir():
    """准备静态文件目录"""
    static_dir = Path("./STATIC_DIR")
    static_dir.mkdir(parents=True, exist_ok=True)
    return static_dir

def create_input_template():
    """创建输入模板配置"""
    # 将类别分成几列显示
    num_cols = 4
    rows = [CLASSES[i:i+num_cols] for i in range(0, len(CLASSES), num_cols)]
    
    # 创建HTML表格
    table_html = "<table style='width:100%; border-collapse:collapse;'>"
    for row in rows:
        table_html += "<tr>"
        for cls in row:
            table_html += f"<td style='padding:3px 8px; border:1px solid #ddd;'>{cls}</td>"
        # 如果该行类别不足num_cols个，添加空单元格
        for _ in range(num_cols - len(row)):
            table_html += "<td style='border:1px solid #ddd;'></td>"
        table_html += "</tr>"
    table_html += "</table>"
    
    return (
        InputTemplateConfig()
        .set_title("食物分类模型部署")
        .add_html_content(
            content="<h2>从MinIO加载的食物分类模型</h2>"
                    "<p>此应用从MinIO加载预训练的食品分类模型，可以识别以下101种食物类别：</p>"
                    f"{table_html}"
        )
        .add_image_field(
            name="image",
            label="请上传食物图片",
            placeholder="支持jpg、png等常见图片格式",
            required=True
        )
        .build()
    )

def create_output_template():
    """创建输出模板配置"""
    return (
        OutputTemplateConfig()
        .set_title("食物分类结果")
        .add_html_field(
            name="prediction",
            label="预测类别"
        )
        .add_html_field(
            name="confidence",
            label="置信度"
        )
        .add_table_field(
            name="probabilities_table",
            headers=["类别", "概率"],
            label="前5个最可能的类别"
        )
        .build()
    )

def main(port=8000):
    """启动应用主函数
    
    参数:
        port (int): 服务端口号，默认为8000
    """
    # 设置静态目录
    static_dir = setup_static_dir()
    
    app = create_app(
        get_model=get_model,
        timeout_minutes=30,  # 设置30分钟超时
        input_template_config=create_input_template(),
        output_template_config=create_output_template()
    )
    
    print(f"服务已启动，端口: {port}")
    print(f"模型将在30分钟后自动关闭")
    run_app(app, port)

if __name__ == "__main__":
    main() 