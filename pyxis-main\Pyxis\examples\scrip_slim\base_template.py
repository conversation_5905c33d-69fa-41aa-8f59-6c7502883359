base_html = """
<!DOCTYPE html>
<html>
<head>
    <title>Scrip</title>
    <style>
        :root {
            --primary-color: #4285f4;
            --secondary-color: #34a853;
            --error-color: #ea4335;
            --light-gray: #f5f5f5;
            --dark-gray: #333;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--dark-gray);
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        
        .countdown {
            text-align: center;
            padding: 12px;
            background-color: #fff8e1;
            color: #ff8f00;
            border-radius: 6px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        #input-container {
            margin-bottom: 20px;
        }
        
        #output-container {
            display: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #3367d6;
        }
    </style>
</head>
<body>
    <div class="countdown">
        <div>Service will automatically shut down in <span id="countdown-timer">00:00</span></div>
    </div>

    <div id="input-container" class="container">
        {{ input_template|safe }}
    </div>

    <div id="output-container" class="container">
        <div id="output-content"></div>
    </div>

    <script>
        // Countdown functionality
        const destroyTime = {{ destroy_time }};

        function updateCountdown() {
            const now = new Date().getTime();
            const distance = destroyTime - now;

            if (distance < 0) {
                document.getElementById('countdown-timer').textContent = "00:00";
                document.querySelector('.countdown').style.backgroundColor = '#ffebee';
                document.querySelector('.countdown').textContent = "Service has expired, please redeploy";
                return;
            }

            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            document.getElementById('countdown-timer').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            setTimeout(updateCountdown, 1000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            updateCountdown();
            
            // Handle form submission
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const outputContainer = document.getElementById('output-container');
                    const outputContent = document.getElementById('output-content');
                    const loading = document.createElement('div');
                    loading.className = 'loading';
                    loading.innerHTML = `
                        <div class="spinner"></div>
                        <div>Processing request...</div>
                    `;
                    
                    outputContent.innerHTML = '';
                    outputContent.appendChild(loading);
                    outputContainer.style.display = 'block';
                    
                    const formData = new FormData(form);
                    
                    fetch('predict', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.text())
                    .then(html => {
                        outputContent.innerHTML = html;
                    })
                    .catch(error => {
                        outputContent.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                    });
                });
            }
        });
    </script>
</body>
</html>
"""

input_html = """
<style>
    /* Form styling */
    #prediction-form {
        max-width: 600px;
        margin: 20px auto;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #495057;
    }
    
    .form-group input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        box-sizing: border-box;
    }
    
     .file-upload-wrapper {
        position: relative;
        width: 100%;
        margin-top: 5px;
    }
    
    .file-upload-button {
        display: block;
        padding: 10px;
        background-color: transparent;
        border: 2px dashed #ced4da;
        border-radius: 4px;
        color: #495057;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .file-upload-button:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }
    
    .file-upload-wrapper input[type="file"] {
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }
    
    .file-name-display {
        display: block;
        margin-top: 8px;
        font-size: 13px;
        color: #6c757d;
        font-style: italic;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .file-upload-wrapper.has-file .file-upload-button {
        border-color: #4e73df;
        background-color: #f8f9fa;
    }
    
    #prediction-form button {
        background-color: #4e73df;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s;
        width: 100%;
    }
    
    #prediction-form button:hover {
        background-color: #2e59d9;
    }
</style>

<form id="prediction-form">
    {% for field in config['fields'] %}
        {% if field['type'] == 'file' %}
            <div class="form-group">
                <label for="{{ field['name'] }}">{{ field['label'] }}</label>
                <div class="file-upload-wrapper" id="wrapper-{{ field['name'] }}">
                    <label for="{{ field['name'] }}" class="file-upload-button">Select files</label>
                    <input type="file" 
                           id="{{ field['name'] }}" 
                           name="{{ field['name'] }}" 
                           class="file-input"
                           {% if field.accept %}accept="{{ field.accept }}"{% endif %}>
                    <div class="file-name-display" id="display-{{ field['name'] }}">no file selected</div>
                </div>
            </div>
        {% else %}
            <div class="form-group">
                <label for="{{ field['name'] }}">{{ field['label'] }}</label>
                <input type="{{ field['type'] }}" id="{{ field['name'] }}" name="{{ field['name'] }}" 
                    {% if field['placeholder'] %}placeholder="{{ field['placeholder'] }}"{% endif %}
                    {% if field['value'] %}value="{{ field['value'] }}"{% endif %}>
            </div>
        {% endif %}
    {% endfor %}
    <button type="submit">Submit</button>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.file-input').forEach(input => {
        const wrapper = input.closest('.file-upload-wrapper');
        const display = wrapper.querySelector('.file-name-display');
        

        if(input.files.length > 0) {
            wrapper.classList.add('has-file');
            display.textContent = input.files[0].name;
        }
        
        input.addEventListener('change', function(e) {
            if(this.files.length > 0) {
                wrapper.classList.add('has-file');
                display.textContent = this.files[0].name;
            } else {
                wrapper.classList.remove('has-file');
                display.textContent = '未选择文件';
            }
        });
        
        wrapper.addEventListener('dragover', (e) => {
            e.preventDefault();
            wrapper.classList.add('drag-over');
        });
        
        wrapper.addEventListener('dragleave', () => {
            wrapper.classList.remove('drag-over');
        });
        
        wrapper.addEventListener('drop', (e) => {
            e.preventDefault();
            wrapper.classList.remove('drag-over');
            input.files = e.dataTransfer.files;
            input.dispatchEvent(new Event('change'));
        });
    });
});
</script>


"""

output_html = """
<style>
    /* Results styling */
    .results {
        max-width: 600px;
        margin: 20px auto;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .results h2 {
        color: #2e59d9;
        margin-top: 0;
        padding-bottom: 10px;
        border-bottom: 1px solid #dee2e6;
    }
    
    .results table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .results th {
        background-color: #4e73df;
        color: white;
        text-align: left;
        padding: 10px;
    }
    
    .results td {
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
    }
    
    .results tr:nth-child(even) {
        background-color: #f2f2f2;
    }
    
    /* Enhanced progress bar styling */
    .progress-container {
        width: 100%;
        background-color: #e9ecef;
        border-radius: 4px;
        margin: 5px 0;
        position: relative;
    }
    
    .progress-bar {
        height: 24px;
        border-radius: 4px;
        background-color: #4e73df;
        text-align: right;
        line-height: 24px;
        color: #333;
        font-size: 12px;
        font-weight: bold;
        position: relative;
        overflow: hidden;
    }
    
    .progress-text {
        position:absolute;
        z-index: 2;
        color: black;
        width:100%;
        text-align: center;
    }
    
    /* Result value styling */
    .result-value {
        padding: 15px;
        background-color: #e9ecef;
        border-radius: 4px;
        font-size: 16px;
        color: #495057;
    }
</style>

{% if result %}
    <div class="results">
        <h2>Results</h2>
        {% if result is mapping %}
            <table>
                <thead>
                    <tr>
                        <th>Class</th>
                        <th>Probability</th>
                    </tr>
                </thead>
                <tbody>
                    {% for class_name, prob in result.items() %}
                        <tr>
                            <td>
                                {{ class_name }}
                            </td>
                            <td>
                                <div class="progress-container">
                                    <span class="progress-text">{{ "%.2f"|format(prob * 100) }}%</span>
                                    <div class="progress-bar" style="width: {{ prob * 100 }}%">        
                                    </div>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="result-value">{{ result }}</div>
        {% endif %}
    </div>
{% endif %}
"""
