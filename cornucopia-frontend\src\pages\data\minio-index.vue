<template>
  <div class="data-overview-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><FolderOpened /></el-icon>
          <h2>数据集桶总览</h2>
        </div>
        <div class="sub-title">查看系统数据集桶的整体统计信息</div>
      </div>
      
      <div class="header-right">
        <el-button 
          type="primary" 
          @click="navigateToDataList"
          plain
          round
        >
          <el-icon><Folder /></el-icon>
          浏览数据集
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="stat in statistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><PieChart /></el-icon>
                <span>文件类型分布</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="pieChartRef"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><DataLine /></el-icon>
                <span>近期上传趋势</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="lineChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  FolderOpened, 
  Document, 
  DataLine,
  PieChart,
  Folder,
  Download,
  Share,
  Files,
  FolderAdd,
  Coin
} from '@element-plus/icons-vue'
// 导入所有图标并重命名
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
const StorageIcon = ElementPlusIconsVue.Storage

import { toast } from '~/composables/util'
import service from '~/axios'
import * as echarts from 'echarts'

const router = useRouter()
const pageLoading = ref(false)
const pieChartRef = ref(null)
const lineChartRef = ref(null)
let pieChart = null
let lineChart = null

// 统计数据
const statistics = ref([
  {
    title: '总文件数',
    value: 0,
    type: 'primary',
    icon: Files
  },
  {
    title: '文件夹数',
    value: 0,
    type: 'success',
    icon: FolderAdd
  },
  {
    title: '总存储容量',
    value: '0 MB',
    type: 'warning',
    icon: Coin
  },
  {
    title: '今日上传',
    value: 0,
    type: 'info',
    icon: Document
  }
])

// 获取数据集统计数据
const fetchStatistics = async () => {
  pageLoading.value = true
  try {
    // 使用统计接口
    const res = await service.get('/api/v1.0/sys/file/datasets/statistics')
    
    if (res.code === 10000) {
      const data = res.data;
      
      // 更新统计数据
      statistics.value[0].value = data.totalFiles || 0;
      statistics.value[1].value = data.totalFolders || 0;
      statistics.value[2].value = formatFileSize(data.totalSize || 0);
      statistics.value[3].value = data.todayUploads || 0;
      
      // 更新图表
      updatePieChart(data.fileTypes || {});
      
      // 获取文件列表用于趋势图
      const listRes = await service.get('/api/v1.0/sys/file/datasets/list', {
        params: {
          page: 1,
          size: 1000
        }
      });
      
      if (listRes.code === 10000) {
        updateLineChart(listRes.data.files || []);
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    toast('错误', '获取统计数据失败', 'error');
  } finally {
    pageLoading.value = false;
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size === 0) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(size) / Math.log(1024));
  
  return (size / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
}

// 获取文件扩展名
const getFileExtension = (filename) => {
  if (!filename || typeof filename !== 'string') return '';
  
  const parts = filename.split('.');
  if (parts.length === 1) return '';
  
  return parts[parts.length - 1].toLowerCase();
}

// 初始化饼图
const initPieChart = () => {
  if (pieChartRef.value) {
    pieChart = echarts.init(pieChartRef.value);
  }
}

// 更新饼图数据
const updatePieChart = (fileTypes) => {
  if (!pieChart) return;
  
  const data = Object.entries(fileTypes).map(([name, value]) => ({
    name,
    value
  }));
  
  // 按数量排序
  data.sort((a, b) => b.value - a.value);
  
  // 如果类型太多，只显示前8个，其余归为"其他"
  let finalData = data;
  if (data.length > 8) {
    const topData = data.slice(0, 7);
    const otherCount = data.slice(7).reduce((sum, item) => sum + item.value, 0);
    
    finalData = [
      ...topData,
      { name: '其他', value: otherCount }
    ];
  }
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      type: 'scroll'
    },
    series: [
      {
        name: '文件类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: finalData
      }
    ]
  };
  
  pieChart.setOption(option);
}

// 初始化折线图
const initLineChart = () => {
  if (lineChartRef.value) {
    lineChart = echarts.init(lineChartRef.value);
  }
}

// 更新折线图数据
const updateLineChart = (files) => {
  if (!lineChart) return;
  
  // 获取最近30天的日期
  const today = new Date();
  const dates = [];
  const dateMap = {};
  const formattedDates = []; // 用于显示的格式化日期
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    
    const dateStr = date.toISOString().split('T')[0];
    // 格式化为更简短的日期显示 (MM-DD)
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const formattedDate = `${month}-${day}`;
    
    dates.push(dateStr);
    formattedDates.push(formattedDate);
    dateMap[dateStr] = 0;
  }
  
  // 统计每天上传的文件数
  files.forEach(file => {
    if (!file.lastModified) return;
    
    const dateStr = file.lastModified.split(' ')[0];
    if (dateMap[dateStr] !== undefined) {
      dateMap[dateStr]++;
    }
  });
  
  // 提取数据
  const counts = dates.map(date => dateMap[date]);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%', // 增加底部空间
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: formattedDates, // 使用格式化后的日期
      axisLabel: {
        interval: 'auto',
        rotate: 45, // 增加旋转角度
        fontSize: 10, // 减小字体大小
        margin: 8 // 增加与轴的距离
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '上传文件数',
        type: 'line',
        data: counts,
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 3,
          shadowColor: 'rgba(0,0,0,0.2)',
          shadowBlur: 10,
          shadowOffsetY: 10
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.7)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }
  
  lineChart.setOption(option)
}

// 跳转到数据列表页面
const navigateToDataList = () => {
  router.push('/data/list')
}

onMounted(async () => {
  initPieChart()
  initLineChart()
  await fetchStatistics()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    pieChart?.resize()
    lineChart?.resize()
  })
})

onUnmounted(() => {
  // 销毁图表实例
  pieChart?.dispose()
  lineChart?.dispose()
  
  // 移除事件监听器
  window.removeEventListener('resize', () => {
    pieChart?.resize()
    lineChart?.resize()
  })
})
</script>

<style scoped>
.data-overview-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.stat-card {
  height: 120px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 8px;
}

.stat-icon.primary { background-color: var(--el-color-primary-light-9); }
.stat-icon.success { background-color: var(--el-color-success-light-9); }
.stat-icon.warning { background-color: var(--el-color-warning-light-9); }
.stat-icon.info { background-color: var(--el-color-info-light-9); }

.stat-info {
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  
  .stat-title {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.chart-card {
  height: 450px;
  overflow: hidden;
  margin-bottom: 20px;
}

.chart-container {
  height: 390px;
  padding: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  
  .icon {
    margin-right: 8px;
    font-size: 18px;
    color: var(--el-color-primary);
  }
}

/* 深色模式适配 */
html.dark {
  .stat-card,
  .chart-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style> 