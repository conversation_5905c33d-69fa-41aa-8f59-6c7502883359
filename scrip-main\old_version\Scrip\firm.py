from flask import Flask, request, render_template, jsonify, Response, redirect
import requests
import time
import os
import uuid
import threading
from collections import defaultdict
import shutil
import docker
import socket
import logging

app = Flask(__name__)
app.logger.setLevel(logging.DEBUG)

# Configuration
SERVICE_TIMEOUT_MINUTES = 30  # Service lifetime in minutes
START_PORT = 50000  # Starting port for child services

# Docker client connected to host's Docker socket
docker_client = docker.from_env()
active_services = defaultdict(dict)  # {service_id: {container_id, port, etc.}}
used_ports = set()  # Track used ports to avoid conflicts


def is_port_available(port):
    """Check if a port is available on the host"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return True
        except socket.error:
            return False


def docker_port_in_use(port):
    """Check if port is used by docker"""
    try:
        containers = docker_client.containers.list(
            filters={"publish": f"{port}"})
        return len(containers) > 0
    except docker.errors.APIError:
        return False


def find_available_port(start_port=START_PORT):
    """Find the next available port starting from start_port"""
    port = start_port
    while port <= 65535:
        if is_port_available(port) and not docker_port_in_use(port):
            return port
        port += 1
    raise RuntimeError("No available ports")


def start_docker_service(port, temp_dir, service_id):
    """Launch a child container via host Docker"""

    host_temp_dir = os.getenv('SERVICE_TEMP_FILE', 'temp')
    host_temp_dir = os.path.join(host_temp_dir, os.path.basename(temp_dir))

    app.logger.info(f"{service_id} with host_temp dir {host_temp_dir}")

    try:
        container = docker_client.containers.run(
            image='scrip-service',
            name=f'scrip-service-{service_id}',
            detach=True,
            ports={},
            network='firm-network',
            volumes={
                f"{host_temp_dir}": {'bind': f'/app/user_code', 'mode': 'ro'}
            },
            environment={
                'SERVICE_ID': service_id,
                'PORT': '5000',
                'CODE': f'/app/user_code/user_code.py',
                'TIMEOUT': str(SERVICE_TIMEOUT_MINUTES)
            }
        )

        return container.id

    except docker.errors.APIError as e:
        raise RuntimeError(f"Docker API error: {str(e)}")


def wait_for_service_ready(service_id, port, max_retries=30, retry_interval=1):
    """Wait for child service to become ready"""

    container_id = active_services[service_id]['container_id']
    container = docker_client.containers.get(container_id)
    for _ in range(max_retries):

        container.reload()
        if container.status == 'exited':
            app.logger.info(
                f"Container crashed with logs: {container.logs(tail=20)}")
            raise RuntimeError(
                f"Container crashed with ExitCode {container.attrs['State']['ExitCode']}")
        try:
            resp = requests.get(
                f'http://scrip-service-{service_id}:5000/health', timeout=1)
            if resp.status_code == 200:
                active_services[service_id]['ready'] = True
                return True
        except requests.ConnectionError as e:
            pass

        time.sleep(retry_interval)

    raise RuntimeError("out of time")


def cleanup(service_id, force=False):
    """Clean up service resources"""
    if service_id not in active_services:
        return

    app.logger.info(f"start {service_id} cleanup")

    service = active_services[service_id]
    container_id = service.get('container_id')
    if container_id:
        try:
            container = docker_client.containers.get(container_id)
            container.stop(timeout=5)
            container.reload()
            if force and container.status == 'running':
                container.kill()

        except docker.errors.NotFound as e:
            app.logger.info(f"Container Not Found: {e}")
        except docker.errors.APIError as e:
            app.logger.info(f"Error stopping container: {e}")
        finally:
            try:
                container.remove()
                app.logger.info(f"remove {container_id}")
            except Exception as e:
                app.logger.info(f"failed to remove container:{str(e)}")

    # Remove temporary files
    if 'temp_dir' in service and os.path.exists(service['temp_dir']):
        try:
            shutil.rmtree(service['temp_dir'])
        except Exception as e:
            app.logger.info(f"Error removing temp directory: {e}")

    # Release port
    if 'port' in service:
        used_ports.discard(service['port'])

    # Remove service record
    del active_services[service_id]

    app.logger.info(f"cleanup {service_id}")


def stop_service_after_timeout(service_id, force=False):
    """Stop service after timeout period"""
    if service_id not in active_services:
        return

    service = active_services[service_id]
    time_left = service['stop_time'] - time.time()

    if time_left > 0 and not force:
        time.sleep(time_left)

    cleanup(service_id, force=force)


# ==== routes ====

@app.route('/')
def home():
    """Render the main upload page"""
    return render_template('upload.jinja2')


@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "firm"}), 200


@app.route('/service/<service_id>/', methods=['GET'])
@app.route('/service/<service_id>/<path:service_api>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'])
def access_service(service_id, service_api=''):
    """Proxy requests to child services"""
    if service_id not in active_services:
        return jsonify({
            "status": "error",
            "message": "Service not found or expired",
            "service_id": service_id
        }), 404

    target_url = f'http://scrip-service-{service_id}:5000/{service_api}'

    # Filter and forward headers
    headers = {
        key: value for key, value in request.headers
        if key.lower() not in ['host', 'x-forwarded-for']
    }
    headers['X-Forwarded-For'] = request.remote_addr

    try:
        resp = requests.request(
            method=request.method,
            url=target_url,
            headers=headers,
            data=request.get_data(),
            cookies=request.cookies,
            allow_redirects=False,
            timeout=(10, 30)  # Connect and read timeouts
        )
        # Handle redirects
        if 300 <= resp.status_code < 400:
            return redirect(resp.headers['Location'], code=resp.status_code)

        # Forward the response
        response = Response(
            response=resp.iter_content(chunk_size=8192),
            status=resp.status_code,
            headers=dict(resp.headers)
        )
        return response

    except requests.exceptions.Timeout:
        return jsonify({
            "status": "error",
            "message": "Backend service timeout",
            "service_id": service_id
        }), 504
    except requests.exceptions.ConnectionError:
        return jsonify({
            "status": "error",
            "message": "Failed to connect to backend service",
            "service_id": service_id
        }), 502
    except requests.exceptions.RequestException as e:
        return jsonify({
            "status": "error",
            "message": f"Request forwarding failed: {str(e)}",
            "service_id": service_id
        }), 500
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Unknow Error: {str(e)}",
            "service_id": service_id
        }), 500


@app.route('/upload', methods=['POST'])
def upload_code():
    """Handle code upload and service creation"""
    service_id = str(uuid.uuid4().hex)

    try:
        # Validate input
        if 'code' not in request.form:
            return jsonify({
                "status": "error",
                "message": "No code provided in form data"
            }), 400

        # Find available port
        port = find_available_port()
        used_ports.add(port)

        # Save user code
        temp_dir = f'temp/service_{service_id}'
        os.makedirs(temp_dir, exist_ok=True)
        code_filename = os.path.join(temp_dir, f'user_code.py')
        with open(code_filename, 'w') as f:
            f.write(request.form['code'])

        # Start child container
        container_id = start_docker_service(port, temp_dir, service_id)

        # Record service info
        active_services[service_id] = {
            'container_id': container_id,
            'port': port,
            'stop_time': time.time() + SERVICE_TIMEOUT_MINUTES * 60,
            'temp_dir': temp_dir,
            'ready': False
        }

        app.logger.info(
            f"launch {service_id} with {active_services[service_id]}")

        # Start timeout thread
        threading.Thread(
            target=stop_service_after_timeout,
            args=(service_id,),
            daemon=True
        ).start()

        # Wait for service readiness
        wait_for_service_ready(service_id, port)

        return jsonify({
            "status": "success",
            "service_id": service_id,
            "access_url": f"/service/{service_id}/",
            "expires_in": f"{SERVICE_TIMEOUT_MINUTES} minutes"
        })

    except Exception as e:
        cleanup(service_id, force=True)
        return jsonify({
            "status": "error",
            "message": str(e),
            "service_id": service_id
        }), 500


@app.route('/services')
def list_services():
    """List all active services (for debugging)"""
    services = []
    for sid, info in active_services.items():
        services.append({
            'service_id': sid,
            'container_id': info['container_id'],
            'port': info['port'],
            'time_left': max(0, info['stop_time'] - time.time())
        })
    return jsonify(services)


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
