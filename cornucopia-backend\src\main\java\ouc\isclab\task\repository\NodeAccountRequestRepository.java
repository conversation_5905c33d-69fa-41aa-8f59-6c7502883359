package ouc.isclab.task.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import ouc.isclab.task.entity.NodeAccountRequestEntity;
import ouc.isclab.task.entity.NodeAccountRequestEntity.ApprovalStatus;

import java.util.List;

public interface NodeAccountRequestRepository extends JpaRepository<NodeAccountRequestEntity, Long> {
    // 查询用户的所有申请
    Page<NodeAccountRequestEntity> findByApplicantId(Long applicantId, Pageable pageable);
    
    // 查询节点所有者需要审批的申请
    Page<NodeAccountRequestEntity> findByNode_CreatorIdAndStatus(Long nodeOwnerId, ApprovalStatus status, Pageable pageable);
    
    // 查询用户在特定节点上的已批准账号
    NodeAccountRequestEntity findByApplicantIdAndNode_IdAndStatus(Long applicantId, Long nodeId, ApprovalStatus status);
    
    // 检查用户是否已经有针对该节点的申请
    boolean existsByApplicantIdAndNode_IdAndStatusNot(Long applicantId, Long nodeId, ApprovalStatus status);

    // 查询节点所有者的所有申请
    Page<NodeAccountRequestEntity> findByNode_CreatorId(Long nodeOwnerId, Pageable pageable);

    boolean existsByApplicantIdAndNodeIdIn(Long applicantId, List<Long> nodeIds);
} 