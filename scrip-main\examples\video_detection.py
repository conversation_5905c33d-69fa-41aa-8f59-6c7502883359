from .scrip_slim import create_app, run_app, Template<PERSON>oader
from pathlib import Path
import os
import base64
import time
import cv2
from ultralytics import Y<PERSON><PERSON>


def get_model():
    class Model():
        def __init__(self):
            self.model = YOLO('yolov8n.pt')

        def pipe(self, image_path, confidence=0.5, **kwargs):
            start_time = time.time()

            if not image_path:
                raise ValueError("no img found")

            img = cv2.imread(image_path)
            if img is None:
                raise ValueError("unable to read the imag")

            results = self.model.predict(img, conf=confidence)
            result = results[0]

            detections = []
            for box in result.boxes:
                detections.append(
                    f"{result.names[int(box.cls)]}:{float(box.conf):0.2f} - {[round(x, 2) for x in box.xywhn[0].tolist()]}"
                )

            annotated_img = result.plot()
            _, buffer = cv2.imencode('.jpg', annotated_img)
            img_base64 = "data:image/jpeg;base64," + \
                base64.b64encode(buffer).decode('utf-8')

            return {
                'image': img_base64,
                'detections': detections,
                'processing_time': f"{str(round(time.time() - start_time, 2))} sec"
            }

    model = Model()
    return model


def yolo_preprocessor(app, request):
    input_data = {
        'confidence': float(request.form.get('confidence', 0.5))
    }

    frame_data = request.form.get('frame_data', '')
    if frame_data:
        header, encoded = frame_data.split(",", 1)
        binary_data = base64.b64decode(encoded)

        temp_dir = app.config["temp_dir"]
        os.makedirs(temp_dir, exist_ok=True)
        filename = f"frame_{time.time()}.jpg"
        filepath = os.path.join(temp_dir, filename)

        with open(filepath, 'wb') as f:
            f.write(binary_data)

        input_data['image_path'] = filepath

    return input_data


def main(port=8000):
    base_path = Path(__file__).parent

    app = create_app(
        get_model=get_model,
        preprocessor=yolo_preprocessor,
        input_template_generate=TemplateLoader(
            base_path / "custom/vedio_img_input.html"),
        output_template_config={
            "title": "FrameAnalysis",
            "fields": [{
                "type": "img",
                "name": "image"
            }, {
                "type": "list",
                "label": "details",
                "name": "detections"
            }, {
                "type": "text",
                "label": "processing_time",
                "name": "processing_time"
            }]
        },
        timeout_minutes=120
    )
    run_app(app, port)
