<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Audit</title>
    <style>
        :root {
            --color-dark: #333;
            --color-deep: #555;
            --color-saturated: #9e9e9e;
            --color-light: #d3d3d3;
            --color-pale: #f4f4f4;
            --color-font-deep: black;
            --color-font-light: white;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            overflow: hidden;
            font-size: 1rem;
            line-height: 1rem;
        }

        .container {
            display: grid;
            grid-template-areas:
                "header header"
                "sidebar content";
            grid-template-columns: auto 1fr;
            grid-template-rows: auto 1fr;
            height: 100vh;
            width: 100vw;
        }

        .header {
            grid-area: header;
            background-color: var(--color-dark);
            color: var(--color-font-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1.25rem;
            height: 3rem;
        }

        .header .logo {
            font-size: 1.5rem;
        }

        .header .nav-items {
            display: flex;
            gap: 1.25rem;
        }


        .content {
            grid-area: content;
            padding: 1.25rem;
            background-color: var(--color-pale);
            overflow: auto;
            display: flex;
            flex-direction: column;
        }

        .button {
            display: inline-block;
            padding: 0.625rem 1.25rem;
            background-color: var(--color-dark);
            color: var(--color-font-light);
            text-decoration: none;
            border: none;
            border-radius: 0.25rem;
            cursor: pointer;
        }

        .button:hover {
            background-color: var(--color-deep);
        }

        .userspan {
            display: inline-block;
            padding: 0.625rem 1.25rem;
            background-color: var(--color-dark);
            color: var(--color-light);
            text-decoration: none;
            border: none;
            border-radius: 0.25rem;
        }

        .userspan>a,
        .userspan>a:visited {
            color: var(--color-light);
            text-decoration: none;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        th,
        td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--color-light);
            max-width: 40vw;
            min-width: 5em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        th {
            background-color: var(--color-dark);
            color: var(--color-font-light);
        }

        tr:hover {
            background-color: var(--color-light);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            height: 80vh;
            width: 80vw;
            overflow: auto;
            justify-content: center;
            display: flex;
            flex-wrap: wrap;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            width: 65vw;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .close {
            cursor: pointer;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
        }

        .form-group textarea {
            width: 60vw;
            padding: 0.5rem;
            border: 1px solid var(--color-light);
            border-radius: 0.25rem;
            height: 10vh;
        }

        .button-approve {
            background-color: #4CAF50;
        }

        .button-reject {
            background-color: #f44336;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            color: white;
            font-size: 0.8rem;
        }

        .badge-pending {
            background-color: #FFA500;
        }

        .badge-approved {
            background-color: #4CAF50;
        }

        .badge-rejected {
            background-color: #f44336;
        }

        .file-browser {
            margin-top: 1rem;
            border: 1px solid var(--color-light);
            border-radius: 0.25rem;
            padding: 0.5rem;
            height: 30vh;
            width: 60vw;
            overflow: auto;
        }

        .file-browser-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background-color: var(--color-pale);
            border-bottom: 1px solid var(--color-light);
        }

        .file-browser-path {
            font-family: monospace;
            font-size: 0.9rem;
        }

        .file-browser-content {
            padding: 0.5rem;
        }

        .file-item {
            padding: 0.25rem 0.5rem;
            cursor: pointer;
            border-radius: 0.25rem;
        }

        .file-item:hover {
            background-color: var(--color-light);
        }

        .file-item.directory {
            font-weight: bold;
        }

        .file-item.directory::before {
            content: "📁 ";
        }

        .file-item.file::before {
            content: "📄 ";
        }

        .file-content {
            white-space: pre-wrap;
            font-family: monospace;
            background-color: white;
            padding: 0.5rem;
            border-radius: 0.25rem;
            border: 1px solid var(--color-light);
            max-height: 200px;
            overflow-y: auto;
        }

        .file-meta {
            font-size: 0.8rem;
            color: var(--color-saturated);
            margin-top: 0.5rem;
        }

        .download-btn {
            background-color: var(--color-dark);
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            cursor: pointer;
            margin-left: 0.5rem;
            font-size: 0.8rem;
        }

        .download-btn:hover {
            background-color: var(--color-deep);
        }
    </style>
</head>

<body>
    <div class="container">
        <header class="header">
            <div class="logo">Task Audit System</div>
            <div class="nav-items">
                <span id="currentUser" class="userspan"></span>
                <a href="#" class="button" onclick="window.location.reload()">Refresh</a>
                <a href="#" id="logout" class="button">Logout</a>
            </div>
        </header>
        <main class="content">
            <h1>Pending Tasks for Approval</h1>
            <table id="tasksTable">
                <thead>
                    <tr>
                        <th>Task ID</th>
                        <th>Owner ID</th>
                        <th>Status</th>
                        <th>Message</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </main>
    </div>

    <!-- Audit Modal -->
    <div id="auditModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Review Task</h2>
                <span class="close">&times;</span>
            </div>
            <form id="auditForm">
                <input type="hidden" id="taskId">
                <div class="form-group">
                    <label for="auditMessage">Review Message</label>
                    <textarea id="auditMessage" required></textarea>
                </div>
                <div class="form-group">
                    <label>Task Files Browser</label>
                    <div class="file-browser">
                        <div class="file-browser-header">
                            <div class="file-browser-path" id="currentPath">/</div>
                            <button type="button" id="refreshFiles" class="button">Refresh</button>
                        </div>
                        <div class="file-browser-content" id="fileBrowserContent">
                            Loading files...
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" id="rejectBtn" class="button button-reject">Reject</button>
                    <button type="button" id="approveBtn" class="button button-approve">Approve</button>
                    <button type="button" id="cancelBtn" class="button">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentUser = null
        let pendingTasks = []

        // DOM Elements
        const tasksTable = document.getElementById('tasksTable').getElementsByTagName('tbody')[0]
        const auditModal = document.getElementById('auditModal')
        const closeModal = document.querySelector('.close')
        const cancelBtn = document.getElementById('cancelBtn')
        const logoutBtn = document.getElementById('logout')
        const currentUserSpan = document.getElementById('currentUser')
        const approveBtn = document.getElementById('approveBtn')
        const rejectBtn = document.getElementById('rejectBtn')
        const auditForm = document.getElementById('auditForm')

        // Event Listeners
        closeModal.addEventListener('click', () => auditModal.style.display = 'none')
        cancelBtn.addEventListener('click', () => auditModal.style.display = 'none')
        logoutBtn.addEventListener('click', logout)
        auditModal.addEventListener('click', (e) => {
            if (e.target === auditModal) {
                auditModal.style.display = 'none'
            }
        })

        // Initialize
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('access_token')
            if (!token) {
                window.location.href = '/static/login.html'
                return
            }

            try {
                await fetchCurrentUser()
                if (currentUser.role !== 'auditor') {
                    window.location.href = '/'
                    return
                }
                await fetchPendingTasks()
            } catch (error) {
                console.error('Error:', error)
                window.location.href = '/static/login.html'
            }
        })

        // Functions
        async function fetchCurrentUser() {
            const response = await fetch('/users/me', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            })

            if (!response.ok) {
                throw new Error('Failed to fetch current user')
            }

            currentUser = await response.json()
            currentUserSpan.innerHTML = `
            <a target="_blank" href="/static/task_panel.html">
                Welcome, ${currentUser.name} (${currentUser.role})
            </a>`
        }

        async function fetchPendingTasks() {
            const response = await fetch('/audit/pending', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            })

            if (!response.ok) {
                throw new Error('Failed to fetch pending tasks')
            }

            pendingTasks = await response.json()
            renderTasksTable()
        }

        function renderTasksTable() {
            tasksTable.innerHTML = ''
            pendingTasks.sort((a, b) => b.task_id.localeCompare(a.task_id))

            pendingTasks.forEach(task => {
                const row = tasksTable.insertRow()
                row.innerHTML = `
                    <td title="${task.task_id}">${task.task_id}</td>
                    <td title="${task.owner_id}">${task.owner_id}</td>
                    <td><span class="status-badge badge-pending">Pending</span></td>
                    <td>${task.audit_message || 'No message'}</td>
                    <td>
                        <button onclick="openReviewModal('${task.task_id}')" class="button">Review</button>
                    </td>
                `
            })

            if (pendingTasks.length == 0) {
                const row = tasksTable.insertRow()
                row.innerHTML = `<td colspan="5" style="text-align: center; color: #999;">
                    no pending task
                </td>`
            }
        }

        function openReviewModal(taskId) {
            document.getElementById('taskId').value = taskId
            document.getElementById('auditMessage').value = ''
            document.getElementById('currentPath').textContent = '/'
            document.getElementById('fileBrowserContent').innerHTML = 'Loading files...'
            auditModal.style.display = 'flex'

            // Load root directory contents
            loadTaskFiles(taskId, '')
        }


        async function loadTaskFiles(taskId, path) {
            try {
                const response = await fetch(`/task/${taskId}/browse/${encodeURIComponent(path)}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    }
                })

                if (!response.ok) {
                    throw new Error('Failed to load files')
                }

                const fileInfo = await response.json()
                renderFileBrowser(taskId, fileInfo)
            } catch (error) {
                document.getElementById('fileBrowserContent').innerHTML =
                    `<div class="error">Error loading files: ${error.message}</div>`
            }
        }

        function renderFileBrowser(taskId, fileInfo) {
            const contentEl = document.getElementById('fileBrowserContent')

            if (fileInfo.type === 'error') {
                contentEl.innerHTML = `<div class="error">${fileInfo.error}</div>`
                return
            }

            if (fileInfo.type === 'directory') {
                let html = '<div class="file-list">'


                if (fileInfo.path) {
                    const parentPath = fileInfo.path.split('/').slice(0, -1).join('/')
                    html += `
                        <div class="file-item directory" onclick="loadTaskFiles('${taskId}', '${parentPath}')">
                            ..
                        </div>
                    `
                }


                fileInfo.children.forEach(child => {
                    const clickAction = child.type === 'directory' ?
                        `loadTaskFiles('${taskId}', '${escapeHtml(child.path)}')` :
                        ` downloadFile('${taskId}', '${escapeHtml(child.path)}')`

                    html += `
                        <div class="file-item ${child.type}" onclick="${clickAction}">
                            ${child.name}
                        </div>
                    `
                })

                html += '</div>'
                contentEl.innerHTML = html
                document.getElementById('currentPath').textContent = fileInfo.path || '/'
            }
        }

        async function downloadFile(taskId, path) {
            try {
                const response = await fetch(`/task/${taskId}/download/${encodeURIComponent(path)}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    }
                })

                if (!response.ok) {
                    throw new Error('Failed to download files')
                }

                let filename = path.split('/').pop()

                const blob = await response.blob()

                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = filename
                document.body.appendChild(a)
                a.click()

                window.URL.revokeObjectURL(url)
                document.body.removeChild(a)

            } catch (error) {
                alert(error)
            }
        }

        function escapeHtml(unsafe) {
            if (typeof unsafe === 'string') {
                return unsafe
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;")
            }
            return unsafe
        }

        document.getElementById('refreshFiles').addEventListener('click', function () {
            const taskId = document.getElementById('taskId').value
            const currentPath = document.getElementById('currentPath').textContent
            loadTaskFiles(taskId, currentPath === '/' ? '' : currentPath)
        })

        approveBtn.addEventListener('click', async () => {
            await submitReview(true)
        })

        rejectBtn.addEventListener('click', async () => {
            await submitReview(false)
        })

        async function submitReview(approve) {
            const taskId = document.getElementById('taskId').value
            const message = document.getElementById('auditMessage').value

            if (!message) {
                alert('Please enter a review message')
                return
            }

            try {
                const response = await fetch('/audit/approve', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    },
                    body: JSON.stringify({
                        task_id: taskId,
                        approve: approve,
                        message: message
                    })
                })

                if (!response.ok) {
                    const error = await response.json()
                    throw new Error(error.detail || 'Review submission failed')
                }

                auditModal.style.display = 'none'
                await fetchPendingTasks()
            } catch (error) {
                alert(error.message)
            }
        }

        function logout() {
            localStorage.removeItem('access_token')
            window.location.href = '/static/login.html'
        }

        // Expose function to global scope for inline event handlers
        window.openReviewModal = openReviewModal;
    </script>
</body>

</html>