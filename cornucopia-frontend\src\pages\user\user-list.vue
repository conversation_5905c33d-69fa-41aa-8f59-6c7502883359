<template>
  <div class="user-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><List /></el-icon>
          <h2>用户列表</h2>
        </div>
        <div class="sub-title">管理系统中的所有用户账号</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.name"
                placeholder="请输入用户名"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="action-buttons">
            <el-button type="primary" @click="handleAdd" plain round>
              <el-icon><Plus /></el-icon>新增用户
            </el-button>
            <el-button 
              type="danger" 
              :disabled="!selectedIds.length"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <!-- 用户表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        border
        stripe
        :default-sort="defaultSort"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        highlight-current-row
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="username" label="用户名" align="center" min-width="120" />
        <el-table-column prop="fullname" label="姓名" align="center" min-width="120" />
        <el-table-column prop="roles" label="角色" align="center" min-width="150">
          <template #default="scope">
            {{ scope.row.roles.map(role => role.name).join(', ') }}
          </template>
        </el-table-column>
        <el-table-column prop="online" label="在线状态" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.online ? 'success' : 'info'" size="small">
              {{ scope.row.online ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enable" label="状态" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.enable ? 'success' : 'danger'" size="small">
              {{ scope.row.enable ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          prop="lastLogin" 
          label="最后登录" 
          align="center" 
          width="180"
          sortable
        >
          <template #default="scope">
            {{ scope.row.lastLogin || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" align="center" min-width="180" />
        <el-table-column prop="timeCreated" label="创建时间" align="center" width="180" />
        <el-table-column label="操作" width="220" align="center" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  type="info"
                  size="small"
                  @click.stop="handleDetail(scope.row)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑用户" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="handleEdit(scope.row)"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除用户" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click.stop="handleDelete(scope.row)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip :content="scope.row.enable ? '禁用用户' : '启用用户'" placement="top">
                <el-button
                  :type="scope.row.enable ? 'warning' : 'success'"
                  size="small"
                  @click.stop="handleToggleEnable(scope.row)"
                >
                  <el-icon>
                    <component :is="scope.row.enable ? 'Lock' : 'Unlock'" />
                  </el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="分配角色" placement="top">
                <el-button
                  type="success"
                  size="small"
                  @click.stop="handleAssignRole(scope.row)"
                >
                  <el-icon><UserFilled /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Search, 
  Refresh, 
  Plus, 
  Delete, 
  Edit, 
  List,
  View,
  Lock,
  Unlock,
  UserFilled
} from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import service from '~/axios'

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const selectedIds = ref([])

// 搜索表单
const searchForm = ref({
  name: ''
})

// 获取用户列表
const fetchUsers = async (page = 1) => {
  loading.value = true
  try {
    const response = await service.get('/api/v1.0/sys/users', {
      params: {
        page: page,
        size: pageSize.value,
        username: searchForm.value.name || undefined
      }
    })
    if (response.code === 10000) {
      tableData.value = response.data.users
      total.value = response.data.pagination.total
      pageSize.value = response.data.pagination.size
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索用户
const handleSearch = async () => {
  loading.value = true
  try {
    if (!searchForm.value.name) {
      fetchUsers(currentPage.value)
      return
    }
    
    const response = await service.get(`/api/v1.0/sys/user/search/${searchForm.value.name}`)
    if (response.code === 10000) {
      if (response.data === null) {
        // 处理搜索无结果的情况
        tableData.value = []
        total.value = 0
        currentPage.value = 1
        toast('提示', '未找到匹配的用户', 'info')
      } else {
        tableData.value = [response.data]
        total.value = 1
        currentPage.value = 1
      }
    } else {
      toast('错误', response.message || '搜索失败', 'error')
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
    toast('错误', '搜索用户失败', 'error')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    name: ''
  }
  // 重置后获取所有用户列表
  fetchUsers(1)
}

// 新增用户
const handleAdd = () => {
  router.push('/user/create')
}

// 编辑用户
const handleEdit = (row) => {
  router.push({
    path: '/user/update',
    query: { id: row.id }
  })
}

// 查看用户详情
const handleDetail = (row) => {
  router.push({
    path: '/user/detail',
    query: { id: row.id }
  })
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await showModal('确定要删除该用户吗？', 'warning', '提示')
    const response = await service.delete(`/api/v1.0/sys/user/${row.id}`)
    
    if (response?.code === 10000) {
      toast('成功', '删除成功')
      fetchUsers(currentPage.value)
    } else {
      toast('错误', response?.message || '删除失败', 'error')
    }
  } catch (error) {
    console.error('删除用户失败:', error)
    if (error !== 'cancel') {
      toast('错误', '删除失败', 'error')
    }
  }
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.id)
}

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedIds.value.length) {
    toast('警告', '请选择要删除的用户', 'warning')
    return
  }
  
  try {
    await showModal(`确定要删除选中的 ${selectedIds.value.length} 个用户吗？`, 'warning', '提示')
    
    // 构造请求参数
    const queryParams = selectedIds.value.map(id => `ids=${id}`).join('&')
    const response = await service.delete(`/api/v1.0/sys/user/batch?${queryParams}`)
    
    if (response?.code === 10000) {
      toast('成功', '批量删除成功')
      // 清空选中的ID
      selectedIds.value = []
      // 刷新列表
      fetchUsers(currentPage.value)
    } else {
      toast('错误', response?.message || '批量删除失败', 'error')
    }
  } catch (error) {
    console.error('批量删除用户失败:', error)
    if (error !== 'cancel') {
      toast('错误', '批量删除失败', 'error')
    }
  }
}

// 页码变化
const handleCurrentChange = (val) => {
  fetchUsers(val)
}

// 每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchUsers(1)
}

// 添加启用/禁用处理方法
const handleToggleEnable = async (row) => {
  try {
    await showModal(
      `确定要${row.enable ? '禁用' : '启用'}用户 "${row.username}" 吗？`,
      'warning',
      '提示'
    )
    
    const res = await service.put(
      `/api/v1.0/sys/user/${row.id}/enable?enable=${!row.enable}`
    )
    
    if (res.code === 10000) {
      row.enable = !row.enable
      toast(
        '成功', 
        `用户${row.enable ? '启用' : '禁用'}成功`, 
        row.enable ? 'success' : 'warning'
      )
      fetchUsers(currentPage.value)
    } else {
      throw new Error(res.message || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', error.message || '操作失败', 'error')
    }
  }
}

// 分配角色
const handleAssignRole = (row) => {
  router.push({
    path: '/user/role/assign',
    query: { id: row.id }
  })
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

:deep(.el-table th) {
  font-weight: bold;
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
}

:deep(.el-button--primary) {
  --el-button-hover-bg-color: var(--el-color-primary-light-3);
  --el-button-hover-border-color: var(--el-color-primary-light-3);
  --el-button-active-bg-color: var(--el-color-primary-dark-2);
  --el-button-active-border-color: var(--el-color-primary-dark-2);
}

:deep(.el-button--default) {
  --el-button-hover-bg-color: var(--el-color-primary-light-9);
  --el-button-hover-border-color: var(--el-color-primary-light-7);
  --el-button-hover-text-color: var(--el-color-primary);
}

:deep(.el-button--danger) {
  --el-button-hover-bg-color: var(--el-color-danger-light-3);
  --el-button-hover-border-color: var(--el-color-danger-light-3);
  --el-button-active-bg-color: var(--el-color-danger-dark-2);
  --el-button-active-border-color: var(--el-color-danger-dark-2);
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

:deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

.ml-4 {
  margin-left: 4px;
}
</style> 