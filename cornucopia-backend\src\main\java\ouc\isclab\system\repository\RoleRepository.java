package ouc.isclab.system.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import ouc.isclab.system.entity.RoleEntity;
import ouc.isclab.system.entity.PermissionEntity;
import java.util.Optional;

public interface RoleRepository extends JpaRepository<RoleEntity, Long> {

    boolean existsByPermissionsContaining(PermissionEntity permission);

    Optional<RoleEntity> findById(Long id);

    RoleEntity getRoleEntityById(Long id);

    @Query("SELECT MAX(r.id) FROM RoleEntity r")
    Long findMaxId();

    Optional<RoleEntity> findByName(String name);

    boolean existsByName(String name);

}
