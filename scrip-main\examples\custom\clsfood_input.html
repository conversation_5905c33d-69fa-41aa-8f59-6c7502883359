<style>
    .upload-form {
        max-width: 75vw;
    }

    .image-preview {
        max-width: 100%;
        max-height: 50vh;
        margin-top: var(--space-sm);
        display: none;
        border-radius: var(--radius-sm);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .drag-over {
        border-color: var(--primary-color) !important;
        background-color: rgba(66, 133, 244, 0.1) !important;
    }
</style>

<form id="image-upload-form" class="upload-form">
    <div class="form-group">
        <label for="image-upload">Upload Image</label>
        <div class="file-upload-wrapper" id="image-upload-wrapper">
            <label for="image-upload" class="file-upload-button">
                Click to select or drag & drop an image
            </label>
            <input type="file" id="image-upload" name="file" class="file-input" accept="image/*">
            <div class="file-name-display" id="image-name-display">No image selected</div>
        </div>
        <div style="text-align: center;">
            <img id="image-preview" class="image-preview" alt="Image preview">
        </div>
    </div>
    <button type="submit">Upload Image</button>
</form>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const fileInput = document.getElementById('image-upload')
        const wrapper = document.getElementById('image-upload-wrapper')
        const display = document.getElementById('image-name-display')
        const preview = document.getElementById('image-preview')

        fileInput.addEventListener('change', function (e) {
            if (this.files.length > 0) {
                wrapper.classList.add('has-file')
                display.textContent = this.files[0].name

                // Create preview
                const file = this.files[0]
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader()
                    reader.onload = function (e) {
                        preview.src = e.target.result
                        preview.style.display = 'inline-block'
                    }
                    reader.readAsDataURL(file)
                }
            } else {
                wrapper.classList.remove('has-file')
                display.textContent = 'No image selected'
                preview.style.display = 'none'
            }
        })

        // Drag and drop handling
        wrapper.addEventListener('dragover', (e) => {
            e.preventDefault()
            wrapper.classList.add('drag-over')
        })

        wrapper.addEventListener('dragleave', () => {
            wrapper.classList.remove('drag-over')
        })

        wrapper.addEventListener('drop', (e) => {
            e.preventDefault()
            wrapper.classList.remove('drag-over')

            if (e.dataTransfer.files.length) {
                fileInput.files = e.dataTransfer.files
                fileInput.dispatchEvent(new Event('change'))
            }
        })
    });
</script>