<template>
  <div class="node-list-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Monitor /></el-icon>
          <h2>节点总览</h2>
        </div>
        <div class="sub-title">总览联邦学习网络中的节点</div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="4" v-for="stat in statistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 节点状态和类型分布图表 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><DataLine /></el-icon>
                <span>节点状态分布</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="statusChartRef"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><Monitor /></el-icon>
                <span>节点类型分布</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="typeChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { 
  Monitor, 
  CircleCheck, 
  Warning, 
  CircleClose,
  DataLine
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'
import * as echarts from 'echarts'

const pageLoading = ref(false)
const statusChartRef = ref(null)
const typeChartRef = ref(null)
let statusChart = null
let typeChart = null

// 统计数据
const statistics = ref([
  {
    title: '总节点数',
    value: 0,
    type: 'primary',
    icon: Monitor
  },
  {
    title: '在线节点',
    value: 0,
    type: 'success',
    icon: CircleCheck
  },
  {
    title: '离线节点',
    value: 0,
    type: 'warning',
    icon: Warning
  },
  {
    title: '错误节点',
    value: 0,
    type: 'danger',
    icon: CircleClose
  },
  {
    title: 'Sycee节点',
    value: 0,
    type: 'primary',
    icon: Monitor
  },
  {
    title: 'Pyxis节点',
    value: 0,
    type: 'primary',
    icon: Monitor
  }
])

// 获取节点统计数据
const fetchStatistics = async () => {
  pageLoading.value = true
  try {
    const res = await service.get('/api/v1.0/sys/node/statistics')
    if (res.code === 10000) {
      statistics.value[0].value = res.data.total || 0
      statistics.value[1].value = res.data.online || 0
      statistics.value[2].value = res.data.offline || 0
      statistics.value[3].value = res.data.error || 0
      statistics.value[4].value = res.data.syceeNodes || 0
      statistics.value[5].value = res.data.pyxisNodes || 0
      updateStatusChart()
      updateTypeChart()
    }
  } catch (error) {
    toast('错误', '获取统计数据失败', 'error')
  } finally {
    pageLoading.value = false
  }
}

// 初始化状态分布饼图
const initStatusChart = () => {
  if (statusChartRef.value) {
    statusChart = echarts.init(statusChartRef.value)
    updateStatusChart()
  }
}

// 初始化类型分布饼图
const initTypeChart = () => {
  if (typeChartRef.value) {
    typeChart = echarts.init(typeChartRef.value)
    updateTypeChart()
  }
}

// 更新状态分布饼图数据
const updateStatusChart = () => {
  if (!statusChart) return
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: statistics.value[1].value, 
            name: '在线节点',
            itemStyle: { color: '#67C23A' }
          },
          { 
            value: statistics.value[2].value, 
            name: '离线节点',
            itemStyle: { color: '#E6A23C' }
          },
          { 
            value: statistics.value[3].value, 
            name: '错误节点',
            itemStyle: { color: '#F56C6C' }
          }
        ]
      }
    ]
  }
  
  statusChart.setOption(option)
}

// 更新类型分布饼图数据
const updateTypeChart = () => {
  if (!typeChart) return
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: statistics.value[4].value, 
            name: 'Sycee节点',
            itemStyle: { color: '#409EFF' }
          },
          { 
            value: statistics.value[5].value, 
            name: 'Pyxis节点',
            itemStyle: { color: '#909399' }
          }
        ]
      }
    ]
  }
  
  typeChart.setOption(option)
}

onMounted(async () => {
  await fetchStatistics()
  nextTick(() => {
    initStatusChart()
    initTypeChart()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    statusChart?.resize()
    typeChart?.resize()
  })
})

onUnmounted(() => {
  // 销毁图表实例
  statusChart?.dispose()
  typeChart?.dispose()
  
  // 移除事件监听器
  window.removeEventListener('resize', () => {
    statusChart?.resize()
    typeChart?.resize()
  })
})
</script>

<style scoped>
.node-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.stat-card {
  height: 120px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 8px;
}

.stat-icon.primary { background-color: var(--el-color-primary-light-9); }
.stat-icon.success { background-color: var(--el-color-success-light-9); }
.stat-icon.warning { background-color: var(--el-color-warning-light-9); }
.stat-icon.danger { background-color: var(--el-color-danger-light-9); }

.stat-info {
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  
  .stat-title {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.chart-card {
  height: 400px;
  overflow: hidden;
}

.chart-container {
  height: 340px;
  padding: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  
  .icon {
    margin-right: 8px;
    font-size: 18px;
    color: var(--el-color-primary);
  }
}

/* 深色模式适配 */
html.dark {
  .stat-card,
  .chart-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style>