package ouc.isclab.system.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import ouc.isclab.system.entity.RolePermissionEntity;
import java.util.List;

public interface RolePermissionRepository extends JpaRepository<RolePermissionEntity, Long> {
    List<RolePermissionEntity> findByRoleId(Long roleId);
    List<RolePermissionEntity> findByPermissionId(Long permissionId);
    void deleteByRoleId(Long roleId);
    void deleteByPermissionId(Long permissionId);
    boolean existsByRoleIdAndPermissionId(Long roleId, Long permissionId);
} 