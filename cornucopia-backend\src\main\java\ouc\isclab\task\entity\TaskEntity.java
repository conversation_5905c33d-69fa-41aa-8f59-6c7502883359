package ouc.isclab.task.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;
import ouc.isclab.dataset.entity.DatasetEntity;
import ouc.isclab.node.entity.NodeEntity;

import java.util.HashSet;
import java.util.Set;

/**
 * 任务实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_TASK")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class TaskEntity extends BaseEntity {

    @Column(nullable = false)
    private String name; // 任务名称
    
    @Lob
    @Column(columnDefinition = "text")
    private String description; // 任务描述
    
    @Column(name = "creator_id", nullable = false)
    private Long creatorId; // 创建者ID

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "SYS_TASK_CODE",
            joinColumns = @JoinColumn(name = "task_id"),
            inverseJoinColumns = @JoinColumn(name = "code_id")
    ) // 任务可能对应多个code
    private Set<CodeEntity> codes = new HashSet<>();

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private TaskStatus status; // 任务状态

    @Column
    private String modelPath; // 模型文件夹路径

    public enum TaskStatus {
        CREATED,    // 已创建
        RUNNING,    // 运行中
        COMPLETED,  // 已完成
        FAILED,     // 失败
    }
} 