package ouc.isclab.node.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.pojo.NodeInfo;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.node.service.NodeService;
import ouc.isclab.dataset.entity.DatasetEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys")
public class NodeController {
    @Autowired
    private NodeService nodeService;

    /**
     * 创建新节点
     */
    @PostMapping("/node")
    public NodeEntity createNode(@RequestBody NodeInfo nodeInfo, @CurrentUserId Long userId) {
        log.info(nodeInfo.toString());
        return nodeService.createNode(nodeInfo,userId);
    }

    /**
     * 查看所有节点
     */
    @GetMapping("/nodes")
    public Map<String, Object> listNode(
            @RequestParam(defaultValue = "1") int page,  // 默认页码为 1
            @RequestParam(defaultValue = "10") int size // 默认每页大小为 10
    ) {
        Pageable pageable = PageRequest.of(page - 1, size);  // 页码从 0 开始，所以减 1
        Page<NodeEntity> nodePage = nodeService.getAllNodes(pageable);
        Map<String, Object> data = new HashMap<>();
        data.put("nodes", nodePage.getContent());  // 当前页的数据
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);  // 当前页码
        pagination.put("size", size);  // 每页大小
        pagination.put("total", nodePage.getTotalElements());  // 总记录数
        data.put("pagination", pagination);  // 将pagination放入data中
        return data;
    }

    /**
     * 根据id查看节点
     */
    @GetMapping("/node/{id}")
    public NodeEntity findNode(@PathVariable Long id) {
        return nodeService.getNodeById(id);
    }

    /**
     * 根据id删除节点
     */
    @DeleteMapping( "/node/{id}")
    public void deleteNode(@PathVariable Long id) {
        nodeService.deleteNode(id);
    }

    /**
     * 根据id更新节点资料
     */
    @PutMapping("/node/{id}")
    public NodeEntity updateNodeById(@PathVariable Long id,@RequestBody NodeInfo nodeInfo) {
        return nodeService.updateNodeById(id, nodeInfo);
    }

    /**
     * 获取节点统计数据
     */
    @GetMapping("/node/statistics")
    public Map<String, Object> getNodeStatistics() {
        return nodeService.getNodeStatistics();
    }

    /**
     * 根据节点名称搜索节点
     */
    @GetMapping("/node/search/{name}")
    public NodeEntity searchNodeByName(@PathVariable String name) {
        return nodeService.findNodeByName(name);
    }

    /**
     * 批量删除节点
     */
    @DeleteMapping("/node/batch")
    public void batchDeleteNodes(@RequestParam List<Long> ids) {
        nodeService.batchDeleteNodes(ids);
    }

    /**
     * 获取我的节点列表
     */
    @GetMapping("/nodes/my")
    public Map<String, Object> listMyNodes(
            @CurrentUserId Long userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<NodeEntity> nodePage = nodeService.getNodesByCreatorId(userId, pageable);
        
        Map<String, Object> data = new HashMap<>();
        data.put("nodes", nodePage.getContent());
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", nodePage.getTotalElements());
        data.put("pagination", pagination);
        
        return data;
    }

    /**
     * 删除用户的节点
     */
    @DeleteMapping("/node/my/{id}")
    public void deleteUserNode(@PathVariable Long id, @CurrentUserId Long userId) {
        nodeService.deleteUserNode(id, userId);
    }

    /**
     * 批量删除用户的节点
     */
    @DeleteMapping("/node/my/batch") 
    public void batchDeleteUserNodes(@RequestParam List<Long> ids, @CurrentUserId Long userId) {
        nodeService.batchDeleteUserNodes(ids, userId);
    }

    /**
     * 获取节点关联的数据集
     */
    @GetMapping("/node/{id}/datasets")
    public Map<String, Object> getNodeDatasets(
            @PathVariable Long id,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<DatasetEntity> datasetPage = nodeService.getNodeDatasets(id, pageable);
        
        Map<String, Object> data = new HashMap<>();
        data.put("datasets", datasetPage.getContent());
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", datasetPage.getTotalElements());
        data.put("pagination", pagination);
        
        return data;
    }
}
