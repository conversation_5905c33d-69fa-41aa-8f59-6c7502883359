package ouc.isclab.system.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.annotation.RequirePermission;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.common.response.ResponseResult;
import ouc.isclab.system.entity.PermissionEntity;
import ouc.isclab.system.service.PermissionService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys")
public class PermissionController {
    
    @Autowired
    private PermissionService permissionService;

//    /**
//     * 创建权限
//     */
//    @PostMapping("/permission")
//    public PermissionEntity createPermission(
//            @RequestParam(value = "code") String code,
//            @RequestParam(value = "name") String name,
//            @RequestParam(value = "description", required = false) String description) {
//        log.info("[PERMISSION_CODE] " + code + " successfully created!");
//        return permissionService.createPermission(code, name, description);
//    }
//    /**
//     * 分页获取权限列表
//     */
//    @GetMapping("/permissions")
//    public Map<String, Object> listPermissions(
//            @RequestParam(defaultValue = "1") int page,
//            @RequestParam(defaultValue = "10") int size) {
//        Pageable pageable = PageRequest.of(page - 1, size);
//        Page<PermissionEntity> permissionPage = permissionService.listPermissions(pageable);
//
//        Map<String, Object> data = new HashMap<>();
//        data.put("permissions", permissionPage.getContent());
//
//        Map<String, Object> pagination = new HashMap<>();
//        pagination.put("page", page);
//        pagination.put("size", size);
//        pagination.put("total", permissionPage.getTotalElements());
//        data.put("pagination", pagination);
//
//        return data;
//    }

//    /**
//     * 更新权限信息
//     */
//    @PutMapping("/permission/{id}")
//    public PermissionEntity updatePermission(
//            @PathVariable Long id,
//            @RequestParam(value = "name", required = false) String name,
//            @RequestParam(value = "description", required = false) String description) {
//        log.info("[PERMISSION_ID] " + id + " updating...");
//        return permissionService.updatePermission(id, name, description);
//    }
//
//    /**
//     * 删除权限
//     */
//    @DeleteMapping("/permission/{id}")
//    public ResponseResult deletePermission(@PathVariable Long id) {
//        log.info("[PERMISSION_ID] " + id + " deleting...");
//        return permissionService.deletePermission(id);
//    }
//
//    /**
//     * 批量删除权限
//     */
//    @DeleteMapping("/permission/batch")
//    public ResponseResult deletePermissions(@RequestParam List<Long> ids) {
//        log.info("Batch delete permissions: {}", ids);
//        return permissionService.deletePermissions(ids);
//    }

    /**
     * 根据code查询权限
     */
    @GetMapping("/permission/search/{code}")
    public PermissionEntity findByCode(@PathVariable String code) {
        return permissionService.findByCode(code);
    }

    /**
     * 根据ID查询权限
     */
    @GetMapping("/permission/{id}")
    public PermissionEntity findById(@PathVariable Long id) {
        return permissionService.findById(id);
    }

    /**
     * 获取所有权限（不分页）
     */
    @GetMapping("/permissions")
    public List<PermissionEntity> getAllPermissions() {
        return permissionService.getAllPermissions();
    }

} 