"""
scrip_slim - A Flask-based web service for model deployment with automatic cleanup.

This module provides a complete solution for quickly deploying machine learning models
as temporary web services with configurable interfaces and automatic resource management.

Key Components:
1. Web Interface:
   - Auto-generated HTML forms based on configuration
   - Customizable input/output templates
   - Built-in file upload handling

2. Model Integration:
   - Standardized interface for model call
   - Request preprocessing pipeline
   - Result rendering system

3. Resource Management:
   - Temporary file handling
   - Automatic cleanup
   - Configurable timeout system

Typical Workflow:
1. Define your model loader function that returns a model with .pipe() method
2. Configure input/output templates as needed
3. Create and run the application:
    >>> from scrip_slim import create_app, run_app
    >>> app = create_app(get_model=your_model_loader)
    >>> run_app(app, port=8000)

Model Requirements:
    The model object returned by get_model() must implement:
    - A .pipe(**kwargs) method that:
        * Accepts processed input data as keyword arguments
        * Returns results in a format compatible with your output template
        * By default, the file is converted to a file path and passed to model.pipe()
        * By default, the keys are the values of the name attribute of elements in the <form> element,
            such as:
                <form>
                    <input type='text' name='textinput'>
                </form>
            >>> **{ "textinput": "value of <input>" }
    - The .pipe() method should handle all model inference logic

Input/Output Specifications:
    Input Processing:
    - Form fields are automatically converted to appropriate Python types
    - Files are saved to temporary directory with secure filenames
    - Processed data is passed to model.pipe() as keyword arguments

    Output Handling:
    - Model should return a result object/dict
    - The output_template renders this result
    - Error messages are returned as JSON when exceptions occur

Security Considerations:
    - File uploads are secured with werkzeug.secure_filename()
    - Temporary files are automatically cleaned up
    - No persistent storage is used by default
"""


from .scrip_slim import create_app, run_app
from .template_loader import TemplateLoader
from .template_config_builder import InputTemplateConfig, OutputTemplateConfig
