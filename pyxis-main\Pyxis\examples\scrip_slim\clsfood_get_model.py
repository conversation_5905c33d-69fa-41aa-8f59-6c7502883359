def get_model():
    """返回食物分类模型实例，包含predict方法"""
    import io
    import torch
    from PIL import Image
    from torchvision import transforms
    import torch.nn as nn
    import torch.nn.functional as F
    from minio import Minio
    from minio.error import S3Error

    # 配置参数
    CLASSES = ['apple_pie', 'bibimbap', 'caesar_salad', 'donuts', 'fried_rice',
               'hamburger', 'ice_cream', 'pizza', 'ramen', 'steak']
    IMG_SIZE = 224
    DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 模型定义
    class FoodCNN(nn.Module):
        def __init__(self, num_classes):
            super(FoodCNN, self).__init__()
            self.features = nn.Sequential(
                nn.Conv2d(3, 64, kernel_size=3, padding=1),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True),
                nn.Conv2d(64, 64, kernel_size=3, padding=1),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),

                nn.Conv2d(64, 128, kernel_size=3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True),
                nn.Conv2d(128, 128, kernel_size=3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),

                nn.Conv2d(128, 256, kernel_size=3, padding=1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True),
                nn.Conv2d(256, 256, kernel_size=3, padding=1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True),
                nn.Conv2d(256, 256, kernel_size=3, padding=1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),

                nn.Conv2d(256, 512, kernel_size=3, padding=1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True),
                nn.Conv2d(512, 512, kernel_size=3, padding=1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True),
                nn.Conv2d(512, 512, kernel_size=3, padding=1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),

                nn.Conv2d(512, 512, kernel_size=3, padding=1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True),
                nn.Conv2d(512, 512, kernel_size=3, padding=1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True),
                nn.Conv2d(512, 512, kernel_size=3, padding=1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),
            )

            self.classifier = nn.Sequential(
                nn.Linear(512 * 7 * 7, 4096),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5),
                nn.Linear(4096, 4096),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5),
                nn.Linear(4096, num_classes),
            )

        def forward(self, x):
            x = self.features(x)
            x = torch.flatten(x, 1)
            x = self.classifier(x)
            return x

    # 模型加载类
    class FoodClassifier:
        def __init__(self):
            self.minio_client = Minio(
                '10.140.34.208:9000',
                access_key='eaZ2nCveMAYIc0yoeVU2',
                secret_key='ouc.edu.cn',
                secure=False
            )
            self.transform = transforms.Compose([
                transforms.Resize((IMG_SIZE, IMG_SIZE)),
                transforms.ToTensor(),
                transforms.Normalize([0.485, 0.456, 0.406], [
                                     0.229, 0.224, 0.225])
            ])
            self.classes = CLASSES
            self.model = self._load_model()

        def _load_model(self):
            try:
                response = self.minio_client.get_object(
                    'models', 'food_classifier_best.pt')
                model_data = io.BytesIO(response.data)
                model = FoodCNN(len(self.classes)).to(DEVICE)
                model.load_state_dict(torch.load(
                    model_data, map_location=DEVICE))
                model.eval()
                return model
            except Exception as e:
                raise RuntimeError(f"模型加载失败: {str(e)}")

        def predict(self, file, **kwargs):
            """预测图像类别，返回{类别: 置信度}字典"""
            try:
                print(f"load file:{file}")
                img = Image.open(file).convert('RGB')
                img_tensor = self.transform(img).unsqueeze(0).to(DEVICE)

                with torch.no_grad():
                    outputs = self.model(img_tensor)
                    probs = torch.softmax(outputs, dim=1)
                    conf, pred = torch.max(probs, 1)

                # 返回所有类别及其置信度
                results = {
                    self.classes[i]: float(probs.squeeze()[i])
                    for i in range(len(self.classes))
                }
                print("results:")
                print(results)
                return results
            except Exception as e:
                raise RuntimeError(f"预测失败: {str(e)}")

    # 创建并返回模型实例
    try:
        return FoodClassifier()
    except Exception as e:
        raise RuntimeError(f"无法初始化模型: {str(e)}")
