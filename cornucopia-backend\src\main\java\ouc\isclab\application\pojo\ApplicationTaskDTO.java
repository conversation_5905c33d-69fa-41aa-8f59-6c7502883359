package ouc.isclab.application.pojo;

import lombok.Data;
import java.util.List;

@Data
public class ApplicationTaskDTO {
    private String name;               // 任务名称
    private String description;        // 任务描述
    private Long modelId;             // 模型ID
    private Long nodeId;              // 节点ID
    private String modelPath;         // 模型文件路径
    private String taskId;            // 部署任务ID
    private List<String> savedFiles;  // 保存的文件列表
} 