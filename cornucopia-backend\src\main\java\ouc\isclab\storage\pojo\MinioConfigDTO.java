package ouc.isclab.storage.pojo;

import lombok.Data;
import ouc.isclab.storage.entity.MinioConfigEntity;
import java.util.Date;

@Data
public class MinioConfigDTO {
    private Long id;
    private String name;
    private String endpoint;
    private String accessKey;
    private String secretKey;
    private String bucket;
    private MinioConfigEntity.ConfigType type;
    private Boolean active;
    private Date timeCreated;

    public static MinioConfigDTO fromEntity(MinioConfigEntity entity) {
        MinioConfigDTO dto = new MinioConfigDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setEndpoint(entity.getEndpoint());
        dto.setAccessKey(entity.getAccessKey());
        dto.setSecretKey(entity.getSecretKey());
        dto.setBucket(entity.getBucket());
        dto.setType(entity.getType());
        dto.setActive(entity.getActive());
        dto.setTimeCreated(entity.getTimeCreated());
        return dto;
    }

    public MinioConfigEntity toEntity() {
        MinioConfigEntity entity = new MinioConfigEntity();
        entity.setName(this.name);
        entity.setEndpoint(this.endpoint);
        entity.setAccessKey(this.accessKey);
        entity.setSecretKey(this.secretKey);
        entity.setBucket(this.bucket);
        entity.setType(this.type);
        entity.setActive(this.active);
        return entity;
    }
} 