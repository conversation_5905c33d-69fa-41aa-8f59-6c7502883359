<template>
  <div class="log-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Document /></el-icon>
          <h2>系统日志</h2>
        </div>
        <div class="sub-title">查看系统操作日志记录</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="IP地址">
              <el-input
                v-model="searchForm.ip"
                placeholder="请输入IP地址"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="请求方法">
              <el-select 
                v-model="searchForm.method" 
                placeholder="请选择" 
                clearable
                class="search-select"
              >
                <el-option 
                  v-for="method in ['GET', 'POST', 'PUT', 'DELETE']" 
                  :key="method"
                  :label="method"
                  :value="method"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="searchForm.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="[
                  new Date(2000, 1, 1, 0, 0, 0),
                  new Date(2000, 1, 1, 23, 59, 59)
                ]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
              <el-button 
                v-permission="['log:delete']"
                type="danger" 
                @click="handleDeleteByConditions" 
                round
              >
                <el-icon><Delete /></el-icon>条件删除
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa' }"
        stripe
        border
      >
        <el-table-column prop="timeCreated" label="时间" width="220">
          <template #default="scope">
            {{ formatDateTime(scope.row.timeCreated) }}
          </template>
        </el-table-column>
        <el-table-column prop="clientIp" label="IP地址" width="140">
          <template #default="scope">
            <el-tag size="small" type="info">{{ scope.row.clientIp }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="clientPort" label="端口" width="100" align="center" />
        <el-table-column prop="httpMethod" label="请求方法" width="100" align="center">
          <template #default="scope">
            <el-tag 
              size="small"
              :type="getMethodType(scope.row.httpMethod)"
            >
              {{ scope.row.httpMethod }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="requestUri" label="请求路径" show-overflow-tooltip>
          <template #default="scope">
            <span class="request-uri">{{ scope.row.requestUri }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="耗时" width="100" align="right">
          <template #default="scope">
            <span :class="getDurationClass(scope.row.duration)">
              {{ scope.row.duration }}ms
            </span>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Document, Search, Refresh, Delete } from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import service from '~/axios'

const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const searchForm = ref({
  ip: '',
  method: '',
  timeRange: []
})

// 获取日志列表
const fetchLogs = async (page = 1) => {
  loading.value = true
  try {
    const params = new URLSearchParams()
    params.append('page', page)
    params.append('size', pageSize.value)
    
    // 添加搜索条件
    if (searchForm.value.ip?.trim()) {
      params.append('ip', searchForm.value.ip.trim())
    }
    if (searchForm.value.method !== '') {
      params.append('method', searchForm.value.method)
    }
    if (searchForm.value.timeRange?.length === 2) {
      params.append('startTime', searchForm.value.timeRange[0])
      params.append('endTime', searchForm.value.timeRange[1])
    }

    const res = await service.get('/api/v1.0/logs', { params })
    if (res.code === 10000) {
      tableData.value = res.data.data || []
      total.value = res.data.pagination?.total || 0
      currentPage.value = res.data.pagination?.page || 1
      pageSize.value = res.data.pagination?.size || 10
    } else {
      toast('错误', res.message || '获取日志列表失败', 'error')
    }
  } catch (error) {
    console.error('获取日志列表失败:', error)
    toast('错误', error.message || '获取日志列表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchLogs(1)
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    ip: '',
    method: '',
    timeRange: []
  }
  currentPage.value = 1
  pageSize.value = 10
  fetchLogs(1)
}

// 分页相关方法
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchLogs()
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchLogs(page)
}

// 工具方法
const getMethodType = (method) => {
  const types = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return types[method] || 'info'
}

const getDurationClass = (duration) => {
  if (duration <= 10) return 'duration-fast'
  if (duration <= 100) return 'duration-normal'
  return 'duration-slow'
}

const formatDateTime = (time) => {
  if (!time) return '-'
  const date = new Date(time)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 按条件删除日志
const handleDeleteByConditions = async () => {
  try {
    // 构造删除条件
    const conditions = {}
    const conditionTexts = []
    
    if (searchForm.value.ip?.trim()) {
      conditions.ip = searchForm.value.ip.trim()
      conditionTexts.push(`IP地址: ${searchForm.value.ip.trim()}`)
    }
    if (searchForm.value.method) {
      conditions.method = searchForm.value.method
      conditionTexts.push(`请求方法: ${searchForm.value.method}`)
    }
    if (searchForm.value.timeRange?.length === 2) {
      conditions.startTime = searchForm.value.timeRange[0]
      conditions.endTime = searchForm.value.timeRange[1]
      conditionTexts.push(`时间范围: ${searchForm.value.timeRange[0]} 至 ${searchForm.value.timeRange[1]}`)
    }
    
    // 如果没有设置任何条件，提示用户
    if (Object.keys(conditions).length === 0) {
      toast('警告', '请至少设置一个删除条件', 'warning')
      return
    }

    // 显示确认对话框
    await showModal(
      `确定要删除符合以下条件的日志吗？<br/><div style="border-top: 1px solid #dcdfe6; margin: 10px 0;"></div>${conditionTexts.join('<br/>')}`,
      'warning',
      '删除确认'
    )

    const response = await service.delete('/api/v1.0/logs', { params: conditions })
    
    if (response.code === 10000) {
      toast('成功', `成功删除 ${response.data} 条日志`)
      fetchLogs(1)
    } else {
      toast('错误', response.message || '删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '删除失败', 'error')
    }
  }
}

onMounted(() => {
  fetchLogs()
})
</script>

<style scoped>
.log-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  flex-shrink: 0;
  
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.search-form {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  margin: 0;
}

.search-input {
  width: 200px;
}

.search-select {
  width: 120px;
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

:deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 0;
}

:deep(.el-form-item:last-child) {
  margin-left: auto;
}

:deep(.el-form-item__content) {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

/* 深色模式适配 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.el-date-editor--datetimerange {
  width: 380px !important;
}

.el-tag {
  min-width: 70px;
  text-align: center;
}

.request-uri {
  color: var(--el-text-color-secondary);
  font-family: monospace;
}

.duration-fast {
  color: var(--el-color-success);
  font-weight: 600;
}

.duration-normal {
  color: var(--el-color-warning);
  font-weight: 600;
}

.duration-slow {
  color: var(--el-color-danger);
  font-weight: 600;
}
</style> 