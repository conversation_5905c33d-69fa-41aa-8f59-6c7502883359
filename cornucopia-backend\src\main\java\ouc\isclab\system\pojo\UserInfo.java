package ouc.isclab.system.pojo;

import lombok.Data;
import lombok.Builder;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.system.pojo.RoleInfo;

import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Builder
public class UserInfo {
    private Long userId;
    private String username;
    private String fullname;
    private String email;
    private boolean enable;
    private Set<RoleInfo> roles;
    private Date lastLogin;
    private Date createTime;

    public static UserInfo fromEntity(UserEntity entity) {
        return UserInfo.builder()
                .userId(entity.getId())
                .username(entity.getUsername())
                .fullname(entity.getFullname())
                .email(entity.getEmail())
                .enable(entity.isEnable())
                .createTime(entity.getTimeCreated())
                .lastLogin(entity.getLastLogin())
                .roles(entity.getRoles().stream()
                        .map(role -> RoleInfo.builder()
                                .id(role.getId())
                                .name(role.getName())
                                .permissions(role.getPermissions().stream()
                                        .map(permission -> permission.getCode())
                                        .collect(Collectors.toSet()))
                                .build())
                        .collect(Collectors.toSet()))
                .build();
    }
}
