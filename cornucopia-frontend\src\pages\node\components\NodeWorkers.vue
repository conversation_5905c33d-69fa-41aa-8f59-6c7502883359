<template>
    <div class="code-list-container">
      <div class="page-header">
        <div class="header-left">
          <div class="title-wrapper">
            <el-icon class="title-icon"><Monitor /></el-icon>
            <h2>执行器列表</h2>
          </div>
          <div class="sub-title">查看节点中的执行器列表</div>
        </div>
        
        <div class="header-right">
          <div class="header-actions">
            <div class="action-wrapper">
              <el-button type="primary" @click="handleCreate" plain round>
                <el-icon><Plus /></el-icon>新增执行器
              </el-button>
            </div>
          </div>
        </div>
      </div>
  
      <el-card class="list-card" shadow="hover">
        <el-table 
          v-loading="loading"
          :data="workers" 
          stripe 
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          border
        >
          <el-table-column prop="id" label="执行器ID" min-width="200" align="center" show-overflow-tooltip />
          <el-table-column prop="name" label="执行器名称" min-width="150" align="center" show-overflow-tooltip />
          <el-table-column prop="worker_pool_name" label="执行器池" min-width="150" align="center" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="consumer_state" label="消费者状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getConsumerStateType(scope.row.consumer_state)">
                {{ scope.row.consumer_state }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="healthcheck" label="健康状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getHealthcheckType(scope.row.healthcheck)">
                {{ scope.row.healthcheck }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right" align="center">
            <template #default="scope">
              <el-button-group class="operation-group">
                <el-tooltip content="刷新" placement="top">
                  <el-button type="primary" size="small" @click="handleRefresh(scope.row)">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button type="danger" size="small" @click="handleDelete(scope.row)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-tooltip>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { Delete, Refresh, Plus, Monitor } from '@element-plus/icons-vue'
  import { toast, showModal } from '~/composables/util'
  import sycee from '~/sycee.js'
  
  const route = useRoute()
  const nodeId = ref('')
  const workers = ref([])
  const loading = ref(false)
  
  // 获取状态对应的类型
  const getStatusType = (status) => {
    switch (status) {
      case 'RUNNING':
        return 'success'
      case 'STOPPED':
        return 'danger'
      case 'PAUSED':
        return 'warning'
      default:
        return 'info'
    }
  }
  
  // 获取消费者状态对应的类型
  const getConsumerStateType = (state) => {
    switch (state) {
      case 'IDLE':
        return 'info'
      case 'CONSUMING':
        return 'warning'
      case 'ERROR':
        return 'danger'
      default:
        return 'info'
    }
  }
  
  // 获取健康状态对应的类型
  const getHealthcheckType = (health) => {
    switch (health) {
      case 'HEALTHY':
        return 'success'
      case 'UNHEALTHY':
        return 'danger'
      default:
        return 'warning'
    }
  }
  
  // 获取所有执行器
  const getWorkers = async () => {
    if (!nodeId.value) return
    loading.value = true
    try {
      const res = await sycee(nodeId.value, 'api/worker/get_all', {})
      if (res.code === 10000) {
        workers.value = res.data
      } else {
        toast("错误", res.data, "error")
      }
    } catch (error) {
      console.error('获取执行器列表失败:', error)
      toast("错误", "获取执行器列表失败", "error")
    } finally {
      loading.value = false
    }
  }
  
  // 刷新单个执行器
  const handleRefresh = async (row) => {
    try {
      const res = await sycee(nodeId.value, 'api/worker/get', { uid: row.id })
      if (res.code === 10000) {
        const index = workers.value.findIndex(item => item.id === row.id)
        if (index !== -1) {
          workers.value[index] = res.data
        }
        toast('成功', '执行器信息已刷新', 'success')
      } else {
        toast('错误', res.data, 'error')
      }
    } catch (error) {
      toast('错误', '刷新失败', 'error')
    }
  }
  
  // 删除执行器
  const handleDelete = async (row) => {
    try {
      await showModal('确定要删除该执行器吗？', 'warning', '提示')
      const res = await sycee(nodeId.value, 'api/worker/delete', { uid: row.id })
      if (res.code === 10000) {
        toast('成功', '删除成功')
        getWorkers()
      } else {
        toast('错误', res.data, 'error')
      }
    } catch (error) {
      if (error !== 'cancel') {
        toast('错误', '删除失败', 'error')
      }
    }
  }
  
  // 创建执行器
  const handleCreate = async () => {
    try {
      loading.value = true
      const res = await sycee(nodeId.value, 'api/worker/start_workers', {})
      if (res.code === 10000) {
        toast('成功', '执行器启动成功')
        getWorkers() // 刷新列表
      } else {
        toast('错误', res.data, 'error')
      }
    } catch (error) {
      toast('错误', '启动失败', 'error')
    } finally {
      loading.value = false
    }
  }
  
  onMounted(() => {
    nodeId.value = route.query.id
    getWorkers()
  })
  </script>
  
  <style>
  .code-list-container {
    padding: 20px;
  }
  
  .list-card {
    border-radius: 8px;
    margin-bottom: 20px;
  }
  
  .code-list-container .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 20px;
  }
  
  .code-list-container .header-left {
    flex-shrink: 0;
  }
  
  .code-list-container .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .code-list-container .title-icon {
    margin-right: 8px;
    font-size: 24px;
    color: var(--el-color-primary);
  }
  
  .code-list-container .title-wrapper h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
  }
  
  .code-list-container .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
  
  .code-list-container .header-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding-left: 32px;
  }
  
  .code-list-container .header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .action-wrapper {
    flex-shrink: 0;
  }
  
  .operation-group {
    display: flex;
    justify-content: center;
    gap: 4px;
  }
  
  @media (max-width: 768px) {
    .code-list-container .page-header {
      flex-direction: column;
    }
  }
  </style>
  
  
  