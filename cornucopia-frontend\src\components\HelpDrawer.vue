<template>
  <div class="help-drawer-container">
    <!-- 问号图标按钮 -->
    <el-button 
      type="primary" 
      circle 
      class="help-icon-button"
      @click="openDrawer"
    >
      <el-icon class="question-icon"><QuestionFilled /></el-icon>
    </el-button>
    
    <!-- 抽屉组件 -->
    <el-drawer
      v-model="drawerVisible"
      :title="title"
      :size="size"
      :direction="direction"
      :before-close="closeDrawer"
      :with-header="true"
      :destroy-on-close="destroyOnClose"
      :custom-class="customClass"
    >
      <!-- 自定义内容插槽 -->
      <slot></slot>
      
      <!-- 默认提示内容 -->
      <div v-if="!$slots.default" class="default-help-content" v-html="content"></div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps({
  // 抽屉标题
  title: {
    type: String,
    default: '操作指南'
  },
  // 抽屉内容
  content: {
    type: String,
    default: '这里是帮助信息内容'
  },
  // 抽屉大小
  size: {
    type: String,
    default: '25%'
  },
  // 抽屉方向
  direction: {
    type: String,
    default: 'rtl'  // 从右侧打开
  },
  // 是否在关闭时销毁内容
  destroyOnClose: {
    type: Boolean,
    default: false
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: 'help-drawer'
  }
})

const emit = defineEmits(['open', 'close'])

// 抽屉可见性状态
const drawerVisible = ref(false)

// 打开抽屉
const openDrawer = () => {
  drawerVisible.value = true
  emit('open')
}

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false
  emit('close')
}

// 暴露方法给父组件
defineExpose({
  openDrawer,
  closeDrawer
})
</script>

<style scoped>
.help-drawer-container {
  display: inline-block;
  position: relative;
}

.help-icon-button {
  font-size: 12px;
  transition: all 0.3s ease;
  box-shadow: none;
  border: none;
  position: relative;
  overflow: hidden;
  background-color: transparent;
  color: #909399;
}

.help-icon-button:hover {
  transform: translateY(-2px);
  box-shadow: none;
  color: #409eff;
}

.help-icon-button:active {
  transform: translateY(0);
  box-shadow: none;
}

/* 问号图标 */
.question-icon {
  font-size: 14px;
  transition: all 0.3s ease;
}

.help-icon-button:hover .question-icon {
  transform: scale(1.15);
  animation: pulse 1.5s infinite ease-in-out;
}

/* 脉冲动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);
  }
  100% {
    transform: scale(1);
  }
}

/* 按钮光晕效果 */
.help-icon-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: all 0.4s ease;
  z-index: 0;
  opacity: 0;
}

.help-icon-button:hover::after {
  transform: translate(-50%, -50%) scale(1.8);
  opacity: 0;
  animation: ripple 1.5s infinite;
}

/* 涟漪动画效果 */
@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

.default-help-content {
  padding: 24px;
  line-height: 1.8;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

:deep(.help-drawer .el-drawer__header) {
  margin-bottom: 0;
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-light);
  font-weight: 600;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
  font-size: 26px;
}

:deep(.help-drawer .el-drawer__body) {
  padding: 0;
  overflow-y: auto;
}

:deep(.help-drawer) {
  border-radius: 4px 0 0 4px;
  overflow: hidden;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
}

:deep(.help-drawer .el-drawer__close-btn) {
  font-size: 26px;
  color: var(--el-text-color-secondary);
  transition: all 0.3s;
}

:deep(.help-drawer .el-drawer__close-btn:hover) {
  transform: rotate(90deg);
  color: var(--el-color-primary);
}

/* 美化内容中的常见HTML元素 */
:deep(.default-help-content h1, 
       .default-help-content h2,
       .default-help-content h3) {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.4;
  color: var(--el-text-color-primary);
}

:deep(.default-help-content h1) {
  font-size: 24px;
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 12px;
}

:deep(.default-help-content h2) {
  font-size: 20px;
}

:deep(.default-help-content h3) {
  font-size: 18px;
}

:deep(.default-help-content p) {
  margin: 16px 0;
}

:deep(.default-help-content ul, 
       .default-help-content ol) {
  padding-left: 24px;
  margin: 16px 0;
}

:deep(.default-help-content li) {
  margin: 8px 0;
}

:deep(.default-help-content code) {
  background-color: var(--el-bg-color-page);
  border-radius: 3px;
  padding: 2px 6px;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  font-size: 13px;
}

:deep(.default-help-content pre) {
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
  padding: 16px;
  margin: 16px 0;
  overflow: auto;
}

:deep(.default-help-content table) {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

:deep(.default-help-content th, 
       .default-help-content td) {
  border: 1px solid var(--el-border-color-light);
  padding: 12px 16px;
  text-align: left;
}

:deep(.default-help-content th) {
  background-color: var(--el-bg-color-page);
  font-weight: 600;
}

:deep(.default-help-content blockquote) {
  margin: 16px 0;
  padding: 0 16px;
  color: var(--el-text-color-regular);
  border-left: 4px solid var(--el-border-color-light);
}

:deep(.default-help-content hr) {
  height: 1px;
  border: none;
  background-color: var(--el-border-color-light);
  margin: 24px 0;
}

:deep(.default-help-content a) {
  color: var(--el-color-primary);
  text-decoration: none;
  transition: all 0.3s;
}

:deep(.default-help-content a:hover) {
  color: var(--el-color-primary-light-3);
  text-decoration: underline;
}
</style> 