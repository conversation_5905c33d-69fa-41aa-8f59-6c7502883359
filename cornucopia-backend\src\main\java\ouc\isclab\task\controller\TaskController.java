package ouc.isclab.task.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.task.entity.TaskEntity;
import ouc.isclab.task.pojo.TaskDTO;
import ouc.isclab.task.service.TaskService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys")
public class TaskController {

    @Autowired
    private TaskService taskService;

    /**
     * 创建任务
     */
    @PostMapping("/task")
    public TaskEntity createTask(
            @RequestBody TaskDTO taskDTO,
            @CurrentUserId Long userId) {
        log.info("创建任务: {}, 用户ID: {}", taskDTO, userId);
        return taskService.createTask(taskDTO, userId);
    }
    
    /**
     * 获取用户的任务列表
     */
    @GetMapping("/tasks")
    public Map<String, Object> getUserTasks(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @CurrentUserId Long userId) {
        log.info("获取用户任务列表: 用户ID={}, 页码={}, 大小={}, 关键词={}", userId, page, size, keyword);
        
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<TaskEntity> tasksPage = taskService.getUserTasks(userId, keyword, pageable);
        
        Map<String, Object> result = new HashMap<>();
        result.put("tasks", tasksPage.getContent());
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", tasksPage.getTotalElements());
        result.put("pagination", pagination);
        
        return result;
    }
    
    /**
     * 获取任务详情
     */
    @GetMapping("/task/{taskId}")
    public TaskEntity getTaskDetail(
            @PathVariable Long taskId,
            @CurrentUserId Long userId) {
        log.info("获取任务详情: 任务ID={}, 用户ID={}", taskId, userId);
        return taskService.getTaskDetail(taskId, userId);
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/task/{taskId}")
    public void deleteTask(
            @PathVariable Long taskId,
            @CurrentUserId Long userId
    ) {
        log.info("删除任务: taskId={}, userId={}", taskId, userId);
        taskService.deleteTask(taskId, userId);
    }

    /**
     * 批量删除任务
     */
    @DeleteMapping("/task/batch")
    public void batchDeleteTasks(
            @RequestParam List<Long> ids,
            @CurrentUserId Long userId
    ) {
        log.info("批量删除任务: ids={}, userId={}", ids, userId);
        taskService.batchDeleteTasks(ids, userId);
    }

    /**
     * 获取任务统计数据
     */
    @GetMapping("/task/statistics")
    public Map<String, Object> getTaskStatistics() {
        log.info("获取任务统计数据");
        return taskService.getTaskStatistics();
    }

    /**
     * 刷新任务状态
     * 刷新任务关联的所有代码状态，并更新任务状态
     */
    @PostMapping("/task/refresh/{taskId}")
    public TaskEntity refreshTaskStatus(
            @PathVariable Long taskId,
            @CurrentUserId Long userId
    ) {
        log.info("刷新任务状态: taskId={}, userId={}", taskId, userId);
        return taskService.refreshTaskStatus(taskId, userId);
    }
} 