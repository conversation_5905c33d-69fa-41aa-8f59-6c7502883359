package ouc.isclab.storage.service;

import io.minio.*;
import io.minio.messages.Item;
import io.minio.messages.DeleteObject;
import io.minio.messages.DeleteError;
import io.minio.errors.ErrorResponseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.storage.pojo.FileItemDTO;
import ouc.isclab.storage.config.MinioConfiguration;
import ouc.isclab.storage.pojo.MinioConfigDTO;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.FileSystemResource;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.nio.charset.StandardCharsets;
import java.io.FileOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.http.Method;
import io.minio.StatObjectResponse;
import io.minio.StatObjectArgs;
import ouc.isclab.storage.pojo.MinioStatusDTO;

@Slf4j
@Service
public class MinioService {

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfiguration minioConfig;

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 上传文件
     */
    public FileItemDTO uploadFile(String bucket, MultipartFile file, String description) {
        return uploadFile(bucket, file, description, null);
    }

    /**
     * 上传文件到指定目录
     */
    public FileItemDTO uploadFile(String bucket, MultipartFile file, String description, String directory) {
        try {
            // 生成唯一文件名
            String fileName = UUID.randomUUID().toString() + "_" + file.getOriginalFilename();
            
            // 如果指定了目录，则将文件放在该目录下
            String objectName = fileName;
            if (directory != null && !directory.isEmpty()) {
                // 确保目录以斜杠结尾
                if (!directory.endsWith("/")) {
                    directory = directory + "/";
                }
                objectName = directory + fileName;
            }
            
            // 获取文件输入流
            InputStream inputStream = file.getInputStream();
            
            // 构建元数据
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", file.getContentType());
            if (description != null && !description.isEmpty()) {
                headers.put("X-Amz-Meta-Description", description); // 使用X-Amz-Meta前缀
            }
            
            // 上传文件到MinIO
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucket)
                    .object(objectName)
                    .stream(inputStream, file.getSize(), -1)
                    .headers(headers)  // 使用headers而不是userMetadata
                    .build());

            // 构建文件信息DTO
            FileItemDTO fileInfo = FileItemDTO.builder()
                    .name(file.getOriginalFilename())
                    .path(objectName)
                    .url(minioConfig.getEndpoint() + "/" + bucket + "/" + objectName)
                    .size(file.getSize())
                    .directory(directory != null ? directory.replaceAll("/$", "") : "")
                    .lastModified(dateFormat.format(new Date()))
                    .description(description)
                    .build();
            
            log.info("File uploaded successfully: {}", fileInfo);
            return fileInfo;
            
        } catch (Exception e) {
            log.error("Failed to upload file", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取对象的元数据
     */
    private String getObjectDescription(String bucket, String objectName) {
        try {
            StatObjectResponse stat = minioClient.statObject(
                StatObjectArgs.builder()
                    .bucket(bucket)
                    .object(objectName)
                    .build()
            );
            
            Map<String, String> metadata = stat.userMetadata();
            // 直接使用 "description" 作为 key
            return metadata != null ? metadata.get("description") : null;
            
        } catch (Exception e) {
            log.error("Failed to get object metadata for {}: {}", objectName, e.getMessage());
            return null;
        }
    }

    /**
     * 获取文件列表
     */
    public Map<String, Object> listFiles(String bucket, int page, int size) {
        return listFiles(bucket, page, size, null, null);
    }

    /**
     * 获取文件列表，支持目录过滤和文件名搜索
     */
    public Map<String, Object> listFiles(String bucket, int page, int size, String directory, String name) {
        try {
            List<FileItemDTO> allItems = new ArrayList<>(); // 存储所有符合条件的项目
            Set<String> processedDirectories = new HashSet<>(); // 跟踪已处理的目录，避免重复
            int skip = (page - 1) * size;
            int totalItems = 0;

            // 规范化目录路径
            String prefix = "";
            if (directory != null && !directory.isEmpty()) {
                // 确保目录以斜杠结尾
                prefix = directory.endsWith("/") ? directory : directory + "/";
            }

            // 使用单次API调用获取所有对象和目录
            Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                    .bucket(bucket)
                    .prefix(prefix)
                    .recursive(false) // 不递归，只获取直接子项
                    .build()
            );

            // 处理所有对象和目录
            for (Result<Item> result : results) {
                Item item = result.get();
                String objectName = item.objectName();
                
                // 跳过当前目录
                if (objectName.equals(prefix)) {
                    continue;
                }
                
                // 处理目录项
                if (item.isDir() || objectName.endsWith("/")) {
                    // 提取目录名
                    String dirName = objectName;
                    if (prefix.length() > 0) {
                        dirName = objectName.substring(prefix.length());
                    }
                    
                    // 移除末尾斜杠
                    if (dirName.endsWith("/")) {
                        dirName = dirName.substring(0, dirName.length() - 1);
                    }
                    
                    // 只处理直接子目录，不处理嵌套子目录
                    if (dirName.contains("/")) {
                        continue;
                    }
                    
                    // 应用名称过滤
                    if (name != null && !name.isEmpty() && 
                        !dirName.toLowerCase().contains(name.toLowerCase())) {
                        continue;
                    }
                    
                    // 避免重复处理相同目录
                    if (processedDirectories.contains(dirName)) {
                        continue;
                    }
                    
                    processedDirectories.add(dirName);
                    String fullPath = prefix + dirName;
                    
                    // 创建目录项
                    FileItemDTO dirItem = FileItemDTO.builder()
                        .name(dirName)
                        .path(fullPath)
                        .directory(fullPath)
                        .isDirectory(true)
                        .size(0L)
                        .lastModified(dateFormat.format(new Date()))
                        .build();
                    
                    allItems.add(dirItem);
                    totalItems++;
                    continue;
                }
                
                // 处理文件项
                String itemDirectory = objectName.contains("/") ? 
                    objectName.substring(0, objectName.lastIndexOf("/")) : "";
                String itemName = objectName.contains("/") ? 
                    objectName.substring(objectName.lastIndexOf("/") + 1) : objectName;
                
                // 检查文件是否在当前目录下
                if (prefix.length() > 0) {
                    String relativePath = objectName.substring(prefix.length());
                    if (relativePath.contains("/")) {
                        continue; // 跳过子目录中的文件
                    }
                }
                
                // 应用名称过滤
                if (name != null && !name.isEmpty() && 
                    !itemName.toLowerCase().contains(name.toLowerCase())) {
                    continue;
                }
                
                // 获取文件描述
                String description = getObjectDescription(bucket, objectName);
                
                // 创建文件项
                FileItemDTO fileItem = FileItemDTO.builder()
                    .name(itemName)
                    .path(objectName)
                    .url(minioConfig.getEndpoint() + "/" + bucket + "/" + objectName)
                    .size(item.size())
                    .directory(itemDirectory)
                    .lastModified(dateFormat.format(Date.from(item.lastModified().toInstant())))
                    .description(description)
                    .build();
                
                allItems.add(fileItem);
                totalItems++;
            }
            
            // 对所有项目进行排序，目录在前
            allItems.sort((a, b) -> {
                boolean aIsDir = a.getIsDirectory() != null && a.getIsDirectory();
                boolean bIsDir = b.getIsDirectory() != null && b.getIsDirectory();
                if (aIsDir && !bIsDir) return -1;
                if (!aIsDir && bIsDir) return 1;
                return a.getName().compareTo(b.getName());
            });
            
            // 应用分页
            List<FileItemDTO> pagedItems = new ArrayList<>();
            if (!allItems.isEmpty() && skip < allItems.size()) {
                int end = Math.min(skip + size, allItems.size());
                pagedItems = allItems.subList(skip, end);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("files", pagedItems);
            
            Map<String, Object> pagination = new HashMap<>();
            pagination.put("total", totalItems);
            pagination.put("page", page);
            pagination.put("size", size);
            result.put("pagination", pagination);
            
            return result;

        } catch (Exception e) {
            log.error("Failed to list files", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件或文件夹
     */
    public ResponseEntity<Resource> downloadFile(String bucket, String path) {
        try {
            // 检查文件是否存在
            StatObjectResponse stat = minioClient.statObject(
                StatObjectArgs.builder()
                    .bucket(bucket)
                    .object(path)
                    .build()
            );
            
            // 获取文件流
            GetObjectResponse response = minioClient.getObject(
                GetObjectArgs.builder()
                    .bucket(bucket)
                    .object(path)
                    .build()
            );

            // 创建输入流资源
            InputStreamResource resource = new InputStreamResource(response);
            
            // 获取文件名（从路径中提取）
            String fileName = path.contains("/") ? 
                path.substring(path.lastIndexOf("/") + 1) : path;
            
            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                "attachment; filename=\"" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()) + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, stat.contentType());
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(stat.size()));
            
            // 返回文件下载响应
            return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType(stat.contentType()))
                .body(resource);
            
        } catch (ErrorResponseException e) {
            log.error("File not found: {}", path);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "文件不存在");
        } catch (Exception e) {
            log.error("Failed to download file: {}", path, e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "下载失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件或文件夹
     */
    public void deleteFile(String bucket, String path) {
        try {
            // 检查是否是文件夹
            boolean isDirectory = path.endsWith("/");
            if (!isDirectory) {
                // 检查是否存在以此路径为前缀的其他对象
                Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                        .bucket(bucket)
                        .prefix(path + "/")
                        .recursive(true)
                        .build()
                );
                if (results.iterator().hasNext()) {
                    isDirectory = true;
                }
            }

            if (isDirectory) {
                // 删除文件夹及其内容
                List<DeleteObject> objects = new LinkedList<>();
                
                // 列出目录下所有文件
                Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                        .bucket(bucket)
                        .prefix(path)
                        .recursive(true)
                        .build()
                );

                // 收集要删除的对象
                for (Result<Item> result : results) {
                    objects.add(new DeleteObject(result.get().objectName()));
                }

                // 批量删除
                if (!objects.isEmpty()) {
                    Iterable<Result<DeleteError>> deleteResults = minioClient.removeObjects(
                        RemoveObjectsArgs.builder()
                            .bucket(bucket)
                            .objects(objects)
                            .build()
                    );

                    // 检查删除错误
                    for (Result<DeleteError> result : deleteResults) {
                        DeleteError error = result.get();
                        log.error("Error in deleting object {}: {}", error.objectName(), error.message());
                    }
                }
                
                log.info("Directory deleted successfully: {}", path);
            } else {
                // 删除单个文件
                minioClient.removeObject(
                    RemoveObjectArgs.builder()
                        .bucket(bucket)
                        .object(path)
                        .build()
                );
                log.info("File deleted successfully: {}", path);
            }
        } catch (Exception e) {
            log.error("Failed to delete: {}", path, e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 生成文件或文件夹的分享链接
     */
    public Object generateShareLink(String bucket, String path, Integer expirySeconds) {
        try {
            // 默认24小时有效
            if (expirySeconds == null) {
                expirySeconds = 24 * 60 * 60;
            }

            // 检查是否是文件夹
            boolean isDirectory = path.endsWith("/");
            if (!isDirectory) {
                // 检查是否存在以此路径为前缀的其他对象
                Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                        .bucket(bucket)
                        .prefix(path + "/")
                        .recursive(true)
                        .build()
                );
                if (results.iterator().hasNext()) {
                    isDirectory = true;
                }
            }

            if (isDirectory) {
                // 处理文件夹分享
                List<Map<String, String>> shareLinks = new ArrayList<>();
                
                // 列出目录下所有文件
                Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                        .bucket(bucket)
                        .prefix(path)
                        .recursive(true)
                        .build()
                );

                // 为每个文件生成分享链接
                for (Result<Item> result : results) {
                    Item item = result.get();
                    if (!item.isDir()) {
                        String shareUrl = minioClient.getPresignedObjectUrl(
                            GetPresignedObjectUrlArgs.builder()
                                .method(Method.GET)
                                .bucket(bucket)
                                .object(item.objectName())
                                .expiry(expirySeconds)
                                .build()
                        );

                        Map<String, String> fileShare = new HashMap<>();
                        // 获取相对路径
                        String relativePath = item.objectName().substring(path.length());
                        if (relativePath.startsWith("/")) {
                            relativePath = relativePath.substring(1);
                        }
                        fileShare.put("name", relativePath);
                        fileShare.put("url", shareUrl);
                        shareLinks.add(fileShare);
                    }
                }

                Map<String, Object> result = new HashMap<>();
                result.put("isDirectory", true);
                result.put("name", path);
                result.put("files", shareLinks);
                result.put("expiry", expirySeconds);
                
                log.info("Share links generated for directory {}: {} files", path, shareLinks.size());
                return result;
            } else {
                // 处理单个文件分享
                // 检查文件是否存在
                StatObjectResponse stat = minioClient.statObject(
                    StatObjectArgs.builder()
                        .bucket(bucket)
                        .object(path)
                        .build()
                );
                
                String shareUrl = minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                        .method(Method.GET)
                        .bucket(bucket)
                        .object(path)
                        .expiry(expirySeconds)
                        .build()
                );

                Map<String, Object> result = new HashMap<>();
                result.put("isDirectory", false);
                result.put("name", path);
                result.put("url", shareUrl);
                result.put("expiry", expirySeconds);
                
                log.info("Share link generated for file {}: {}", path, shareUrl);
                return result;
            }
            
        } catch (ErrorResponseException e) {
            log.error("File not found: {}", path);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "文件不存在");
        } catch (Exception e) {
            log.error("Failed to generate share link for {}: {}", path, e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "生成分享链接失败: " + e.getMessage());
        }
    }

    /**
     * 创建目录
     */
    public void createDirectory(String bucket, String directory, String description) {
        try {
            // 确保目录以斜杠结尾
            if (!directory.endsWith("/")) {
                directory = directory + "/";
            }
            
            // 创建一个空对象作为目录标记
            InputStream emptyContent = new ByteArrayInputStream(new byte[0]);
            
            // 构建元数据
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/directory");
            if (description != null && !description.isEmpty()) {
                headers.put("X-Amz-Meta-Description", description);
            }
            
            // 上传空对象到MinIO
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucket)
                    .object(directory)
                    .stream(emptyContent, 0, -1)
                    .headers(headers)
                    .build());
            
            log.info("Directory created successfully: {}", directory);
        } catch (Exception e) {
            log.error("Failed to create directory", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "创建目录失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据集统计信息
     */
    public Map<String, Object> getDatasetStatistics(String bucket) {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 获取所有文件
            Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                    .bucket(bucket)
                    .recursive(true)
                    .build()
            );
            
            // 统计数据
            int totalFiles = 0;
            int totalFolders = 0;
            long totalSize = 0;
            int todayUploads = 0;
            Map<String, Integer> fileTypes = new HashMap<>();
            List<Map<String, Object>> recentFiles = new ArrayList<>();
            
            // 获取今天的日期
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            
            // 处理所有文件
            for (Result<Item> result : results) {
                Item item = result.get();
                
                // 跳过空目录标记
                if (item.size() == 0 && item.objectName().endsWith("/")) {
                    totalFolders++;
                    continue;
                }
                
                // 计算文件总数和大小
                totalFiles++;
                totalSize += item.size();
                
                // 获取文件信息
                String objectName = item.objectName();
                String lastModified = dateFormat.format(Date.from(item.lastModified().toInstant()));
                
                // 检查是否是今天上传的
                if (lastModified.startsWith(today)) {
                    todayUploads++;
                }
                
                // 获取文件类型
                String extension = "其他";
                if (objectName.contains(".")) {
                    extension = objectName.substring(objectName.lastIndexOf(".") + 1).toLowerCase();
                }
                fileTypes.put(extension, fileTypes.getOrDefault(extension, 0) + 1);
                
                // 收集最近文件信息
                if (recentFiles.size() < 5) {
                    String itemName = objectName.contains("/") ? 
                        objectName.substring(objectName.lastIndexOf("/") + 1) : objectName;
                    String itemDirectory = objectName.contains("/") ? 
                        objectName.substring(0, objectName.lastIndexOf("/")) : "";
                    
                    Map<String, Object> fileInfo = new HashMap<>();
                    fileInfo.put("name", itemName);
                    fileInfo.put("path", objectName);
                    fileInfo.put("size", item.size());
                    fileInfo.put("directory", itemDirectory);
                    fileInfo.put("lastModified", lastModified);
                    
                    recentFiles.add(fileInfo);
                }
            }
            
            // 按上传时间排序最近文件
            recentFiles.sort((a, b) -> {
                String timeA = (String) a.get("lastModified");
                String timeB = (String) b.get("lastModified");
                return timeB.compareTo(timeA); // 降序排列
            });
            
            // 构建返回结果
            statistics.put("totalFiles", totalFiles);
            statistics.put("totalFolders", totalFolders);
            statistics.put("totalSize", totalSize);
            statistics.put("todayUploads", todayUploads);
            statistics.put("fileTypes", fileTypes);
            statistics.put("recentFiles", recentFiles);
            
            return statistics;
        } catch (Exception e) {
            log.error("Failed to get dataset statistics", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取数据集统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新MinIO客户端
     */
    public void updateMinioClient(MinioConfigDTO config) {
        minioClient = MinioClient.builder()
                .endpoint(config.getEndpoint())
                .credentials(config.getAccessKey(), config.getSecretKey())
                .build();
    }

    /**
     * 检查 MinIO 服务状态
     */
    public MinioStatusDTO checkMinioStatus() {
        try {
            // 检查服务是否在线
            minioClient.listBuckets();
            
            // 获取存储桶数量
            int bucketCount = minioClient.listBuckets().size();
            
            // 获取默认存储桶的对象数量和总大小
            String bucket = minioConfig.getBucket();
            long objectCount = 0;
            long totalSize = 0;
            
            try {
                Iterable<Result<Item>> objects = minioClient.listObjects(
                        ListObjectsArgs.builder().bucket(bucket).recursive(true).build()
                );
                
                for (Result<Item> result : objects) {
                    Item item = result.get();
                    objectCount++;
                    totalSize += item.size();
                }
            } catch (Exception e) {
                log.warn("Failed to get objects statistics: {}", e.getMessage());
            }
            
            return MinioStatusDTO.builder()
                    .isOnline(true)
                    .bucketCount(bucketCount)
                    .objectCount(objectCount)
                    .totalSize(totalSize)
                    .build();
        } catch (Exception e) {
            log.error("Failed to check MinIO status: {}", e.getMessage());
            return MinioStatusDTO.builder()
                    .isOnline(false)
                    .bucketCount(0)
                    .objectCount(0)
                    .totalSize(0)
                    .build();
        }
    }

    /**
     * 上传文件夹中的多个文件
     * @param bucket 存储桶名称
     * @param files 上传的文件列表
     * @param relativePaths 每个文件在文件夹中的相对路径
     * @param baseDirectory 文件夹在MinIO中的基础路径
     * @param description 描述信息
     * @return 上传的文件信息列表
     */
    public List<FileItemDTO> uploadFolder(String bucket, List<MultipartFile> files, 
                                        List<String> relativePaths, String baseDirectory, 
                                        String description) {
        if (files.size() != relativePaths.size()) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "文件数量与路径数量不匹配");
        }
        
        List<FileItemDTO> uploadedFiles = new ArrayList<>();
        
        try {
            // 规范化基础目录
            String basePath = "";
            if (baseDirectory != null && !baseDirectory.isEmpty()) {
                basePath = baseDirectory.endsWith("/") ? baseDirectory : baseDirectory + "/";
            }
            
            // 逐个上传文件
            for (int i = 0; i < files.size(); i++) {
                MultipartFile file = files.get(i);
                String relativePath = relativePaths.get(i);
                
                // 确保相对路径的目录分隔符是正斜杠
                relativePath = relativePath.replace("\\", "/");
                
                // 构建完整的对象路径
                String objectName = basePath + relativePath;
                
                // 获取文件输入流
                InputStream inputStream = file.getInputStream();
                
                // 构建元数据
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", file.getContentType());
                if (description != null && !description.isEmpty()) {
                    headers.put("X-Amz-Meta-Description", description);
                }
                
                // 上传文件到MinIO
                minioClient.putObject(PutObjectArgs.builder()
                        .bucket(bucket)
                        .object(objectName)
                        .stream(inputStream, file.getSize(), -1)
                        .headers(headers)
                        .build());
                
                // 提取目录信息
                String directory = objectName.contains("/") ? 
                        objectName.substring(0, objectName.lastIndexOf("/")) : "";
                
                // 构建文件信息
                FileItemDTO fileInfo = FileItemDTO.builder()
                        .name(file.getOriginalFilename())
                        .path(objectName)
                        .url(minioConfig.getEndpoint() + "/" + bucket + "/" + objectName)
                        .size(file.getSize())
                        .directory(directory)
                        .lastModified(dateFormat.format(new Date()))
                        .description(description)
                        .build();
                
                uploadedFiles.add(fileInfo);
                log.info("File uploaded successfully: {}", fileInfo);
            }
            
            return uploadedFiles;
            
        } catch (Exception e) {
            log.error("Failed to upload folder", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "文件夹上传失败: " + e.getMessage());
        }
    }
} 