from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session
from ..stash.models import SessionLocal, Task, User
from .users import get_current_user

audit = APIRouter(prefix="/audit")


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class AuditRequest(BaseModel):
    task_id: str
    approve: bool
    message: str = ""


@audit.get("/pending", response_model=list[dict])
async def list_pending_tasks(db: Session = Depends(get_db),
                             current_user: User = Depends(get_current_user)):
    if current_user.role != "auditor":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Require auditor role"
        )

    tasks = db.query(Task).filter(Task.allowed_times == 0).all()
    return [
        {
            "task_id": task.task_id,
            "owner_id": task.owner_id,
            "audit_message": task.audit_message
        }
        for task in tasks
    ]


@audit.post("/approve")
async def approve_task(audit: AuditRequest, db: Session = Depends(get_db),
                       current_user: User = Depends(get_current_user)):
    if current_user.role != "auditor":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Require auditor role"
        )

    task = db.query(Task).filter(Task.task_id == audit.task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    if audit.approve:
        task.allowed_times = 1
        task.audit_message = audit.message
        db.commit()
        return {"status": "approved", "task_id": task.task_id}
    else:
        task.audit_message = audit.message
        task.allowed_times = -1
        db.commit()
        return {"status": "rejected", "task_id": task.task_id}
