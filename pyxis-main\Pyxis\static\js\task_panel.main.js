// ====================== 工具模块 ======================
const UIUtils = (() => {
    const showLoading = (element, message) => {
        element.innerHTML = `<div class="loading">${message}</div>`
    }

    const showError = (element, error) => {
        console.error(error)
        element.innerHTML = `<div class="loading">Error: ${error.message}</div>`
    }

    const setButtonState = (button, disabled, text) => {
        button.disabled = disabled
        button.textContent = text
    }

    const renderSize = (filesize) => {
        if (!filesize) return "0 B"
        const unitArr = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]
        const index = Math.floor(Math.log(filesize) / Math.log(1024))
        const size = (filesize / Math.pow(1024, index)).toFixed(2)
        return `${size} ${unitArr[index]}`
    }

    return { showLoading, showError, setButtonState, renderSize }
})()

// ====================== 消息模块 ======================
const MessageSystem = (() => {
    let timeout = null
    const box = (() => {
        const el = document.createElement('div')
        el.style.cssText = `
        display:block;
        position:absolute;
        right:0;
        bottom:0;
        padding:0.5rem;
        background-color:var(--color-saturated);
        color:var(--color-font-light)
      `
        document.body.appendChild(el)
        return el
    })()

    const show = (message, duration = 3000) => {
        box.style.display = "block"
        box.innerHTML = message

        if (timeout) clearTimeout(timeout)
        timeout = setTimeout(() => {
            box.style.display = "none"
        }, duration)
    }

    return { show }
})()

// ====================== 状态管理 ======================
const StateManager = (() => {
    let state = {
        currentTask: null,
        activeTab: 'user_code',
        filePaths: {
            user_code: '',
            logs: '',
            results: '',
            workspace: ''
        },
        tasks: []
    }

    const getState = () => ({ ...state })
    const setState = (newState) => {
        state = { ...state, ...newState }
        return getState()
    }

    return { getState, setState }
})()

// ====================== API服务 ======================
const APIService = (() => {
    const request = async (url, options = {}) => {
        const headers = {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            ...options.headers
        }

        const response = await fetch(url, { ...options, headers })
        if (!response.ok) {
            const error = await response.json().catch(() => ({}))
            throw new Error(error.message || `Request failed: ${url}`)
        }
        return response.json()
    }

    return {
        getTasks: () => request('/tasks'),
        getTaskStatus: (taskId) => request(`/task/${taskId}/status`),
        browseFiles: (taskId, tab, path) => request(`/task/${taskId}/browse/${tab}/${path}`),
        downloadFile: (taskId, path) => request(`/task/${taskId}/download/${encodeURIComponent(path)}`),
        createTask: (formData) => request('/task', { method: 'POST', body: formData }),
        stopTask: (taskId) => request(`/task/${taskId}/stop`, { method: 'POST' }),
        killTask: (taskId) => request(`/task/${taskId}/kill`, { method: 'POST' }),
        deleteTask: (taskId) => request(`/task/${taskId}`, { method: 'DELETE' }),
        launchTask: (taskId) => request(`/task/${taskId}/launch`, { method: 'POST' }),
        getCurrentUser: () => request('/users/me')
    }
})()

// ====================== 任务列表组件 ======================
const TaskListComponent = (() => {
    const createTaskItem = (task, currentTaskId) => {
        const item = document.createElement('div')
        item.className = `task-item ${task.task_id === currentTaskId ? 'active' : ''}`
        item.dataset.taskId = task.task_id

        const auditStatus = getAuditStatus(task.runable)
        const auditMessage = task.audit_message || task.task_id

        item.innerHTML = `
        <span class="task-audit-${auditStatus}" title="${auditMessage}">${task.task_id}</span>
        <div class="task-actions">
          <button class="btn btn-danger" data-action="delete">Delete</button>
        </div>
      `

        return item
    }

    const getAuditStatus = (runable) => {
        if (runable === undefined || runable === null) return "none"
        runable = Number(runable)
        if (runable > 0) return "approved"
        if (runable < 0) return "rejected"
        return "pending"
    }

    const render = (container, tasks, currentTaskId) => {
        const fragment = document.createDocumentFragment()

        tasks
            .sort((a, b) => b.task_id.localeCompare(a.task_id))
            .forEach(task => {
                fragment.appendChild(createTaskItem(task, currentTaskId))
            })

        container.innerHTML = ''
        container.appendChild(fragment)
    }

    return { render }
})()

// ====================== 任务信息组件 ======================
const TaskInfoComponent = (() => {
    const render = (container, taskId, status) => {
        container.innerHTML = `
        <div class="info-row">
          <span class="info-label">Task ID:</span>
          <span>${taskId}</span>
        </div>
        <div class="info-row">
          <span class="info-label">Status:</span>
          <span class="status-${status.status.toLowerCase()}" id="status-span">${status.status}</span>
          <a href="/service/${taskId}/#" style="margin-left:2rem;" target="_blank">SERVICE</a>
        </div>
        <div class="info-row">
          <span class="info-label">Created:</span>
          <span>${status.created_time ?? 'Unknown'}</span>
        </div>
        <div class="info-row">
          <span class="info-label">Exit Code:</span>
          <span>${status.exit_code ?? 'N/A'}</span>
        </div>
        <div class="task-actions">
          <button class="btn btn-warning" id="stop-task-btn">Stop</button>
          <button class="btn btn-danger" id="kill-task-btn">Kill</button>
          <button class="btn btn-primary" id="launch-task-btn">Run</button>
          <button class="btn btn-normal" id="refresh-task-btn">Refresh</button>
        </div>
      `
    }

    return { render }
})()

// ====================== 文件列表组件 ======================
const FileListComponent = (() => {
    const createFileItem = (file, pathPrefix = '') => {
        const item = document.createElement('div')
        item.className = 'file-item'
        item.dataset.type = file.type
        item.dataset.path = file.path

        item.innerHTML = `
        <span class="file-icon">${file.type === 'directory' ? '📁' : '📄'}</span>
        <span class="file-name">${file.name}</span>
        ${file.type === 'file' ? `<span class="file-size">${file.size}</span>` : ''}
      `

        return item
    }

    const render = (container, files, currentPath, tab, taskId) => {
        const fragment = document.createDocumentFragment()

        if (currentPath) {
            const parentPath = currentPath.split('/').slice(0, -1).join('/')
            const backItem = createFileItem({
                name: '..',
                type: 'directory',
                path: parentPath
            })
            backItem.classList.add('back-item')
            fragment.appendChild(backItem)
        }

        files.forEach(file => {
            if (file.path) {
                file.path = file.path.split('/').slice(1).join('/')
            }
            fragment.appendChild(createFileItem(file))
        })

        container.innerHTML = ''
        container.appendChild(fragment)
    }

    return { render }
})()

// ====================== 文件上传组件 ======================
const FileUploadComponent = (() => {
    let fileInput = null
    let previewContainer = null

    const init = (inputElement, previewElement) => {
        fileInput = inputElement
        previewContainer = previewElement || createPreviewContainer()
        setupEventListeners()
    }

    const createPreviewContainer = () => {
        const container = document.createElement('div')
        container.className = 'file-list-preview'
        fileInput.parentNode.insertBefore(container, fileInput.nextSibling)
        return container
    }

    const updatePreview = () => {
        previewContainer.innerHTML = ''

        if (!fileInput.files.length) {
            previewContainer.innerHTML = '<p>No files selected</p>'
            return
        }

        Array.from(fileInput.files).forEach((file, index) => {
            const item = document.createElement('div')
            item.className = 'file-preview-item'
            item.innerHTML = `
          <button class="remove-file-btn" data-index="${index}">X</button>
          <span>${file.name} (${UIUtils.renderSize(file.size)})</span>
        `
            previewContainer.appendChild(item)
        })
    }

    const removeFile = (index) => {
        const newList = new DataTransfer()
        Array.from(fileInput.files)
            .filter((_, i) => i !== index)
            .forEach(file => newList.items.add(file))
        fileInput.files = newList.files
        updatePreview()
    }

    const setupEventListeners = () => {
        fileInput.addEventListener('change', updatePreview)
        previewContainer.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-file-btn')) {
                removeFile(parseInt(e.target.dataset.index))
            }
        })
    }

    const getFormData = () => {
        const formData = new FormData()
        Array.from(fileInput.files).forEach((file) => {
            const relativePath = file.webkitRelativePath.split("/").slice(1).join('/')
            formData.append('files', file, relativePath)
        })

        if (fileInput.files[0]?.webkitRelativePath) {
            const parts = fileInput.files[0].webkitRelativePath.split('/')
            if (parts.length > 1) {
                formData.append('folder_name', parts[0])
            }
        }

        return formData
    }

    return { init, getFormData, updatePreview }
})()

// ====================== 主应用控制器 ======================
const AppController = (() => {
    let domElements = {}

    const init = async () => {
        setupDOMReferences()
        setupEventListeners()
        await loadInitialData()
    }

    const setupDOMReferences = () => {
        domElements = {
            taskList: document.getElementById('task-list'),
            taskInfo: document.getElementById('task-info'),
            fileList: document.getElementById('file-list'),
            currentPath: document.getElementById('current-path'),
            createForm: document.getElementById('create-task-form'),
            fileInput: document.getElementById('task-files'),
            fileUpload: document.getElementById('files-upload-btn'),
            refreshBtn: document.getElementById('refresh-btn'),
            settingsBtn: document.getElementById('settings-btn'),
            logoutBtn: document.getElementById('logout-btn'),
            tabButtons: document.querySelectorAll('.tab-btn'),
            currentUser: document.getElementById('currentUser')
        }

        FileUploadComponent.init(domElements.fileInput, document.querySelector('.file-list-preview'))
    }

    const setupEventListeners = () => {
        // 任务列表事件
        domElements.taskList.addEventListener('click', handleTaskListClick)

        // 文件列表事件
        domElements.fileList.addEventListener('click', handleFileListClick)

        // 标签页切换事件
        domElements.tabButtons.forEach(btn => {
            btn.addEventListener('click', () => switchTab(btn.dataset.tab))
        })

        // 表单提交事件
        domElements.createForm.addEventListener('submit', handleTaskCreate)

        // 按钮事件
        domElements.fileUpload.addEventListener('click', () => domElements.fileInput.click())
        domElements.refreshBtn?.addEventListener('click', loadTasks)
        domElements.settingsBtn?.addEventListener('click', () => alert('Settings panel will be available soon'))
        domElements.logoutBtn?.addEventListener('click', logout)
    }

    const loadInitialData = async () => {
        await Promise.all([
            loadCurrentUser(),
            loadTasks()
        ])
    }

    const loadCurrentUser = async () => {
        try {
            const user = await APIService.getCurrentUser()
            domElements.currentUser.textContent = `Welcome, ${user.name} (${user.role})`
        } catch (error) {
            console.error('Failed to load user:', error)
        }
    }

    const loadTasks = async () => {
        try {
            UIUtils.showLoading(domElements.taskList, 'Loading tasks...')

            const tasks = await APIService.getTasks()
            if (tasks.length === 0) {
                UIUtils.showLoading(domElements.taskList, 'No tasks available')
                return
            }

            const state = StateManager.setState({ tasks })
            TaskListComponent.render(domElements.taskList, state.tasks, state.currentTask)

            if (!state.currentTask && tasks.length > 0) {
                selectTask(tasks[0].task_id)
            }
        } catch (error) {
            UIUtils.showError(domElements.taskList, error)
        }
    }

    const selectTask = async (taskId) => {
        const state = StateManager.setState({ currentTask: taskId })

        // 高亮选中的任务
        document.querySelectorAll('.task-item').forEach(item => {
            item.classList.toggle('active', item.dataset.taskId === taskId)
        })

        await Promise.all([
            loadTaskInfo(taskId),
            loadFiles(taskId, state.activeTab, state.filePaths[state.activeTab])
        ])
    }

    const loadTaskInfo = async (taskId) => {
        try {
            UIUtils.showLoading(domElements.taskInfo, 'Loading task info...')

            const status = await APIService.getTaskStatus(taskId)
            TaskInfoComponent.render(domElements.taskInfo, taskId, status)

            // 为操作按钮绑定事件
            document.getElementById('stop-task-btn')?.addEventListener('click', () => stopTask(taskId))
            document.getElementById('kill-task-btn')?.addEventListener('click', () => killTask(taskId))
            document.getElementById('refresh-task-btn')?.addEventListener('click', () => loadTaskInfo(taskId))
            document.getElementById('launch-task-btn')?.addEventListener('click', () => launchTask(taskId))
            document.getElementById('status-span')?.addEventListener('click', () => downloadFile(taskId, "STATUS.json"))
        } catch (error) {
            UIUtils.showError(domElements.taskInfo, error)
        }
    }

    const loadFiles = async (taskId, tab, path = '') => {
        try {
            UIUtils.showLoading(domElements.fileList, 'Loading files...')
            domElements.currentPath.textContent = `${tab}/${path}`

            const data = await APIService.browseFiles(taskId, tab, path)
            FileListComponent.render(domElements.fileList, data.children, path, tab, taskId)

            // 更新状态中的路径
            StateManager.setState({
                filePaths: { ...StateManager.getState().filePaths, [tab]: path }
            })
        } catch (error) {
            UIUtils.showError(domElements.fileList, error)
        }
    }

    const handleTaskListClick = (e) => {
        const taskItem = e.target.closest('.task-item')
        if (!taskItem) return

        const taskId = taskItem.dataset.taskId
        if (e.target.dataset.action === 'delete') {
            e.stopPropagation()
            deleteTask(taskId)
        } else {
            selectTask(taskId)
        }
    }

    const handleFileListClick = (e) => {
        const fileItem = e.target.closest('.file-item')
        if (!fileItem) return

        const { type, path } = fileItem.dataset
        const { currentTask, activeTab } = StateManager.getState()

        if (type === 'directory') {
            loadFiles(currentTask, activeTab, path)
        } else {
            downloadFile(currentTask, `${activeTab}/${path}`)
        }
    }

    const switchTab = (tab) => {
        const state = StateManager.setState({ activeTab: tab })

        domElements.tabButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tab)
        })

        if (state.currentTask) {
            loadFiles(state.currentTask, tab, state.filePaths[tab])
        }
    }

    const handleTaskCreate = async (e) => {
        e.preventDefault()
        const button = domElements.createForm.querySelector('button')

        try {
            UIUtils.setButtonState(button, true, 'Creating...')

            const formData = FileUploadComponent.getFormData()
            const result = await APIService.createTask(formData)

            await loadTasks()
            await selectTask(result.task_id)

            domElements.fileInput.value = ''
            FileUploadComponent.updatePreview()

            MessageSystem.show('Task created successfully')
        } catch (error) {
            MessageSystem.show(`Error: ${error.message}`, 5000)
        } finally {
            UIUtils.setButtonState(button, false, 'Create Task')
        }
    }

    const stopTask = async (taskId) => {
        await performTaskOperation(APIService.stopTask(taskId), 'stop')
    }

    const killTask = async (taskId) => {
        await performTaskOperation(APIService.killTask(taskId), 'kill')
    }

    const deleteTask = async (taskId) => {
        if (!confirm('Delete this task and all its files?')) return
        await performTaskOperation(APIService.deleteTask(taskId), 'delete')
    }

    const launchTask = async (taskId) => {
        await performTaskOperation(APIService.launchTask(taskId), 'launch')
    }

    const performTaskOperation = async (operationPromise, action) => {
        try {
            await operationPromise
            MessageSystem.show(`Successfully ${action} task`)

            if (action === 'delete') {
                await loadTasks()
                const state = StateManager.setState({ currentTask: null })
                domElements.taskInfo.innerHTML = '<p>Select a task to view details</p>'
                domElements.fileList.innerHTML = '<p>Select a tab to browse files</p>'
                domElements.currentPath.textContent = ''
            } else {
                const { currentTask } = StateManager.getState()
                if (currentTask) await loadTaskInfo(currentTask)
            }
        } catch (error) {
            MessageSystem.show(`Error: ${error.message}`, 5000)
        }
    }

    const downloadFile = async (taskId, path) => {
        try {
            const response = await fetch(`/task/${taskId}/download/${encodeURIComponent(path)}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            })

            if (!response.ok) throw new Error('Failed to download file')

            const filename = path.split('/').pop()
            const blob = await response.blob()
            const url = window.URL.createObjectURL(blob)

            const a = document.createElement('a')
            a.href = url
            a.download = filename
            document.body.appendChild(a)
            a.click()

            window.URL.revokeObjectURL(url)
            document.body.removeChild(a)
        } catch (error) {
            MessageSystem.show(`Download error: ${error.message}`, 5000)
        }
    }

    const logout = () => {
        localStorage.removeItem('access_token')
        window.location.href = '/static/login.html'
    }

    return { init }
})()

// ====================== 应用启动 ======================
document.addEventListener('DOMContentLoaded', AppController.init)