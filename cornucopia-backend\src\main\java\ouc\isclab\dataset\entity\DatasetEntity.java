package ouc.isclab.dataset.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;
import ouc.isclab.node.entity.NodeEntity;

import java.util.HashSet;
import java.util.Set;

/**
 * @desc 数据集实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_DATASET")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class DatasetEntity extends BaseEntity {

    @Column(nullable = false)
    private String name; // 数据集名称

    @Column(nullable = false)
    private String path; // 存储路径

    @Column(nullable = false)
    private String type; // 数据集类型：FILE(文件上传) 或 URL(外部链接)

    @Column(nullable = true)
    private String url; // 外部URL地址，仅当type为URL时有效

    @Column(nullable = false)
    private Long size; // 文件大小(字节)

    @Lob
    @Column(columnDefinition = "text")
    private String description; // 数据集描述

    @Lob
    @Column(columnDefinition = "text")
    private String mockData; // 示例的mock数据

    @Column(name = "creator_id", nullable = false)
    private Long creatorId; // 数据集上传者ID

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "SYS_DATASET_NODE",
            joinColumns = @JoinColumn(name = "dataset_id"),
            inverseJoinColumns = @JoinColumn(name = "node_id")
    )
    private Set<NodeEntity> availableNodes = new HashSet<>(); // 可以使用该数据集的节点
} 