package ouc.isclab.storage.controller;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.storage.service.MinioService;
import ouc.isclab.common.annotation.RequirePermission;
import ouc.isclab.storage.pojo.FileItemDTO;
import ouc.isclab.storage.service.MinioConfigService;
import ouc.isclab.storage.entity.MinioConfigEntity;
import ouc.isclab.storage.pojo.MinioConfigDTO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@BaseResponse
@RequestMapping("/api/v1.0/sys/file")
public class MinioController {

    @Autowired
    private MinioService minioService;

    @Autowired
    private MinioConfigService minioConfigService;

    /**
     * 获取指定类型的活动配置
     */
    private MinioConfigDTO getActiveConfigByBucket(String bucket) {
        MinioConfigEntity.ConfigType type;
        if ("datasets".equals(bucket)) {
            type = MinioConfigEntity.ConfigType.DATASET;
        } else if ("models".equals(bucket)) {
            type = MinioConfigEntity.ConfigType.MODEL;
        } else {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "不支持的存储桶类型");
        }
        return minioConfigService.getActiveConfigByType(type);
    }

    /**
     * 上传文件
     */
    @PostMapping("/{bucket}/upload")
    public FileItemDTO upload(
            @PathVariable String bucket,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "directory", required = false) String directory) {
        MinioConfigDTO config = getActiveConfigByBucket(bucket);
        minioService.updateMinioClient(config);
        return minioService.uploadFile(bucket, file, description, directory);
    }

    /**
     * 获取指定桶中的对象列表 - 允许匿名访问
     */
    @GetMapping("/{bucket}/list")
    public Map<String, Object> listObjects(
            @PathVariable String bucket,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(value = "directory", required = false) String directory,
            @RequestParam(value = "name", required = false) String name) {
        MinioConfigDTO config = getActiveConfigByBucket(bucket);
        minioService.updateMinioClient(config);
        return minioService.listFiles(bucket, page, size, directory, name);
    }

    /**
     * 下载文件 (使用查询参数)
     */
    @GetMapping("/{bucket}/download")
    public ResponseEntity<org.springframework.core.io.Resource> downloadByParam(
            @PathVariable String bucket,
            @RequestParam String path) {
        try {
            MinioConfigDTO config = getActiveConfigByBucket(bucket);
            minioService.updateMinioClient(config);
            return minioService.downloadFile(bucket, path);
        } catch (Exception e) {
            log.error("Failed to process download request", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "下载失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件或文件夹 (使用查询参数)
     */
    @DeleteMapping("/{bucket}")
    public Map<String, Object> deleteByParam(
            @PathVariable String bucket,
            @RequestParam String path) {
        try {
            MinioConfigDTO config = getActiveConfigByBucket(bucket);
            minioService.updateMinioClient(config);
            minioService.deleteFile(bucket, path);
            Map<String, Object> response = new HashMap<>();
            response.put("msg", "删除成功");
            return response;
        } catch (Exception e) {
            log.error("Failed to process delete request", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 生成文件分享链接 (使用查询参数)
     */
    @GetMapping("/{bucket}/share")
    public Map<String, Object> generateShareLinkByParam(
            @PathVariable String bucket,
            @RequestParam String path,
            @RequestParam(required = false) Integer expiry) {
        try {
            MinioConfigDTO config = getActiveConfigByBucket(bucket);
            minioService.updateMinioClient(config);
            Object shareResult = minioService.generateShareLink(bucket, path, expiry);
            
            Map<String, Object> response = new HashMap<>();
            response.put("url", shareResult instanceof Map ? ((Map)shareResult).get("url") : shareResult);
            response.put("expiry", expiry != null ? expiry : 24 * 60 * 60);
            
            if (shareResult instanceof Map && ((Map)shareResult).containsKey("isDirectory")) {
                response.put("isDirectory", ((Map)shareResult).get("isDirectory"));
                response.put("files", ((Map)shareResult).get("files"));
            }
            
            return response;
        } catch (Exception e) {
            log.error("Failed to generate share link", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "生成分享链接失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建文件夹
     */
    @PostMapping("/{bucket}/directory")
    public void createDirectory(
            @PathVariable String bucket,
            @RequestParam String directory,
            @RequestParam(value = "description", required = false) String description) {
        try {
            MinioConfigDTO config = getActiveConfigByBucket(bucket);
            minioService.updateMinioClient(config);
            minioService.createDirectory(bucket, directory, description);
        } catch (Exception e) {
            log.error("Failed to create directory", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "创建目录失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据集统计信息
     */
    @GetMapping("/datasets/statistics")
    public Map<String, Object> getDatasetStatistics() {
        try {
            Map<String, Object> statistics = minioService.getDatasetStatistics("datasets");
            return statistics;
        } catch (Exception e) {
            log.error("Failed to get dataset statistics", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取数据集统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件夹
     */
    @PostMapping("/{bucket}/upload-folder")
    public Map<String, Object> uploadFolder(
            @PathVariable String bucket,
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam("relativePaths") List<String> relativePaths,
            @RequestParam(value = "directory", required = false) String directory,
            @RequestParam(value = "description", required = false) String description) {
        try {
            MinioConfigDTO config = getActiveConfigByBucket(bucket);
            minioService.updateMinioClient(config);
            List<FileItemDTO> uploadedFiles = minioService.uploadFolder(bucket, files, relativePaths, directory, description);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "文件夹上传成功");
            response.put("totalFiles", uploadedFiles.size());
            response.put("files", uploadedFiles);
            
            return response;
        } catch (Exception e) {
            log.error("Failed to upload folder", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "文件夹上传失败: " + e.getMessage());
        }
    }
}
