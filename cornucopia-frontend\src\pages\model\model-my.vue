<template>
  <div class="model-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><List /></el-icon>
          <h2>我的模型</h2>
        </div>
        <div class="sub-title">查看我保存在存储桶中的模型</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.keyword"
                placeholder="搜索模型名称或描述"
                clearable
                class="search-input"
                @clear="handleSearch"
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="action-buttons">
            <el-button 
              type="danger" 
              :disabled="!selectedIds.length"
              @click="handleBatchDelete"
              round
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        highlight-current-row
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="name" label="模型名称" min-width="150" align="center" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip align="center" />
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.timeCreated) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  type="info"
                  size="small"
                  @click="viewModelDetail(row)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看文件夹" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click="viewModelFolder(row)"
                >
                  <el-icon><Folder /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除模型" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 模型详情对话框 -->
    <el-dialog
      v-model="modelDetailVisible"
      title="模型详情"
      width="700px"
      destroy-on-close
    >
      <div v-loading="modelDetailLoading">
        <el-descriptions v-if="modelDetail" :column="1" border>
          <el-descriptions-item label="模型ID">{{ modelDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="模型名称">{{ modelDetail.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(modelDetail.status)">
              {{ getStatusText(modelDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(modelDetail.timeCreated) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(modelDetail.timeUpdated) }}</el-descriptions-item>
          <el-descriptions-item label="创建者">{{ modelDetail.creatorName }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ modelDetail.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="关联任务">
            <el-tag 
              v-if="modelDetail.task"
              type="info"
              @click="viewTaskDetail(modelDetail.task)"
              style="cursor: pointer;"
            >
              {{ modelDetail.task.name }} (ID: {{ modelDetail.task.id }})
            </el-tag>
            <span v-else>无关联任务</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="modelDetailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Search, 
  Refresh, 
  Delete, 
  List,
  View,
  Folder
} from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import service from '~/axios'

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const selectedIds = ref([])

// 搜索表单
const searchForm = ref({
  keyword: ''
})

// 模型详情相关
const modelDetailVisible = ref(false)
const modelDetailLoading = ref(false)
const modelDetail = ref(null)

// 获取模型列表
const fetchModels = async (page = 1) => {
  loading.value = true
  try {
    const response = await service.get('/api/v1.0/sys/models', {
      params: {
        page,
        size: pageSize.value,
        keyword: searchForm.value.keyword
      }
    })
    
    if (response.code === 10000) {
      tableData.value = response.data.models
      total.value = response.data.pagination.total
    } else {
      toast('错误', response.message || '获取模型列表失败', 'error')
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
    toast('错误', '获取模型列表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 获取模型状态类型标签样式
const getStatusType = (status) => {
  const statusMap = {
    'CREATED': 'info',
    'TRAINING': 'warning',
    'COMPLETED': 'success',
    'FAILED': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取模型状态文本
const getStatusText = (status) => {
  const statusMap = {
    'CREATED': '已创建',
    'TRAINING': '训练中',
    'COMPLETED': '已完成',
    'FAILED': '失败'
  }
  return statusMap[status] || status
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString()
}

// 查看模型详情
const viewModelDetail = async (row) => {
  modelDetailVisible.value = true
  modelDetailLoading.value = true
  
  try {
    const response = await service.get(`/api/v1.0/sys/model/${row.id}`)
    if (response.code === 10000) {
      modelDetail.value = {
        ...response.data,
        createTime: response.data.timeCreated,
        updateTime: response.data.timeUpdated,
        creatorName: response.data.creatorName || `用户${response.data.creatorId}`
      }
    } else {
      toast('错误', response.message || '获取模型详情失败', 'error')
    }
  } catch (error) {
    console.error('获取模型详情失败:', error)
    toast('错误', '获取模型详情失败', 'error')
  } finally {
    modelDetailLoading.value = false
  }
}

// 查看任务详情
const viewTaskDetail = (task) => {
  router.push({
    path: '/task/my',
    query: { id: task.id }
  })
}

// 查看模型文件夹
const viewModelFolder = (row) => {
  router.push({
    path: '/model/folder',
    query: {
      id: row.id
    }
  })
}

// 删除模型
const handleDelete = async (row) => {
  try {
    await showModal(`确定要删除模型 "${row.name}" 吗？`, 'warning', '提示')
    const response = await service.delete(`/api/v1.0/sys/model/${row.id}`)
    
    if (response.code === 10000) {
      toast('成功', '删除成功')
      fetchModels(currentPage.value)
    } else {
      toast('错误', response.message || '删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模型失败:', error)
      toast('错误', '删除失败', 'error')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await showModal(`确定要删除选中的 ${selectedIds.value.length} 个模型吗？`, 'warning', '提示')
    const response = await service.delete('/api/v1.0/sys/models', {
      data: selectedIds.value
    })
    
    if (response.code === 10000) {
      toast('成功', '批量删除成功')
      fetchModels(currentPage.value)
    } else {
      toast('错误', response.message || '批量删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除模型失败:', error)
      toast('错误', '批量删除失败', 'error')
    }
  }
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.id)
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchModels(1)
}

// 重置搜索
const resetSearch = () => {
  searchForm.value.keyword = ''
  handleSearch()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  fetchModels(val)
}

// 处理每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchModels(1)
}

onMounted(() => {
  fetchModels()
})
</script>

<style scoped>
.model-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;  /* 添加左边距，与图标对齐 */
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

:deep(.el-table th) {
  font-weight: bold;
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
}

:deep(.el-button--primary) {
  --el-button-hover-bg-color: var(--el-color-primary-light-3);
  --el-button-hover-border-color: var(--el-color-primary-light-3);
  --el-button-active-bg-color: var(--el-color-primary-dark-2);
  --el-button-active-border-color: var(--el-color-primary-dark-2);
}

:deep(.el-button--default) {
  --el-button-hover-bg-color: var(--el-color-primary-light-9);
  --el-button-hover-border-color: var(--el-color-primary-light-7);
  --el-button-hover-text-color: var(--el-color-primary);
}

:deep(.el-button--danger) {
  --el-button-hover-bg-color: var(--el-color-danger-light-3);
  --el-button-hover-border-color: var(--el-color-danger-light-3);
  --el-button-active-bg-color: var(--el-color-danger-dark-2);
  --el-button-active-border-color: var(--el-color-danger-dark-2);
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

:deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

.batch-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 10px 0;
  position: sticky;
  bottom: 0;
  background-color: var(--el-bg-color);
  z-index: 1;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-table .el-table__cell.is-center .cell) {
  justify-content: center;
}

:deep(.el-table .el-checkbox) {
  margin-right: 0;
}

/* 深色模式下的批量操作栏样式 */
html.dark .batch-actions {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
}
</style> 