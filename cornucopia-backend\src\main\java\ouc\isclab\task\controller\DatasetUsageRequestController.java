package ouc.isclab.task.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.task.entity.DatasetUsageRequestEntity;
import ouc.isclab.task.pojo.DatasetUsageRequestDTO;
import ouc.isclab.task.service.DatasetUsageRequestService;
import ouc.isclab.task.service.UserResourceService;
import ouc.isclab.system.service.UserService;
import ouc.isclab.system.pojo.UserInfo;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.system.entity.UserEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys")
public class DatasetUsageRequestController {

    @Autowired
    private DatasetUsageRequestService datasetUsageRequestService;
    
    @Autowired
    private UserResourceService userResourceService;

    @Autowired
    private UserService userService;

    /**
     * 创建数据集使用申请
     */
    @PostMapping("/dataset/usage/request")
    public DatasetUsageRequestEntity createRequest(
            @RequestBody DatasetUsageRequestDTO requestDTO,
            @CurrentUserId Long userId) {
        log.info("创建数据集使用申请: {}, 用户ID: {}", requestDTO, userId);
        return datasetUsageRequestService.createRequest(requestDTO, userId);
    }

    /**
     * 获取用户的所有数据集使用申请
     */
    @GetMapping("/dataset/usage/requests")
    public Map<String, Object> getUserRequests(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @CurrentUserId Long userId) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.ASC, "id"));
        Page<DatasetUsageRequestEntity> requestsPage = datasetUsageRequestService.getUserRequests(userId, pageable);

        Map<String, Object> result = new HashMap<>();
        result.put("requests", requestsPage.getContent());

        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", requestsPage.getTotalElements());
        result.put("pagination", pagination);

        return result;
    }

    // /**
    //  * 获取数据集所有者需要审批的申请
    //  */
    // @GetMapping("/dataset/usage/pending")
    // public Map<String, Object> getPendingRequests(
    //         @RequestParam(defaultValue = "1") int page,
    //         @RequestParam(defaultValue = "10") int size,
    //         @CurrentUserId Long userId) {
    //     Pageable pageable = PageRequest.of(page - 1, size);
    //     Page<DatasetUsageRequestEntity> requestsPage = datasetUsageRequestService.getPendingRequestsForDatasetOwner(userId, pageable);

    //     Map<String, Object> result = new HashMap<>();
    //     result.put("requests", requestsPage.getContent());

    //     Map<String, Object> pagination = new HashMap<>();
    //     pagination.put("page", page);
    //     pagination.put("size", size);
    //     pagination.put("total", requestsPage.getTotalElements());
    //     result.put("pagination", pagination);

    //     return result;
    // }

    /**
     * 审批数据集使用申请
     */
    @PostMapping("/dataset/usage/approve/{requestId}")
    public DatasetUsageRequestEntity approveRequest(
            @PathVariable Long requestId,
            @RequestParam boolean approved,
            @RequestParam(required = false) String rejectReason,
            @CurrentUserId Long userId) {
        log.info("审批数据集使用申请: {}, 审批结果: {}, 审批人: {}", requestId, approved, userId);
        
        // 如果批准申请，则添加用户资源权限
        DatasetUsageRequestEntity request = datasetUsageRequestService.approveRequest(requestId, approved, rejectReason, userId);
        
        if (approved) {
            // 添加用户对该节点上数据集的访问权限
            userResourceService.grantResourceAccess(
                request.getApplicantId(), 
                "DATASET", 
                request.getDataset().getId(), 
                request.getNodeId()
            );
        }
        
        return request;
    }

    /**
     * 获取数据集所有者的所有申请（不仅限于待审批）
     */
    @GetMapping("/dataset/usage/examine")
    public Map<String, Object> getOwnerAllRequests(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @CurrentUserId Long userId) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.ASC, "id"));
        
        Page<DatasetUsageRequestEntity> requestsPage;
        if (status != null && !status.isEmpty()) {
            // 如果提供了状态参数，按状态过滤
            DatasetUsageRequestEntity.ApprovalStatus approvalStatus = 
                DatasetUsageRequestEntity.ApprovalStatus.valueOf(status);
            requestsPage = datasetUsageRequestService.getRequestsByDatasetOwnerAndStatus(userId, approvalStatus, pageable);
        } else {
            // 否则获取所有请求
            requestsPage = datasetUsageRequestService.getAllRequestsByDatasetOwner(userId, pageable);
        }

        // 提取请求中的用户ID列表
        List<Long> userIds = new ArrayList<>();
        for (DatasetUsageRequestEntity request : requestsPage.getContent()) {
            userIds.add(request.getApplicantId());
            if (request.getApproverId() != null) {
                userIds.add(request.getApproverId());
            }
        }
        
        // 查询用户信息
        Map<Long, UserEntity> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            List<UserEntity> users = userService.findUsersByIds(userIds);
            for (UserEntity user : users) {
                userMap.put(user.getId(), user);
            }
        }
        
        // 构建返回结果，添加申请人和审批人的名字
        List<Map<String, Object>> requestsWithNames = requestsPage.getContent().stream()
            .map(request -> {
                Map<String, Object> requestMap = new HashMap<>();
                requestMap.put("id", request.getId());
                requestMap.put("timeCreated", request.getTimeCreated());
                requestMap.put("timeUpdated", request.getTimeUpdated());
                requestMap.put("dataset", request.getDataset());
                requestMap.put("nodeId", request.getNodeId());
                requestMap.put("applicantId", request.getApplicantId());
                
                // 添加申请人名字
                UserEntity applicant = userMap.get(request.getApplicantId());
                if (applicant != null) {
                    requestMap.put("applicantName", applicant.getFullname());
                    requestMap.put("applicantUsername", applicant.getUsername());
                }
                
                requestMap.put("purpose", request.getPurpose());
                requestMap.put("status", request.getStatus());
                requestMap.put("rejectReason", request.getRejectReason());
                requestMap.put("approverId", request.getApproverId());
                
                // 添加审批人名字
                if (request.getApproverId() != null) {
                    UserEntity approver = userMap.get(request.getApproverId());
                    if (approver != null) {
                        requestMap.put("approverName", approver.getFullname());
                        requestMap.put("approverUsername", approver.getUsername());
                    }
                }
                
                return requestMap;
            })
            .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("requests", requestsWithNames);

        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", requestsPage.getTotalElements());
        result.put("pagination", pagination);

        return result;
    }

    /**
     * 获取申请数据集的用户信息
     */
    @GetMapping("/dataset/usage/user/{userId}")
    public Object getUserInfo(@PathVariable Long userId, @CurrentUserId Long currentUserId) {
        // 验证该用户是否向当前用户发起过数据集使用请求
        boolean hasRequestToCurrentUser = datasetUsageRequestService.hasRequestFromUserToDatasetOwner(userId, currentUserId);
        
        if (!hasRequestToCurrentUser) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权查看该用户信息");
        }
        
        UserEntity userEntity = userService.findUserById(userId);
        return UserInfo.fromEntity(userEntity);
    }
} 