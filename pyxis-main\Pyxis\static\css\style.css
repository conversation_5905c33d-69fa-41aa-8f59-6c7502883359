:root {
    --color-dark: #333;
    --color-deep: #555;
    --color-saturated: #9e9e9e;
    --color-light: #d3d3d3;
    --color-pale: #f4f4f4;
    --color-font-deep: black;
    --color-font-light: white;
}

body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    overflow: hidden;
    font-size: 1rem;
    line-height: 1rem;
}

.container {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar content";
    grid-template-columns: auto 1fr;
    grid-template-rows: auto 1fr;
    height: 100vh;
    width: 100vw;
}

.header {
    grid-area: header;
    background-color: var(--color-dark);
    color: var(--color-font-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1.25rem;
    height: 3rem;
}

.header .logo {
    font-size: 1.5rem;
}

.header .nav-items {
    display: flex;
    gap: 1.25rem;
}

.sidebar {
    resize: horizontal;
    grid-area: sidebar;
    width: 26vw;
    display: flex;
    background-color: var(--color-deep);
    color: var(--color-font-light);
    padding: 1.25rem;
    flex-direction: column;
    overflow: auto;
    position: relative;
}

.sidebar::after {
    content: ' ';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 0 1.2em 1.2em;
    border-color: transparent transparent #3498db transparent;
}


.content {
    grid-area: content;
    padding: 1.25rem;
    background-color: var(--color-pale);
    overflow: auto;
    display: flex;
    flex-direction: column;
}

.button {
    display: inline-block;
    padding: 0.625rem 1.25rem;
    background-color: var(--color-dark);
    color: var(--color-light);
    text-decoration: none;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
}

.button:hover {
    background-color: var(--color-deep);
}

.userspan {
    display: inline-block;
    padding: 0.625rem 1.25rem;
    background-color: var(--color-dark);
    color: var(--color-light);
    text-decoration: none;
    border: none;
    border-radius: 0.25rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s;
    width: 100%;
    min-width: 7rem;
    margin: 0.25rem 0;
    text-align: center;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-upload {
    background-color: transparent;
    color: black;
    border: dashed black;
}

.btn-upload:hover {
    background-color: grey;
}


.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
}

.btn-warning:hover {
    background-color: #d35400;
}

.btn-normal {
    background-color: var(--color-saturated);
    color: var(--color-font-deep);
}

.btn-normal:hover {
    background-color: var(--color-deep);
    color: var(--color-font-light);
}

.task-controls {
    width: 100%;
    height: 15rem;
    overflow: auto;
    flex-shrink: 0.5;
}

.task-list-container {
    width: 100%;
    flex-grow: 0;
    flex-shrink: 0.5;
    flex-basis: 0%;
    display: flex;
    flex-direction: column;
    height: calc(100% - 15rem)
}

.task-list {
    flex: 1;
    margin-top: 1rem;
    overflow: auto;
    border: 1px solid var(--color-saturated);
    border-radius: 0.25rem;
    min-height: 4rem;
    display: grid;
    grid-template-columns: auto;
}


.task-item {
    padding: 0.5rem;
    border-bottom: 1px solid var(--color-saturated);
    cursor: pointer;
    white-space: nowrap;
    width: 100%;
    display: grid;
    grid-template-columns: auto 1fr auto;
    justify-content: space-between;
    grid-column: 1;
    box-sizing: border-box;
}

.task-item>span {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    min-width: 12em;
    max-width: 100%;
    height: 100%;
    font-family: monospace;
    text-align: center;
    grid-column: 1;
    padding-left: 1em;
    margin-right: 1em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.task-item:hover,
.task-item.active {
    background-color: var(--color-saturated);
}


.task-audit-none {
    color: var(--color-font-light);
}

.task-audit-approved {
    color: greenyellow;
}

.task-audit-approved::before {
    content: '[AP]';
}

.task-audit-rejected {
    color: red;
}

.task-audit-rejected::before {
    content: '[RJ]';
}

.task-audit-pending {
    color: var(--color-font-light);
}

.task-audit-pending::before {
    content: '[PD]';
}

.task-actions {
    display: inline-block;
    gap: 0.5rem;
    grid-column: 3;
}

.task-actions .btn {
    width: auto;
    min-width: 5rem;
}

.tab-content {
    position: relative;
}

.file-list-preview {
    height: 5em;
    overflow: auto;
}


.task-info {
    background-color: white;
    padding: 1rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: 6.7em;
    min-width: 10em;
    overflow: auto;
}

.info-row {
    max-width: 100%;
    min-width: 5em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tab-buttons {
    display: flex;
    border-bottom: 1px solid var(--color-light);
    margin-bottom: 1rem;
}

.tab-btn {
    padding: 0.5rem 1rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
}

.tab-btn.active {
    border-bottom-color: #3498db;
    color: #3498db;
    font-weight: bold;
}

.file-tabs {
    flex-grow: 1;
    flex-shrink: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.file-browser {
    background-color: white;
    padding: 1rem;
    border-radius: 0.25rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    flex-shrink: 1;
    height: 3em;
}

.file-path {
    font-weight: bold;
    margin-bottom: 0.5em;
}

.file-list {
    overflow-y: auto;
    flex-grow: 1;
    flex-shrink: 1;
    height: 0;
}

.file-item {
    padding: 0.5rem;
    border-bottom: 1px solid var(--color-light);
    display: flex;
    align-items: center;
    cursor: pointer;
}

.file-size {
    margin-left: auto;
    padding-left: 1rem;
    color: var(--color-deep);
    font-size: 0.9rem;
}

.file-item:hover {
    background-color: var(--color-light);
}

.form-group {
    margin-bottom: 1rem;
}

.file-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--color-light);
    border-radius: 0.25rem;
}

.loading {
    text-align: center;
    padding: 1rem;
    color: var(--color-saturated);
}