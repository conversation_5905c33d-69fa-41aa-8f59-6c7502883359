<template>
  <div class="minio-config-update">
    <el-loading :full-screen="false" :body="true" v-if="loading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Edit /></el-icon>
          <h2>编辑 MinIO 配置</h2>
        </div>
        <div class="sub-title">修改 MinIO 存储配置信息</div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCancel" plain round>
          <el-icon><Back /></el-icon>返回列表
        </el-button>
      </div>
    </div>

    <el-empty 
      v-if="!route.query.id" 
      description="未找到配置ID" 
      :image-size="200"
    >
      <el-button type="primary" @click="$router.push('/minio/list')" round>
        返回配置列表
      </el-button>
    </el-empty>

    <el-card v-else class="form-card" shadow="hover">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="config-form"
      >
        <el-form-item label="配置名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入配置名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="服务地址" prop="endpoint">
          <el-input
            v-model="form.endpoint"
            placeholder="请输入 MinIO 服务地址"
            clearable
          />
        </el-form-item>

        <el-form-item label="Access Key" prop="accessKey">
          <el-input
            v-model="form.accessKey"
            placeholder="请输入 Access Key"
            clearable
          />
        </el-form-item>

        <el-form-item label="Secret Key" prop="secretKey">
          <el-input
            v-model="form.secretKey"
            type="password"
            placeholder="请输入 Secret Key"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item label="存储桶" prop="bucket">
          <el-input
            v-model="form.bucket"
            placeholder="请输入存储桶名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择配置类型" style="width: 100%">
            <el-option label="数据集" value="DATASET" />
            <el-option label="模型" value="MODEL" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="active">
          <el-switch
            v-model="form.active"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            保存
          </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Edit, Back } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const router = useRouter()
const route = useRoute()
const formRef = ref(null)
const loading = ref(false)

const form = reactive({
  name: '',
  endpoint: '',
  accessKey: '',
  secretKey: '',
  bucket: '',
  type: 'DATASET',
  active: true
})

const rules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  endpoint: [
    { required: true, message: '请输入服务地址', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: '请输入有效的服务地址', trigger: 'blur' }
  ],
  accessKey: [
    { required: true, message: '请输入 Access Key', trigger: 'blur' }
  ],
  secretKey: [
    { required: true, message: '请输入 Secret Key', trigger: 'blur' }
  ],
  bucket: [
    { required: true, message: '请输入存储桶名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择配置类型', trigger: 'change' }
  ]
}

// 获取配置详情
const fetchConfigDetail = async (id) => {
  loading.value = true
  try {
    const response = await service.get(`/api/v1.0/sys/storage/minio/config/${id}`)
    if (response?.code === 10000) {
      Object.assign(form, response.data)
    } else {
      toast('错误', response?.message || '获取配置详情失败', 'error')
      router.push('/minio/list')
    }
  } catch (error) {
    toast('错误', error.message || '获取配置详情失败', 'error')
    router.push('/minio/list')
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    const response = await service.put(`/api/v1.0/sys/storage/minio/config/${route.query.id}`, form)
    
    if (response?.code === 10000) {
      toast('成功', '更新成功', 'success')
      router.push('/minio/list')
    } else {
      toast('错误', response?.message || '更新失败', 'error')
    }
  } catch (error) {
    toast('错误', error.message || '更新失败', 'error')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  router.push('/minio/list')
}

onMounted(() => {
  if (route.query.id) {
    fetchConfigDetail(route.query.id)
  }else{
    toast('错误', '未找到配置ID', 'error')
  }
})
</script>

<style scoped>
.minio-config-update {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.form-card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.form-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

.config-form {
  max-width: 600px;
  margin: 0 auto;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button) {
  border-radius: 20px;
  padding: 0 24px;
}

:deep(.el-button--primary) {
  --el-button-hover-bg-color: var(--el-color-primary-light-3);
  --el-button-hover-border-color: var(--el-color-primary-light-3);
  --el-button-active-bg-color: var(--el-color-primary-dark-2);
  --el-button-active-border-color: var(--el-color-primary-dark-2);
}

:deep(.el-button--default) {
  --el-button-hover-bg-color: var(--el-color-primary-light-9);
  --el-button-hover-border-color: var(--el-color-primary-light-7);
  --el-button-hover-text-color: var(--el-color-primary);
}

/* 深色模式样式 */
html.dark .form-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .form-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

:deep(.el-empty) {
  padding: 40px 0;
}
</style> 