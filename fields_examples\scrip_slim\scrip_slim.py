import shutil
import os
from pathlib import Path
from jinja2 import Template
from typing import Callable, Dict, Any
import threading
import time
import uuid

from flask import Flask, request, render_template_string, jsonify
from werkzeug.utils import secure_filename
from werkzeug.serving import make_server

from .template_loader import TemplateLoader


class FormData:
    def __init__(self):
        self.form_data = {}

    def put(self, key, value):
        if key in self.form_data:
            if isinstance(self.form_data[key], list):
                self.form_data[key].append(value)
            else:
                self.form_data[key] = [self.form_data[key], value]
        else:
            self.form_data[key] = value

    def get(self, key):
        return self.form_data.get(key)

    def all(self):
        return self.form_data.copy()

    def remove(self, key):
        self.form_data.pop(key, None)


def default_preprocessor(app, request) -> Dict[str, Any]:
    """Default request preprocessor that handles form data and file uploads.

    Processes form data by converting to appropriate types (bool, int, float)
    and handles file uploads by saving them to temporary directory.

    Args:
        app: Flask application instance
        request: Flask request object containing form data and files

    Returns:
        Dict[str, Any]: Processed input data with converted types and file paths
    """
    form_data = FormData()

    TEMP_DIR = Path(app.config["temp_dir"])
    TEMP_DIR.mkdir(parents=True, exist_ok=True)

    for key, value in request.form.items(multi=True):

        if not value:
            form_data.put(key, value)
            continue

        if value.lower() in ('true', 'false'):
            form_data.put(key, (value.lower() == 'true'))
        elif value.isdigit():
            form_data.put(key, int(value))
        elif value.replace('.', '', 1).isdigit():
            form_data.put(key, float(value))
        else:
            form_data.put(key, value)

    for file_key, file in request.files.items(multi=True):
        if file.filename:
            filepath = TEMP_DIR / \
                secure_filename(f"{uuid.uuid4().hex}_{file.filename}")
            file.save(str(filepath))

            form_data.put(file_key, str(filepath))
    return form_data.all()


_BASE_TEMPLATE_DIR = (Path(__file__).parent / "base_template").resolve()


def default_input_loader(path, **config: Dict[str, Any]) -> str:
    """Load default HTML input form template.

    Args:
        path: Path of base input template
        **config: Configuration dictionary containing:
            - fields: List of field definitions (name, label, type etc.)

    Returns:
        str: Rendered HTML template string for the input form

    Note:
        If no fields are provided in config, defaults to a single file upload field.
    """
    if not config.get('fields'):
        config['fields'] = [
            {
                "name": "file",
                "label": "Upload File",
                "type": "file"
            }
        ]

    file_path = Path(path).resolve()

    if not file_path.exists():
        raise FileNotFoundError(f"Template file does not exist: {file_path}")

    template_str = ""
    with file_path.open("r", encoding='utf-8') as f:
        template_str = f.read()

    if not template_str:
        raise Exception(f"Failed to load template: {file_path}")

    template = Template(template_str)

    return template.render(config=config)


def default_output_loader(path, **config: Dict[str, Any]) -> str:
    """Load default HTML output form template.

    Args:
        path: Path of base output template
        **config: Configuration dictionary containing:
            - fields: List of field definitions (name, label, type etc.)

    Returns:
        str: Rendered output HTML template string to render result

    Note:
        If no fields are provided in config, defaults to single default type field.
    """
    if not config.get('fields'):
        config['fields'] = [
            {
                "type": "default"
            }
        ]

    file_path = Path(path).resolve()

    if not file_path.exists():
        raise FileNotFoundError(f"Template file does not exist: {file_path}")

    template_str = ""
    with file_path.open("r", encoding='utf-8') as f:
        template_str = f.read()

    if not template_str:
        raise Exception(f"Failed to load template: {file_path}")

    template = Template(template_str)
    template_with_config = template.render(config=config)
    # print(template_with_config)
    return template_with_config


def create_app(
    get_model: Callable,
    preprocessor: Callable = default_preprocessor,
    input_template_generate: Callable = TemplateLoader(
        path=_BASE_TEMPLATE_DIR / "input_template.jinja2",
        load_template_function=default_input_loader),
    output_template_generate: Callable = TemplateLoader(
        path=_BASE_TEMPLATE_DIR / "output_template.jinja2",
        load_template_function=default_output_loader),
    input_template_config: Dict = {},
    output_template_config: Dict = {},
    base_template=TemplateLoader(
        _BASE_TEMPLATE_DIR / "base_template.jinja2")(),
    timeout_minutes: int = 30,
    temp_dir='./TEMP_DIR',
    static_dir='./STATIC_DIR',
    static_url='/static',
    **kwargs
) -> Flask:
    """Factory function to create a configured Flask application for model serving.

    Args:
        get_model: Callable that returns the model instance
        preprocessor: Function(app,request) to preprocess incoming requests
        input_template_generate: Function that generates HTML input template 
            (default: TemplateLoader(
                path=_BASE_TEMPLATE_DIR / "input_template.jinja2",
                load_template_function=default_input_loader)
            )
        output_template_generate: Function that generates HTML output template
            (default: TemplateLoader(
                path=_BASE_TEMPLATE_DIR / "output_template.jinja2"),
                load_template_function=default_output_loader)
            )
        input_template_config: Configuration dict for input template (default: {})
        output_template_config: Configuration dict for output template (default: {})
        base_template: The base template for frontend (default:TemplateLoader(
            _BASE_TEMPLATE_DIR / "base_template.jinja2")())
        timeout_minutes: Timeout in minutes before automatic shutdown (default: 30)
        temp_dir: Directory for temporary file storage (default: './TEMP_DIR')
        static_dir: Directory for static file used by static_url (default: './STATIC_DIR')
        static_url: url to visit static files (default: '/static')
        **kwargs: Additional keyword arguments passed to Flask app

    Returns:
        Flask: Configured Flask application instance with routes:
            - '/' for main interface
            - '/pipe' for model call

    Note:
        When using the default_input_template, the input_template_config should follow
        this structure:
        ```    
            {
                'fields': [
                    {
                        'name': str,         # Field name (used in form submission)
                        'label': str,        # Display label for the field
                        'type': str,         # Field type ('text', 'number', 'file', etc.)
                        'default': Any,      # Optional default value
                        'required': bool,    # Optional required flag
                        'options': list      # Optional list of options for select fields
                    },
                    ...
                ]
            }
        ```
        If no config is provided, defaults to a single file upload field.

        By default, the file is converted to a file path and passed to model.pipe()

    The application will automatically clean up temporary files on shutdown.
    If timeout_minutes is negative, the server will run forever.
    """

    temp_dir = os.path.abspath(temp_dir)
    static_dir = os.path.abspath(static_dir)

    app = Flask(
        import_name=__name__,
        static_folder=static_dir,
        static_url_path=static_url
    )
    model = get_model()

    app.config["timeout_minutes"] = timeout_minutes
    app.config["temp_dir"] = temp_dir
    app.config["static_dir"] = static_dir
    app.config["static_url"] = static_url

    os.makedirs(temp_dir, exist_ok=True)
    os.makedirs(static_dir, exist_ok=True)

    # generate template
    input_template = input_template_generate(**input_template_config)
    output_template = output_template_generate(**output_template_config)

    # base template
    base_html = base_template

    destroy_time = int((time.time() + timeout_minutes * 60) * 1000)
    run_forever = True if timeout_minutes <= 0 else False

    @app.route('/')
    def home():
        return render_template_string(
            base_html,
            input_template=input_template,
            destroy_time=destroy_time,
            run_forever=run_forever
        )

    @app.route('/pipe', methods=['POST'])
    def pipe():
        input_data = {}
        try:
            # preprocess
            input_data = preprocessor(app, request)

            # model call
            result = model.pipe(**input_data)

            # render output template
            return render_template_string(
                output_template,
                result=result
            )

        except Exception as e:
            print(e)
            return jsonify({
                "status": "error",
                "message": str(e)
            }), 500

    return app


def cleanup_resources(app):
    """Cleans up temporary files and resources used by the application.

    Args:
        app: Flask application instance containing configuration

    Note:
        Silently handles any cleanup errors by printing them to stdout.
    """
    try:
        if app.config.get("temp_dir"):
            TEMP_DIR = app.config.get("temp_dir")
            if os.path.exists(TEMP_DIR):
                shutil.rmtree(TEMP_DIR)
    except Exception as e:
        print(f"Cleanup error: {str(e)}")


def run_app(app, port=8000):
    """Runs the Flask application with automatic timeout and cleanup.

    Args:
        app: Flask application instance to run
        port: Port number to listen on (default: 8000)

    Note:
        Runs the server in a separate thread with automatic shutdown after
        the timeout period specified in app.config['timeout_minutes'].
        Performs cleanup of resources after shutdown.
        If timeout_minutes is negative, the server will run forever.
    """
    server = None
    server_thread = None

    try:
        server = make_server('0.0.0.0', port, app)
        timeout_minutes = app.config.get("timeout_minutes", 30)

        server_thread = threading.Thread(
            target=server.serve_forever,
            daemon=True
        )
        server_thread.start()
        print(f"Server running on port {port}. Press CTRL+C to stop.")

        if timeout_minutes >= 0:
            time.sleep(timeout_minutes * 60+10)
        else:
            while server_thread.is_alive():
                time.sleep(15 * 60)

        print("Server is shutting down")
        server.socket.close()

    except KeyboardInterrupt:
        print("\nServer stopped by KeyboardInterrupt")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if server:
            server.shutdown()
        if server_thread:
            server_thread.join()
        print("Server shutdown complete.")
        cleanup_resources(app)
        print("Cleanup finished")
