from sqlalchemy import create_engine, Column, Integer, String, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from passlib.context import CryptContext

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    
    user_id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    password = Column(String, nullable=False)
    role = Column(String, nullable=False)  # 'admin', 'user', 'auditor'
    
    tasks = relationship("Task", back_populates="owner")

class Task(Base):
    __tablename__ = 'tasks'
    
    task_id = Column(String, primary_key=True)
    owner_id = Column(String, ForeignKey('users.user_id'))
    allowed_times = Column(Integer, default=0)
    audit_message = Column(String, default="")
    
    owner = relationship("User", back_populates="tasks")

engine = create_engine('sqlite:///database/pyxis.db')
Base.metadata.create_all(engine)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
def get_password_hash(password):
    return pwd_context.hash(password)

# Check if database needs initialization with default admin account
def initialize_default_admin():
    
    db = SessionLocal()
    try:
        # Check if any users exist
        if not db.query(User).first():
            default_admin = User(
                user_id="admin",
                name="pyxis",
                password=get_password_hash("pyxis"),
                role="admin"
            )
            db.add(default_admin)
            db.commit()
            print("Initialized default admin account: username=pyxis, password=pyxis")
    finally:
        db.close()

# Call the initialization function
initialize_default_admin()