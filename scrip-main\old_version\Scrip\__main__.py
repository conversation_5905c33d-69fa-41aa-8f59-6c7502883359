import argparse

from . import firm


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Run the service manager')
    parser.add_argument('--port', type=int, default=5000,
                        help='Port to run the manager service on')
    return parser.parse_args()


def launch():
    args = parse_args()
    port = args.port
    firm.launch(port=port)


launch()
