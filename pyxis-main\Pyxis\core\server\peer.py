from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from datetime import datetime, timedelta
import threading
from typing import Dict, Any


from ..stash import kv_stash

peer = APIRouter()

# Create key-value store instance
kv_store = kv_stash.KeyValueStash()

# Dictionary to store last access times for each nickname
last_access_times: Dict[str, datetime] = {}
access_times_lock = threading.RLock()

# Cleanup thread and event
cleanup_thread_running = True
cleanup_event = threading.Event()

# Expires config
EXPIRES_MINUTES = 15


class PeerInfo(BaseModel):
    nickname: str
    value: dict[str, Any]


def cleanup_inactive_peers():
    """Background thread to clean up peers inactive for more than 15 minutes."""
    while cleanup_thread_running:
        # Run every minute to check for expired peers
        # EXPIRES_MINUTES * 60 / 2 seconds
        cleanup_event.wait(EXPIRES_MINUTES * 30)
        if not cleanup_thread_running:
            break

        current_time = datetime.now()
        expired_peers = []

        # Find expired peers
        with access_times_lock:
            for nickname, last_access in last_access_times.items():
                if current_time - last_access > timedelta(minutes=EXPIRES_MINUTES):
                    expired_peers.append(nickname)

            # Remove expired peers
            for nickname in expired_peers:
                if nickname in last_access_times:
                    del last_access_times[nickname]
                kv_store.delete(nickname)
                print(f"Cleaned up inactive peer: {nickname}")


# Start the cleanup thread
cleanup_thread = threading.Thread(target=cleanup_inactive_peers, daemon=True)
cleanup_thread.start()


@peer.post("/register/")
async def register_peer(peer_info: PeerInfo, request: Request):
    """Register a new peer or update an existing registration."""
    nickname = peer_info.nickname

    # Update last access time
    with access_times_lock:
        last_access_times[nickname] = datetime.now()

    value = peer_info.value
    value['ip'] = request.client.host

    kv_store.set(nickname, value)

    return value


@peer.get("/lookup/{nickname}")
async def lookup_peer(nickname: str):
    """Look up a peer by nickname."""
    # Update last access time if peer exists
    peer_data = kv_store.get(nickname)
    if not peer_data:
        raise HTTPException(status_code=404, detail="Peer not found")

    with access_times_lock:
        last_access_times[nickname] = datetime.now()

    return peer_data


@peer.post("/unregister/{nickname}")
async def unregister_peer(nickname: str):
    """Unregister a peer."""
    if not kv_store.delete(nickname):
        raise HTTPException(status_code=404, detail="Peer not found")

    with access_times_lock:
        if nickname in last_access_times:
            del last_access_times[nickname]

    return {"message": "Peer unregistered successfully"}


@peer.get("/list_peers/", response_model=Dict[str, datetime])
async def list_peers():
    """List all currently registered peers and their last access times."""
    with access_times_lock:
        return {
            nickname: last_access
            for nickname, last_access in last_access_times.items()
            if kv_store.get(nickname) is not None
        }
