<template>
  <div class="user-update-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Edit /></el-icon>
          <h2>更新用户资料</h2>
        </div>
        <div class="sub-title">修改系统中的用户信息</div>
      </div>
      <div class="header-right">
        <el-button 
          @click="$router.push('/user/list')"
          plain
          round
        >
          <el-icon><Back /></el-icon>
          返回用户列表
        </el-button>
      </div>
    </div>

    <div class="main-content">
      <el-card class="form-card" shadow="hover" v-if="hasUserId">
        <div class="form-header">
          <div class="step-info">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
            <span>请修改用户信息，带 * 号的为必填项</span>
          </div>
        </div>   
        <el-form :model="userForm" :rules="rules" ref="formRef" label-width="120px" class="user-form">
          <el-form-item label="用户名" prop="username" class="form-item">
            <el-input 
              v-model="userForm.username" 
              :placeholder="originalUser.username || '请输入用户名'"
              size="large"
            >
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="姓名" prop="fullname" class="form-item">
            <el-input 
              v-model="userForm.fullname" 
              :placeholder="originalUser.fullname || '请输入姓名'"
              size="large"
            >
              <template #prefix>
                <el-icon><UserFilled /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email" class="form-item">
            <el-input 
              v-model="userForm.email" 
              :placeholder="originalUser.email || '请输入邮箱'"
              size="large"
            >
              <template #prefix>
                <el-icon><Message /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="状态" class="form-item">
            <el-switch
              v-model="userForm.enable"
              active-text="启用"
              inactive-text="禁用"
              size="large"
            />
          </el-form-item>
          
          <el-form-item class="form-buttons">
            <el-button 
              type="primary" 
              @click="submitForm" 
              :loading="loading"
              size="large"
              class="submit-btn"
              round
              color="#626aef"
            >
              <el-icon><Check /></el-icon>更新用户
            </el-button>
            <el-button 
              @click="resetForm"
              size="large"
              round
            >
              <el-icon><RefreshRight /></el-icon>重置表单
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
      
      <el-empty 
        v-else 
        description="未找到用户ID" 
        :image-size="200"
      >
        <el-button type="primary" @click="$router.push('/user/list')" round>
          返回用户列表
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Edit,
  User,
  UserFilled,
  Message,
  Check, 
  RefreshRight, 
  Back, 
  InfoFilled 
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const route = useRoute()
const router = useRouter()
const formRef = ref(null)
const loading = ref(false)
const pageLoading = ref(false)
const hasUserId = ref(false)

const userForm = reactive({
  username: '',
  fullname: '',
  email: '',
  enable: true
})

const originalUser = reactive({
  username: '',
  fullname: '',
  email: '',
  enable: true
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  fullname: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 获取用户详情
const getUserDetail = async (id) => {
  pageLoading.value = true
  try {
    const res = await service.get(`/api/v1.0/sys/user/${id}`)
    if (res.code === 10000) {
      const { username, fullname, email, enable } = res.data
      Object.assign(originalUser, { username, fullname, email, enable })
    } else {
      toast('错误', res.message || '获取用户信息失败', 'error')
    }
  } catch (error) {
    toast('错误', '获取用户信息失败', 'error')
  } finally {
    pageLoading.value = false
  }
}

const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // 构建查询参数
        const params = new URLSearchParams({
          username: userForm.username,
          fullname: userForm.fullname,
          email: userForm.email,
          enable: userForm.enable
        })
        
        const response = await service.put(
          `/api/v1.0/sys/user/${route.query.id}?${params.toString()}`
        )
        
        if (response.code === 10000) {
          toast('成功', '用户更新成功', 'success')
          router.push('/user/list')
        } else {
          toast('错误', response.message || '更新失败', 'error')
        }
      } catch (error) {
        toast('错误', '更新失败', 'error')
      } finally {
        loading.value = false
      }
    }
  })
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  if (route.query.id) {
    hasUserId.value = true
    console.log('用户ID:', route.query.id)
    getUserDetail(route.query.id)
  } else {
    hasUserId.value = false
    console.log('未找到用户ID')
    toast('错误', '未找到用户ID', 'error')
  }
})
</script>

<style scoped>
.user-update-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.main-content {
  .form-card {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .form-header {
    margin-bottom: 24px;
    
    .step-info {
      display: flex;
      align-items: center;
      color: #909399;
      
      .info-icon {
        margin-right: 8px;
        color: var(--el-color-primary);
      }
    }
  }
}

.user-form {
  .form-item {
    margin-bottom: 24px;
  }
  
  .form-buttons {
    margin-top: 32px;
    text-align: center;
    
    .submit-btn {
      margin-right: 16px;
      min-width: 120px;
    }
  }
}

:deep(.el-empty) {
  padding: 40px 0;
}
</style>

