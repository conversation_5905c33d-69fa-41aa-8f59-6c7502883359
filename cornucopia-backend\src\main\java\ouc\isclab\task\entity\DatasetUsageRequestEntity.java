package ouc.isclab.task.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;
import ouc.isclab.dataset.entity.DatasetEntity;

/**
 * 数据集使用申请实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_DATASET_USAGE_REQUEST")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class DatasetUsageRequestEntity extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dataset_id", nullable = false)
    private DatasetEntity dataset; // 申请使用的数据集
    
    @Column(name = "applicant_id", nullable = false)
    private Long applicantId; // 申请人ID
    
    @Column(name = "node_id", nullable = false)
    private Long nodeId; // 使用数据集的节点ID
    
    @Column(nullable = false)
    private String purpose; // 使用目的
    
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ApprovalStatus status; // 审批状态
    
    @Column
    private String rejectReason; // 拒绝理由
    
    @Column
    private Long approverId; // 审批人ID
    
    @Column
    private Long nodeAccountRequestId; // 关联的节点账号申请ID
    
    public enum ApprovalStatus {
        PENDING,    // 待审批
        APPROVED,   // 已批准
        REJECTED    // 已拒绝
    }
} 