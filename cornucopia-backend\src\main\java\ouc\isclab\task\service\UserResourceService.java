package ouc.isclab.task.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.dataset.entity.DatasetEntity;
import ouc.isclab.dataset.repository.DatasetRepository;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.repository.NodeRepository;
import ouc.isclab.system.repository.UserRepository;
import ouc.isclab.task.entity.ResourceEntity;
import ouc.isclab.task.entity.UserResourceEntity;
import ouc.isclab.task.repository.ResourceRepository;
import ouc.isclab.task.repository.UserResourceRepository;
import ouc.isclab.storage.service.MinioConfigService;
import ouc.isclab.storage.pojo.MinioConfigDTO;
import ouc.isclab.storage.entity.MinioConfigEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserResourceService {

    @Autowired
    private ResourceRepository resourceRepository;
    
    @Autowired
    private UserResourceRepository userResourceRepository;

    @Autowired
    private NodeRepository nodeRepository;

    @Autowired
    private DatasetRepository datasetRepository;
    
    @Autowired
    private MinioConfigService minioConfigService;

    
    /**
     * 授予用户对资源的访问权限
     */
    @Transactional
    public UserResourceEntity grantResourceAccess(Long userId, String resourceType, Long resourceId, Long nodeId) {
        // 查找或创建资源
        ResourceEntity resource = resourceRepository.findByResourceTypeAndResourceIdAndNodeId(
                resourceType, resourceId, nodeId);
        
        if (resource == null) {
            // 如果资源不存在，则创建新资源

            if (resourceType.equals("POWER")) {
                resource = new ResourceEntity();
                resource.setResourceType(resourceType);
                resource.setResourceId(resourceId);
                resource.setNodeId(nodeId);

                NodeEntity nodeEntity = nodeRepository.findById(nodeId)
                        .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));

                resource.setName(resourceType + "@" + nodeEntity.getNodeType() + "#" + nodeEntity.getId());
                resource = resourceRepository.save(resource);
            }
            else {
                resource = new ResourceEntity();
                resource.setResourceType(resourceType);
                resource.setResourceId(resourceId);
                resource.setNodeId(nodeId);

                NodeEntity nodeEntity = nodeRepository.findById(nodeId)
                        .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));

                DatasetEntity datasetEntity = datasetRepository.findById(resourceId)
                        .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));


                resource.setName(resourceType + "#" + datasetEntity.getId() + "@" + nodeEntity.getNodeType() + "#" + nodeEntity.getId());
                resource = resourceRepository.save(resource);

            }
        }
        
        // 检查用户是否已有该资源的权限
        UserResourceEntity userResource = userResourceRepository.findByUserIdAndResource_Id(userId, resource.getId());
        
        if (userResource == null) {
            // 如果用户没有该资源的权限，则创建新权限
            userResource = new UserResourceEntity();
            userResource.setUserId(userId);
            userResource.setResource(resource);
            userResource.setActive(true);
            userResource = userResourceRepository.save(userResource);
        } else if (!userResource.isActive()) {
            // 如果用户已有该资源的权限但被禁用，则重新启用
            userResource.setActive(true);
            userResource = userResourceRepository.save(userResource);
        }
        
        return userResource;
    }
    
    /**
     * 撤销用户对资源的访问权限
     */
    @Transactional
    public void revokeResourceAccess(Long userId, String resourceType, Long resourceId, Long nodeId) {
        ResourceEntity resource = resourceRepository.findByResourceTypeAndResourceIdAndNodeId(
                resourceType, resourceId, nodeId);
        
        if (resource != null) {
            UserResourceEntity userResource = userResourceRepository.findByUserIdAndResource_Id(userId, resource.getId());
            
            if (userResource != null) {
                userResource.setActive(false);
                userResourceRepository.save(userResource);
            }
        }
    }
    
    /**
     * 检查用户是否有权限访问特定资源
     */
    public boolean hasResourceAccess(Long userId, String resourceType, Long resourceId, Long nodeId) {
        ResourceEntity resource = resourceRepository.findByResourceTypeAndResourceIdAndNodeId(
                resourceType, resourceId, nodeId);
        
        if (resource == null) {
            return false;
        }
        
        UserResourceEntity userResource = userResourceRepository.findByUserIdAndResource_Id(userId, resource.getId());
        
        return userResource != null && userResource.isActive();
    }
    
    /**
     * 获取用户可访问的资源列表
     */
    public Page<ResourceEntity> getUserResources(Long userId, String resourceType, Pageable pageable) {
        if (resourceType != null && !resourceType.isEmpty()) {
            return resourceRepository.findByUserIdAndResourceTypeAndActive(userId, resourceType, true, pageable);
        } else {
            return resourceRepository.findByUserIdAndActive(userId, true, pageable);
        }
    }
    
    /**
     * 获取用户可访问的资源列表（包含节点完整信息）
     */
    public Page<Map<String, Object>> getUserResourcesWithNodeInfo(Long userId, String resourceType, Pageable pageable) {
        Page<Object[]> resourcePage;
        
        if (resourceType != null && !resourceType.isEmpty()) {
            resourcePage = resourceRepository.findResourcesWithNodeInfoByUserIdAndResourceTypeAndActive(userId, resourceType, true, pageable);
        } else {
            resourcePage = resourceRepository.findResourcesWithNodeInfoByUserIdAndActive(userId, true, pageable);
        }
        
        List<Map<String, Object>> resourceList = resourcePage.getContent().stream()
                .map(objects -> {
                    ResourceEntity resource = (ResourceEntity) objects[0];
                    NodeEntity node = (NodeEntity) objects[1];
                    
                    Map<String, Object> resourceMap = new HashMap<>();
                    // 资源信息
                    resourceMap.put("id", resource.getId());
                    resourceMap.put("resourceType", resource.getResourceType());
                    resourceMap.put("resourceId", resource.getResourceId());
                    resourceMap.put("nodeId", resource.getNodeId());
                    resourceMap.put("name", resource.getName());
                    resourceMap.put("description", resource.getDescription());
                    resourceMap.put("timeCreated", resource.getTimeCreated());
                    resourceMap.put("timeUpdated", resource.getTimeUpdated());
                    
                    // 节点信息
                    resourceMap.put("nodeType", node.getNodeType());
                    resourceMap.put("nodeName", node.getName());
                    resourceMap.put("nodeIpAddress", node.getIpAddress());
                    resourceMap.put("nodePort", node.getPort());
                    resourceMap.put("nodeDescription", node.getDescription());
                    resourceMap.put("nodeTimeCreated", node.getTimeCreated());
                    resourceMap.put("nodeTimeUpdated", node.getTimeUpdated());
                    
                    return resourceMap;
                })
                .collect(Collectors.toList());
        
        return new PageImpl<>(resourceList, pageable, resourcePage.getTotalElements());
    }
    
    /**
     * 获取资源的Minio配置
     * 根据资源ID和可选的模型路径，返回相应的Minio配置信息
     */
    public Map<String, Object> getResourceMinioConfig(Long userId, Long resourceId, String modelPath) {
        // 验证资源是否存在
        ResourceEntity resource = resourceRepository.findById(resourceId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "资源不存在"));
        
        // 验证用户是否有权限访问该资源
        boolean hasAccess = hasResourceAccess(
                userId, resource.getResourceType(), resource.getResourceId(), resource.getNodeId()
        );
        
        if (!hasAccess) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权访问该资源");
        }
        
        Map<String, Object> result = new HashMap<>();
        
        // 如果是数据集资源，加载数据集配置
        if (resource.getResourceType().equals("DATASET")) {
            MinioConfigDTO dataConfig = minioConfigService.getActiveConfigByType(MinioConfigEntity.ConfigType.DATASET);
            result.put("data_endpoint", dataConfig.getEndpoint());
            result.put("data_access_key", dataConfig.getAccessKey());
            result.put("data_secret_key", dataConfig.getSecretKey());
            result.put("data_bucket", dataConfig.getBucket());
            
            DatasetEntity dataset = datasetRepository.findById(resource.getResourceId())
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));
            result.put("data_path", dataset.getPath());
        }
        
        // 如果提供了模型路径，加载模型配置
        if (modelPath != null && !modelPath.isEmpty()) {
            MinioConfigDTO modelConfig = minioConfigService.getActiveConfigByType(MinioConfigEntity.ConfigType.MODEL);
            result.put("model_endpoint", modelConfig.getEndpoint());
            result.put("model_access_key", modelConfig.getAccessKey());
            result.put("model_secret_key", modelConfig.getSecretKey());
            result.put("model_bucket", modelConfig.getBucket());
            result.put("model_path", modelPath);
        }
        
        return result;
    }

} 