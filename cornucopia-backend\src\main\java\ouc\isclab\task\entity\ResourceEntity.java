package ouc.isclab.task.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;

/**
 * 系统资源实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_RESOURCE", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"resource_type", "resource_id", "node_id"}))
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class ResourceEntity extends BaseEntity {

    @Column(name = "resource_type", nullable = false)
    private String resourceType; // 资源类型，如 DATASET, POWER 等
    
    @Column(name = "resource_id", nullable = false)
    private Long resourceId; // 资源ID
    
    @Column(name = "node_id", nullable = false)
    private Long nodeId; // 节点ID
    
    @Column(nullable = false)
    private String name; // 资源名称
    
    @Column
    private String description; // 资源描述
} 