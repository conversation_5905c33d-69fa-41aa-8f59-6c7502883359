#!/bin/bash
set -e  # Exit immediately on error

# Configuration Variables
MAIN_IMAGE="firm-service"
MODEL_IMAGE="scrip-service"
MAIN_PORT=5000
DOCKERFILES_DIR="dockerfiles"
NETWORK_NAME="firm-network"

# Create Docker network if not exists
create_network() {
  if ! docker network inspect $NETWORK_NAME >/dev/null 2>&1; then
    echo "=== CREATING DOCKER NETWORK: $NETWORK_NAME ==="
    docker network create $NETWORK_NAME
  fi
}

# Build all Docker images
build_images() {
  echo "=== BUILDING IMAGES ==="
  echo "Building $MODEL_IMAGE..."
  docker build -t $MODEL_IMAGE -f $DOCKERFILES_DIR/scrip.Dockerfile .
  
  echo "Building $MAIN_IMAGE..."
  docker build -t $MAIN_IMAGE -f $DOCKERFILES_DIR/firm.Dockerfile .
}

# Start the main service container with network
start_service() {
  echo "=== STARTING SERVICE ==="
  docker run -d \
    --name $MAIN_IMAGE \
    --network $NETWORK_NAME \
    -p $MAIN_PORT:5000 \
    -v $(pwd)/templates:/app/templates:ro \
    -v $(pwd)/temp:/app/temp \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -e SERVICE_TEMP_FILE=$(pwd)/temp \
    $MAIN_IMAGE
}

# Stop running containers
stop_services() {
  echo "=== STOPPING SERVICES ==="
  # Stop main service
  docker stop $MAIN_IMAGE 2>/dev/null || true
  docker rm $MAIN_IMAGE 2>/dev/null || true

  # Stop all model services
  for container in $(docker ps -aq -f "name=scrip-service-"); do
    docker stop $container 2>/dev/null || true
    docker rm $container 2>/dev/null || true
  done
}

# Remove all resources
clean_all() {
  stop_services
  echo "=== CLEANING IMAGES ==="
  docker rmi $MAIN_IMAGE $MODEL_IMAGE 2>/dev/null || true
  
  echo "=== REMOVING NETWORK ==="
  docker network rm $NETWORK_NAME 2>/dev/null || true
  
  echo "=== CLEANING TEMP FILES ==="
  rm -rf temp_*
}

# Show usage information
show_help() {
  cat << EOF
Usage: $0 [COMMAND]

Available Commands:
  build      Only build images
  start      Start main service
  stop       Stop all services
  clean      Remove all resources (containers, images, network)
  help       Show this help message

Default behavior (no arguments): Build and start service
EOF
}

# Main execution
case "$1" in
  build)
    build_images
    ;;
  start)
    create_network
    start_service
    ;;
  stop)
    stop_services
    ;;
  clean)
    clean_all
    ;;
  help|--help|-h)
    show_help
    ;;
  *)
    # Default behavior: build and start
    build_images
    create_network
    start_service
    ;;
esac

echo "=== OPERATION COMPLETE ==="