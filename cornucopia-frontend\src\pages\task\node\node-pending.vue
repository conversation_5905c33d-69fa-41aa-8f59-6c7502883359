<template>
  <div class="node-pending-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Connection /></el-icon>
          <h2>节点账号申请审批</h2>
        </div>
        <div class="sub-title">审批用户的节点账号申请请求</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.keyword"
                placeholder="搜索申请人或节点"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
        highlight-current-row
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column label="申请人" min-width="120" align="center">
          <template #default="scope">
            <el-button 
              type="primary" 
              link 
              @click="viewUserInfo(scope.row.applicantId)"
            >
              {{ scope.row.applicantName || `用户${scope.row.applicantId}` }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="节点" min-width="150" align="center">
          <template #default="scope">
            <el-tag>{{ scope.row.node?.name || '未知节点' }} ({{ scope.row.node?.ipAddress }}:{{ scope.row.node?.port }})</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="申请理由" min-width="200" show-overflow-tooltip align="center" />
        <el-table-column label="状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          prop="timeCreated" 
          label="申请时间" 
          width="180" 
          align="center"
          sortable
          :formatter="formatDateTime"
        />
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-buttons">
              <el-button-group v-if="scope.row.status === 'PENDING'">
                <el-tooltip content="批准" placement="top">
                  <el-button type="success" size="small" @click="approveRequest(scope.row)">
                    <el-icon><Check /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="拒绝" placement="top">
                  <el-button type="danger" size="small" @click="showRejectDialog(scope.row)">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </el-tooltip>
              </el-button-group>
              <el-tooltip content="查看详情" placement="top" v-else>
                <el-button type="info" size="small" @click="viewRequestDetail(scope.row)">
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 拒绝理由对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="拒绝申请"
      width="500px"
      destroy-on-close
    >
      <el-form :model="rejectForm" :rules="rejectRules" ref="rejectFormRef">
        <el-form-item label="拒绝理由" prop="reason" label-width="100px">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝理由"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="rejectRequest" :loading="rejectLoading">
            确认拒绝
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 申请详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="申请详情"
      width="700px"
      destroy-on-close
    >
      <div v-if="currentRequest" class="request-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="申请ID">{{ currentRequest.id }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentRequest.applicantId }}</el-descriptions-item>
          <el-descriptions-item label="节点">{{ currentRequest.node?.name || '未知节点' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentRequest.status)">
              {{ getStatusText(currentRequest.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请理由">{{ currentRequest.reason }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime({timeCreated: currentRequest.timeCreated}) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间" v-if="currentRequest.timeUpdated">
            {{ formatDateTime({timeUpdated: currentRequest.timeUpdated}) }}
          </el-descriptions-item>
          <el-descriptions-item label="拒绝理由" v-if="currentRequest.rejectReason">
            {{ currentRequest.rejectReason }}
          </el-descriptions-item>
          <el-descriptions-item label="节点用户名" v-if="currentRequest.nodeUsername">
            {{ currentRequest.nodeUsername }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 用户信息对话框 -->
    <el-dialog
      v-model="userInfoDialogVisible"
      title="申请人信息"
      width="500px"
    >
      <div v-loading="userInfoLoading">
        <el-descriptions :column="1" border v-if="userInfo">
          <el-descriptions-item label="用户ID">{{ userInfo.userId }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ userInfo.fullname || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ userInfo.email || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ userInfo.enable ? '启用' : '禁用' }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ formatDateTime({timeCreated: userInfo.lastLogin}) }}</el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatDateTime({timeCreated: userInfo.createTime}) }}</el-descriptions-item>
        </el-descriptions>
        <el-empty v-else description="无法获取用户信息"></el-empty>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Search, Refresh, Check, Close, View, Connection } from '@element-plus/icons-vue';
import service from '~/axios';
import { toast, showModal } from '~/composables/util';

// 数据加载状态
const loading = ref(false);
const rejectLoading = ref(false);
const userInfoLoading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 表格数据
const tableData = ref([]);

// 搜索表单
const searchForm = reactive({
  keyword: '',
});

// 对话框控制
const rejectDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const rejectFormRef = ref(null);
const currentRequest = ref(null);
const userInfoDialogVisible = ref(false);
const userInfo = ref(null);

// 拒绝表单
const rejectForm = reactive({
  requestId: null,
  reason: ''
});

// 拒绝表单验证规则
const rejectRules = {
  reason: [{ required: true, message: '请输入拒绝理由', trigger: 'blur' }]
};

// 获取申请列表
const loadRequests = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
    };
    
    if (searchForm.keyword) {
      params.keyword = searchForm.keyword;
    }
    
    const res = await service.get('/api/v1.0/sys/node/account/examine', { params });
    if (res.code === 10000) {
      tableData.value = res.data.requests || [];
      total.value = res.data.pagination?.total || 0;
    } else {
      toast('错误', res.message || '获取节点账号申请列表失败', 'error');
    }
  } catch (error) {
    console.error('获取节点账号申请列表失败:', error);
    toast('错误', '获取节点账号申请列表失败', 'error');
  } finally {
    loading.value = false;
  }
};

// 批准申请
const approveRequest = async (row) => {
  try {
    await showModal(`确定要批准用户 ${row.applicantName || row.applicantId} 的节点账号申请吗？`, 'warning', '确认批准');
    
    loading.value = true;
    const res = await service.post(`/api/v1.0/sys/node/account/approve/${row.id}`, null, {
      params: {
        approved: true
      }
    });
    
    if (res.code === 10000) {
      toast('成功', '已批准申请', 'success');
      loadRequests(); // 刷新列表
    } else {
      toast('错误', res.message || '批准申请失败', 'error');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批准申请失败:', error);
      toast('错误', error.response?.data?.message || '批准申请失败', 'error');
    }
  } finally {
    loading.value = false;
  }
};

// 显示拒绝对话框
const showRejectDialog = (row) => {
  rejectForm.requestId = row.id;
  rejectForm.reason = '';
  rejectDialogVisible.value = true;
};

// 拒绝申请
const rejectRequest = async () => {
  if (!rejectFormRef.value) return;
  
  await rejectFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    rejectLoading.value = true;
    try {
      const res = await service.post(`/api/v1.0/sys/node/account/approve/${rejectForm.requestId}`, null, {
        params: {
          approved: false,
          rejectReason: rejectForm.reason
        }
      });
      
      if (res.code === 10000) {
        toast('成功', '已拒绝申请', 'success');
        rejectDialogVisible.value = false;
        loadRequests(); // 刷新列表
      } else {
        toast('错误', res.message || '拒绝申请失败', 'error');
      }
    } catch (error) {
      console.error('拒绝申请失败:', error);
      toast('错误', error.response?.data?.message || '拒绝申请失败', 'error');
    } finally {
      rejectLoading.value = false;
    }
  });
};

// 查看申请详情
const viewRequestDetail = (row) => {
  currentRequest.value = row;
  detailDialogVisible.value = true;
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadRequests();
};

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = '';
  currentPage.value = 1;
  loadRequests();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadRequests();
};

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadRequests();
};

// 格式化日期时间
const formatDateTime = (row, column) => {
  const dateStr = row.timeCreated || row.timeUpdated;
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
};

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已拒绝'
  };
  return statusMap[status] || status;
};

// 查看用户信息
const viewUserInfo = async (userId) => {
  userInfoDialogVisible.value = true;
  userInfoLoading.value = true;
  userInfo.value = null;
  
  try {
    const response = await service.get(`/api/v1.0/sys/node/account/user/${userId}`);
    if (response.code === 10000) {
      userInfo.value = response.data;
    } else {
      toast('错误', response.message || '获取用户信息失败', 'error');
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    toast('错误', '获取用户信息失败', 'error');
  } finally {
    userInfoLoading.value = false;
  }
};

// 页面加载时执行
onMounted(() => {
  loadRequests();
});
</script>

<style scoped>
.node-pending-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
}

.list-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

.operation-buttons {
  display: flex;
  justify-content: center;
}

.request-detail {
  max-height: 60vh;
  overflow-y: auto;
}

/* 深色模式适配 */
html.dark {
  .list-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style>
