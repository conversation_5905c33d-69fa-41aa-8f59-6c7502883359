import os
import sys
import importlib
from pathlib import Path
import json


def execute_in_framework(user_code_path: str, workspace_path: str) -> dict:
    """Executes user code in a controlled environment while capturing output in real-time.

    Redirects stdout to a log file with line buffering to ensure immediate output capture.
    Imports and executes the user's module, then saves both the execution results and logs.

    Args:
        user_code_path: Path to the user's Python module/file to execute.
        workspace_path: Working directory where the execution should occur.

    Returns:
        dict: The result returned by the user's main() function.

    Raises:
        ImportError: If the user module cannot be imported.
        Exception: Any exceptions raised by the user code will propagate up.
    """
    os.chdir(workspace_path)

    TASK_DIR = Path(os.getenv("TASK_DIR"))

    # Set up logging path
    log_path = TASK_DIR / "logs" / "user_output.log"
    # Ensure log directory exists
    log_path.parent.mkdir(parents=True, exist_ok=True)

    # Save original stdout for restoration later
    original_stdout = sys.stdout

    try:
        # Open log file with line buffering (flushes after each newline)
        # buffering=1 enables line buffering
        log_file = open(log_path, "a", buffering=1)
        sys.stdout = log_file  # Redirect standard output

        # Import user module
        user_code_path = os.path.abspath(user_code_path)
        package_parent = os.path.dirname(user_code_path)
        sys.path.insert(0, package_parent)  # Temporarily add to Python path

        package_name = os.path.basename(user_code_path)
        user_module = importlib.import_module(package_name)

        # Execute and capture results
        result = user_module.main()

        sys.stdout.flush()
    finally:
        # Restore original stdout and clean up
        sys.stdout = original_stdout
        log_file.close()

    # Save execution results
    result_path = TASK_DIR / "results" / "result.json"
    # Ensure directory exists
    result_path.parent.mkdir(parents=True, exist_ok=True)

    with open(result_path, "w") as f:
        json.dump(result, f, indent=2)

    return result
