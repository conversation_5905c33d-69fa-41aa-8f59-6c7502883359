import router from "~/router"
import { getToken } from "~/composables/auth"
import { 
    toast,
    showFullLoading,
    hideFullLoading
} from "~/composables/util"
import store from "./store"

// 全局前置守卫
router.beforeEach(async (to,from,next)=>{
    // 显示loading
    showFullLoading()

    const token = getToken()

    // 没有登录，强制跳转回登录页
    if(!token && to.path != "/login" && to.path != "/register"){
        toast("提示","请先登录后再访问","warning")
        return next({ path:"/login" })
    }

    // 防止重复登录
    if(token && to.path == "/login" && to.path == "/register"){
        toast("提示","您已登录，请勿重复登录","warning")
        return next({ path:from.path ? from.path : "/" })
    }

    // 先获取用户信息
    if(token){
        try {
            await store.dispatch("getinfo")
        } catch(error) {
            if(error.response?.status === 401){
                toast("提示","登录状态已过期，请重新登录","warning")
                store.dispatch("logout")
                return next({ path:"/login" })
            }
        }
    }

    // 权限检查移到获取用户信息之后
    const requiredPermissions = to.meta.permissions
    if (requiredPermissions && requiredPermissions.length > 0) {
        if (!store.getters.hasAnyPermission(requiredPermissions)) {
            toast("错误","您没有访问该页面的权限","error")
            next(from.path)
            return
        }
    }

    // 设置页面标题
    let title = (to.meta.title ? to.meta.title : "") + " - Cornucopia"
    document.title = title

    next()
})

// 全局后置守卫
router.afterEach((to, from) => hideFullLoading())