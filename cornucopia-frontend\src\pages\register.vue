<template>
    <el-row class="login-container">
        <el-col :lg="16" :md="12" class="left">
            <div>
                <div class="cornucopia-title">Cornucopia</div>
                <div class="main-title">联邦学习与共享计算平台</div>
                <div class="description">致力于数据安全技术研究与创新应用</div>
            </div>
        </el-col>
        <el-col :lg="8" :md="12" class="right">
            <h2 class="title">用户注册</h2>
            <div>
                <span class="line"></span>
                <span>注册新账号</span>
                <span class="line"></span>
            </div>
            <el-form ref="formRef" :rules="rules" :model="form" class="w-[400px]">
                <el-form-item prop="username" class="form-item">
                    <el-input v-model="form.username" placeholder="请输入用户名" :size="large">
                        <template #prefix>
                            <el-icon><user /></el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item prop="password" class="form-item">
                    <el-input type="password" v-model="form.password" placeholder="请输入密码" show-password :size="large">
                        <template #prefix>
                            <el-icon><lock /></el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item prop="repassword" class="form-item">
                    <el-input type="password" v-model="form.repassword" placeholder="请确认密码" show-password :size="large">
                        <template #prefix>
                            <el-icon><lock /></el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item prop="roleId" class="form-item">
                    <el-select 
                        v-model="form.roleId" 
                        placeholder="请选择角色类型" 
                        :size="large"
                    >
                        <el-option
                            v-for="role in roles"
                            :key="role.id"
                            :label="role.name"
                            :value="role.id"
                            class="w-[800px]"
                        >
                            <div class="flex items-center justify-between w-full">
                                <span>{{ role.name }}</span>
                                <el-tooltip 
                                    :content="role.description" 
                                    placement="left"
                                >
                                    <el-icon><info-filled /></el-icon>
                                </el-tooltip>
                            </div>
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button round color="#626aef" class="w-[400px]" type="primary" @click="onSubmit" :loading="loading" :size="large">注 册</el-button>
                </el-form-item>
            </el-form>
            <div class="flex items-center justify-center mt-4">
                <router-link to="/login" class="text-gray-600 hover:text-indigo-500">
                    已有账号？立即登录
                </router-link>
            </div>
            <div class="copyright" style="color:#666">
                2025 @海尔海大数据安全联合开发实验室 青岛
            </div>
        </el-col>
    </el-row>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { toast } from '~/composables/util'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { InfoFilled } from '@element-plus/icons-vue'

const store = useStore()
const router = useRouter()

// 角色列表
const roles = ref([
    {
        id: 1,
        name: "用户",
        description: "平台的一般用户"
    },
    {
        id: 2,
        name: "数据拥有者", 
        description: "数据拥有者，向平台上传、管理自己的数据"
    },
    {
        id: 3,
        name: "数据科学家",
        description: "数据科学家，利用平台资源进行科学计算"
    },
    {
        id: 4,
        name: "节点拥有者",
        description: "节点拥有者，管理注册在平台的Agent"
    }
])

const form = reactive({
    username: "",
    password: "",
    repassword: "",
    roleId: ""
})

const rules = {
    username: [
        { required: true, message: '用户名不能为空', trigger: 'blur' },
    ],
    password: [
        { required: true, message: '密码不能为空', trigger: 'blur' },
    ],
    repassword: [
        { required: true, message: '确认密码不能为空', trigger: 'blur' },
        {
            validator: (rule, value, callback) => {
                if (value !== form.password) {
                    callback(new Error('两次输入密码不一致'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ],
    roleId: [
        { required: true, message: '请选择角色类型', trigger: 'change' }
    ]
}

const formRef = ref(null)
const loading = ref(false)
const onSubmit = () => {
    formRef.value.validate((valid) => {
        if (!valid) {
            return false
        }
        loading.value = true
        
        const registerData = {
            username: form.username,
            password: form.password,
            roleId: form.roleId.toString(),
            repassword: form.repassword
        }
        
        store.dispatch("register", registerData).then(res => {
            if(res.code === 10005) {
                toast("请求成功", "注册成功", "success")
                router.push("/login")
            } else {
                toast("注册失败", res.message || "创建账号失败", "error")
            }
        }).catch(err => {
            toast("注册失败", err.message || "创建账号失败", "error")
        }).finally(() => {
            loading.value = false
        })
    })
}
</script>

<style scoped>
.login-container{
    @apply min-h-screen bg-indigo-500;
}
.login-container .left,.login-container .right{
    @apply flex items-center justify-center;
}
.login-container .right{
    @apply bg-light-50 flex-col;
}
.left>div>div:first-child{
    @apply font-bold text-7xl text-light-50 mb-4;
}
.left>div>div:nth-child(2){
    @apply font-bold text-5xl text-light-50 mb-6;
}
.left>div>div:nth-child(3){
    @apply text-gray-200 text-3xl;
}
.right .title{
    @apply font-bold text-5xl text-gray-800 mb-2 mt-16;
}
.right>div{
    @apply flex items-center justify-center my-8 text-gray-300 space-x-4;
}
.right .line{
    @apply h-[1px] w-28 bg-gray-200;
}
.copyright {
    @apply text-lg mt-12 opacity-80;
}
.right .form-item {
    @apply mb-6;
}
.right :deep(.el-input__wrapper) {
    @apply h-[50px] text-lg;
}
.right :deep(.el-button) {
    @apply h-[50px] text-lg;
}
.right :deep(.el-input__prefix-inner) {
    @apply text-xl;
}
</style>
