package ouc.isclab.model.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.model.entity.ModelEntity;
import ouc.isclab.model.entity.ModelEntity.ModelStatus;
import ouc.isclab.model.repository.ModelRepository;
import ouc.isclab.task.entity.TaskEntity;
import ouc.isclab.task.repository.TaskRepository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ModelService {

    @Autowired
    private ModelRepository modelRepository;
    
    @Autowired
    private TaskRepository taskRepository;

    /**
     * 获取用户的模型列表
     */
    public Page<ModelEntity> getUserModels(Long userId, String keyword, Pageable pageable) {
        if (keyword != null && !keyword.trim().isEmpty()) {
            return modelRepository.findByCreatorIdAndKeyword(userId, keyword.trim(), pageable);
        } else {
            return modelRepository.findByCreatorId(userId, pageable);
        }
    }
    
    /**
     * 获取模型详情
     */
    public ModelEntity getModelDetail(Long modelId, Long userId) {
        ModelEntity model = modelRepository.findById(modelId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "模型不存在"));
        
        // 验证是否是模型创建者
        if (!model.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权查看此模型");
        }
        
        return model;
    }

    /**
     * 获取任务关联的模型列表
     */
    public List<ModelEntity> getTaskModels(Long taskId, Long userId) {
        // 验证任务是否存在且属于该用户
        TaskEntity task = taskRepository.findById(taskId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "任务不存在"));
        
        if (!task.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权查看此任务的模型");
        }
        
        return modelRepository.findByTaskId(taskId);
    }

    /**
     * 删除模型
     */
    @Transactional
    public void deleteModel(Long modelId, Long userId) {
        // 1. 获取模型实体
        ModelEntity model = modelRepository.findById(modelId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "模型不存在"));
        
        // 2. 检查权限（只有创建者可以删除）
        if (!model.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权删除此模型");
        }
        
        // 3. 检查模型状态（不能删除正在训练中的模型）
        if (model.getStatus() == ModelStatus.TRAINING) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无法删除正在训练中的模型");
        }
        
        // 4. 删除数据库记录
        modelRepository.delete(model);
    }

    /**
     * 批量删除模型
     */
    @Transactional
    public void batchDeleteModels(List<Long> ids, Long userId) {
        for (Long id : ids) {
            deleteModel(id, userId);
        }
    }
    
    /**
     * 获取模型统计数据
     */
    public Map<String, Object> getModelStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取所有模型
        List<ModelEntity> allModels = modelRepository.findAll();
        
        // 计算基本统计数据
        int totalModels = allModels.size();
        int completedModels = (int) modelRepository.countByStatus(ModelStatus.COMPLETED);
        int trainingModels = (int) modelRepository.countByStatus(ModelStatus.TRAINING);
        int failedModels = (int) modelRepository.countByStatus(ModelStatus.FAILED);
        
        // 添加基本统计数据到结果
        result.put("totalModels", totalModels);
        result.put("completedModels", completedModels);
        result.put("trainingModels", trainingModels);
        result.put("failedModels", failedModels);
        
        // 计算模型创建趋势（最近7天）
        List<Map<String, Object>> modelTrend = calculateModelTrend(allModels);
        result.put("modelTrend", modelTrend);
        
        return result;
    }
    
    /**
     * 计算模型创建趋势（最近7天）
     */
    private List<Map<String, Object>> calculateModelTrend(List<ModelEntity> models) {
        List<Map<String, Object>> result = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");
        
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date today = calendar.getTime();
        
        // 创建从今天开始的7天日期映射
        Map<String, Integer> dateCountMap = new HashMap<>();
        // 先添加今天
        String todayStr = dateFormat.format(today);
        dateCountMap.put(todayStr, 0);
        
        // 再添加前6天
        Calendar tempCalendar = (Calendar) calendar.clone();
        for (int i = 1; i <= 6; i++) {
            tempCalendar.add(Calendar.DAY_OF_MONTH, -1);
            String dateStr = dateFormat.format(tempCalendar.getTime());
            dateCountMap.put(dateStr, 0);
        }
        
        // 获取7天前的日期
        calendar.add(Calendar.DAY_OF_MONTH, -6);
        Date sevenDaysAgo = calendar.getTime();
        
        // 创建明天的日期，用于比较
        Calendar tomorrowCalendar = Calendar.getInstance();
        tomorrowCalendar.add(Calendar.DAY_OF_MONTH, 1);
        tomorrowCalendar.set(Calendar.HOUR_OF_DAY, 0);
        tomorrowCalendar.set(Calendar.MINUTE, 0);
        tomorrowCalendar.set(Calendar.SECOND, 0);
        tomorrowCalendar.set(Calendar.MILLISECOND, 0);
        Date tomorrow = tomorrowCalendar.getTime();
        
        // 统计每天创建的模型数量，包括今天
        for (ModelEntity model : models) {
            Date createTime = model.getTimeCreated();
            if (createTime != null && createTime.after(sevenDaysAgo) && createTime.before(tomorrow)) {
                String dateStr = dateFormat.format(createTime);
                dateCountMap.put(dateStr, dateCountMap.getOrDefault(dateStr, 0) + 1);
            }
        }
        
        // 构建结果
        for (Map.Entry<String, Integer> entry : dateCountMap.entrySet()) {
            Map<String, Object> item = new HashMap<>();
            item.put("date", entry.getKey());
            item.put("count", entry.getValue());
            result.add(item);
        }
        
        // 按日期排序
        result.sort((a, b) -> ((String) a.get("date")).compareTo((String) b.get("date")));
        
        return result;
    }
} 