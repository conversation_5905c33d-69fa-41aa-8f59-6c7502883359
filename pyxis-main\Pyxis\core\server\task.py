import json
import re

from fastapi import APIRouter, File, Form, HTTPException, UploadFile, status
from fastapi.responses import J<PERSON><PERSON>espo<PERSON>, FileResponse
import docker

from ..manager import DockerTaskManager, FilesManager
from ..utils import snowflake

task = APIRouter()
docker_task_manager = DockerTaskManager()
files_manager = FilesManager(MAX_CONTENT_SIZE=0)
SnowflakeGenerator = snowflake.SnowflakeGenerator()


def sanitize_folder_name(folder_name: str) -> str:
    """
    Sanitize folder name to be Linux filesystem compatible

    Args:
        folder_name: Original folder name

    Returns:
        Sanitized folder name with only alphanumeric, hyphen and underscore
    """
    sanitized = folder_name.replace(' ', '_')
    sanitized = re.sub(r'[^\w-]', '', sanitized)
    return sanitized[:50]


@task.post("/task", status_code=status.HTTP_201_CREATED)
async def create_task(files: list[UploadFile] = File(...), folder_name: str = Form(None)):
    """
    Create and start a new task container with uploaded files

    Args:
        files: List of files to upload to the task's user_code directory

    Returns:
        JSON response containing:
        - task_id: The generated task ID
        - container_id: The Docker container ID
        - status: Initial container status
    """
    try:
        task_id = SnowflakeGenerator.generate_id_hex()

        task_name = "usercode"
        if folder_name:
            sanitized_folder = sanitize_folder_name(folder_name)
            if sanitized_folder:
                task_name = f"{sanitized_folder}"

        task_id = f"{task_id}_{task_name}"

        savedfiles = await files_manager.save_user_code(task_id, files)

        result = {
            "task_id": task_id,
            "saved_files": savedfiles
        }
        return JSONResponse(
            content=result,
            status_code=status.HTTP_201_CREATED
        )
    except Exception as e:
        try:
            files_manager.delete_task(task_id)
        except Exception:
            pass

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create task: {str(e)}"
        )


@task.post("/task/{task_id}/launch")
async def launch_task(task_id: str):
    try:
        if docker_task_manager.is_task_running(task_id):
            raise Exception("Task is already running")

        if docker_task_manager.is_container_exists(task_id):
            raise Exception("Container already exists")

        task_status = files_manager.get_task_status(task_id)
        error_message = task_status.get('error')
        if error_message:
            raise Exception(f"Failed to load task status : {error_message}")

        # Launch the task
        result = await docker_task_manager.launch_task(task_id)
        return JSONResponse(
            content=result,
            status_code=status.HTTP_201_CREATED
        )
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to launch task: {str(e)}"
        )


@task.post("/task/{task_id}/stop")
async def stop_task(task_id: str):
    """
    Gracefully stop a running task container

    Args:
        task_id: The ID of the task to stop

    Returns:
        JSON response with stop operation result
    """
    try:
        result = await docker_task_manager.stop_task(task_id)
        return JSONResponse(content=result)
    except docker.errors.NotFound:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop task: {str(e)}"
        )


@task.post("/task/{task_id}/kill")
async def kill_task(task_id: str):
    """
    Forcefully kill a task container

    Args:
        task_id: The ID of the task to kill

    Returns:
        JSON response with kill operation result
    """
    try:
        result = await docker_task_manager.kill_task(task_id)
        return JSONResponse(content=result)
    except docker.errors.NotFound:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to kill task: {str(e)}"
        )


@task.get("/tasks")
async def list_tasks():
    """
    Get a list of all available tasks

    Returns:
        JSON array of task objects containing:
        - task_id: The task ID
        - path: Relative path to the task directory
    """
    try:
        tasks = files_manager.list_tasks()
        return JSONResponse(content=tasks)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list tasks: {str(e)}"
        )


@task.get("/task/{task_id}/status")
async def get_task_status(task_id: str):
    """
    Get the status of a specific task

    Args:
        task_id: The ID of the task to query

    Returns:
        JSON response with task status information
    """
    try:
        status_info = files_manager.get_task_status(task_id)
        if "error" in status_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=status_info["error"]
            )

        status_info = status_info.get('content')
        status_info = json.loads(status_info)
        return JSONResponse(content=status_info)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get task status: {str(e)}"
        )


@task.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """
    Delete a task and its associated resources

    Args:
        task_id: The ID of the task to delete

    Returns:
        JSON response with deletion result
    """
    try:
        if docker_task_manager.is_container_running(task_id):
            raise Exception("task is running; please stop first")

        deleted_task = files_manager.delete_task(task_id)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "deleted_task": deleted_task
            }
        )
    except FileNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete task: {str(e)}"
        )


@task.get("/task/{task_id}/browse/{path:path}")
async def browse_task_files(task_id: str, path: str = ""):
    """
    Browse files in a task's workspace

    Args:
        task_id: The ID of the task
        path: Relative path within the task's workspace

    Returns:
        JSON response with file/directory information
    """
    try:
        file_info = files_manager.get_file_info(task_id, path)
        if "error" in file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=file_info["error"]
            )
        return JSONResponse(content=file_info)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to browse task files: {str(e)}"
        )


@task.get("/task/{task_id}/download/{path:path}")
async def download_task_file(task_id: str, path: str):
    """
    Download a file from a task's workspace

    Args:
        task_id: The ID of the task
        path: Relative path to the file within the task's workspace

    Returns:
        File download response
    """
    try:
        result = files_manager.get_file_download(task_id, path)
        return FileResponse(
            **result,
            media_type="application/octet-stream"
        )
    except FileNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download file: {str(e)}"
        )
