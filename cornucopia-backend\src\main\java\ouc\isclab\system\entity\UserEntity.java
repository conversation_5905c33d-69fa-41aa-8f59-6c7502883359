package ouc.isclab.system.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;

import java.util.Date;
import java.util.Set;

/**
 * @desc 用户实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_USER")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class UserEntity extends BaseEntity {
    @Column(nullable = false, unique = true)
    private String username;
    private String fullname;
    private String email;

    @JsonIgnore
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String password;

    private boolean enable;
    private boolean online;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "last_login")
    private Date lastLogin;
    @ManyToMany(cascade = CascadeType.REFRESH, mappedBy = "users")

    //@JsonIgnore
    @JsonIgnoreProperties(ignoreUnknown = true, value = {"users"})
    private Set<RoleEntity> roles;
}