package ouc.isclab.system.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import ouc.isclab.system.entity.UserRoleEntity;
import java.util.List;

public interface UserRoleRepository extends JpaRepository<UserRoleEntity, Long> {
    // 根据用户ID查找所有角色关联
    List<UserRoleEntity> findByUserId(Long userId);
    
    // 根据角色ID查找所有用户关联
    List<UserRoleEntity> findByRoleId(Long roleId);
    
    // 删除用户的所有角色关联
    @Modifying
    @Query("DELETE FROM UserRoleEntity u WHERE u.userId = :userId")
    void deleteByUserId(@Param("userId") Long userId);
    
    // 删除角色的所有用户关联
    @Modifying
    @Query("DELETE FROM UserRoleEntity u WHERE u.roleId = :roleId")
    void deleteByRoleId(@Param("roleId") Long roleId);
}
