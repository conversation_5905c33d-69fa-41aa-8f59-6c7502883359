package ouc.isclab.model.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.model.entity.ModelEntity;
import ouc.isclab.model.service.ModelService;
import ouc.isclab.pyxis.service.PyxisService;
import ouc.isclab.storage.service.MinioService;
import ouc.isclab.storage.service.MinioConfigService;
import ouc.isclab.storage.pojo.MinioConfigDTO;
import ouc.isclab.storage.entity.MinioConfigEntity;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys")
public class ModelController {

    @Autowired
    private ModelService modelService;

    @Autowired
    private MinioService minioService;

    @Autowired
    private MinioConfigService minioConfigService;
    
    @Autowired
    private PyxisService pyxisService;


    /**
     * 获取用户的模型列表
     */
    @GetMapping("/models")
    public Map<String, Object> getUserModels(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @CurrentUserId Long userId) {
        log.info("获取用户模型列表: 用户ID={}, 页码={}, 大小={}, 关键词={}", userId, page, size, keyword);
        
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<ModelEntity> modelsPage = modelService.getUserModels(userId, keyword, pageable);
        
        Map<String, Object> result = new HashMap<>();
        result.put("models", modelsPage.getContent());
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", modelsPage.getTotalElements());
        result.put("pagination", pagination);
        
        return result;
    }
    
    /**
     * 获取模型详情
     */
    @GetMapping("/model/{modelId}")
    public ModelEntity getModelDetail(
            @PathVariable Long modelId,
            @CurrentUserId Long userId) {
        log.info("获取模型详情: 模型ID={}, 用户ID={}", modelId, userId);
        return modelService.getModelDetail(modelId, userId);
    }

    /**
     * 获取任务关联的模型列表
     */
    @GetMapping("/task/{taskId}/models")
    public List<ModelEntity> getTaskModels(
            @PathVariable Long taskId,
            @CurrentUserId Long userId) {
        log.info("获取任务模型列表: 任务ID={}, 用户ID={}", taskId, userId);
        return modelService.getTaskModels(taskId, userId);
    }

    /**
     * 删除模型
     */
    @DeleteMapping("/model/{modelId}")
    public void deleteModel(
            @PathVariable Long modelId,
            @CurrentUserId Long userId) {
        log.info("删除模型: modelId={}, userId={}", modelId, userId);
        modelService.deleteModel(modelId, userId);
    }

    /**
     * 批量删除模型
     */
    @DeleteMapping("/models")
    public void batchDeleteModels(
            @RequestBody List<Long> ids,
            @CurrentUserId Long userId) {
        log.info("批量删除模型: ids={}, userId={}", ids, userId);
        modelService.batchDeleteModels(ids, userId);
    }
    
    /**
     * 获取模型统计数据
     */
    @GetMapping("/model/statistics")
    public Map<String, Object> getModelStatistics() {
        log.info("获取模型统计数据");
        return modelService.getModelStatistics();
    }
    
    /**
     * 获取模型和部署任务统计数据（合并接口）
     */
    @GetMapping("/model/statistics/all")
    public Map<String, Object> getModelAndDeployStatistics() {
        log.info("获取模型和部署任务统计数据");
        
        Map<String, Object> result = new HashMap<>();
        
        // 获取模型统计数据
        Map<String, Object> modelStats = modelService.getModelStatistics();
        
//        // 获取部署任务统计数据
//        Map<String, Object> deployStats = modelDeployTaskService.getDeployTaskStatistics();
        
        // 合并统计数据
        result.put("model", modelStats);
//        result.put("deploy", deployStats);
        
        return result;
    }

    /**
     * 获取模型文件夹内容
     */
    @GetMapping("/model/{modelId}/files")
    public Map<String, Object> getModelFiles(
            @PathVariable Long modelId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String subDirectory,
            @CurrentUserId Long userId) {
        log.info("获取模型文件夹内容: 模型ID={}, 用户ID={}, 页码={}, 大小={}, 子目录={}", 
                modelId, userId, page, size, subDirectory);
        
        // 获取模型详情以验证权限并获取保存路径
        ModelEntity model = modelService.getModelDetail(modelId, userId);
        
        // 获取模型存储配置
        MinioConfigDTO config = minioConfigService.getActiveConfigByType(MinioConfigEntity.ConfigType.MODEL);
        minioService.updateMinioClient(config);
        
        // 构建完整路径
        String fullPath = model.getModelPath();
        if (subDirectory != null && !subDirectory.isEmpty()) {
            // 验证子目录是否属于该模型
            if (!subDirectory.startsWith(fullPath)) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "无权访问该目录");
            }
            fullPath = subDirectory;
        }
        
        // 使用完整路径获取文件列表
        return minioService.listFiles(config.getBucket(), page, size, fullPath, null);
    }

//    /**
//     * 部署模型
//     */
//    @PostMapping("/model/deploy")
//    public Map<String, Object> deployModel(
//            @RequestParam("files") MultipartFile[] files,
//            @RequestParam("nodeId") Long nodeId,
//            @RequestParam(value = "folder_name", required = false) String folderName,
//            @CurrentUserId Long userId) {
//        log.info("部署模型: 节点ID={}, 用户ID={}, 文件夹名称={}", nodeId, userId, folderName);
//        return pyxisService.createPyxisTask(nodeId, userId, files, folderName);
//    }
    
} 