package ouc.isclab.system.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.system.service.UserService;
import ouc.isclab.common.response.ResponseResult;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.common.annotation.RequirePermission;
import ouc.isclab.system.pojo.UserInfo;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys")
public class UserController {
    @Autowired
    private UserService userService;

    /**
     * 创建用户
     */
    @PostMapping("/user")
    public UserEntity createUsers(
            @RequestParam(value = "username") String username,
            @RequestParam(value = "fullname") String fullname,
            @RequestParam(value = "email") String email,
            @RequestParam(value = "password") String password,
            @RequestParam(value = "role") Long role) {
        log.info("[USER_NAME] " + username + "successfully created!");

        return userService.createUser(username, fullname, email, password, role);
    }

    /**
     * 列出所有用户
     */
    @GetMapping("/users")
    public Map<String, Object> listUsers(
            @RequestParam(defaultValue = "1") int page,  // 默认页码为 1
            @RequestParam(defaultValue = "10") int size // 默认每页大小为 10
    ) {
        Pageable pageable = PageRequest.of(page - 1, size);  // 页码从 0 开始，所以减 1
        Page<UserEntity> userPage = userService.listUsers(pageable);
        Map<String, Object> data = new HashMap<>();
        data.put("users", userPage.getContent());  // 当前页的数据
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);  // 当前页码
        pagination.put("size", size);  // 每页大小
        pagination.put("total", userPage.getTotalElements());  // 总记录数
        data.put("pagination", pagination);  // 将pagination放入data中
        return data;
    }

    /**
     * 根据用户名查用户
     */
    @GetMapping(value = "/user/search/{username}")
    public UserEntity findUserByUsername(@PathVariable String username) {
        return userService.findUserByUsername(username);
    }

    /**
     * 根据ID查用户
     */
    @GetMapping(value = "/user/{id}")
    public UserEntity findUserById(@PathVariable Long id) {
        return userService.findUserById(id);
    }

    /**
     * 删除用户和对应角色表信息
     */
    @DeleteMapping("/user/{id}")
    public void deleteUser(@PathVariable Long id) {
        userService.deleteUserById(id);
    }

    /**
     * 修改用户基本信息
     */
    @PutMapping("/user/{id}")
    public UserEntity updateUserById(@PathVariable Long id,
                                     @RequestParam(value = "username") String username,
                                     @RequestParam(value = "fullname") String fullname,
                                     @RequestParam(value = "email") String email,
                                     @RequestParam(value = "enable") boolean enable) {
        return userService.updateUserById(id, username, fullname, email, enable);
    }

    /**
     * 修改用户密码
     */
    @PostMapping("/user/updatePwd")
    public UserEntity updatePwdById(@RequestParam(value = "id") Long id,
                                    @RequestParam(value = "password") String password,
                                    @RequestParam(value = "newPassword") String newPassword) throws Exception {
        return userService.updatePwdById(id, password, newPassword);
    }

    /**
     * 修改用户角色
     */
    @PutMapping("/user/{id}/roles")
    public UserEntity updateUserRoles(@PathVariable Long id,
                                    @RequestBody Map<String, List<Long>> request) {
        log.info("[USER_ID] {} updating roles: {}", id, request.get("roleIds"));
        return userService.updateUserRoles(id, request.get("roleIds"));
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/user")
    public Object getUserInfo(@CurrentUserId Long userId) {
        UserEntity userEntity = userService.findUserById(userId);
        return UserInfo.fromEntity(userEntity);
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/user/statistics")
    public ResponseResult getUserStatistics() {
        try {
            Map<String, Object> statistics = userService.getUserStatistics();
            return new ResponseResult(
                ResponseCode.SUCCESS.getCode(),
                ResponseCode.SUCCESS.getMessage(),
                statistics
            );
        } catch (Exception e) {
            log.error("获取用户统计信息失败", e);
            return new ResponseResult(
                ResponseCode.SERVICE_ERROR.getCode(),
                "获取统计信息失败",
                null
            );
        }
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping(value = "/user/batch")
    public void deleteUsersByIds(@RequestParam List<Long> ids) {
        log.info("Batch delete users: {}", ids);
        userService.deleteUsersByIds(ids);
    }

    /**
     * 更新用户启用状态
     */
    @PutMapping(value = "/user/{id}/enable")
    public UserEntity updateUserEnable(@PathVariable Long id, @RequestParam boolean enable) {
        log.info("Update user enable status: id={}, enable={}", id, enable);
        return userService.updateUserEnable(id, enable);
    }
}
