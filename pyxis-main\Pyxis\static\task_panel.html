<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pyxis</title>
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="logo">Pyxis</div>
            <div class="nav-items">
                <span id="currentUser" class="userspan"></span>
                <a href="#" class="button" id="refresh-btn">Refresh</a>
                <a href="#" class="button" id="settings-btn" style="display: none;">Settings</a>
                <a href="#" class="button" id="logout-btn">LOGOUT</a>
            </div>
        </div>

        <div class="sidebar">
            <div class="task-controls">
                <h3>Create Task</h3>
                <form id="create-task-form">
                    <div class="form-group">
                        <button type="button" id="files-upload-btn" class="btn btn-upload">Upload Folder</button>
                        <input type="file" title="file" id="task-files" webkitdirectory directory multiple
                            class="file-input" style="display: none;">
                        <div class="file-list-preview">
                            <p>No files selected</p>
                        </div>
                    </div>
                    <button type="submit" id="create-task-btn" class="btn btn-primary">Create Task</button>
                </form>
            </div>

            <div class="task-list-container">
                <h3>Active Tasks</h3>
                <div id="task-list" class="task-list">
                    <div class="loading">Loading tasks...</div>
                </div>
            </div>
        </div>

        <div class="content">
            <div id="task-info" class="task-info">
                <p>Select a task to view details</p>
            </div>

            <div class="file-tabs">
                <div class="tab-buttons">
                    <button class="tab-btn active" data-tab="user_code">User Code</button>
                    <button class="tab-btn" data-tab="logs">Logs</button>
                    <button class="tab-btn" data-tab="results">Results</button>
                    <button class="tab-btn" data-tab="workspace">Workspace</button>
                </div>

                <div class="file-browser">
                    <div class="file-path">
                        <span id="current-path"></span>
                    </div>
                    <div id="file-list" class="file-list">
                        <p>Select a tab to browse files</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/task_panel.main.js"></script>
</body>

</html>