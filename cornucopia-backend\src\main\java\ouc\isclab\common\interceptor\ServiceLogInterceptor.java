package ouc.isclab.common.interceptor;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import ouc.isclab.common.pojo.ServiceLog;
import ouc.isclab.common.service.ServiceLogService;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

@Slf4j
public class ServiceLogInterceptor implements HandlerInterceptor {

    @Autowired
    private ServiceLogService serviceLogService;

    /**
     * preHandle 在请求处理之前被调用。如果返回true，则继续处理请求;如果返回false，则中止请求
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        long startTime = System.currentTimeMillis();
        request.setAttribute("startTime", startTime);
        return true;
    }

    /**
     * postHandle 在请求处理之后，但在视图渲染之前被调用，可以对模型和视图进行进一步处理
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        log.info("postHandle: " + response.getStatus());
    }

    /**
     * afterCompletion 在请求处理完成且视图渲染之后被调用，可以进行资源清理等操作
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws UnsupportedEncodingException {
        long startTime = (Long) request.getAttribute("startTime");
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        String ip = request.getRemoteAddr();
        int port = request.getRemotePort();
        String httpMethod = request.getMethod();
        String requestUri = request.getRequestURI();

        String requestUriDecoded = URLDecoder.decode(requestUri, "ISO-8859-1"); // 解决中文URL编码问题，老版本的Java没有遇到此问题
        String requestUriEncodedUtf8 = new String(requestUriDecoded.getBytes("ISO-8859-1"), StandardCharsets.UTF_8);

        log.info("{} {} {} took {} ms", "[" + ip + ":" + port + "]", httpMethod, requestUriEncodedUtf8, duration);

        ServiceLog serviceLog = new ServiceLog();
        serviceLog.setClientIp(ip);
        serviceLog.setClientPort(port);
        serviceLog.setHttpMethod(httpMethod);
        serviceLog.setRequestUri(requestUriEncodedUtf8);
        serviceLog.setDuration(duration); // 毫秒

        serviceLogService.create(serviceLog);
    }
}
