package ouc.isclab.node.repository;

import java.util.List;
import java.util.Optional;
import ouc.isclab.node.entity.NodeResourceEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface NodeResourceRepository extends JpaRepository<NodeResourceEntity, Long> {

    NodeResourceEntity findByNodeId(Long nodeId);
    void deleteByNodeId(Long nodeId);
    void deleteByNodeIdIn(List<Long> nodeIds);

    // 统计各状态节点数量
    @Query("SELECT r.status, COUNT(r) FROM NodeResourceEntity r GROUP BY r.status")
    List<Object[]> countByStatus();
}

