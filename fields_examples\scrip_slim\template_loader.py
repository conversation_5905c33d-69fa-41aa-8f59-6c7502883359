from pathlib import Path
import sys


def load_template(path, **kwargs):
    """Loads template content from a file with the path.

    Args:
        path: Path to template file
        **kwargs: Additional keyword arguments (currently unused)

    Returns:
        str: Content of the template file

    Raises:
        FileNotFoundError: If the template file doesn't exist at resolved path

    Example:
        >>> load_template("templates/my_template.html")
        '<html>...template content...</html>'
    """
    file_path = Path(path).resolve()

    if not file_path.exists():
        raise FileNotFoundError(f"Template file does not exist: {file_path}")

    with file_path.open("r", encoding='utf-8') as f:
        return f.read()


def TemplateLoader(path, load_template_function=load_template):
    """Creates a template loader function for a specific template path.

    Args:
        path: Path to template file (passed to load_template_function)
        load_template_function: Function that actually loads the template
            (default: load_template)

    Returns:
        Callable: A function that when called will load the template
        with the configured path and any provided kwargs

    Note:
        The returned function has the same signature as load_template_function
        but with the path argument pre-bound.

    Example:
        >>> loader = TemplateLoader("templates/my_template.html")
        >>> template_content = loader()
    """
    def load_function(**kwargs):
        return load_template_function(path, **kwargs)
    return load_function
