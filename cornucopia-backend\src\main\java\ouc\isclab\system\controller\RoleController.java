package ouc.isclab.system.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.response.ResponseResult;
import ouc.isclab.system.entity.PermissionEntity;
import ouc.isclab.system.entity.RoleEntity;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.system.service.PermissionService;
import ouc.isclab.system.service.RoleService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys")
public class RoleController {
    @Autowired
    private RoleService roleService;
    @Autowired
    private PermissionService permissionService;

    /**
     * 创建角色
     */
    @PostMapping("/role")
    public RoleEntity createRoles(
            @RequestParam(value = "name") String name,
            @RequestParam(value = "description") String description) {
        log.info("[ROLE_NAME] " + name + "successfully created!");

        return roleService.createRole(name, description);
    }

    /**
     * 列出所有角色
     */
    @GetMapping("/roles")
    public Map<String, Object> listRoles(
            @RequestParam(defaultValue = "1") int page,  // 默认页码为 1
            @RequestParam(defaultValue = "10") int size // 默认每页大小为 10
    ) {
        Pageable pageable = PageRequest.of(page - 1, size);  // 页码从 0 开始，所以减 1
        Page<RoleEntity> rolePage = roleService.listRoles(pageable);
        Map<String, Object> data = new HashMap<>();
        data.put("roles", rolePage.getContent());  // 当前页的数据
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);  // 当前页码
        pagination.put("size", size);  // 每页大小
        pagination.put("total", rolePage.getTotalElements());  // 总记录数
        data.put("pagination", pagination);  // 将pagination放入data中
        return data;
    }

    /**
     * 删除角色（无绑定的用户）
     */
    @DeleteMapping("/role/{id}")
    public void deleteRoleById(@PathVariable Long id) {
        roleService.deleteRoleById(id);
    }

    /**
     * 修改角色信息
     */
    @PutMapping(value = "/role/{id}")
    public RoleEntity updateRoleById(@PathVariable Long id,
                                     @RequestParam(value = "name") String name,
                                     @RequestParam(value = "description") String description) {
        return roleService.updateRoleById(id, name, description);
    }

    /**
     * 批量删除角色
     */
    @DeleteMapping("/role/batch")
    public void deleteRolesByIds(@RequestParam List<Long> ids) {
        log.info("Batch delete roles: {}", ids);
        roleService.deleteRolesByIds(ids);
    }

    /**
     * 根据角色名称查询角色
     */
    @GetMapping("/role/search/{name}")
    public RoleEntity findRoleByName(@PathVariable String name) {
        return roleService.findRoleByName(name);
    }

    /**
     * 根据ID查角色
     */
    @GetMapping("/role/{id}")
    public RoleEntity findRoleById(@PathVariable Long id) {
        return roleService.findRoleById(id);
    }

    /**
     * 为角色分配权限
     */
    @PutMapping("/role/{id}/permissions")
    public RoleEntity assignPermissionsToRole(@PathVariable Long id,
                                            @RequestBody Map<String, List<Long>> request) {
        log.info("[ROLE_ID] {} updating permissions: {}", id, request.get("permissionIds"));
        return roleService.assignPermissionsToRole(id, request.get("permissionIds"));
    }

//    /**
//     * 移除角色的权限
//     */
//    @DeleteMapping("/role/{roleId}/permissions")
//    public ResponseResult removePermissionsFromRole(
//            @PathVariable Long roleId,
//            @RequestParam List<Long> permissionIds) {
//        log.info("[ROLE_ID] {} removing permissions: {}", roleId, permissionIds);
//        return permissionService.removePermissionsFromRole(roleId, permissionIds);
//    }

    /**
     * 获取角色的所有权限
     */
    @GetMapping("/role/{roleId}/permissions")
    public List<PermissionEntity> getRolePermissions(@PathVariable Long roleId) {
        return permissionService.getRolePermissions(roleId);
    }

    /**
     * 清空角色的所有权限
     */
    @DeleteMapping("/role/{id}/permissions")
    public RoleEntity clearRolePermissions(@PathVariable Long id) {
        log.info("[ROLE_ID] {} clearing all permissions", id);
        return roleService.clearRolePermissions(id);
    }
}
