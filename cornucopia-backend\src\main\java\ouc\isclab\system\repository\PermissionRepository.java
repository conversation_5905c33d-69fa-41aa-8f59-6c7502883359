package ouc.isclab.system.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import ouc.isclab.system.entity.PermissionEntity;

public interface PermissionRepository extends JpaRepository<PermissionEntity, Long> {
    PermissionEntity findByCode(String code);
    Page<PermissionEntity> findAll(Pageable pageable);
} 