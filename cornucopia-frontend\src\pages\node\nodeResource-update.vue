<template>
  <div class="resource-update-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Refresh /></el-icon>
          <h2>更新资源状态</h2>
        </div>
        <div class="sub-title">更新节点资源的使用状态</div>
      </div>
      <div class="header-right">
        <el-button 
          @click="$router.push('/node/resource/list')"
          plain
          round
        >
          <el-icon><Back /></el-icon>
          返回资源列表
        </el-button>
      </div>
    </div>

    <div class="main-content">
      <el-empty 
        v-if="!hasNodeId" 
        description="未找到节点ID" 
        :image-size="200"
      >
        <el-button type="primary" @click="$router.push('/node/resource/list')" round>
          返回资源列表
        </el-button>
      </el-empty>

      <div v-else class="update-content" v-loading="pageLoading" element-loading-text="正在更新节点资源...">
        <div class="loading-animation" v-if="pageLoading">
          <div class="circle"></div>
          <div class="circle"></div>
          <div class="circle"></div>
        </div>

        <template v-if="updateSuccess && resourceData">
          <el-result
            icon="success"
            title="更新成功"
            sub-title="节点资源状态已更新"
          />
          
          <!-- 资源使用率信息已隐藏 -->
          <div class="resource-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="节点名称">
                {{ resourceData.node.name }}
              </el-descriptions-item>
              <el-descriptions-item label="IP地址">
                {{ resourceData.node.ipAddress }}:{{ resourceData.node.port }}
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="resourceData.status === 'ERROR' ? 'danger' : 'success'">
                  {{ resourceData.status }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="action-buttons">
            <el-button type="primary" @click="$router.push('/node/resource/list')" round>
              返回资源列表
            </el-button>
          </div>
        </template>

        <el-result
          v-if="updateError"
          icon="error"
          title="更新失败"
          :sub-title="errorMessage"
        >
          <template #extra>
            <el-button type="primary" @click="handleRetry" round>重试</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Refresh, Back } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const route = useRoute()
const router = useRouter()
const pageLoading = ref(false)
const hasNodeId = ref(false)
const updateSuccess = ref(false)
const updateError = ref(false)
const errorMessage = ref('')
const resourceData = ref(null)

const handleRetry = async () => {
  updateError.value = false
  await updateNodeResource()
}

const updateNodeResource = async () => {
  try {
    pageLoading.value = true
    updateSuccess.value = false
    updateError.value = false
    
    const nodeId = route.query.nodeId
    const res = await service.put(`/api/v1.0/sys/nodeResource/${nodeId}`, {}, {
      timeout: 60000
    })
    
    if (res.code === 10000) {
      resourceData.value = res.data
      updateSuccess.value = true
      toast('成功', '节点资源状态已更新', 'success')
    } else {
      throw new Error(res.message || '更新失败')
    }
  } catch (error) {
    updateError.value = true
    errorMessage.value = error.message
    toast('错误', error.message, 'error')
  } finally {
    pageLoading.value = false
  }
}

onMounted(() => {
  if (route.query.nodeId) {
    hasNodeId.value = true
    updateNodeResource()
  } else {
    hasNodeId.value = false
    toast('错误', '未找到节点ID', 'error')
  }
})
</script>

<style scoped>
.resource-update-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.main-content {
  margin: 20px auto;
  max-width: 800px;
}

.update-content {
  padding: 40px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-animation {
  display: flex;
  gap: 8px;
  margin: 20px 0;
}

.circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--el-color-primary);
  animation: bounce 0.5s ease-in-out infinite;
}

.circle:nth-child(2) {
  animation-delay: 0.1s;
}

.circle:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 深色模式适配 */
html.dark .update-content {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.resource-info {
  margin: 20px 0;
  width: 100%;
  max-width: 800px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
  padding: 20px;
}

:deep(.el-descriptions) {
  padding: 8px 0;
}

:deep(.el-descriptions__cell) {
  padding: 16px;
}

:deep(.el-progress) {
  margin: 0;
  width: 90%;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}
</style>