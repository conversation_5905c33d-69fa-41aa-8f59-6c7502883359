package ouc.isclab.node.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;

/**
 * @desc 节点状态实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_NODE_RESOURCE")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class NodeResourceEntity extends BaseEntity {

    @ManyToOne(cascade = CascadeType.ALL)  // 级联保存
    @JoinColumn(name = "node_id", nullable = false)
    private NodeEntity node;  // 关联的节点，不能为空

    private float cpuUsage; // CPU使用率
    private float memoryUsage; // 内存使用率
    private float bandwidthUsage; // 带宽使用率

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NodeStatus status; // 节点状态

    public enum NodeStatus {
        ONLINE,
        OFFLINE,
        ERROR
    }

}
