package ouc.isclab.auth.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.common.response.ResponseResult;
import ouc.isclab.auth.service.TokenService;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/api/v1.0/sys/token")
public class TokenController {
    @Autowired
    private TokenService tokenService;

    @GetMapping("/refresh")
    public Object refreshToken(@RequestHeader("token") String token) {
        String newToken = tokenService.refreshToken(token);
        if (newToken != null) {
            Map<String, Object> data = new HashMap<>();
            data.put("token", newToken);
            return new ResponseResult(ResponseCode.SUCCESS.getCode(),
                    "Token刷新成功",
                    data);
        }
        throw new BaseException(ResponseCode.SERVICE_ERROR, "Token刷新失败");
    }

    @GetMapping("/validate")
    public Object validateToken(@RequestHeader("token") String token) {
        Optional<Long> userId = tokenService.validateToken(token);
        if (userId.isPresent()) {
            return new ResponseResult(ResponseCode.SUCCESS.getCode(),
                    "Token有效",
                    null);
        }
        throw new BaseException(ResponseCode.SERVICE_ERROR, "Token无效或已过期");
    }
} 