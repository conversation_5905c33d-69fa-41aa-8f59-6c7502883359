package ouc.isclab.storage.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;

/**
 * MinIO 配置实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_MINIO_CONFIG")
public class MinioConfigEntity extends BaseEntity {

    @Column(nullable = false)
    private String name;

    @Column(nullable = false)
    private String endpoint;

    @Column(nullable = false)
    private String accessKey;

    @Column(nullable = false)
    private String secretKey;

    @Column(nullable = false)
    private String bucket;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ConfigType type;

    // 是否为当前活动配置
    @Column(nullable = false)
    private Boolean active = true;

    public enum ConfigType {
        MODEL,
        DATASET
    }
} 