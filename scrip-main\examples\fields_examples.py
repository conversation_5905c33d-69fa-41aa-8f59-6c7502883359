from .scrip_slim import create_app, run_app
from .scrip_slim import InputTemplateConfig, OutputTemplateConfig
import base64


def get_model():
    class Model:
        def __init__(self):
            pass

        def image_to_base64(self, file_path):
            with open(file_path, "rb") as image_file:
                encoded_string = base64.b64encode(
                    image_file.read()).decode('utf-8')
            return encoded_string

        def pipe(self, **kwargs):
            res = {
                "input": kwargs,
            }

            if kwargs.get('avatar'):
                res['avatar'] = f"data:image/jpeg;base64,{self.image_to_base64(kwargs.get('avatar'))}"

            if kwargs.get('interests'):
                interests = kwargs.get('interests')
                if isinstance(interests, str):
                    interests = [interests]
                res['interests'] = interests

            if kwargs.get('website'):
                res['website'] = '<a href=' + \
                    kwargs.get('website')+'>PersonalWebsite</a>'

            res['tableA'] = [[1, 3, 5], [2, 4, 6]]
            res['tableB'] = [{'d': 7, "e": 9, "f": 11},
                             {'d': 10, "e": 12, "f": 14}]
            return res

    model = Model()

    return model


def main(port=8000):
    app = create_app(
        get_model=get_model,
        timeout_minutes=120,
        input_template_config=(
            InputTemplateConfig()
            .set_title("Input Fields examples")
            # Basic input types
            .add_text_field(
                name="username",
                label="Username",
                placeholder="Please enter 3-20 characters",
                value="Default user",
                description="The unique identifier for the user account (3-20 alphanumeric characters)"
            )
            .add_number_field(
                name="age",
                label="Age",
                placeholder="18-99",
                value=25,
                description="The user's age in years (must be between 18 and 99)"
            )
            .add_email_field(
                name="email",
                label="Email",
                placeholder="<EMAIL>",
                required=True,
                description="The user's primary email address for account verification and notifications"
            )
            .add_password_field(
                name="password",
                label="Password",
                placeholder="At least 8 characters",
                description="Secure password containing at least 8 characters with mixed case, numbers and symbols"
            )
            # File upload types
            .add_image_field(
                name="avatar",
                label="Profile Avatar",
                placeholder="Upload your profile photo",
                description="Profile picture in JPEG or PNG format (recommended size 200x200px)"
            )
            .add_file_field(
                name="singlefile",
                description="Single file upload with maximum size of 5MB"
            )
            .add_multi_file_field(
                name="documents",
                label="Document upload",
                help_text="Multiple files allowed, max 10MB",
                description="Supporting documents in PDF, DOCX or JPG format (max 10 files)"
            )
            # img display
            .add_image_display(
                label="img_display",
                src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgNTAiIHdpZHRoPSIxMDAiIGhlaWdodD0iNTAiPgogIDxwYXRoIGQ9Ik0xMCw0MCBRNTAsMTAgOTAsNDAiIHN0cm9rZT0iIzM0OThkYiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIiAvPgo8L3N2Zz4=",
                # src = "/static/your_image.png" # if use static_folder
            )
            # Selection types
            .add_select_field(
                name="education",
                label="Education",
                options=[
                    {"value": "high_school", "label": "High School"},
                    {"value": "college", "label": "College", "selected": True},
                    {"value": "bachelor", "label": "Bachelor"},
                    {"value": "master", "label": "Master"},
                    {"value": "phd", "label": "PhD"}
                ],
                description="Highest completed education level"
            )
            # Radio button group
            .add_radio_field(
                name="gender",
                label="Gender",
                options=[
                    {"value": "male", "label": "Male"},
                    {"value": "female", "label": "Female", "checked": True},
                    {"value": "other", "label": "Other"}
                ],
                description="User's self-identified gender"
            )
            # Multiple checkbox
            .add_checkbox_group(
                name="interests",
                label="Interests",
                options=[
                    {"value": "sports", "label": "Sports", "checked": True},
                    {"value": "music", "label": "Music"},
                    {"value": "reading", "label": "Reading"},
                    {"value": "travel", "label": "Travel"}
                ],
                description="User's areas of interest for personalized content"
            )
            # Single checkbox
            .add_checkbox_field(
                name="agree_terms",
                label="I have read and agree to the terms",
                value="agreed",
                checked=False,
                required=True,
                description="Confirmation of acceptance of terms and conditions"
            )
            # Date/time types
            .add_date_field(
                name="birthday",
                label="Birthday",
                value="1990-01-01",
                description="User's date of birth for age verification"
            )
            .add_datetime_field(
                name="meeting_time",
                label="Meeting time",
                description="Scheduled meeting time in user's local timezone"
            )
            # Other HTML5 input types
            .add_color_field(
                name="theme_color",
                label="Theme color",
                value="#4285f4",
                description="Preferred UI color theme in hexadecimal format"
            )
            .add_range_field(
                name="volume",
                label="Volume control",
                min=0,
                max=100,
                step=5,
                value=50,
                description="Audio volume preference (0-100 in 5% increments)"
            )
            .add_url_field(
                name="website",
                label="Personal website",
                placeholder="https://example.com",
                description="Optional personal or professional website URL"
            )
            .add_phone_field(
                name="phone",
                label="Phone number",
                pattern="[0-9]{11}",
                description="Mobile phone number for SMS verification (11 digits)"
            )
            .add_html_content(
                content="<a href='https://www.bing.com'>test html display</a>"
            )
            .build()
        ),

        output_template_config=(
            OutputTemplateConfig()
            .set_title("Output Fields examples")
            .add_json_field(
                name="input",
                label="json",
                description="Raw JSON output of all input parameters received by the model"
            )
            .add_image_field(
                name="avatar",
                label="img",
                description="Base64 encoded profile image returned by the model"
            )
            .add_list_field(
                name="interests",
                label="list",
                description="Array of selected interests processed by the model"
            )
            .add_html_field(
                name="website",
                label="html",
                description="Formatted HTML link generated from the website URL"
            )
            .add_table_field(
                name="tableA",
                headers=["a", "b", "c"],
                label="tableA",
                description="Example 2D array output rendered as a table"
            )
            .add_table_field(
                name="tableB",
                headers=["d", "e", "f"],
                label="tableB",
                description="Example dictionary array output rendered as a table"
            )
            .build()
        ),
        error_traceback=True
    )
    run_app(app, port)