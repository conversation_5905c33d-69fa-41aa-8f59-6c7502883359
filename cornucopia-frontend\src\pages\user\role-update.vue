<template>
  <div class="role-update-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Edit /></el-icon>
          <h2>更新角色信息</h2>
        </div>
        <div class="sub-title">修改系统中的角色信息</div>
      </div>
      <div class="header-right">
        <el-button 
          @click="$router.push('/user/role/list')"
          plain
          round
        >
          <el-icon><Back /></el-icon>
          返回角色列表
        </el-button>
      </div>
    </div>

    <div class="main-content">
      <el-card class="form-card" shadow="hover" v-if="hasRoleId">
        <div class="form-header">
          <div class="step-info">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
            <span>请修改角色信息，带 * 号的为必填项</span>
          </div>
        </div>   
        <el-form :model="roleForm" :rules="rules" ref="formRef" label-width="120px" class="role-form">
          <el-form-item label="角色名称" prop="name" class="form-item">
            <el-input 
              v-model="roleForm.name" 
              :placeholder="originalRole.name || '请输入角色名称'"
              size="large"
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="描述" prop="description" class="form-item">
            <el-input 
              v-model="roleForm.description" 
              :placeholder="originalRole.description || '请输入角色描述'"
              type="textarea"
              :rows="4"
              size="large"
            />
          </el-form-item>
          
          <el-form-item class="form-buttons">
            <el-button 
              type="primary" 
              @click="handleSubmit" 
              :loading="loading"
              size="large"
              class="submit-btn"
              round
              color="#626aef"
            >
              <el-icon><Check /></el-icon>更新角色
            </el-button>
            <el-button 
              @click="resetForm"
              size="large"
              round
            >
              <el-icon><RefreshRight /></el-icon>重置表单
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
      
      <el-empty 
        v-else 
        description="未找到角色ID" 
        :image-size="200"
      >
        <el-button type="primary" @click="$router.push('/user/role/list')" round>
          返回角色列表
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Edit,
  Lock,
  Check, 
  RefreshRight, 
  Back, 
  InfoFilled 
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const route = useRoute()
const router = useRouter()
const formRef = ref(null)
const loading = ref(false)
const pageLoading = ref(false)
const hasRoleId = ref(false)

const roleForm = reactive({
  name: '',
  description: ''
})

const originalRole = reactive({
  name: '',
  description: ''
})

const rules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 获取角色详情
const getRoleDetail = async (id) => {
  pageLoading.value = true
  try {
    const res = await service.get(`/api/v1.0/sys/roles/${id}`)
    if (res.code === 10000) {
      const { name, description } = res.data
      Object.assign(originalRole, { name, description })
      Object.assign(roleForm, { name, description })
    } else {
      toast('错误', res.message || '获取角色信息失败', 'error')
    }
  } catch (error) {
    toast('错误', '获取角色信息失败', 'error')
  } finally {
    pageLoading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const params = new URLSearchParams()
        Object.keys(roleForm).forEach(key => {
          params.append(key, roleForm[key])
        })
        
        const res = await service.put(`/api/v1.0/sys/role/${route.query.id}`, params)
        if (res.code === 10000) {
          toast('成功', '角色更新成功')
          router.push('/user/role/list')
        } else {
          toast('错误', res.message || '角色更新失败', 'error')
        }
      } catch (error) {
        toast('错误', '角色更新失败', 'error')
      }
    }
  })
}

const resetForm = () => {
  if (formRef.value) {
    Object.assign(roleForm, originalRole)
    formRef.value.resetFields()
  }
}

onMounted(() => {
  if (route.query.id) {
    hasRoleId.value = true
    getRoleDetail(route.query.id)
  } else {
    hasRoleId.value = false
    toast('错误', '未找到角色ID', 'error')
  }
})
</script>

<style scoped>
.role-update-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.main-content {
  .form-card {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .form-header {
    margin-bottom: 24px;
    
    .step-info {
      display: flex;
      align-items: center;
      color: #909399;
      
      .info-icon {
        margin-right: 8px;
        color: var(--el-color-primary);
      }
    }
  }
}

.role-form {
  .form-item {
    margin-bottom: 24px;
  }
  
  .form-buttons {
    margin-top: 32px;
    text-align: center;
    
    .submit-btn {
      margin-right: 16px;
      min-width: 120px;
    }
  }
}

:deep(.el-empty) {
  padding: 40px 0;
}
</style> 