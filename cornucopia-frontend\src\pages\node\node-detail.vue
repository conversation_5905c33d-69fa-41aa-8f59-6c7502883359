<template>
  <div class="node-detail-container">
    <el-loading :full-screen="false" :body="true" v-if="loading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Monitor /></el-icon>
          <h2>节点详情</h2>
        </div>
        <div class="sub-title">查看联邦学习网络中的节点信息</div>
      </div>
      <div class="header-right">
        <el-button 
          @click="$router.push('/node/list')"
          plain
          round
        >
          <el-icon><Back /></el-icon>
          返回节点列表
        </el-button>
      </div>
    </div>

    <div class="main-content">
      <el-empty 
        v-if="!hasNodeId" 
        description="未找到节点ID" 
        :image-size="200"
      >
        <el-button type="primary" @click="$router.push('/node/list')" round>
          返回节点列表
        </el-button>
      </el-empty>

      <div v-else>
        <div class="node-info" v-if="nodeData">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="节点名称">
              {{ nodeData.name }}
            </el-descriptions-item>
            <el-descriptions-item label="节点类型">
              {{ nodeData.nodeType }}
            </el-descriptions-item>
            <el-descriptions-item label="IP地址">
              {{ nodeData.ipAddress }}
            </el-descriptions-item>
            <el-descriptions-item label="端口">
              {{ nodeData.port }}
            </el-descriptions-item>
            <el-descriptions-item label="创建用户">
              {{ nodeData.creatorId }}
            </el-descriptions-item>
            <el-descriptions-item label="连接地址">
              <el-tag size="small">{{ nodeData.ipAddress }}:{{ nodeData.port }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">
              {{ formatDateTime(nodeData.timeCreated) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间" :span="2">
              {{ formatDateTime(nodeData.timeUpdated) }}
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              {{ nodeData.description || '暂无描述' }}
            </el-descriptions-item>
          </el-descriptions>

          <div class="action-buttons">
            <el-button type="primary" @click="handleEdit" plain round>
              <el-icon><Edit /></el-icon>编辑
            </el-button>
            <el-button type="danger" @click="handleDelete" round>
              <el-icon><Delete /></el-icon>删除
            </el-button>
          </div>
        </div>

        <!-- 资源信息已隐藏 -->
        <div class="resource-info" v-if="resourceData">
          <h3>节点状态</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(resourceData.status)">
                {{ resourceData.status }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="最后更新时间">
              {{ formatDateTime(resourceData.timeUpdated) }}
            </el-descriptions-item>
          </el-descriptions>

          <div class="action-buttons">
            <el-button type="primary" @click="handleRefreshResource" plain round>
              <el-icon><Refresh /></el-icon>刷新
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Monitor, Back, Edit, Delete, Refresh } from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import service from '~/axios'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const hasNodeId = ref(false)
const nodeData = ref(null)
const resourceData = ref(null)

const fetchNodeData = async (id) => {
  try {
    const response = await service.get(`/api/v1.0/sys/node/${id}`)
    if (response.code === 10000) {
      nodeData.value = response.data
    } else {
      toast('错误', '获取节点信息失败', 'error')
    }
  } catch (error) {
    console.error('获取节点信息失败:', error)
    toast('错误', '获取节点信息失败', 'error')
  }
}

const fetchResourceData = async (id) => {
  try {
    const response = await service.get(`/api/v1.0/sys/nodeResource/${id}`)
    if (response.code === 10000) {
      resourceData.value = response.data
    }
  } catch (error) {
    console.error('获取资源信息失败:', error)
  }
}

const handleEdit = () => {
  router.push({
    path: '/node/update',
    query: { id: route.query.id }
  })
}

const handleDelete = async () => {
  try {
    await showModal('确定要删除该节点吗？', 'warning', '提示')
    const response = await service.delete(`/api/v1.0/sys/node/${route.query.id}`)
    if (response.code === 10000) {
      toast('成功', '删除成功')
      router.push('/node/list')
    } else {
      toast('错误', response.message || '删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '删除失败', 'error')
    }
  }
}

const handleRefreshResource = async () => {
  try {
    const response = await service.put(`/api/v1.0/sys/nodeResource/${route.query.id}`)
    if (response.code === 10000) {
      resourceData.value = response.data
      toast('成功', '资源信息已更新')
    } else {
      toast('错误', response.message || '更新失败', 'error')
    }
  } catch (error) {
    console.error('更新资源信息失败:', error)
    toast('错误', '更新资源信息失败', 'error')
  }
}

const formatDateTime = (time) => {
  if (!time) return '-'
  const date = new Date(time)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const getStatusType = (status) => {
  const statusMap = {
    'ERROR': 'danger',
    'ONLINE': 'success',
    'OFFLINE': 'info',
    'WARNING': 'warning'
  }
  return statusMap[status] || 'info'
}

onMounted(async () => {
  if (route.query.id) {
    hasNodeId.value = true
    loading.value = true
    try {
      await fetchNodeData(route.query.id)
      await fetchResourceData(route.query.id)
    } finally {
      loading.value = false
    }
  } else {
    hasNodeId.value = false
    toast('错误', '未找到节点ID', 'error')
  }
})
</script>

<style scoped>
.node-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
}

.node-info, .resource-info {
  margin: 20px auto;
  width: 100%;
  max-width: 800px;
  
  h3 {
    margin: 0 0 16px;
    font-size: 18px;
    font-weight: 600;
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

:deep(.el-descriptions) {
  padding: 8px 0;
}

:deep(.el-descriptions__cell) {
  padding: 16px;
}

:deep(.el-progress) {
  margin: 0;
  width: 90%;
}

/* 深色模式样式 */
html.dark .node-info,
html.dark .resource-info {
  background-color: var(--el-bg-color-overlay);
}
</style>

