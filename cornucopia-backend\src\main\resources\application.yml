spring:
  profiles:
    active: dev
    # dev：开发环境  自动加载 application-dev.yml,用于本地测试
    # prod：生产环境  自动加载 application-prod.yml,用于项目部署
    # gpuserver：部署在GPU服务器

#  thymeleaf:
#    cache: false
#    check-template-location: true
#    servlet:
#      content-type: text/html
#    enabled: true
#    encoding: utf-8
#    mode: HTML
#    prefix: classpath:/templates/
#    suffix: .html

  web:
    resources:
      static-locations: classpath:/static

#  mvc:
#    static-path-pattern: /static/**

  servlet:
    multipart:
      max-file-size: 5000MB
      max-request-size: 5000MB
      enabled: true

  tomcat:
    uri-encoding: UTF-8
    max-http-form-post-size: -1
    connection-timeout: 20000

  jpa:
    hibernate:
      ddl-auto: update
      show-sql: true
    # 自动创建表采用InnoDB引擎
    database-platform: org.hibernate.dialect.MySQL8Dialect
    # 批量插入数据（未验证）
    properties:
      hibernate:
        #generate_statistics: true
        jdbc:
          batch_size: 5
        order_inserts: true
        enable_lazy_load_no_trans: true # 解决延迟加载问题
        transaction:
          jta:
            platform: org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform

    transaction:
      rollback-on-commit-failure: true