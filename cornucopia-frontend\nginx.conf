server {
    listen 80;
    server_name localhost;
    
    # 增加客户端请求体大小限制
    client_max_body_size 5000M;
    
    # 前端静态资源
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API 请求转发到后端
    location /api/ {
        proxy_pass http://backend:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Content-Type $content_type;
        proxy_set_header Accept $http_accept;
        proxy_set_header Origin $http_origin;
        proxy_set_header Access-Control-Request-Method $http_access_control_request_method;
        proxy_set_header Access-Control-Request-Headers $http_access_control_request_headers;
        
        # 对于 API 路径也设置大文件上传限制
        client_max_body_size 5000M;
    }
    
} 