<template>
  <div class="tag-list">
    <el-scrollbar class="scroll-container">
      <div class="tags-wrapper">
        <el-tag
          v-for="(tag, index) in tags"
          :key="tag.path"
          :closable="tag.path !== '/'"
          :type="isActive(tag) ? '' : 'info'"
          :effect="isActive(tag) ? 'dark' : 'plain'"
          class="tag-item"
          @click="handleClick(tag)"
          @close="handleClose(tag)"
          :class="{ active: isActive(tag) }"
          size="large"
        >
          <el-icon class="tag-icon" v-if="tag.meta?.icon">
            <component :is="tag.meta.icon" />
          </el-icon>
          {{ tag.title }}
        </el-tag>
      </div>
    </el-scrollbar>

    <div class="tag-actions">
      <el-dropdown trigger="click" @command="handleCommand">
        <el-button type="primary" size="small" plain>
          <el-icon><Operation /></el-icon>
          标签操作
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="closeOthers">
              <el-icon><CircleClose /></el-icon>关闭其他
            </el-dropdown-item>
            <el-dropdown-item command="closeAll">
              <el-icon><Remove /></el-icon>关闭所有
            </el-dropdown-item>
            <el-dropdown-item command="closeLeft">
              <el-icon><Back /></el-icon>关闭左侧
            </el-dropdown-item>
            <el-dropdown-item command="closeRight">
              <el-icon><Right /></el-icon>关闭右侧
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Operation, CircleClose, Remove, Back, Right } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 标签列表
const tags = ref([
  {
    title: '首页',
    path: '/',
    meta: { icon: 'House' }
  }
])

// 判断标签是否激活
const isActive = (tag) => {
  return tag.path === route.path
}

// 添加标签
const addTag = (route) => {
  const { name, path, meta } = route
  if (path === '/login') return
  
  const tag = {
    title: meta.title || name,
    path,
    meta
  }
  
  const isExist = tags.value.some(item => item.path === path)
  if (!isExist) {
    tags.value.push(tag)
  }
}

// 移除标签
const removeTag = (tag) => {
  const index = tags.value.findIndex(item => item.path === tag.path)
  if (index === -1) return
  
  // 如果关闭的是当前标签,则跳转到前一个标签
  if (isActive(tag) && tags.value.length > 1) {
    const nextTag = tags.value[index + 1] || tags.value[index - 1]
    router.push(nextTag.path)
  }
  
  tags.value.splice(index, 1)
}

// 点击标签
const handleClick = (tag) => {
  router.push(tag.path)
}

// 关闭标签
const handleClose = (tag) => {
  removeTag(tag)
}

// 标签操作
const handleCommand = (command) => {
  switch (command) {
    case 'closeOthers':
      tags.value = tags.value.filter(tag => tag.path === '/' || isActive(tag))
      break
    case 'closeAll':
      tags.value = tags.value.filter(tag => tag.path === '/')
      router.push('/')
      break
    case 'closeLeft':
      const currentIndex = tags.value.findIndex(tag => isActive(tag))
      tags.value = tags.value.filter((tag, index) => tag.path === '/' || index >= currentIndex)
      break
    case 'closeRight':
      const curIndex = tags.value.findIndex(tag => isActive(tag))
      tags.value = tags.value.filter((tag, index) => tag.path === '/' || index <= curIndex)
      break
  }
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    addTag(route)
  },
  { immediate: true }
)
</script>

<style scoped>
.tag-list {
  display: flex;
  align-items: center;
  padding: 6px 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
}

.scroll-container {
  flex: 1;
  white-space: nowrap;
  padding-right: 16px;
}

.tags-wrapper {
  display: inline-flex;
  align-items: center;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  margin-right: 6px;
  cursor: pointer;
  height: 32px;
  padding: 0 8px;
  font-size: 13px;
  transition: all 0.3s;
}

.tag-item:hover {
  transform: translateY(-1px);
}

.tag-item.active {
  font-weight: bold;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.tag-icon {
  margin-right: 4px;
  font-size: 14px;
}

.tag-actions {
  margin-left: auto;
  flex-shrink: 0;
}

/* 深色模式适配 */
html.dark {
  .tag-list {
    background: var(--el-bg-color-overlay);
    border-color: var(--el-border-color-darker);
  }

  .tag-item.active {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  }
}
</style>