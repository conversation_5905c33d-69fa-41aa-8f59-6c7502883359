"""
scrip_slim - A lightweight Flask framework for rapid model deployment.

This module provides a streamlined solution for deploying ML models as temporary web services
with configurable interfaces and built-in resource management. Designed for prototyping,
demonstrations, and temporary deployments with minimal setup.

Core Features:
1. Web Interface System:
   - Dynamic form generation from configuration
   - Customizable Jinja2 templates for input/output
   - Built-in file upload handling with secure storage
   - Automatic type conversion (bool/int/float/file)

2. Model Integration Layer:
   - Standardized .pipe() interface for model inference
   - Extensible preprocessing pipeline
   - Flexible result rendering system
   - Built-in error handling

3. Resource Management:
   - Automatic temporary file cleanup
   - Configurable service timeout
   - Thread-safe operation

Basic Usage:
>>> from scrip_slim import create_app, run_app
>>> app = create_app(
...     get_model=your_model_loader,
...     input_template_config=input_config,
...     output_template_config=output_config
... )
>>> run_app(app, port=8000)

Model Interface Specification:
    Required Model Implementation:
    - Must provide a .pipe(**kwargs) method that:
        * Accepts preprocessed inputs as keyword arguments
        * Returns results in template-compatible format
        * Handles all inference logic
        * Manages any model-specific resources

    Input Handling:
    - Form fields auto-converted to Python types:
        "true"/"false" → bool
        "42" → int
        "3.14" → float
    - Files are saved to temp dir with UUID prefixes
    - Original filenames are sanitized

    Output Requirements:
    - Return dict with keys matching output template
    - Supported output types:
        * Text/numbers
        * Lists/tables
        * Images/SVG
        * HTML/JSON

Template Configuration:
    Input Templates:
    - Configure via InputTemplateConfig builder
    - Supports 20+ field types (text, files, selects, etc.)

    Output Templates:
    - Configure via OutputTemplateConfig builder
    - Multiple display formats available

Security Considerations:
    - All file uploads use secure_filename()
    - Temporary files have randomized names
    - Automatic cleanup on shutdown
    - No persistent storage by default

Advanced Features:
    - Custom preprocessors for specialized input handling
    - Template overrides for custom UI
    - Static file serving
    - Request timing headers
    - Configurable cleanup hooks

Example Implementations:
    See included examples for:
    - Image classification
    - Text generation
    - Time series forecasting
    - Object detection
    - Complex form handling
"""


from .scrip_slim import create_app, run_app
from .template_loader import TemplateLoader
from .template_config_builder import InputTemplateConfig, OutputTemplateConfig
