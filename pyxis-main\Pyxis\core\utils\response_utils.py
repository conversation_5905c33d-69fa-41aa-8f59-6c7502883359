from fastapi.responses import JSONResponse, Response
import json
from typing import Any, Dict


async def read_response_body(response: Response) -> bytes:
    return b''.join([chunk async for chunk in response.body_iterator])


async def parse_json_response(response: Response) -> Dict[str, Any]:
    body = await read_response_body(response)
    try:
        return json.loads(body.decode())
    except (json.JSONDecodeError, UnicodeDecodeError) as e:
        raise ValueError(f"Invalid JSON response: {str(e)}")


async def rebuild_response(
    original_response: Response,
    content: Any,
    status_code: int = None,
    headers: Dict[str, str] = None
) -> Response:
    return JSONResponse(
        content=content,
        status_code=status_code or original_response.status_code,
        headers=headers or dict(original_response.headers)
    )


async def safely_read_json_response(response: Response) -> tuple[Dict[str, Any], Response]:
    data = await parse_json_response(response)
    new_response = await rebuild_response(response, data)
    return data, new_response
