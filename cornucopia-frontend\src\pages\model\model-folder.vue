<template>
  <div class="model-folder-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Folder /></el-icon>
          <h2>模型文件夹</h2>
        </div>
        <div class="sub-title">查看和管理模型文件</div>
      </div>
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/model/my' }">
          <el-icon><List /></el-icon> 模型列表
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          <el-icon><Folder /></el-icon> {{ modelName }}
        </el-breadcrumb-item>
        <template v-for="(part, index) in currentPathParts" :key="index">
          <el-breadcrumb-item 
            :to="{ path: '/model/folder', query: { id: route.query.id, subDirectory: getPathUpTo(index) } }"
          >
            {{ part }}
          </el-breadcrumb-item>
        </template>
      </el-breadcrumb>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
        highlight-current-row
      >
        <el-table-column prop="name" label="文件名" min-width="200" show-overflow-tooltip align="center">
          <template #default="scope">
            <div 
              style="display: flex; align-items: center; justify-content: center; cursor: pointer;"
              @click="scope.row.isDirectory ? navigateToDirectory(scope.row.directory) : null"
            >
              <el-icon class="file-icon">
                <Folder v-if="scope.row.isDirectory" />
                <Document v-else />
              </el-icon>
              <span>{{ getDisplayName(scope.row) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="120" align="center">
          <template #default="scope">
            {{ scope.row.isDirectory ? '—' : formatFileSize(scope.row.size) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastModified" label="修改时间" width="180" align="center" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.lastModified) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button
              v-if="!scope.row.isDirectory"
              type="primary"
              size="small"
              @click="handleDeploy(scope.row)"
            >
              部署
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Folder,
  Document,
  List
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const modelName = ref('')
const modelId = computed(() => route.query.id)

// 当前目录
const currentDirectory = computed(() => route.query.subDirectory || '')
const currentPathParts = computed(() => {
  if (!currentDirectory.value) return []
  return currentDirectory.value.split('/').filter(Boolean)
})

// 获取模型信息
const fetchModelInfo = async () => {
  try {
    const response = await service.get(`/api/v1.0/sys/model/${modelId.value}`)
    if (response.code === 10000) {
      modelName.value = response.data.name
    }
  } catch (error) {
    console.error('获取模型信息失败:', error)
    toast('错误', '获取模型信息失败', 'error')
  }
}

// 获取文件列表
const fetchFiles = async () => {
  loading.value = true
  try {
    const response = await service.get(`/api/v1.0/sys/model/${modelId.value}/files`, {
      params: {
        subDirectory: currentDirectory.value,
        page: currentPage.value,
        size: pageSize.value
      }
    })
    
    if (response.code === 10000) {
      tableData.value = response.data.files
      total.value = response.data.pagination.total
    } else {
      toast('错误', response.message || '获取文件列表失败', 'error')
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    toast('错误', '获取文件列表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '-'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(2)} ${units[index]}`
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString()
}

// 获取显示名称
const getDisplayName = (file) => {
  return file.name || file.directory?.split('/').pop() || '-'
}

// 导航到目录
const navigateToDirectory = (directory) => {
  router.push({
    path: '/model/folder',
    query: {
      id: modelId.value,
      subDirectory: directory
    }
  })
}

// 获取路径到指定索引
const getPathUpTo = (index) => {
  return currentPathParts.value.slice(0, index + 1).join('/')
}

// 处理页码变化
const handleCurrentChange = (val) => {
  fetchFiles()
}

// 处理每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchFiles()
}

// 部署模型
const handleDeploy = (file) => {
  router.push({
    path: '/application/deploy',
    query: {
      modelId: modelId.value,
      filePath: file.path,
      fileName: file.name
    }
  })
}

// 监听路由参数变化
watch(
  () => [route.query.id, route.query.subDirectory],
  () => {
    if (route.query.id) {
      fetchModelInfo()
      fetchFiles()
    }
  },
  { immediate: true }
)

onMounted(() => {
  // 移除这里的 fetchModelInfo() 和 fetchFiles()，因为已经在 watch 中处理了
})
</script>

<style scoped>
.model-folder-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
}

.breadcrumb-container {
  margin-bottom: 20px;
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

:deep(.el-table th) {
  font-weight: bold;
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

:deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
</style> 