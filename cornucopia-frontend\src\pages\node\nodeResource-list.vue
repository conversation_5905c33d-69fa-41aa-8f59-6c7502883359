<template>
  <div class="node-resource-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Cpu /></el-icon>
          <h2>节点资源详情</h2>
        </div>
        <div class="sub-title">查看联邦学习网络中的节点资源信息</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.name"
                placeholder="请输入节点名称"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="action-buttons">
            <el-button 
              type="primary" 
              @click="handleRefreshAll" 
              :loading="refreshLoading"
              :disabled="isRefreshing"
              plain 
              round
            >
              <el-icon><RefreshRight /></el-icon>
              {{ refreshLoading ? '更新中...' : '全部刷新' }}
            </el-button>
            <el-button 
              type="danger" 
              :disabled="!selectedIds.length"
              @click="handleBatchDelete"
              round
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table 
        v-loading="loading"
        :data="tableData" 
        stripe 
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        border
        highlight-current-row
        @selection-change="handleSelectionChange"
        :default-sort="defaultSort"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column 
          label="节点ID" 
          width="100"
          align="center"
          sortable
          :sort-method="sortByNodeId"
        >
          <template #default="scope">
            {{ scope.row.node.id }}
          </template>
        </el-table-column>
        <el-table-column label="节点信息" min-width="350">
          <template #default="scope">
            <div class="node-info-inline">
              <span class="node-name">{{ scope.row.node.name }}</span>
              <el-tag size="small" :type="scope.row.node.nodeType === 'Sycee' ? 'success' : 'primary'" class="node-tag">
                {{ scope.row.node.nodeType }}
              </el-tag>
              <el-tag size="small" class="node-tag">{{ scope.row.node.ipAddress }}:{{ scope.row.node.port }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          prop="timeCreated" 
          label="创建时间" 
          width="200"
          show-overflow-tooltip
          sortable
          :formatter="formatDateTime"
          :sort-method="sortByTime"
        />
        <el-table-column 
          prop="timeUpdated" 
          label="更新时间" 
          width="200"
          show-overflow-tooltip
          sortable
          :formatter="formatDateTime"
          :sort-method="sortByTime"
        />
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <el-button-group>
              <el-tooltip content="查看详情" placement="top">
                <el-button type="info" size="small" @click.stop="handleDetail(scope.row)">
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="更新资源" placement="top">
                <el-button type="primary" size="small" @click.stop="handleEdit(scope.row)">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除资源" placement="top">
                <el-button type="danger" size="small" @click.stop="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          v-model:current-page="currentPage"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Cpu, Refresh, Delete, View, Search, RefreshRight } from '@element-plus/icons-vue'
import service from '~/axios'
import { toast, showModal } from '~/composables/util'
import { useRouter } from 'vue-router'

const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

const router = useRouter()

// 添加搜索表单
const searchForm = ref({
  name: ''
})

// 添加选中ID数组
const selectedIds = ref([])

// 添加刷新加载状态
const refreshLoading = ref(false)
const refreshProgress = ref(0)
const isRefreshing = ref(false)

const fetchNodeResources = async (page = 1) => {
  loading.value = true
  try {
    const response = await service.get('/api/v1.0/sys/nodeResources', {
      params: {
        page: page,
        size: pageSize.value
      }
    })
    if (response.code === 10000) {
      tableData.value = response.data.nodeResources
      total.value = response.data.pagination.total
      pageSize.value = response.data.pagination.size
    }
  } catch (error) {
    console.error('获取节点资源列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchNodeResources(page)
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchNodeResources()
}

const formatDateTime = (row, column) => {
  const value = row[column.property]
  if (!value) return '-'
  const date = new Date(value)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const sortByTime = (a, b) => {
  const timeA = new Date(a.timeCreated).getTime()
  const timeB = new Date(b.timeCreated).getTime()
  return timeA - timeB
}

const sortByNodeId = (a, b) => {
  return a.node.id - b.node.id
}

const getStatusType = (status) => {
  const statusMap = {
    'ERROR': 'danger',
    'ONLINE': 'success',
    'OFFLINE': 'info',
    'WARNING': 'warning'
  }
  return statusMap[status] || 'info'
}

const defaultSort = {
  prop: 'node.id',
  order: 'ascending'
}

const handleEdit = (row) => {
  router.push({
    path: '/node/resource/update',
    query: {
      nodeId: row.node.id
    }
  })
}

const handleDelete = (row) => {
  showModal(
    `确定要删除节点 ${row.node.name} 的资源信息吗？`,
    'warning',
    '警告'
  ).then(async () => {
    try {
      const response = await service.delete(`/api/v1.0/sys/nodeResource/${row.node.id}`)
      if (response.code === 10000) {
        toast('删除成功')
        fetchNodeResources(currentPage.value)
      } else {
        toast('删除失败', response.message, 'error')
      }
    } catch (error) {
      console.error('删除节点资源失败:', error)
      toast('删除失败', error.message, 'error')
    }
  }).catch(() => {
    toast('已取消操作', '', 'info')
  })
}

const handleDetail = (row) => {
  router.push({
    path: '/node/resource/detail',
    query: {
      nodeId: row.node.id
    }
  })
}

// 处理搜索
const handleSearch = async () => {
  loading.value = true
  try {
    if (!searchForm.value.name) {
      fetchNodeResources(currentPage.value)
      return
    }
    
    const response = await service.get(`/api/v1.0/sys/nodeResource/search/${searchForm.value.name}`)
    if (response.code === 10000) {
      if (response.data === null) {
        tableData.value = []
        total.value = 0
        currentPage.value = 1
        toast('提示', '未找到匹配的节点资源', 'info')
      } else {
        tableData.value = [response.data]
        total.value = 1
        currentPage.value = 1
      }
    }
  } catch (error) {
    console.error('搜索节点资源失败:', error)
    toast('错误', '搜索失败', 'error')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    name: ''
  }
  fetchNodeResources(1)
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.node.id)
}

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedIds.value.length) {
    toast('警告', '请选择要删除的节点资源', 'warning')
    return
  }
  
  try {
    await showModal(`确定要删除选中的 ${selectedIds.value.length} 个节点资源吗？`, 'warning', '提示')
    const queryParams = selectedIds.value.map(id => `ids=${id}`).join('&')
    const response = await service.delete(`/api/v1.0/sys/nodeResource/batch?${queryParams}`)
    
    if (response.code === 10000) {
      toast('成功', '批量删除成功')
      selectedIds.value = []
      // 刷新当前页数据
      await fetchNodeResources(currentPage.value)
      // 如果当前页没有数据了，且不是第一页，则跳转到上一页
      if (tableData.value.length === 0 && currentPage.value > 1) {
        currentPage.value--
        await fetchNodeResources(currentPage.value)
      }
    } else {
      toast('错误', response.message || '批量删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '批量删除失败', 'error')
    }
  }
}

// 修改全部刷新方法
const handleRefreshAll = async () => {
  try {
    refreshLoading.value = true
    isRefreshing.value = true
    refreshProgress.value = 0
    
    // 发起刷新请求
    const response = await service.put('/api/v1.0/sys/nodeResources', null, {
      timeout: 60000  // 设置60秒超时
    })
    
    if (response.code === 10000) {
      toast('成功', '所有节点资源信息已更新')
      // 刷新当前页数据
      await fetchNodeResources(currentPage.value)
    } else {
      toast('错误', response.message || '更新失败', 'error')
    }
  } catch (error) {
    console.error('更新所有节点资源失败:', error)
    if (error.code === 'ECONNABORTED') {
      toast('错误', '请求超时，请稍后重试', 'error')
    } else {
      toast('错误', '更新失败', 'error')
    }
  } finally {
    refreshLoading.value = false
    isRefreshing.value = false
    refreshProgress.value = 0
  }
}

onMounted(() => {
  fetchNodeResources()
})
</script>

<style scoped>
.node-resource-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  flex-shrink: 0;
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
}

:deep(.el-button--primary) {
  --el-button-hover-bg-color: var(--el-color-primary-light-3);
  --el-button-hover-border-color: var(--el-color-primary-light-3);
  --el-button-active-bg-color: var(--el-color-primary-dark-2);
  --el-button-active-border-color: var(--el-color-primary-dark-2);
}

:deep(.el-button--default) {
  --el-button-hover-bg-color: var(--el-color-primary-light-9);
  --el-button-hover-border-color: var(--el-color-primary-light-7);
  --el-button-hover-text-color: var(--el-color-primary);
}

:deep(.el-button--danger) {
  --el-button-hover-bg-color: var(--el-color-danger-light-3);
  --el-button-hover-border-color: var(--el-color-danger-light-3);
  --el-button-active-bg-color: var(--el-color-danger-dark-2);
  --el-button-active-border-color: var(--el-color-danger-dark-2);
}

/* 添加新的样式 */
.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

/* 添加加载状态的按钮样式 */
.el-button.is-loading,
.el-button.is-disabled {
  opacity: 0.8;
  cursor: not-allowed;
}

/* 节点信息同行布局样式 */
.node-info-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.node-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-right: 4px;
}

.node-tag {
  flex-shrink: 0;
}
</style>