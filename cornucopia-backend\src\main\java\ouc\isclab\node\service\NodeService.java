package ouc.isclab.node.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.pojo.NodeInfo;
import ouc.isclab.node.repository.NodeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ouc.isclab.node.repository.NodeResourceRepository;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import ouc.isclab.node.entity.NodeResourceEntity;
import ouc.isclab.node.entity.NodeAuthEntity;
import ouc.isclab.node.repository.NodeAuthRepository;
import ouc.isclab.system.service.UserService;
import ouc.isclab.dataset.entity.DatasetEntity;
import ouc.isclab.dataset.repository.DatasetRepository;

@Service
public class NodeService {

    @Autowired
    private NodeRepository nodeRepository;
    
    @Autowired
    private NodeAuthRepository nodeAuthRepository;
    
    @Autowired
    private NodeResourceRepository nodeResourceRepository;
    
    @Autowired
    private DatasetRepository datasetRepository;
    /**
     * 创建节点
     */
    @Transactional
    public NodeEntity createNode(NodeInfo nodeInfo,Long userId) {
        // 获取当前用户作为创建者

        // 创建并保存节点基本信息
        NodeEntity nodeEntity = new NodeEntity();
        nodeEntity.setName(nodeInfo.getName());
        nodeEntity.setIpAddress(nodeInfo.getIpAddress());
        nodeEntity.setPort(nodeInfo.getPort());
        nodeEntity.setDescription(nodeInfo.getDescription());
        nodeEntity.setCreatorId(userId); // 设置创建者ID
        nodeEntity.setNodeType(nodeInfo.getNodeType()); // 设置节点类型
        NodeEntity savedNode = nodeRepository.save(nodeEntity);

        // 创建并保存节点认证信息
        NodeAuthEntity nodeAuthEntity = new NodeAuthEntity();
        nodeAuthEntity.setNode(savedNode);
        nodeAuthEntity.setUsername(nodeInfo.getUsername());
        nodeAuthEntity.setPassword(nodeInfo.getPassword());
        nodeAuthRepository.save(nodeAuthEntity);


        return savedNode;
    }

    /**
     * 列出所有节点
     */
    public Page<NodeEntity> getAllNodes(Pageable pageable) {
        return nodeRepository.findAll(pageable);  // 通过 Pageable 对象执行分页查询
    }

    /**
     * 查找节点
     */
    public NodeEntity getNodeById(Long id) {
        return nodeRepository.findById(id).orElseThrow(() -> new RuntimeException("Node not found"));
    }

    /**
     * 修改节点基本信息
     */
    public NodeEntity updateNodeById(Long id, NodeInfo nodeInfo) {
        NodeEntity nodeEntity = nodeRepository.findById(id).orElseThrow(() -> new RuntimeException("Node not found"));
        nodeEntity.setName(nodeInfo.getName());
        nodeEntity.setIpAddress(nodeInfo.getIpAddress());
        nodeEntity.setPort(nodeInfo.getPort());
        nodeEntity.setNodeType(nodeInfo.getNodeType());
        nodeEntity.setDescription(nodeInfo.getDescription());

        return nodeRepository.save(nodeEntity);
    }

    /**
     * 删除节点(一并删除节点状态和认证信息)
     */
    @Transactional
    public void deleteNode(Long id) {
        // 1. 删除数据集关联
        NodeEntity node = nodeRepository.findById(id)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));
        
        // 1. 解除与数据集的关联关系
        for (DatasetEntity dataset : node.getDatasets()) {
            dataset.getAvailableNodes().remove(node);
        }
        node.getDatasets().clear();
        
        // 2. 删除节点资源信息
        nodeResourceRepository.deleteByNodeId(id);
        
        // 3. 删除节点认证信息
        nodeAuthRepository.deleteByNodeId(id);
        
        // 4. 删除节点本身
        nodeRepository.deleteById(id);
    }

    /**
     * 获取节点统计数据
     */
    public Map<String, Object> getNodeStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取总节点数
        long totalNodes = nodeRepository.count();
        statistics.put("total", totalNodes);
        
        // 获取各状态节点数量
        Map<NodeResourceEntity.NodeStatus, Long> statusCounts = new HashMap<>();
        List<Object[]> results = nodeResourceRepository.countByStatus();
        
        // 初始化所有状态的计数为0
        statusCounts.put(NodeResourceEntity.NodeStatus.ONLINE, 0L);
        statusCounts.put(NodeResourceEntity.NodeStatus.OFFLINE, 0L);
        statusCounts.put(NodeResourceEntity.NodeStatus.ERROR, 0L);
        
        // 更新实际的计数
        for (Object[] result : results) {
            NodeResourceEntity.NodeStatus status = (NodeResourceEntity.NodeStatus) result[0];
            Long count = (Long) result[1];
            statusCounts.put(status, count);
        }
        
        statistics.put("online", statusCounts.get(NodeResourceEntity.NodeStatus.ONLINE));
        statistics.put("offline", statusCounts.get(NodeResourceEntity.NodeStatus.OFFLINE));
        statistics.put("error", statusCounts.get(NodeResourceEntity.NodeStatus.ERROR));
        
        // 获取各节点类型数量
        List<NodeEntity> allNodes = nodeRepository.findAll();
        Map<NodeEntity.NodeType, Long> typeCounts = new HashMap<>();
        
        // 初始化所有类型的计数为0
        typeCounts.put(NodeEntity.NodeType.Sycee, 0L);
        typeCounts.put(NodeEntity.NodeType.Pyxis, 0L);
        
        // 统计各类型节点数量
        for (NodeEntity node : allNodes) {
            NodeEntity.NodeType nodeType = node.getNodeType();
            typeCounts.put(nodeType, typeCounts.get(nodeType) + 1);
        }
        
        statistics.put("syceeNodes", typeCounts.get(NodeEntity.NodeType.Sycee));
        statistics.put("pyxisNodes", typeCounts.get(NodeEntity.NodeType.Pyxis));
        
        return statistics;
    }

    /**
     * 根据节点名称查找节点
     */
    public NodeEntity findNodeByName(String name) {
        return nodeRepository.findByName(name);
    }

    /**
     * 批量删除节点
     */
    @Transactional
    public void batchDeleteNodes(List<Long> ids) {
        List<NodeEntity> nodes = nodeRepository.findAllById(ids);
        
        // 1. 解除所有节点与数据集的关联关系
        for (NodeEntity node : nodes) {
            for (DatasetEntity dataset : node.getDatasets()) {
                dataset.getAvailableNodes().remove(node);
            }
            node.getDatasets().clear();
        }
        
        // 2. 删除节点资源信息
        nodeResourceRepository.deleteByNodeIdIn(ids);
        
        // 3. 删除节点认证信息
        for (Long id : ids) {
            nodeAuthRepository.deleteByNodeId(id);
        }
        
        // 4. 删除节点本身
        nodeRepository.deleteAll(nodes);
    }

    /**
     * 获取用户创建的节点列表
     */
    public Page<NodeEntity> getNodesByCreatorId(Long creatorId, Pageable pageable) {
        return nodeRepository.findByCreatorId(creatorId, pageable);
    }

    /**
     * 检查节点是否属于指定用户
     */
    public boolean isNodeOwner(Long nodeId, Long userId) {
        return nodeRepository.existsByIdAndCreatorId(nodeId, userId);
    }

    /**
     * 删除用户的节点
     */
    @Transactional
    public void deleteUserNode(Long nodeId, Long userId) {
        // 1. 检查节点是否属于该用户
        if (!isNodeOwner(nodeId, userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR,"无权删除此节点");
        }
        
        // 2. 获取要删除的节点
        NodeEntity node = nodeRepository.findById(nodeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));
        
        // 3. 解除与数据集的关联关系
        for (DatasetEntity dataset : node.getDatasets()) {
            dataset.getAvailableNodes().remove(node);
        }
        node.getDatasets().clear();
        
        // 4. 删除节点资源信息
        nodeResourceRepository.deleteByNodeId(nodeId);
        
        // 5. 删除节点认证信息
        nodeAuthRepository.deleteByNodeId(nodeId);
        
        // 6. 删除节点本身
        nodeRepository.delete(node);
    }

    /**
     * 批量删除用户的节点
     */
    @Transactional
    public void batchDeleteUserNodes(List<Long> ids, Long userId) {
        // 1. 检查所有节点是否都属于该用户
        for (Long nodeId : ids) {
            if (!isNodeOwner(nodeId, userId)) {
                throw new BaseException(ResponseCode.SERVICE_ERROR,"存在无权删除的节点");
            }
        }
        
        // 2. 获取所有要删除的节点
        List<NodeEntity> nodes = nodeRepository.findAllById(ids);
        
        // 3. 解除所有节点与数据集的关联关系
        for (NodeEntity node : nodes) {
            for (DatasetEntity dataset : node.getDatasets()) {
                dataset.getAvailableNodes().remove(node);
            }
            node.getDatasets().clear();
        }
        
        // 4. 删除节点资源信息
        nodeResourceRepository.deleteByNodeIdIn(ids);
        
        // 5. 删除节点认证信息
        for (Long id : ids) {
            nodeAuthRepository.deleteByNodeId(id);
        }
        
        // 6. 删除节点本身
        nodeRepository.deleteAll(nodes);
    }

    /**
     * 获取节点关联的数据集
     */
    public Page<DatasetEntity> getNodeDatasets(Long nodeId, Pageable pageable) {
        NodeEntity node = nodeRepository.findById(nodeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));
        
        // 获取与该节点关联的数据集
        return datasetRepository.findByAvailableNodesContaining(node, pageable);
    }

}
