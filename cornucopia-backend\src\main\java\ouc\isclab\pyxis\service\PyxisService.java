package ouc.isclab.pyxis.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.common.response.ResponseResult;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.service.NodeService;
import ouc.isclab.node.entity.NodeAuthEntity;
import ouc.isclab.node.repository.NodeAuthRepository;
import ouc.isclab.task.entity.NodeAccountRequestEntity;
import ouc.isclab.task.repository.NodeAccountRequestRepository;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.Arrays;
import java.util.stream.Collectors;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.servlet.http.HttpServletRequest;

@Slf4j
@Service
public class PyxisService {

    @Autowired
    private NodeService nodeService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private NodeAuthRepository nodeAuthRepository;

    @Autowired
    private NodeAccountRequestRepository nodeAccountRequestRepository;

    public String getPyxisToken(Long nodeId, String username, String password) {
        
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/users/token";
        //username=pyxis&password=pyxis&grant_type=password
        String requestBody = "username="+ username +"&password="+ password +"&grant_type=password";

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            try {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                log.info("获取Pyxis令牌成功: {}", response.getBody());
                return jsonNode.get("access_token").asText();
            } catch (Exception e) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "解析Pyxis令牌响应失败");
            }
        } else {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取Pyxis令牌失败");
        }
    }

    public Boolean createPyxisUser(Long nodeId, String username, String password) {
        
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }
        
        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());

        // 创建Pyxis用户
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/users/";
        //{"name":"1","role":"user","password":"1"}
        String requestBody = "{\"name\":\""+ username +"\",\"role\":\"user\",\"password\":\""+ password +"\"}";

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);      

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            try {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                log.info("创建Pyxis用户成功: {}", response.getBody());
                //{"user_id":"f7bc3ed3-b4c7-449d-a877-332f37cc96b9","name":"1","role":"user"}
                return true;
            } catch (Exception e) {
                log.error("解析Pyxis用户创建响应失败: {}", response.getBody(), e);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "解析Pyxis用户创建响应失败");
            }
        } else {
            log.error("创建Pyxis用户失败: 状态码={}, 响应={}", response.getStatusCode(), response.getBody());
            throw new BaseException(ResponseCode.SERVICE_ERROR, "创建Pyxis用户失败: 状态码=" + response.getStatusCode());
        }
    }   

    public Map<String, Object> createPyxisTask(Long nodeId, Long userId, MultipartFile[] files, String folderName) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token;
        if (node.getCreatorId().equals(userId)) {
            // 如果是节点所有者，使用管理员账户
            log.info("用户是节点所有者，使用管理员账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());
        } else {
            // 如果不是节点所有者，使用申请来的账户
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }
            log.info("用户使用申请来的账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAccount.getNodeUsername(), nodeAccount.getNodePassword());
        }

        // 创建Pyxis任务
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/task"; 

        try {
            // 创建MultipartBody
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            for (MultipartFile file : files) {
                body.add("files", new ByteArrayResource(file.getBytes()) {
                    @Override
                    public String getFilename() {
                        return file.getOriginalFilename();
                    }
                });
            }
            
            // 添加文件夹名称
            if (folderName != null && !folderName.isEmpty()) {
                body.add("folder_name", folderName);
            }

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.setBearerAuth(token);

            // 创建请求实体
            HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(body, headers);

            // 发送请求并获取响应
            ResponseEntity<String> response = restTemplate.exchange(
                    targetUrl,
                    HttpMethod.POST,
                    httpEntity,
                    String.class
            );  

            if ((response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) 
                    && response.getBody() != null) {
                try {
                    JsonNode jsonNode = objectMapper.readTree(response.getBody());
                    log.info("创建Pyxis任务成功: {}", response.getBody());
                    return objectMapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {});
                } catch (Exception e) {
                    log.error("解析Pyxis任务创建响应失败: {}", response.getBody(), e);
                    throw new BaseException(ResponseCode.SERVICE_ERROR, "解析Pyxis任务创建响应失败");
                }
            } else {
                log.error("创建Pyxis任务失败: 状态码={}, 响应={}", response.getStatusCode(), response.getBody());
                throw new BaseException(ResponseCode.SERVICE_ERROR, "创建Pyxis任务失败: 状态码=" + response.getStatusCode());
            }
        } catch (IOException e) {
            log.error("处理文件上传失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "处理文件上传失败: " + e.getMessage());
        }
    }

    public Map<String, Object> approvePyxisTask(Long nodeId, String taskId, String message, Boolean isApproved) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());

        // 审批Pyxis任务
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/audit/approve";
        //{"task_id":"09DFAFF642400000_fields_examples","approve":true,"message":"123"}
        String requestBody = "{\"task_id\":\""+ taskId +"\",\"approve\":"+ isApproved +",\"message\":\""+ message +"\"}";

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );  
        //{"status":"approved","task_id":"09DFAFF642400000_fields_examples"}
        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            try {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                log.info("审批Pyxis任务成功: {}", response.getBody());
                return objectMapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                log.error("解析Pyxis任务审批响应失败: {}", response.getBody(), e);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "解析Pyxis任务审批响应失败");
            }   
        } else {
            log.error("审批Pyxis任务失败: 状态码={}, 响应={}", response.getStatusCode(), response.getBody());
            throw new BaseException(ResponseCode.SERVICE_ERROR, "审批Pyxis任务失败: 状态码=" + response.getStatusCode());
        }   
    }

    public Map<String, Object> executePyxisTask(Long nodeId, String taskId, Long userId) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token;
        if (node.getCreatorId().equals(userId)) {
            // 如果是节点所有者，使用管理员账户
            log.info("用户是节点所有者，使用管理员账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());
        } else {
            // 如果不是节点所有者，使用申请来的账户
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }
            log.info("用户使用申请来的账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAccount.getNodeUsername(), nodeAccount.getNodePassword());
        }

        // 执行Pyxis任务
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/task/" + taskId + "/launch";

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);        

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );
        //{"task_id":"09DFBE1824400000_fields_examples","status":"queued"}
        if ((response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) 
                && response.getBody() != null) {
            try {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                log.info("执行Pyxis任务成功: {}", response.getBody());
                return objectMapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                log.error("解析Pyxis任务执行响应失败: {}", response.getBody(), e);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "解析Pyxis任务执行响应失败");
            }
        } else {
            log.error("执行Pyxis任务失败: 状态码={}, 响应={}", response.getStatusCode(), response.getBody());
            throw new BaseException(ResponseCode.SERVICE_ERROR, "执行Pyxis任务失败: 状态码=" + response.getStatusCode());
        }
    }

    /**
     * 停止Pyxis任务
     */
    public Map<String, Object> stopPyxisTask(Long nodeId, String taskId, Long userId) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token;
        if (node.getCreatorId().equals(userId)) {
            // 如果是节点所有者，使用管理员账户
            log.info("用户是节点所有者，使用管理员账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());
        } else {
            // 如果不是节点所有者，使用申请来的账户
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }
            log.info("用户使用申请来的账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAccount.getNodeUsername(), nodeAccount.getNodePassword());
        }

        // 停止Pyxis任务
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/task/" + taskId + "/stop";

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        if ((response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) 
                && response.getBody() != null) {
            try {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                log.info("停止Pyxis任务成功: {}", response.getBody());
                return objectMapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                log.error("解析Pyxis任务停止响应失败: {}", response.getBody(), e);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "解析Pyxis任务停止响应失败");
            }
        } else {
            log.error("停止Pyxis任务失败: 状态码={}, 响应={}", response.getStatusCode(), response.getBody());
            throw new BaseException(ResponseCode.SERVICE_ERROR, "停止Pyxis任务失败: 状态码=" + response.getStatusCode());
        }
    }

    /**
     * 强制终止Pyxis任务
     */
    public Map<String, Object> killPyxisTask(Long nodeId, String taskId, Long userId) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token;
        if (node.getCreatorId().equals(userId)) {
            // 如果是节点所有者，使用管理员账户
            log.info("用户是节点所有者，使用管理员账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());
        } else {
            // 如果不是节点所有者，使用申请来的账户
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }
            log.info("用户使用申请来的账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAccount.getNodeUsername(), nodeAccount.getNodePassword());
        }

        // 强制终止Pyxis任务
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/task/" + taskId + "/kill";

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                httpEntity,
                String.class
        );

        if ((response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) 
                && response.getBody() != null) {
            try {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                log.info("强制终止Pyxis任务成功: {}", response.getBody());
                return objectMapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                log.error("解析Pyxis任务强制终止响应失败: {}", response.getBody(), e);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "解析Pyxis任务强制终止响应失败");
            }
        } else {
            log.error("强制终止Pyxis任务失败: 状态码={}, 响应={}", response.getStatusCode(), response.getBody());
            throw new BaseException(ResponseCode.SERVICE_ERROR, "强制终止Pyxis任务失败: 状态码=" + response.getStatusCode());
        }
    }

    /**
     * 获取Pyxis任务状态
     */
    public Map<String, Object> getPyxisTaskStatus(Long nodeId, String taskId, Long userId) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token;
        if (node.getCreatorId().equals(userId)) {
            // 如果是节点所有者，使用管理员账户
            log.info("用户是节点所有者，使用管理员账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());
        } else {
            // 如果不是节点所有者，使用申请来的账户
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }
            log.info("用户使用申请来的账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAccount.getNodeUsername(), nodeAccount.getNodePassword());
        }

        // 获取Pyxis任务状态
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/task/" + taskId + "/status";

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.GET,
                httpEntity,
                String.class
        );

        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            try {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                log.info("获取Pyxis任务状态成功: {}", response.getBody());
                return objectMapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                log.error("解析Pyxis任务状态响应失败: {}", response.getBody(), e);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "解析Pyxis任务状态响应失败");
            }
        } else {
            log.error("获取Pyxis任务状态失败: 状态码={}, 响应={}", response.getStatusCode(), response.getBody());
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取Pyxis任务状态失败: 状态码=" + response.getStatusCode());
        }
    }

    /**
     * 浏览Pyxis任务文件目录
     * @param nodeId 节点ID
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param path 要浏览的目录路径，如果为null则浏览根目录
     * @return 目录内容
     */
    public Map<String, Object> browsePyxisTaskFiles(Long nodeId, String taskId, Long userId, String path) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token;
        if (node.getCreatorId().equals(userId)) {
            // 如果是节点所有者，使用管理员账户
            log.info("用户是节点所有者，使用管理员账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());
        } else {
            // 如果不是节点所有者，使用申请来的账户
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }
            log.info("用户使用申请来的账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAccount.getNodeUsername(), nodeAccount.getNodePassword());
        }

        // 构建URL，如果path不为null则添加到URL中
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/task/" + taskId + "/browse/";
        if (path != null && !path.isEmpty()) {
            targetUrl += path;
        }
        log.info("浏览Pyxis任务文件目录: {}", targetUrl);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                targetUrl,
                HttpMethod.GET,
                httpEntity,
                String.class
        );

        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            try {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                log.info("浏览Pyxis任务文件目录成功: {}", response.getBody());
                return objectMapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                log.error("解析Pyxis任务文件目录响应失败: {}", response.getBody(), e);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "解析Pyxis任务文件目录响应失败");
            }
        } else {
            log.error("浏览Pyxis任务文件目录失败: 状态码={}, 响应={}", response.getStatusCode(), response.getBody());
            throw new BaseException(ResponseCode.SERVICE_ERROR, "浏览Pyxis任务文件目录失败: 状态码=" + response.getStatusCode());
        }
    }

    /**
     * 下载Pyxis任务文件
     * @param nodeId 节点ID
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param filePath 要下载的文件路径
     * @return ResponseEntity<byte[]> 包含文件内容的响应实体
     */
    public ResponseEntity<byte[]> downloadPyxisTaskFile(Long nodeId, String taskId, Long userId, String filePath) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token;
        if (node.getCreatorId().equals(userId)) {
            // 如果是节点所有者，使用管理员账户
            log.info("用户是节点所有者，使用管理员账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());
        } else {
            // 如果不是节点所有者，使用申请来的账户
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }
            log.info("用户使用申请来的账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAccount.getNodeUsername(), nodeAccount.getNodePassword());
        }

        // 构建URL，只对路径中的特殊字符进行编码，保留路径分隔符
        String[] pathParts = filePath.split("/");
        String encodedFilePath = Arrays.stream(pathParts)
            .map(part -> URLEncoder.encode(part, StandardCharsets.UTF_8))
            .collect(Collectors.joining("/"));
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/task/" + taskId + "/download/" + encodedFilePath;
        log.info("下载Pyxis任务文件: url={}, originalPath={}, encodedPath={}", targetUrl, filePath, encodedFilePath);

        // 设置请求头，与成功的请求保持一致
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.set("Accept", "*/*");
        headers.set("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2");
        headers.set("Accept-Encoding", "gzip, deflate");
        headers.set("Connection", "keep-alive");

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        try {
            // 发送请求并获取响应
            ResponseEntity<byte[]> response = restTemplate.exchange(
                    targetUrl,
                    HttpMethod.GET,
                    httpEntity,
                    byte[].class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                // 获取文件名
                String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                
                // 设置响应头，与成功的响应保持一致
                HttpHeaders responseHeaders = new HttpHeaders();
                responseHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                responseHeaders.setContentDispositionFormData("attachment", fileName);
                responseHeaders.setContentLength(response.getBody().length);
                responseHeaders.set("accept-ranges", "bytes");
                responseHeaders.set("last-modified", response.getHeaders().getFirst("last-modified"));
                responseHeaders.set("etag", response.getHeaders().getFirst("etag"));
                
                log.info("下载Pyxis任务文件成功: filePath={}, size={}", filePath, response.getBody().length);
                return new ResponseEntity<>(response.getBody(), responseHeaders, HttpStatus.OK);
            } else {
                log.error("下载Pyxis任务文件失败: 状态码={}, filePath={}", response.getStatusCode(), filePath);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "下载Pyxis任务文件失败: 状态码=" + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("下载Pyxis任务文件异常: filePath={}, error={}", filePath, e.getMessage());
            // 如果是404错误，返回更友好的错误信息
            if (e.getMessage().contains("404")) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "文件不存在: " + filePath);
            }
            throw new BaseException(ResponseCode.SERVICE_ERROR, "下载Pyxis任务文件异常: " + e.getMessage());
        }
    }

    /**
     * 预览Pyxis任务文本文件
     * @param nodeId 节点ID
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param filePath 要预览的文件路径
     * @return String 文件内容文本
     */
    public String previewPyxisTaskFile(Long nodeId, String taskId, Long userId, String filePath) {
        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token;
        if (node.getCreatorId().equals(userId)) {
            // 如果是节点所有者，使用管理员账户
            log.info("用户是节点所有者，使用管理员账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());
        } else {
            // 如果不是节点所有者，使用申请来的账户
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }
            log.info("用户使用申请来的账户获取令牌: nodeId={}, userId={}", nodeId, userId);
            token = getPyxisToken(nodeId, nodeAccount.getNodeUsername(), nodeAccount.getNodePassword());
        }

        // 构建URL，只对路径中的特殊字符进行编码，保留路径分隔符
        String[] pathParts = filePath.split("/");
        String encodedFilePath = Arrays.stream(pathParts)
            .map(part -> URLEncoder.encode(part, StandardCharsets.UTF_8))
            .collect(Collectors.joining("/"));
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + "/task/" + taskId + "/download/" + encodedFilePath;
        log.info("预览Pyxis任务文件: url={}, originalPath={}, encodedPath={}", targetUrl, filePath, encodedFilePath);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.set("Accept", "text/plain,*/*");
        headers.set("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2");
        headers.set("Accept-Encoding", "gzip, deflate");
        headers.set("Connection", "keep-alive");

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        try {
            // 发送请求并获取响应
            ResponseEntity<byte[]> response = restTemplate.exchange(
                    targetUrl,
                    HttpMethod.GET,
                    httpEntity,
                    byte[].class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                // 将响应体转换为字符串
                String content = new String(response.getBody(), StandardCharsets.UTF_8);
                log.info("预览Pyxis任务文件成功: filePath={}, 内容长度={}", filePath, content.length());
                return content;
            } else {
                log.error("预览Pyxis任务文件失败: 状态码={}, filePath={}", response.getStatusCode(), filePath);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "预览Pyxis任务文件失败: 状态码=" + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("预览Pyxis任务文件异常: filePath={}, error={}", filePath, e.getMessage());
            // 如果是404错误，返回更友好的错误信息
            if (e.getMessage().contains("404")) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "文件不存在: " + filePath);
            }
            throw new BaseException(ResponseCode.SERVICE_ERROR, "预览Pyxis任务文件异常: " + e.getMessage());
        }
    }

    private String getRequestBody(HttpServletRequest request) {
        try {
            return request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
        } catch (IOException e) {
            log.error("获取请求体失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 转发请求
     */
    public Map<String, Object> forwardRequest(Long userId, Long nodeId, HttpServletRequest request) {

        // 验证用户是否是节点的创建者
        if (!nodeService.isNodeOwner(nodeId, userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权访问此节点");
        }

        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 验证是否是Pyxis节点
        if (node.getNodeType() != NodeEntity.NodeType.Pyxis) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不是Pyxis节点");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());

        // 获取Pyxis请求路径
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + request.getRequestURI().replace("/api/v1.0/pyxis/" + nodeId, "");
        log.info("转发Pyxis请求: {}", targetUrl);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);

        // 从请求中获取请求体
        String requestBody = getRequestBody(request);

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求并获取响应
        HttpMethod method = HttpMethod.valueOf(request.getMethod());
        ResponseEntity<String> responseEntity = restTemplate.exchange(
                targetUrl,
                method,
                httpEntity,
                String.class
        );

        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            try {
                // 读取JSON响应为JsonNode，这样可以处理对象或数组
                JsonNode jsonNode = objectMapper.readTree(responseEntity.getBody());
                log.info("转发Pyxis请求成功: {}", responseEntity.getBody());
                
                // 判断是否是数组类型的响应
                if (jsonNode.isArray()) {
                    // 如果是数组，将其封装在一个Map中
                    Map<String, Object> result = new HashMap<>();
                    result.put("data", objectMapper.convertValue(jsonNode, List.class));
                    return result;
                } else {
                    // 如果是对象，直接转换为Map
                    return objectMapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {});
                }
            } catch (Exception e) {
                log.error("解析Pyxis转发请求响应失败: {}", responseEntity.getBody(), e);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "解析Pyxis转发请求响应失败");
            }
        } else {
            log.error("转发Pyxis请求失败: 状态码={}, 响应={}", responseEntity.getStatusCode(), responseEntity.getBody());
            throw new BaseException(ResponseCode.SERVICE_ERROR, "转发Pyxis请求失败: 状态码=" + responseEntity.getStatusCode());
        }
    }

    /**
     * 下载Pyxis文件
     * @param nodeId 节点ID
     * @param userId 用户ID
     * @param request 请求
     * @return ResponseEntity<byte[]> 包含文件内容的响应实体
     */
    public ResponseEntity<byte[]> downloadPyxisFile(Long userId, Long nodeId, HttpServletRequest request) {
       // 验证用户是否是节点的创建者
        if (!nodeService.isNodeOwner(nodeId, userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权访问此节点");
        }

        // 获取节点信息
        NodeEntity node = nodeService.getNodeById(nodeId);
        if (node == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在");
        }

        // 验证是否是Pyxis节点
        if (node.getNodeType() != NodeEntity.NodeType.Pyxis) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点不是Pyxis节点");
        }

        // 获取节点认证信息
        NodeAuthEntity nodeAuth = nodeAuthRepository.findByNodeId(nodeId);
        if (nodeAuth == null) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "节点认证信息不存在");
        }

        // 获取Pyxis令牌
        String token = getPyxisToken(nodeId, nodeAuth.getUsername(), nodeAuth.getPassword());

        // 构建URL，只对路径中的特殊字符进行编码，保留路径分隔符
        // 获取Pyxis请求路径
        String targetUrl = "http://" + node.getIpAddress() + ":" + node.getPort() + request.getRequestURI().replace("/api/v1.0/pyxis/download/" + nodeId, "");
        log.info("下载Pyxis文件: {}", targetUrl);

        // 设置请求头，与成功的请求保持一致
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.set("Accept", "*/*");
        headers.set("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2");
        headers.set("Accept-Encoding", "gzip, deflate");
        headers.set("Connection", "keep-alive");

        // 创建请求实体
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        try {
            // 发送请求并获取响应
            ResponseEntity<byte[]> response = restTemplate.exchange(
                    targetUrl,
                    HttpMethod.GET,
                    httpEntity,
                    byte[].class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                // 获取文件名
                // String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                
                // 设置响应头，与成功的响应保持一致
                HttpHeaders responseHeaders = new HttpHeaders();
                responseHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                // responseHeaders.setContentDispositionFormData("attachment", fileName);
                responseHeaders.setContentLength(response.getBody().length);
                responseHeaders.set("accept-ranges", "bytes");
                responseHeaders.set("last-modified", response.getHeaders().getFirst("last-modified"));
                responseHeaders.set("etag", response.getHeaders().getFirst("etag"));
                
                // log.info("下载Pyxis任务文件成功: filePath={}, size={}", filePath, response.getBody().length);
                return new ResponseEntity<>(response.getBody(), responseHeaders, HttpStatus.OK);
            } else {
                // log.error("下载Pyxis任务文件失败: 状态码={}, filePath={}", response.getStatusCode(), filePath);
                throw new BaseException(ResponseCode.SERVICE_ERROR, "下载Pyxis任务文件失败: 状态码=" + response.getStatusCode());
            }
        } catch (Exception e) {
            // log.error("下载Pyxis任务文件异常: filePath={}, error={}", filePath, e.getMessage());
            // 如果是404错误，返回更友好的错误信息
            if (e.getMessage().contains("404")) {
                // throw new BaseException(ResponseCode.SERVICE_ERROR, "文件不存在: " + filePath);
            }
            throw new BaseException(ResponseCode.SERVICE_ERROR, "下载Pyxis任务文件异常: " + e.getMessage());
        }
    }
   
}
