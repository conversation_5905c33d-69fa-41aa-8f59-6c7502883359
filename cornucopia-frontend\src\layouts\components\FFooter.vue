<template>
    <el-footer height="60px">
        <div class="footer-content">
            <span class="year">2025</span>
            <span class="divider">@</span>
            <span class="org">海尔海大数据安全联合开发实验室</span>
            <span class="location">青岛</span>
        </div>
    </el-footer>
</template>

<script setup>
</script>

<style scoped>
.el-footer {
    margin: 0;
    width: 100%;
    background-color: var(--el-bg-color);
    color: var(--el-text-color-regular);
    text-align: center;
    line-height: 60px;
    box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);
    border-top: 1px solid var(--el-border-color-light);
}

.footer-content {
    font-size: 16px;
    letter-spacing: 0.5px;
}

.year {
    font-weight: 500;
    color: var(--el-color-primary);
    font-size: 16px;
}

.divider {
    margin: 0 4px;
    color: var(--el-text-color-placeholder);
    font-size: 16px;
}

.org {
    color: var(--el-text-color-secondary);
    font-weight: 500;
    font-size: 16px;
}

.location {
    margin-left: 4px;
    color: var(--el-text-color-secondary);
    font-size: 16px;
}

/* 深色模式下的阴影调整 */
html.dark .el-footer {
    box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.15);
}
</style>