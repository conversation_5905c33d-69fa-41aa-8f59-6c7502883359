package ouc.isclab.node.pojo;

import lombok.Data;
import ouc.isclab.node.entity.NodeEntity.NodeType;

@Data
public class NodeInfo {
    private String name; // 节点名称
    private String ipAddress; // 节点IP地址
    private int port; // 节点端口
    private String description; // 节点描述
    private NodeType nodeType; // 节点类型
    
    // 认证信息
    private String username; // 节点访问用户名
    private String password; // 节点访问密码
}
