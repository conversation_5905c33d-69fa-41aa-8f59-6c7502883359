package ouc.isclab.task.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import ouc.isclab.task.entity.UserResourceEntity;

import java.util.List;

public interface UserResourceRepository extends JpaRepository<UserResourceEntity, Long> {
    
    UserResourceEntity findByUserIdAndResource_Id(Long userId, Long resourceId);
    
    List<UserResourceEntity> findByUserIdAndActive(Long userId, boolean active);
} 