
services:
  pyxis-core:
    container_name: pyxis-core
    image: ${MAIN_IMAGE}
    build:
      context: .
      dockerfile: dockerfiles/pyxis-core.Dockerfile
    volumes:
      - ${HOST_DIR}/static:/app/static
      - ${HOST_DIR}/task_workspace:/app/task_workspace
      - ${HOST_DIR}/assets:/app/assets
      - ${HOST_DIR}/database:/app/database
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - HOST_DIR=${HOST_DIR}
      - AUTH_ENABLE=${AUTH_ENABLE}
      - HOST_PORT=${MAIN_PORT}
      - SECRET_KEY=${SECRET_KEY}
      - WORKER_IMAGE=${WORKER_IMAGE}
    networks:
      - pyxis-network

  pyxis-gateway:
    container_name: pyxis-gateway
    image: ${GATEWAY_IMAGE}
    build:
      context: .
      dockerfile: dockerfiles/pyxis-gateway.Dockerfile
    ports:
      - "${MAIN_PORT}:8000"
    volumes:
      - ${HOST_DIR}/nginx.conf:/etc/nginx/conf.d/default.conf
    networks:
      - pyxis-network
    depends_on:
      - pyxis-core

  pyxis-worker:
    container_name: pyxis-worker
    image: ${WORKER_IMAGE}
    build:
      context: .
      dockerfile: dockerfiles/pyxis-worker-cuda.Dockerfile
    networks:
      - pyxis-network

networks:
  pyxis-network:
    name: pyxis-network