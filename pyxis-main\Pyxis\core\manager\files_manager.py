from pathlib import Path
import shutil
from typing import Dict, List
import json
import time

from fastapi import File, UploadFile

from ..utils import archivetools


class FilesManager:
    """A manager for handling task-related file operations and directory browsing.

    This class provides functionality to:
    - Browse and inspect task directories and files
    - Download files
    - List available tasks
    - Delete tasks
    - Get task information and status

    Attributes:
        task_workspace_dir: Root directory containing all task workspaces
        MAX_CONTENT_SIZE: Maximum file size (in bytes) for content preview
    """

    def __init__(self, task_workspace_dir: Path = Path("./task_workspace"), MAX_CONTENT_SIZE=512 * 1024):
        """Initialize the FilesManager.

        Args:
            task_workspace_dir: Path to the task workspace directory (default: ./task_workspace)
            MAX_CONTENT_SIZE: Maximum file size in bytes for content preview (default: 512KB)
        """
        self.task_workspace_dir = task_workspace_dir.resolve()
        self.MAX_CONTENT_SIZE = MAX_CONTENT_SIZE

    def _validate_path(self, task_id: str, subpath: str = "") -> Path:
        """Validate and secure a file path within a task's workspace.

        Args:
            task_id: ID of the task
            subpath: Relative path within the task's workspace

        Returns:
            Absolute Path object to the validated location

        Raises:
            ValueError: If path traversal attempt is detected
        """
        base_path = (self.task_workspace_dir / task_id).resolve()
        full_path = (base_path / subpath).resolve()

        # Prevent directory traversal attacks
        if not full_path.is_relative_to(base_path):
            raise ValueError("Path traversal attempt")
        return full_path

    def _convert_file_size(self, size: int) -> str:
        """Convert file size in bytes to human-readable format.

        Args:
            size: File size in bytes

        Returns:
            Human-readable string with appropriate unit (e.g., "1.23 MB")
        """
        units = ('B', 'KB', 'MB', 'GB', 'TB')
        unit = units[0]
        for i in range(1, len(units)):
            if size >= 1024:
                size /= 1024
                unit = units[i]
            else:
                break
        return f'{size:.2f} {unit}'

    def get_file_info(self, task_id: str, path: str = "") -> Dict:
        """Get detailed information about a file or directory.

        Args:
            task_id: ID of the task
            path: Relative path within the task's workspace (default: root)

        Returns:
            Dictionary containing:
            - For files: type, path, name, size, modified time, extension
              and content (for text files under size limit)
            - For directories: type, path, name, and children list
            - Error information if operation fails

        Example:
            {
                "type": "file",
                "path": "workspace/output.txt",
                "name": "output.txt",
                "size": "12.34 KB",
                "modified": 1678901234.56,
                "extension": ".txt",
                "content": "file contents..."
            }
        """
        try:
            target = self._validate_path(task_id, path)

            if not target.exists():
                raise FileNotFoundError("Path not found")

            if target.is_file():
                file_info = {
                    "type": "file",
                    "path": str(target.relative_to(self.task_workspace_dir / task_id)),
                    "name": target.name,
                    "size": self._convert_file_size(target.stat().st_size),
                    "modified": target.stat().st_mtime,
                    "extension": target.suffix.lower()
                }

                # Add content for supported text files under size limit
                if target.suffix.lower() in ('.json', '.txt', '.log'):
                    file_size = target.stat().st_size
                    if file_size <= self.MAX_CONTENT_SIZE or file_info["path"] == "STATUS.json":
                        try:
                            file_info["content"] = target.read_text()
                        except UnicodeDecodeError:
                            file_info["content"] = "Binary content not displayable"
                    else:
                        file_info["content"] = (
                            f"File too large to display ({self._convert_file_size(file_size)} > "
                            f"{self._convert_file_size(self.MAX_CONTENT_SIZE)} limit)"
                        )
                return file_info

            elif target.is_dir():
                return {
                    "type": "directory",
                    "path": str(target.relative_to(self.task_workspace_dir / task_id)),
                    "name": target.name,
                    "children": [
                        self.get_file_info(task_id, str(
                            (target / f.name).relative_to(self.task_workspace_dir / task_id)))
                        for f in target.iterdir() if not f.name.startswith(".")
                    ]
                }

        except Exception as e:
            return {
                "error": str(e),
                "type": "error"
            }

    def get_file_download(self, task_id: str, path: str) -> Dict:
        """Get a FastAPI FileResponse for downloading a file.

        Args:
            task_id: ID of the task
            path: Relative path to the file within the task's workspace

        Returns:
            {
                path:"path/to/file",
                filename:"file_name"
            }

        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If path validation fails
        """
        target = self._validate_path(task_id, path)
        if not target.is_file():
            raise FileNotFoundError("File not found")
        return {
            "path": str(target),
            "filename": target.name
        }

    def list_tasks(self) -> List[Dict]:
        """List all available tasks in the workspace directory.

        Returns:
            List of dictionaries containing task IDs and paths:
            [{"task_id": str, "path": str}, ...]
        """
        tasks = []
        if self.task_workspace_dir.exists():
            for d in self.task_workspace_dir.iterdir():
                if d.is_dir():
                    tasks.append({
                        "task_id": d.name,
                        "path": str(d.relative_to(self.task_workspace_dir))
                    })
        return tasks

    def delete_task(self, task_id: str):
        """Delete a task directory and all its contents.

        Args:
            task_id: ID of the task to delete

        Returns:
            ID of the task to delete

        Raises:
            FileNotFoundError: If the task directory doesn't exist
        """
        task_dir = self._validate_path(task_id)

        if not task_dir.exists():
            raise FileNotFoundError("Task not found")

        shutil.rmtree(task_dir)
        return task_id

    def get_task_status(self, task_id: str) -> Dict:
        """Get the status information from a task's STATUS.json file.

        Args:
            task_id: ID of the task

        Returns:
            Dictionary containing the parsed STATUS.json contents
            or error information if the file can't be read
        """
        return self.get_file_info(task_id, "STATUS.json")

    def _write_status(self, task_id: str, status_data: dict):
        """Write status information to a task's STATUS.json file.

        Args:
            task_id: ID of the task to update
            status_data: Dictionary containing status information to write
        """
        status_file = self.task_workspace_dir / task_id / "STATUS.json"
        status_file.parent.mkdir(parents=True, exist_ok=True)
        status_file.touch(exist_ok=True)

        old_data = {}
        if status_file.exists():
            try:
                with open(status_file, "r") as f:
                    old_data = json.load(f)
            except json.JSONDecodeError:
                old_data = {}

        new_data = {**old_data, **status_data}

        with open(status_file, "w") as f:
            json.dump(new_data, f, indent=2)

    async def save_user_code(self, task_id: str, files: list[UploadFile] = File(...)):
        """
        Save user-uploaded code files to the specified task directory.

        This asynchronous method creates a 'user_code' directory for the given task ID (if it doesn't exist)
        and saves all uploaded files to that directory.

        Args:
            task_id (str): The unique identifier for the task, used to create the directory path
            files (list[UploadFile]): List of uploaded files to be saved, provided via FastAPI's File(...)

        Returns:
            List of name of files which have been saved

        Notes:
            - The files will be saved to: {task_workspace_dir}/{task_id}/user_code/
            - Existing files with the same names will be overwritten
            - Directory creation is recursive if parent directories don't exist

        Raises:
            IOError: If there are any issues creating directories or writing files
        """

        user_code_dir = (self.task_workspace_dir /
                         task_id / 'user_code').resolve()
        user_logs_dir = (self.task_workspace_dir /
                         task_id / 'logs').resolve()
        user_results_dir = (self.task_workspace_dir /
                            task_id / 'results').resolve()
        user_workspace_dir = (self.task_workspace_dir /
                              task_id / 'workspace').resolve()

        user_code_dir.mkdir(parents=True, exist_ok=True)
        user_logs_dir.mkdir(parents=True, exist_ok=True)
        user_results_dir.mkdir(parents=True, exist_ok=True)
        user_workspace_dir.mkdir(parents=True, exist_ok=True)

        # Save uploaded files
        saved_files = []
        for file in files:
            file_path = (user_code_dir / file.filename).resolve()
            if not file_path.is_relative_to(user_code_dir):
                raise Exception(f"illegal filename: {file.filename}")
            parent_path = file_path.parent
            parent_path.mkdir(parents=True, exist_ok=True)
            with file_path.open("bw") as f:
                f.write(await file.read())
            saved_files.append(file_path.name)

        # auto unpack archive
        if len(saved_files) == 1:
            file = files[0]
            file_path = (user_code_dir / file.filename).resolve()
            if archivetools.is_archive_supported(file_path):
                archivetools.unpack_archive_with_flatten(
                    file_path, flatten_single_dir=True)

        self._write_status(task_id, {
            "task_id": task_id,
            "status": "created",
            "created_time": time.strftime("%Y-%m-%d %H:%M:%S")
        })

        return saved_files
