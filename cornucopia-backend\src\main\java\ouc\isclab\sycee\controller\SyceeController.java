package ouc.isclab.sycee.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.sycee.pojo.SyceeInfo;
import ouc.isclab.sycee.service.SyceeService;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sycee")
public class SyceeController {

    @Autowired
    private SyceeService syceeService;

    /**
     * 通用请求转发处理方法
     */
    @RequestMapping(value = "/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    public JsonNode forwardRequest(
            HttpServletRequest request, 
            HttpServletResponse response,
            @RequestBody SyceeInfo syceeInfo, 
            @CurrentUserId Long userId) {
        Map<String, Object> result = syceeService.handleRequestWithHeaders(request, syceeInfo, userId);
        
        // 从结果中提取时间戳头信息并添加到响应头
        Map<String, String> headers = (Map<String, String>) result.get("headers");
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                response.setHeader(entry.getKey(), entry.getValue());
            }
        }
        
        return (JsonNode) result.get("body");
    }
}
