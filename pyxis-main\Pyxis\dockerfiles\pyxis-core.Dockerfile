FROM ubuntu:latest


ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN apt-get update && apt-get install -y --no-install-recommends \
    python3 \
    python3-pip \
    zip \
    curl \
    lsof \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*


RUN pip3 install --no-cache-dir \
    fastapi \
    uvicorn \
    requests \
    docker \
    python-multipart \
    httpx \
    flask  \
    flask-socketio \
    websockets 
RUN pip3 install --no-cache-dir \    
    sqlalchemy \
    aiomysql \
    passlib \
    python-jose
RUN pip3 install --no-cache-dir \
    psutil \
    aiohttp \ 
    aiohttp-proxy
RUN pip3 install --no-cache-dir \
    etcd3
RUN pip3 install --no-cache-dir \
    protobuf
    

RUN mkdir -p /app
WORKDIR /app
COPY ./core ./core
COPY ./config.json ./config.json 

RUN mkdir -p /tmp/pycache
ENV PYTHONPYCACHEPREFIX=/tmp/pycache

EXPOSE 8000
CMD ["uvicorn", "core.server.app:app","--host", "0.0.0.0", "--port", "8000"]