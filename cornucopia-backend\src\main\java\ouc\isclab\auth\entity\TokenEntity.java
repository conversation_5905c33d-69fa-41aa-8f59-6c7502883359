package ouc.isclab.auth.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.Date;

@Data
@Entity
@Table(name = "sys_token")
@EntityListeners(AuditingEntityListener.class)
public class TokenEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true, length = 36)
    private String token;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "expire_time", nullable = false)
    private Date expireTime;

    @CreatedDate
    @Column(name = "created_time", nullable = false, updatable = false)
    private Date createdTime;
} 