<template>
  <div class="task-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><List /></el-icon>
          <h2>我的任务</h2>
        </div>
        <div class="sub-title">管理您创建的所有任务</div>
      </div>
      <div class="header-right">
        <el-button
          type="danger"
          size="default"
          :disabled="selectedIds.length === 0"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon> 批量删除
        </el-button>
        <el-button type="primary" @click="goToCreateTask" round>
          <el-icon><Plus /></el-icon>
          创建任务
        </el-button>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <!-- 任务表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="任务ID" align="center" width="80" />
        <el-table-column prop="name" label="任务名称" align="center" min-width="180">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center; cursor: pointer;">
              <el-icon class="file-icon" style="margin-right: 5px;">
                <Box />
              </el-icon>
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" align="center" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" align="center" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="creatorName" label="创建者" align="center" width="120" /> -->
        <el-table-column prop="createTime" label="创建时间" align="center" width="180" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" align="center" width="180" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-tooltip content="查看任务详情" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="viewTaskDetail(scope.row)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="刷新任务状态" placement="top">
                <el-button
                  type="info"
                  size="small"
                  @click.stop="refreshTaskStatus(scope.row)">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除任务" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click.stop="handleDelete(scope.row)"
                  :disabled="scope.row.status === 'RUNNING'"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
    
    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="taskDetailVisible"
      title="任务详情"
      width="700px"
      destroy-on-close
    >
      <div v-loading="taskDetailLoading">
        <el-descriptions v-if="taskDetail" :column="1" border>
          <el-descriptions-item label="任务ID">{{ taskDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskDetail.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(taskDetail.status)">{{ getStatusText(taskDetail.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(taskDetail.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(taskDetail.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="创建者">{{ taskDetail.creatorName }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ taskDetail.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="关联代码">
            <div v-if="taskDetail.codes && taskDetail.codes.length">
              <el-tag 
                v-for="code in taskDetail.codes" 
                :key="code.id"
                :class="['code-tag', code.nodeType === 'Pyxis' ? 'code-tag-pyxis' : 'code-tag-normal']"
                :type="getCodeStatusType(code.status)"
                @click="viewCodeDetail(code)"
                style="cursor: pointer; margin-bottom: 8px; display: inline-flex; align-items: center;"
              >
                <span v-if="code.nodeType === 'Pyxis'">
                  <el-icon class="code-icon"><Folder /></el-icon>
                  {{ code.pyxisTaskId || 'Pyxis部署任务' }}
                </span>
                <span v-else>
                  <el-icon class="code-icon"><Document /></el-icon>
                  {{ code.funcName || `代码${code.id}` }}
                </span>
                <el-divider direction="vertical" style="margin: 0 5px;"></el-divider>
                <el-tag size="small" style="margin-right: 5px;" :type="code.nodeType === 'Pyxis' ? 'success' : 'primary'">
                  {{ code.nodeType || '未知类型' }}
                </el-tag>
                <el-tag size="small" :type="getCodeStatusType(code.status)">
                  {{ getCodeStatusText(code.status) }}
                </el-tag>
              </el-tag>
            </div>
            <span v-else>无关联代码</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="taskDetailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加代码详情对话框 -->
    <el-dialog
      v-model="codeDetailVisible"
      :title="codeDetail?.nodeType === 'Pyxis' ? '代码详情: Pyxis部署任务' : `代码详情: ${codeDetail?.funcName || ''}`"
      width="800px"
      destroy-on-close
    >
      <div v-loading="codeDetailLoading">
        <el-descriptions v-if="codeDetail" :column="1" border label-width="120px">
          <el-descriptions-item label="代码ID">{{ codeDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="节点">
            {{ codeDetail.node?.name || codeDetail.nodeName || '未知节点' }} ({{ codeDetail.nodeType || '未知类型' }})
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getCodeStatusType(codeDetail.status)">
              {{ getCodeStatusText(codeDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(codeDetail.timeCreated || codeDetail.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(codeDetail.timeUpdated || codeDetail.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="创建者">{{ codeDetail.creatorName || `用户${codeDetail.creatorId}` }}</el-descriptions-item>
          <el-descriptions-item label="函数名称" v-if="codeDetail.nodeType !== 'Pyxis'">{{ codeDetail.funcName }}</el-descriptions-item>
          
          <!-- Pyxis特有字段 -->
          <el-descriptions-item label="Pyxis任务ID" v-if="codeDetail.nodeType === 'Pyxis' && codeDetail?.pyxisTaskId">
            {{ codeDetail.pyxisTaskId }}
          </el-descriptions-item>
          
          <!-- Sycee特有字段，放在后面 -->
          <el-descriptions-item label="Sycee代码ID" v-if="codeDetail.nodeType !== 'Pyxis' && codeDetail?.syceeCodeId">
            {{ codeDetail.syceeCodeId }}
          </el-descriptions-item>
          <el-descriptions-item label="Sycee请求ID" v-if="codeDetail.nodeType !== 'Pyxis' && codeDetail?.syceeRequestId">
            {{ codeDetail.syceeRequestId }}
          </el-descriptions-item>
          <el-descriptions-item label="Sycee任务ID" v-if="codeDetail.nodeType !== 'Pyxis' && codeDetail?.syceeJobId">
            {{ codeDetail.syceeJobId }}
          </el-descriptions-item>
          
          <!-- 保存的文件 -->
          <el-descriptions-item label="保存的文件" v-if="parsedSavedFiles.length > 0">
            <div class="saved-files-list">
              <el-tag 
                v-for="file in parsedSavedFiles" 
                :key="file"
                class="file-tag"
                type="info"
              >
                {{ file }}
              </el-tag>
            </div>
          </el-descriptions-item>
          
          <el-descriptions-item label="代码内容" v-if="codeDetail.nodeType !== 'Pyxis' && codeDetail.codeContent">
            <div class="code-content">
              <pre><code>{{ codeDetail.codeContent }}</code></pre>
            </div>
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 添加文件操作按钮区域 -->
        <div class="file-actions" v-if="codeDetail && codeDetail.nodeType === 'Pyxis'">
          <el-button type="primary" @click="browseFiles(codeDetail)">
            <el-icon><Folder /></el-icon> 浏览文件
          </el-button>
          <el-button type="success" @click="downloadFiles(codeDetail)">
            <el-icon><Download /></el-icon> 下载文件
          </el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="codeDetailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 文件浏览对话框 -->
    <el-dialog
      v-model="fileBrowserVisible"
      title="文件浏览器"
      width="700px"
      destroy-on-close
    >
      <div v-loading="fileBrowserLoading" class="file-browser">
        <div class="file-browser-header">
          <div class="current-path">{{ currentPath || '/' }}</div>
          <el-button size="small" @click="navigateUp" :disabled="currentPath === ''">
            <el-icon><Back /></el-icon> 返回上级
          </el-button>
        </div>
        
        <el-table :data="browserFiles" size="small" border style="width: 100%">
          <el-table-column prop="name" label="名称">
            <template #default="scope">
              <div class="file-item" @click="navigateToFile(scope.row)">
                <el-icon class="file-icon">
                  <Folder v-if="scope.row.type === 'directory'" />
                  <Document v-else />
                </el-icon>
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" width="100" />
          <el-table-column label="修改时间" width="180">
            <template #default="scope">
              {{ formatFileTime(scope.row.modified) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button-group>
                <el-button 
                  size="small" 
                  type="primary" 
                  @click.stop="downloadBrowserFile(scope.row)"
                  :disabled="scope.row.type === 'directory'"
                >
                  <el-icon><Download /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  @click.stop="previewFile(scope.row)"
                  :disabled="scope.row.type === 'directory' || !isTextFile(scope.row.name)"
                >
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="filePreviewVisible"
      :title="`文件预览: ${previewFileName}`"
      width="700px"
      destroy-on-close
    >
      <div v-loading="filePreviewLoading" class="file-preview">
        <div class="preview-actions">
          <el-button type="primary" @click="downloadPreviewFile">
            <el-icon><Download /></el-icon> 下载文件
          </el-button>
          <el-button type="info" @click="copyPreviewContent">
            <el-icon><DocumentCopy /></el-icon> 复制内容
          </el-button>
        </div>
        <div class="preview-content">
          <pre>{{ filePreviewContent }}</pre>
        </div>
      </div>
    </el-dialog>

    <!-- 文件列表对话框 -->
    <el-dialog
      v-model="fileListDialogVisible"
      title="文件管理"
      width="700px"
      destroy-on-close
    >
      <div v-loading="fileBrowserLoading" class="file-list-container">
        <!-- 文件列表 -->
        <div class="file-list" v-if="fileList.length > 0">
          <div class="section-subtitle">文件列表</div>
          <el-table :data="fileList" size="small" border style="width: 100%">
            <el-table-column prop="name" label="文件名" />
            <el-table-column prop="size" label="大小" width="100" />
            <el-table-column label="修改时间" width="180">
              <template #default="scope">
                {{ formatFileTime(scope.row.modified) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button size="small" type="primary" @click="downloadFile(scope.row)">
                  <el-icon><Download /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-empty v-else description="没有可下载的文件" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileListDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  List,
  Box,
  Document,
  View,
  Plus,
  Delete,
  Refresh,
  Back,
  Download,
  Folder,
  DocumentCopy,
  ZoomIn
} from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import service from '~/axios'
import { ElLoading } from 'element-plus'

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 任务详情相关
const taskDetailVisible = ref(false)
const taskDetailLoading = ref(false)
const taskDetail = ref(null)

// 代码详情相关
const codeDetailVisible = ref(false)
const codeDetailLoading = ref(false)
const codeDetail = ref(null)

// 添加选中行的数据
const selectedIds = ref([])

// 文件浏览和预览相关
const fileBrowserVisible = ref(false)
const fileBrowserLoading = ref(false)
const browserFiles = ref([])
const currentPath = ref('')
const currentCodeId = ref(null)
const fileListDialogVisible = ref(false)
const fileList = ref([])

// 文件预览相关
const filePreviewVisible = ref(false)
const filePreviewLoading = ref(false)
const filePreviewContent = ref('')
const previewFileName = ref('')

// 解析savedFiles
const parsedSavedFiles = ref([])

// 获取任务列表
const fetchTasks = async (page = 1) => {
  loading.value = true
  try {
    const response = await service.get('/api/v1.0/sys/tasks', {
      params: {
        page: page,
        size: pageSize.value
      }
    })
    if (response.code === 10000) {
      tableData.value = response.data.tasks || []
      tableData.value.forEach(task => {
        task.creatorName = task.creatorName || `用户${task.creatorId}`
        task.createTime = task.timeCreated
        task.updateTime = task.timeUpdated
      })
      total.value = response.data.pagination.total
      pageSize.value = response.data.pagination.size
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    toast('错误', '获取任务列表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 获取状态类型标签样式
const getStatusType = (status) => {
  const statusMap = {
    'CREATED': 'info',     // 已创建状态
    'PENDING': 'warning',  // 等待中
    'RUNNING': 'primary',  // 运行中
    'COMPLETED': 'success',// 已完成
    'FAILED': 'danger',    // 失败
    'CANCELLED': 'info'    // 已取消
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'CREATED': '已创建',    // 添加已创建状态
    'PENDING': '等待中',
    'RUNNING': '运行中',
    'COMPLETED': '已完成',
    'FAILED': '失败',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

// 格式化日期时间
const formatDateTime = (val) => {
  if (!val) return '无'
  const date = typeof val === 'string' ? new Date(val.replace(/-/g, '/')) : new Date(val)
  if (isNaN(date.getTime())) return val
  return date.toLocaleString('zh-CN', { hour12: false })
}

// 查看任务详情
const viewTaskDetail = async (row) => {
  taskDetailVisible.value = true
  taskDetailLoading.value = true
  
  try {
    if (row.codes) {
      taskDetail.value = {
        ...row,
        createTime: row.timeCreated,
        updateTime: row.timeUpdated,
        creatorName: row.creatorName || `用户${row.creatorId}`
      }
      taskDetailLoading.value = false
      return
    }
    
    const response = await service.get(`/api/v1.0/sys/task/${row.id}`)
    if (response.code === 10000) {
      taskDetail.value = {
        ...response.data,
        createTime: response.data.timeCreated,
        updateTime: response.data.timeUpdated,
        creatorName: response.data.creatorName || `用户${response.data.creatorId}`
      }
    } else {
      toast('错误', response.message || '获取任务详情失败', 'error')
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    toast('错误', '获取任务详情失败', 'error')
  } finally {
    taskDetailLoading.value = false
  }
}

// 获取代码状态类型标签样式
const getCodeStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取代码状态文本
const getCodeStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已拒绝'
  }
  return statusMap[status] || status
}

// 修改查看代码详情函数
const viewCodeDetail = (code) => {
  // 重置savedFiles
  parsedSavedFiles.value = []
  
  // 如果代码对象已经包含完整信息，直接显示
  if (code.codeContent) {
    codeDetail.value = code
    
    // 解析savedFiles
    if (code.savedFiles) {
      try {
        if (typeof code.savedFiles === 'string') {
          parsedSavedFiles.value = JSON.parse(code.savedFiles)
        } else if (Array.isArray(code.savedFiles)) {
          parsedSavedFiles.value = code.savedFiles
        }
      } catch (error) {
        console.error('解析savedFiles失败:', error)
        parsedSavedFiles.value = []
      }
    }
    
    codeDetailVisible.value = true
    return
  }
  
  // 否则从服务器获取代码详情
  codeDetailLoading.value = true
  codeDetailVisible.value = true
  
  service.get(`/api/v1.0/sys/code/${code.id}`)
    .then(response => {
      if (response.code === 10000) {
        codeDetail.value = response.data
        
        // 解析savedFiles
        if (codeDetail.value.savedFiles) {
          try {
            if (typeof codeDetail.value.savedFiles === 'string') {
              parsedSavedFiles.value = JSON.parse(codeDetail.value.savedFiles)
            } else if (Array.isArray(codeDetail.value.savedFiles)) {
              parsedSavedFiles.value = codeDetail.value.savedFiles
            }
          } catch (error) {
            console.error('解析savedFiles失败:', error)
            parsedSavedFiles.value = []
          }
        }
      } else {
        toast('错误', response.message || '获取代码详情失败', 'error')
      }
    })
    .catch(error => {
      console.error('获取代码详情失败:', error)
      toast('错误', '获取代码详情失败', 'error')
    })
    .finally(() => {
      codeDetailLoading.value = false
    })
}

// 跳转到创建任务页面
const goToCreateTask = () => {
  router.push('/task/create')
}

// 页码变化
const handleCurrentChange = (val) => {
  fetchTasks(val)
}

// 每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchTasks(1)
}

// 添加处理选择变化的方法
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.id)
}

// 添加删除方法
const handleDelete = async (row) => {
  try {
    await showModal(`确定要删除任务 "${row.name}" 吗？`, 'warning', '提示')
    const response = await service.delete(`/api/v1.0/sys/task/${row.id}`)
    
    if (response.code === 10000) {
      toast('成功', '删除成功')
      fetchTasks(currentPage.value)
    } else {
      toast('错误', response.message || '删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error)
      toast('错误', '删除失败', 'error')
    }
  }
}

// 添加批量删除方法
const handleBatchDelete = async () => {
  if (selectedIds.value.length === 0) {
    toast('警告', '请选择要删除的任务', 'warning')
    return
  }
  
  try {
    await showModal(`确定要删除选中的 ${selectedIds.value.length} 个任务吗？`, 'warning', '提示')
    
    // 构造请求参数
    const queryParams = selectedIds.value.map(id => `ids=${id}`).join('&')
    const response = await service.delete(`/api/v1.0/sys/task/batch?${queryParams}`)
    
    if (response.code === 10000) {
      toast('成功', '批量删除成功')
      selectedIds.value = []
      fetchTasks(currentPage.value)
    } else {
      toast('错误', response.message || '批量删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除任务失败:', error)
      toast('错误', '批量删除失败', 'error')
    }
  }
}

// 刷新任务状态
const refreshTaskStatus = async (row) => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: '正在刷新任务状态...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    const response = await service.post(`/api/v1.0/sys/task/refresh/${row.id}`)
    
    if (response.code === 10000) {
      // 更新当前行数据
      Object.assign(row, response.data)
      toast('成功', '任务状态已刷新', 'success')
    } else {
      toast('错误', response.message || '刷新任务状态失败', 'error')
    }
    
    loading.close()
  } catch (error) {
    console.error('刷新任务状态失败:', error)
    toast('错误', '刷新任务状态失败', 'error')
  }
}

// 格式化文件修改时间（处理科学计数法格式的时间戳）
const formatFileTime = (timestamp) => {
  if (!timestamp) return '无';
  
  try {
    // 处理科学计数法格式的时间戳 (1.7474714980392427E9)
    const milliseconds = parseFloat(timestamp) * 1000;
    if (!isNaN(milliseconds)) {
      const date = new Date(milliseconds);
      if (!isNaN(date.getTime())) {
        return date.toLocaleString('zh-CN', { hour12: false });
      }
    }
    
    // 如果不是时间戳格式，尝试普通日期转换
    return formatDateTime(timestamp);
  } catch (e) {
    console.error('文件时间格式化错误:', e, timestamp);
    return String(timestamp);
  }
};

// 打开文件浏览器
const browseFiles = async (code) => {
  currentCodeId.value = code.id;
  fileBrowserVisible.value = true;
  fileBrowserLoading.value = true;
  currentPath.value = '';
  
  try {
    await fetchDirectoryContents('');
  } catch (error) {
    console.error('获取文件列表失败:', error);
    toast('错误', '获取文件列表失败', 'error');
  } finally {
    fileBrowserLoading.value = false;
  }
};

// 获取目录内容
const fetchDirectoryContents = async (path) => {
  if (!currentCodeId.value) return;
  
  fileBrowserLoading.value = true;
  try {
    const response = await service.get(`/api/v1.0/sys/code/browse/${currentCodeId.value}`, {
      params: { path: path || '' }
    });
    
    if (response.code === 10000) {
      // 处理API返回的数据结构
      const data = response.data;
      if (data.children && Array.isArray(data.children)) {
        browserFiles.value = data.children;
      } else if (data.type === 'directory' && data.children && Array.isArray(data.children)) {
        browserFiles.value = data.children;
      } else {
        browserFiles.value = [];
      }
      currentPath.value = path;
    } else {
      toast('错误', response.message || '获取目录内容失败', 'error');
    }
  } catch (error) {
    console.error('获取目录内容失败:', error);
    toast('错误', '获取目录内容失败', 'error');
  } finally {
    fileBrowserLoading.value = false;
  }
};

// 判断是否是文本文件
const isTextFile = (filename) => {
  const textExtensions = ['.txt', '.py', '.js', '.html', '.css', '.json', '.md', '.xml', '.csv', '.log', '.ini', '.conf', '.sh', '.bat', '.yaml', '.yml', '.env'];
  const ext = '.' + filename.split('.').pop().toLowerCase();
  return textExtensions.includes(ext);
};

// 导航到文件或目录
const navigateToFile = (file) => {
  if (file.type === 'directory') {
    const newPath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name);
    fetchDirectoryContents(newPath);
  } else {
    // 判断是否是文本文件
    if (isTextFile(file.name)) {
      previewFile(file);
    } else {
      downloadBrowserFile(file);
    }
  }
};

// 导航到上级目录
const navigateUp = () => {
  if (!currentPath.value) return;
  
  const pathParts = currentPath.value.split('/');
  pathParts.pop();
  const newPath = pathParts.join('/');
  fetchDirectoryContents(newPath);
};

// 下载文件
const downloadBrowserFile = (file) => {
  if (file.type === 'directory') return;
  
  const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name);
  downloadSpecificFile(currentCodeId.value, filePath);
};

// 下载文件列表
const downloadFiles = async (code) => {
  if (!code || !code.id) return;
  
  currentCodeId.value = code.id;
  fileListDialogVisible.value = true;
  fileBrowserLoading.value = true;
  fileList.value = [];
  
  try {
    const response = await service.get(`/api/v1.0/sys/code/browse/${code.id}`);
    
    if (response.code === 10000) {
      // 处理API返回的数据结构
      const data = response.data;
      if (data.children && Array.isArray(data.children)) {
        // 收集所有非目录文件
        const collectFiles = (items, parentPath = '') => {
          let result = [];
          for (const item of items) {
            if (item.type === 'file') {
              result.push({
                name: item.name,
                path: item.path || (parentPath ? `${parentPath}/${item.name}` : item.name),
                size: item.size,
                modified: item.modified
              });
            } else if (item.type === 'directory' && item.children) {
              const dirPath = parentPath ? `${parentPath}/${item.name}` : item.name;
              result = result.concat(collectFiles(item.children, dirPath));
            }
          }
          return result;
        };
        
        fileList.value = collectFiles(data.children);
      } else {
        fileList.value = [];
      }
    } else {
      toast('错误', response.message || '获取文件列表失败', 'error');
    }
  } catch (error) {
    console.error('获取文件列表失败:', error);
    toast('错误', '获取文件列表失败', 'error');
  } finally {
    fileBrowserLoading.value = false;
  }
};

// 下载单个文件
const downloadFile = (file) => {
  if (!file || !file.path) return;
  downloadSpecificFile(currentCodeId.value, file.path);
};

// 下载特定文件
const downloadSpecificFile = async (codeId, filePath) => {
  try {
    // 显示加载动画
    const loading = ElLoading.service({
      lock: true,
      text: '正在下载文件...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 获取文件名
    const filename = filePath.split('/').pop() || 'downloaded_file';
    
    // 使用service发送请求
    const response = await service.get(`/api/v1.0/sys/code/download/${codeId}`, {
      params: { filePath },
      responseType: 'blob'
    });
    
    // 创建 Blob URL
    const blob = new Blob([response]);
    const url = window.URL.createObjectURL(blob);
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }, 100);
    
    // 关闭加载动画
    loading.close();
    
    toast('成功', `文件 ${filename} 下载成功`, 'success');
  } catch (error) {
    console.error('下载文件失败:', error);
    toast('错误', `下载文件失败: ${error.message}`, 'error');
  }
};

// 预览文件
const previewFile = async (file) => {
  filePreviewLoading.value = true;
  filePreviewVisible.value = true;
  previewFileName.value = file.name;
  
  try {
    const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name);
    
    // 获取文件内容
    const response = await service.get(`/api/v1.0/sys/code/preview/${currentCodeId.value}`, {
      params: { filePath },
      responseType: 'text'
    });
    
    filePreviewContent.value = response;
  } catch (error) {
    console.error('预览文件失败:', error);
    toast('错误', `预览文件失败: ${error.message}`, 'error');
    filePreviewContent.value = '文件内容加载失败';
  } finally {
    filePreviewLoading.value = false;
  }
};

// 下载当前预览的文件
const downloadPreviewFile = () => {
  const filePath = currentPath.value ? `${currentPath.value}/${previewFileName.value}` : previewFileName.value;
  downloadSpecificFile(currentCodeId.value, filePath);
};

// 复制预览内容到剪贴板
const copyPreviewContent = async () => {
  try {
    // 确保内容是字符串格式
    let contentToCopy = filePreviewContent.value
    if (typeof contentToCopy === 'object') {
      contentToCopy = JSON.stringify(contentToCopy, null, 2)
    } else if (contentToCopy === null || contentToCopy === undefined) {
      contentToCopy = ''
    } else {
      contentToCopy = String(contentToCopy)
    }

    await navigator.clipboard.writeText(contentToCopy);
    toast('成功', '内容已复制到剪贴板', 'success');
  } catch (error) {
    console.error('复制失败:', error);
    toast('错误', '复制失败', 'error');
  }
};

onMounted(() => {
  fetchTasks()
})
</script>

<style scoped>
.task-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  flex: 1;
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

:deep(.el-table th) {
  font-weight: bold;
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

:deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

.file-icon {
  margin-right: 5px;
  font-size: 16px;
}

.code-tag {
  margin-right: 8px;
  margin-bottom: 5px;
  transition: all 0.3s;
  padding: 0 8px 0 5px;
}

.code-tag:hover {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

.code-tag-pyxis {
  border-left: 3px solid var(--el-color-success);
}

.code-tag-normal {
  border-left: 3px solid var(--el-color-primary);
}

.code-icon {
  margin-right: 5px;
  vertical-align: middle;
}

/* 添加代码内容样式 */
.code-content {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
}

.code-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
}

/* 深色模式下的代码内容样式 */
html.dark .code-content {
  background-color: #1e1e1e;
  color: #e6e6e6;
}

.header-right {
  display: flex;
  align-items: center;
  margin-left: 20px;
  
  .el-button {
    font-size: 15px;
    padding: 10px 20px;
    margin-left: 10px;
  }
  
  .el-icon {
    margin-right: 5px;
    font-size: 16px;
  }
}

.file-tag {
  margin: 2px;
  font-family: monospace;
}

.saved-files-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 调整描述列表标签宽度 */
:deep(.el-descriptions__label) {
  width: 120px !important;
  min-width: 120px !important;
}

/* 文件浏览相关样式 */
.file-browser {
  padding: 10px;
}

.file-browser-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.file-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.current-path {
  font-size: 16px;
  font-weight: 600;
  padding: 4px 8px;
  background-color: var(--el-fill-color);
  border-radius: 4px;
  font-family: monospace;
}

.file-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px;
}

.file-item:hover {
  color: var(--el-color-primary);
}

/* 文件预览相关样式 */
.file-preview {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
}

.preview-actions {
  margin-bottom: 10px;
}

.preview-content {
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
}

/* 深色模式下的文件预览样式 */
html.dark .file-preview {
  background-color: #1e1e1e;
  color: #e6e6e6;
}

.file-list-container {
  padding: 10px;
}

.section-subtitle {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}

.file-list {
  margin-top: 10px;
}
</style> 