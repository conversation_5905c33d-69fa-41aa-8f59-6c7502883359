<template>
  <div class="task-layout">
    <!-- 左侧区域：创建任务和任务列表 -->
    <div class="task-left-panel">
      <!-- 创建任务区域 -->
      <div class="create-task-panel">
        <h3 class="panel-title">创建任务</h3>
        <div class="upload-box" @click="triggerFileUpload">
          <el-icon><Upload /></el-icon>
          <span>上传文件夹</span>
          <input 
            ref="fileInput" 
            type="file" 
            multiple 
            webkitdirectory 
            directory
            @change="handleFileUpload" 
            style="display: none"
          />
        </div>
        <div class="selected-files">
          {{ selectedFiles.length ? `已选择 ${selectedFiles.length} 个文件` : '未选择文件' }}
        </div>
        <div v-if="selectedFolder" class="selected-folder">
          文件夹名称: {{ selectedFolder }}
        </div>
        
        <!-- 文件列表 -->
        <div v-if="selectedFiles.length > 0" class="file-list-box">
          <div v-for="(file, index) in selectedFiles" :key="index" class="file-item-row">
            <el-button 
              class="remove-file-btn" 
              type="danger" 
              size="small" 
              circle
              @click.stop="removeFile(index)"
            >
              <el-icon><Close /></el-icon>
            </el-button>
            <div class="file-item-name">{{ file.name }}</div>
            <div class="file-item-size">({{ formatFileSize(file.size) }})</div>
          </div>
        </div>
        
        <el-button 
          type="primary" 
          class="create-task-btn" 
          :disabled="!selectedFiles.length" 
          @click="createTask"
        >
          创建任务
        </el-button>
      </div>
      
      <!-- 任务列表区域 -->
      <div class="task-list-panel">
        <h3 class="panel-title">任务列表</h3>
        <div class="task-list">
          <div 
            v-for="task in tasks" 
            :key="task.task_id" 
            class="task-item"
            :class="{ 'active': selectedTask && selectedTask.task_id === task.task_id }"
            @click="selectTask(task)"
          >
            <div class="task-item-content">
              <div class="task-id">{{ task.task_id }}</div>
            </div>
            <el-button 
              type="danger" 
              size="small" 
              class="delete-btn"
              @click.stop="confirmDeleteTask(task)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右侧区域：任务详情和文件浏览 -->
    <div class="task-right-panel">
      <div v-if="!selectedTask" class="empty-state">
        <el-empty description="请选择一个任务查看详情" />
      </div>
      
      <template v-else>
        <!-- 任务详情区域 -->
        <div class="task-detail-panel">
          <div class="task-header">
            <div class="task-info">
              <div class="task-id-label">任务ID: {{ selectedTask.task_id }}</div>
              <div class="task-status">
                状态: 
                <el-tag :type="getStatusType(taskStatus)">{{ taskStatus || '未知' }}</el-tag>
              </div>
              <div class="task-created">创建时间: {{ taskCreatedTime || '-' }}</div>
              <div class="task-exit-code">退出代码: {{ taskExitCode || '-' }}</div>
              <div class="task-container" v-if="selectedTask.container_id">容器ID: {{ selectedTask.container_id }}</div>
              <div class="task-start-time" v-if="selectedTask.start_time">开始时间: {{ selectedTask.start_time }}</div>
              <div class="task-update-time" v-if="selectedTask.last_update">更新时间: {{ selectedTask.last_update }}</div>
            </div>
            <div class="task-actions">
              <el-button type="success" @click="openService" :disabled="!canAccessService">
                <el-icon><Link /></el-icon> 服务
              </el-button>
              <el-button type="warning" @click="stopTask" :disabled="!canStop">停止</el-button>
              <el-button type="danger" @click="killTask" :disabled="!canKill">强制终止</el-button>
              <el-button type="primary" @click="runTask" :disabled="!canRun">运行</el-button>
              <el-button @click="refreshTaskStatus">刷新</el-button>
            </div>
          </div>
        </div>
        
        <!-- 文件浏览区域 -->
        <div class="file-browser-panel">
          <div class="current-path">
            <div class="path-nav">
              <el-button 
                v-if="currentPath" 
                size="small" 
                icon="ArrowUp"
                @click="navigateToParent"
                type="primary"
                plain
              >
                返回上级
              </el-button>
              <span>当前路径: {{ currentPath || '/' }}</span>
            </div>
          </div>
          
          <el-empty v-if="files.length === 0" description="暂无文件" />
          
          <!-- 文件列表 -->
          <el-table v-else :data="files" style="width: 100%" border>
            <el-table-column prop="name" label="文件名" min-width="220">
              <template #default="scope">
                <div class="file-item" @click="handleFileClick(scope.row)">
                  <el-icon v-if="scope.row.is_dir" class="file-icon"><Folder /></el-icon>
                  <el-icon v-else class="file-icon"><Document /></el-icon>
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="大小" width="120" align="right">
              <template #default="scope">
                {{ formatSize(scope.row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="last_modified" label="修改时间" width="180" align="center">
              <template #default="scope">
                {{ formatDate(scope.row.last_modified) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template #default="scope">
                <div class="file-actions">
                  <el-button
                    v-if="!scope.row.is_dir"
                    type="primary"
                    size="small"
                    @click.stop="handleDownload(scope.row)"
                    plain
                  >
                    下载
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { 
  Timer, Document, Folder, Upload, Close, Link,
  ArrowDown, ArrowUp, RefreshRight
} from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import service from '~/axios'
import { ElLoading } from 'element-plus'

const route = useRoute()
const nodeId = ref('')
const tasks = ref([])
const loading = ref(false)
const selectedTask = ref(null)
const selectedFiles = ref([])
const fileInput = ref(null)

// 文件浏览相关
const files = ref([])
const filesLoading = ref(false)
const currentPath = ref('')

// 任务状态相关
const taskStatus = ref('')
const taskExitCode = ref(null)
const taskCreatedTime = ref('')

// 引入selectedFolder变量
const selectedFolder = ref('')

// 定义节点信息变量
const nodeInfo = ref({
  ipAddress: '',
  port: ''
})

// 计算属性：按钮状态
const canStop = computed(() => {
  return ['running', 'processing'].includes(taskStatus.value?.toLowerCase())
})

const canKill = computed(() => {
  return ['running', 'processing'].includes(taskStatus.value?.toLowerCase())
})

const canRun = computed(() => {
  return ['exited', 'stopped', 'created', 'finished'].includes(taskStatus.value?.toLowerCase())
})

// 计算是否可以访问服务
const canAccessService = computed(() => {
  return ['running', 'processing'].includes(taskStatus.value?.toLowerCase()) && selectedTask.value?.task_id
})

// 获取状态样式
const getStatusType = (status) => {
  if (!status) return 'info'
  
  const statusLower = status.toLowerCase()
  if (statusLower === 'running' || statusLower === 'processing') return 'success'
  if (statusLower === 'exited' || statusLower === 'finished') return 'warning'
  if (statusLower === 'error' || statusLower === 'failed') return 'danger'
  return 'info'
}

// 获取任务列表
const getTasks = async () => {
  if (!nodeId.value) return
  loading.value = true
  try {
    const res = await service.get(`/api/v1.0/pyxis/${nodeId.value}/tasks`)
    if (res.code === 10000 && res.data) {
      // 处理嵌套的数据结构
      const tasksData = res.data.data || res.data
      
      if (Array.isArray(tasksData)) {
        // 直接使用返回的任务数据
        tasks.value = tasksData.map(task => ({
          ...task,
          // 确保任务对象有基本属性
          task_id: task.task_id || task.id,
          status: task.status || 'unknown'
        }))
        
        // 按照task_id降序排序
        tasks.value.sort((a, b) => b.task_id.localeCompare(a.task_id))
      } else {
        console.error('无效的任务数据格式', tasksData)
        toast("错误", "无效的数据格式", "error")
      }
    } else {
      toast("错误", res.message || "获取任务列表失败", "error")
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    toast("错误", "获取任务列表失败", "error")
  } finally {
    loading.value = false
  }
}

// 选择任务
const selectTask = async (task) => {
  selectedTask.value = task
  currentPath.value = ''
  
  // 获取任务状态
  await getTaskStatus()
  
  // 加载文件
  await loadFiles()
  
  // 如果节点信息为空，获取节点信息
  if (!nodeInfo.value.ipAddress) {
    await getNodeInfo()
  }
}

// 获取任务状态
const getTaskStatus = async () => {
  if (!selectedTask.value) return
  
  try {
    const res = await service.get(`/api/v1.0/pyxis/${nodeId.value}/task/${selectedTask.value.task_id}/status`)
    
    if (res.code === 10000 && res.data) {
      // 处理返回的数据格式
      const statusData = res.data
      
      taskStatus.value = statusData.status || '未知'
      taskExitCode.value = statusData.exit_code
      taskCreatedTime.value = statusData.created_time || '-'
      
      // 存储其他可能需要的状态信息
      if (statusData.start_time) {
        selectedTask.value = {
          ...selectedTask.value,
          start_time: statusData.start_time,
          container_id: statusData.container_id,
          exit_code: statusData.exit_code,
          last_update: statusData.last_update
        }
      }
      
      console.log('任务状态数据:', statusData)
    } else {
      toast("错误", res.message || "获取任务状态失败", "error")
    }
  } catch (error) {
    console.error('获取任务状态失败:', error)
    taskStatus.value = '未知'
  }
}

// 刷新任务状态
const refreshTaskStatus = async () => {
  await getTaskStatus()
  toast("成功", "状态已刷新", "success")
}

// 停止任务
const stopTask = async () => {
  if (!selectedTask.value) return
  
  try {
    const res = await service.post(`/api/v1.0/pyxis/${nodeId.value}/task/${selectedTask.value.task_id}/stop`)
    
    if (res.code === 10000) {
      toast("成功", "任务已停止", "success")
      await getTaskStatus()
    } else {
      toast("错误", res.message || "停止任务失败", "error")
    }
  } catch (error) {
    console.error('停止任务失败:', error)
    toast("错误", "停止任务失败", "error")
  }
}

// 强制终止任务
const killTask = async () => {
  if (!selectedTask.value) return
  
  try {
    const res = await service.post(`/api/v1.0/pyxis/${nodeId.value}/task/${selectedTask.value.task_id}/kill`)
    
    if (res.code === 10000) {
      toast("成功", "任务已强制终止", "success")
      await getTaskStatus()
    } else {
      toast("错误", res.message || "强制终止任务失败", "error")
    }
  } catch (error) {
    console.error('强制终止任务失败:', error)
    toast("错误", "强制终止任务失败", "error")
  }
}

// 运行任务
const runTask = async () => {
  if (!selectedTask.value) return
  
  try {
    const res = await service.post(`/api/v1.0/pyxis/${nodeId.value}/task/${selectedTask.value.task_id}/launch`)
    
    if (res.code === 10000) {
      toast("成功", "任务已启动", "success")
      await getTaskStatus()
    } else {
      toast("错误", res.message || "启动任务失败", "error")
    }
  } catch (error) {
    console.error('启动任务失败:', error)
    toast("错误", "启动任务失败", "error")
  }
}

// 删除任务
const confirmDeleteTask = async (task) => {
  try {
    await showModal('确定要删除此任务吗？', 'warning', '提示')
    
    // 调用删除任务API
    const res = await service.delete(`/api/v1.0/pyxis/${nodeId.value}/task/${task.task_id}`)
    
    if (res.code === 10000) {
      toast("成功", "任务已删除", "success")
      getTasks()
      if (selectedTask.value && selectedTask.value.task_id === task.task_id) {
        selectedTask.value = null
      }
    } else {
      toast("错误", res.message || "删除任务失败", "error")
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error)
      toast("错误", "删除任务失败", "error")
    }
  }
}

// 触发文件上传
const triggerFileUpload = () => {
  fileInput.value.click()
}

// 处理文件上传
const handleFileUpload = (event) => {
  const files = event.target.files
  selectedFiles.value = Array.from(files)
  
  // 从文件路径中提取文件夹名称
  if (files.length > 0 && files[0].webkitRelativePath) {
    selectedFolder.value = files[0].webkitRelativePath.split('/')[0]
  }
}

// 创建任务
const createTask = async () => {
  if (selectedFiles.value.length === 0) return
  
  try {
    const formData = new FormData()
    
    // 添加文件，保留相对路径结构
    for (const file of selectedFiles.value) {
      // 获取相对路径，去除顶层文件夹名称
      if (file.webkitRelativePath) {
        const relativePath = file.webkitRelativePath.split('/').slice(1).join('/')
        
        // 使用相对路径创建新的File对象
        const newFile = new File([file], relativePath, {
          type: file.type,
          lastModified: file.lastModified
        })
        formData.append('files', newFile)
      } else {
        // 如果没有相对路径信息，直接添加文件
        formData.append('files', file)
      }
    }
    
    // 添加文件夹名称
    if (selectedFolder.value) {
      formData.append('folder_name', selectedFolder.value)
    }
    
    // 发送任务创建请求
    const res = await service.post(`/api/v1.0/pyxis/task/${nodeId.value}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    if (res.code === 10000) {
      toast("成功", "任务创建成功", "success")
      selectedFiles.value = []
      selectedFolder.value = ''
      fileInput.value.value = null
      getTasks()
    } else {
      toast("错误", res.message || "创建任务失败", "error")
    }
  } catch (error) {
    console.error('创建任务失败:', error)
    toast("错误", "创建任务失败", "error")
  }
}

// 加载文件
const loadFiles = async (path = '') => {
  if (!selectedTask.value) return
  
  filesLoading.value = true
  try {
    // 确保路径格式正确
    let apiPath = `/api/v1.0/pyxis/${nodeId.value}/task/${selectedTask.value.task_id}/browse/`
    
    if (path) {
      // 添加路径，不对斜杠进行编码
      apiPath += path.split('/').map(segment => encodeURIComponent(segment)).join('/') + '/'
    }
    
    const res = await service.get(apiPath)
    
    if (res.code === 10000 && res.data) {
      // 处理嵌套的目录结构
      let filesList = []
      
      // 检查返回数据格式
      if (res.data.type === 'directory' && res.data.children) {
        // 新的API格式，包含嵌套目录结构
        filesList = res.data.children.map(item => {
          // 处理时间戳，确保正确转换
          let lastModified = null
          if (item.modified) {
            try {
              const ts = parseFloat(item.modified)
              // 判断是秒级还是毫秒级时间戳
              lastModified = ts > 10000000000 ? new Date(ts) : new Date(ts * 1000)
              // 检查是否有效
              if (isNaN(lastModified.getTime()) || lastModified.getFullYear() === 1970) {
                lastModified = null
              }
            } catch (e) {
              console.error('时间戳解析错误:', item.modified)
              lastModified = null
            }
          }
          
          return {
            name: item.name,
            is_dir: item.type === 'directory',
            size: item.size || 0,
            last_modified: lastModified,
            path: item.path,
            extension: item.extension,
            content: item.content
          }
        })
      } else if (Array.isArray(res.data)) {
        // 旧的API格式，直接是文件数组
        filesList = res.data
      }
      
      // 排序：目录在前，文件在后
      filesList.sort((a, b) => {
        if (a.is_dir && !b.is_dir) return -1
        if (!a.is_dir && b.is_dir) return 1
        return a.name.localeCompare(b.name)
      })
      
      files.value = filesList
      currentPath.value = path
    } else {
      toast("错误", res.message || "获取文件列表失败", "error")
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    toast("错误", "获取文件列表失败", "error")
  } finally {
    filesLoading.value = false
  }
}

// 处理文件点击
const handleFileClick = (file) => {
  if (file.is_dir) {
    // 如果是目录，则进入该目录
    if (file.path) {
      // 如果有完整路径，直接使用
      loadFiles(file.path)
    } else {
      // 否则拼接路径
      const newPath = currentPath.value ? `${currentPath.value}/${file.name}` : file.name
      loadFiles(newPath)
    }
  } else {
    // 如果是文件，可以预览或下载
    handleDownload(file)
  }
}

// 处理文件下载
const handleDownload = async (file) => {
  if (!selectedTask.value) return
  
  // 使用文件对象上的path属性，如果存在的话
  const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name)
  
  try {
    // 显示加载动画
    const loading = ElLoading.service({
      lock: true,
      text: '正在下载文件...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    // 获取文件名
    const filename = file.name || filePath.split('/').pop() || 'downloaded_file'
    
    // 构建正确的下载路径 - 根据后端controller的路径结构
    // 注意: 后端使用的是 /download/{nodeId}/** 的路径模式
    let downloadUrl = `/api/v1.0/pyxis/download/${nodeId.value}`
    
    // 添加任务ID和文件路径
    // 需要确保任务ID和文件路径都被正确包含在URL中
    const taskPart = `/task/${selectedTask.value.task_id}`
    const filePart = `/download/${filePath}` 
    
    // 组合完整URL
    const fullUrl = `${downloadUrl}${taskPart}${filePart}`
    
    console.log('下载URL:', fullUrl)
    
    // 直接使用路径方式发送请求，不使用查询参数
    const response = await service.get(fullUrl, {
      responseType: 'blob',
    })
    
    // 创建下载链接
    const blob = new Blob([response])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }, 100)
    
    loading.close()
    toast("成功", "文件下载成功", "success")
  } catch (error) {
    console.error('文件下载失败:', error)
    toast("错误", "文件下载失败: " + (error.message || '未知错误'), "error")
  }
}

// 格式化文件大小
const formatSize = (size) => {
  // 检查size是否有效
  if (!size || isNaN(parseFloat(size))) return '-'
  
  // 尝试提取数字部分
  let numericSize = size
  if (typeof size === 'string') {
    // 如果是形如 "1.60 KB" 的字符串，提取数字部分
    const match = size.match(/^([\d.]+)/)
    if (match) {
      numericSize = parseFloat(match[1])
      
      // 根据单位调整大小
      if (size.includes('KB')) numericSize *= 1024
      else if (size.includes('MB')) numericSize *= 1024 * 1024
      else if (size.includes('GB')) numericSize *= 1024 * 1024 * 1024
    } else {
      return size // 如果无法解析，直接返回原始值
    }
  }
  
  // 格式化大小
  const bytes = parseFloat(numericSize)
  if (isNaN(bytes)) return '-'
  
  if (bytes < 1024) return bytes.toFixed(0) + ' B'
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB'
  if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB'
  return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '-'
  
  try {
    // 检查是否是有效的科学计数法格式时间戳
    let date
    if (typeof timestamp === 'number' || /^[\d.]+(?:e[+-]?\d+)?$/i.test(String(timestamp))) {
      // 尝试解析科学计数法表示的时间戳
      const ts = parseFloat(timestamp)
      
      // 检查时间戳范围判断是秒级还是毫秒级
      if (ts > 10000000000) { // 毫秒级时间戳
        date = new Date(ts)
      } else { // 秒级时间戳
        date = new Date(ts * 1000)
      }
      
      // 如果时间戳生成的日期无效或者是1970年，返回'-'
      if (isNaN(date.getTime()) || date.getFullYear() === 1970) {
        // 打印调试信息
        console.log('无效的时间戳或1970年:', timestamp, ts, date.toString())
        return '-'
      }
      
      // 有效的日期
      return date.toLocaleString()
    } else {
      // 尝试作为标准日期格式解析
      date = new Date(timestamp)
      
      if (isNaN(date.getTime())) {
        return '-'
      }
      
      return date.toLocaleString()
    }
  } catch (error) {
    console.error('日期格式化错误:', error, timestamp)
    return '-'
  }
}

// 返回上级目录
const navigateToParent = () => {
  if (!currentPath.value) return
  
  // 获取父目录路径
  const pathParts = currentPath.value.split('/')
  pathParts.pop()
  const parentPath = pathParts.join('/')
  
  // 加载父目录
  loadFiles(parentPath)
}

// 格式化文件大小的函数
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 删除单个文件
const removeFile = (index) => {
  // 创建一个文件列表的副本
  const newFiles = [...selectedFiles.value]
  newFiles.splice(index, 1)
  selectedFiles.value = newFiles
  
  // 如果删除了所有文件，也清空文件夹名称
  if (selectedFiles.value.length === 0) {
    selectedFolder.value = ''
    fileInput.value.value = null
  }
}

// 获取节点信息
const getNodeInfo = async () => {
  try {
    const res = await service.get(`/api/v1.0/sys/node/${nodeId.value}`)
    if (res.code === 10000 && res.data) {
      console.log('获取节点信息:', res.data)
      nodeInfo.value = {
        ipAddress: res.data.ipAddress,
        port: res.data.port
      }
    }
  } catch (error) {
    console.error('获取节点信息失败:', error)
  }
}

// 打开服务
const openService = () => {
  if (!selectedTask.value) return
  
  // 使用节点的ipAddress和port
  const host = `${nodeInfo.value.ipAddress}:${nodeInfo.value.port}`
  
  // 拼接服务URL
  const serviceUrl = `http://${host}/service/${selectedTask.value.task_id}/#`
  
  console.log('打开服务URL:', serviceUrl)
  
  // 在新窗口打开
  window.open(serviceUrl, '_blank')
}

onMounted(() => {
  nodeId.value = route.query.id
  getTasks()
})
</script>

<style scoped>
.task-layout {
  display: flex;
  height: calc(100vh - 240px);
  min-height: 600px;
  gap: 20px;
}

.task-left-panel {
  width: 32%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.create-task-panel {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.task-list-panel {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.task-right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.panel-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.upload-box {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-box:hover {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
}

.upload-box .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.selected-files {
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

.selected-folder {
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

.create-task-btn {
  width: 100%;
}

.task-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);
}

.task-item:hover {
  background-color: #f4f4f5;
}

.task-item.active {
  background-color: #ecf5ff;
  border-left: 3px solid var(--el-color-primary);
}

.task-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-id {
  font-family: monospace;
  font-size: 14px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-item-status {
  margin-left: 8px;
}

.delete-btn {
  font-size: 12px;
  padding: 4px 6px;
  height: 24px;
  opacity: 0.7;
}

.delete-btn:hover {
  opacity: 1;
}

.task-detail-panel {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.task-header {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.task-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-id-label {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.task-status, .task-created, .task-exit-code {
  font-size: 14px;
  color: #606266;
}

.task-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.file-browser-panel {
  flex: 1;
  padding: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.file-browser-panel :deep(.el-tabs__content) {
  flex: 1;
  overflow: auto;
}

.file-browser-panel :deep(.el-tabs) {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.file-browser-panel :deep(.el-tab-pane) {
  height: 100%;
  overflow: auto;
}

.current-path {
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: #f0f2f5;
  border-radius: 4px;
  font-family: monospace;
}

.path-nav {
  display: flex;
  align-items: center;
  gap: 12px;
}

.path-nav span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.file-item:hover {
  color: var(--el-color-primary);
}

.file-icon {
  margin-right: 8px;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.logs-container, .results-container, .workspace-container {
  height: 100%;
  overflow: auto;
}

.logs-content, .results-content, .workspace-content {
  white-space: pre-wrap;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-family: monospace;
  margin: 0;
  overflow: auto;
  height: 100%;
}

.file-preview {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  white-space: pre-wrap;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-family: monospace;
  margin: 0;
  overflow: auto;
  height: 100%;
}

.file-list-box {
  margin: 16px 0;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
}

.file-item-row {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;
  background-color: #ffffff;
  margin-bottom: 4px;
  border-radius: 4px;
}

.file-item-row:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.remove-file-btn {
  margin-right: 12px;
  flex-shrink: 0;
}

.file-item-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: monospace;
  font-size: 14px;
}

.file-item-size {
  color: #909399;
  margin-left: 8px;
  font-size: 12px;
  flex-shrink: 0;
}
</style>


