package ouc.isclab.storage.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.storage.entity.MinioConfigEntity;
import ouc.isclab.storage.pojo.MinioConfigDTO;
import ouc.isclab.storage.pojo.MinioConfigListDTO;
import ouc.isclab.storage.pojo.MinioStatusDTO;
import ouc.isclab.storage.service.MinioConfigService;
import ouc.isclab.storage.service.MinioService;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@BaseResponse
@RequestMapping("/api/v1.0/sys/storage/minio/config")
public class MinioConfigController {

    @Autowired
    private MinioService minioService;

    @Autowired
    private MinioConfigService minioConfigService;

    /**
     * 获取所有配置
     */
    @GetMapping
    public Map<String, Object> getAllConfigs(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<MinioConfigListDTO> configPage = minioConfigService.getAllConfigs(pageable);

        Map<String, Object> result = new HashMap<>();
        result.put("configs", configPage.getContent());

        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", configPage.getTotalElements());
        result.put("pagination", pagination);

        return result;
    }

    /**
     * 获取指定类型的配置
     */
    @GetMapping("/type/{type}")
    public Map<String, Object> getConfigsByType(
            @PathVariable MinioConfigEntity.ConfigType type,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<MinioConfigListDTO> configPage = minioConfigService.getConfigsByType(type, pageable);

        Map<String, Object> result = new HashMap<>();
        result.put("configs", configPage.getContent());

        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", configPage.getTotalElements());
        result.put("pagination", pagination);

        return result;
    }

    /**
     * 获取指定ID的配置
     */
    @GetMapping("/{id}")
    public MinioConfigDTO getConfigById(@PathVariable Long id) {
        return minioConfigService.getConfigById(id);
    }

    /**
     * 获取指定类型的活动配置
     */
    @GetMapping("/active/{type}")
    public MinioConfigDTO getActiveConfigByType(@PathVariable MinioConfigEntity.ConfigType type) {
        return minioConfigService.getActiveConfigByType(type);
    }

    /**
     * 创建新配置
     */
    @PostMapping
    public MinioConfigDTO createConfig(@RequestBody MinioConfigDTO configDTO) {
        return minioConfigService.createConfig(configDTO);
    }

    /**
     * 更新配置
     */
    @PutMapping("/{id}")
    public MinioConfigDTO updateConfig(@PathVariable Long id, @RequestBody MinioConfigDTO configDTO) {
        return minioConfigService.updateConfig(id, configDTO);
    }

    /**
     * 删除配置
     */
    @DeleteMapping("/{id}")
    public void deleteConfig(@PathVariable Long id) {
        minioConfigService.deleteConfig(id);
    }

    /**
     * 切换配置的活动状态
     */
    @PutMapping("/{id}/active")
    public MinioConfigDTO toggleActiveStatus(@PathVariable Long id) {
        return minioConfigService.toggleActiveStatus(id);
    }

    /**
     * 获取 MinIO 服务状态
     */
    @GetMapping("/status")
    public MinioStatusDTO getMinioStatus() {
        return minioService.checkMinioStatus();
    }
} 