package ouc.isclab.node.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import ouc.isclab.node.entity.NodeEntity;
import java.util.List;

public interface NodeRepository extends JpaRepository<NodeEntity, Long> {
    NodeEntity findByName(String name);
    
    // 统计用户创建的节点数量
    long countByCreatorId(Long creatorId);
    
    // 检查节点是否属于某个用户
    boolean existsByIdAndCreatorId(Long nodeId, Long creatorId);

    // 根据创建者ID查询节点
    Page<NodeEntity> findByCreatorId(Long creatorId, Pageable pageable);

    /**
     * 查找用户创建的所有节点ID
     */
    @Query("SELECT n.id FROM NodeEntity n WHERE n.creatorId = :creatorId")
    List<Long> findNodeIdsByCreatorId(@Param("creatorId") Long creatorId);
}
