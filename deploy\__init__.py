from . import minio_model_deploy

from pathlib import Path
import shutil
import sys
import os

def main():
    """
    初始化和启动MinIO模型部署应用
    """
    # 获取当前目录
    basedir = Path(__file__).parent
    
    # 创建静态文件目录
    staticpath = Path("./STATIC_DIR")
    staticpath.mkdir(parents=True, exist_ok=True)
    
    # 启动应用
    minio_model_deploy.main()

# 当作为pyxis框架的模块导入时的处理
# 导出需要的函数和类
get_model = minio_model_deploy.get_model 