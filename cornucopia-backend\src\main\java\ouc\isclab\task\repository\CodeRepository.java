package ouc.isclab.task.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import ouc.isclab.task.entity.CodeEntity;
import ouc.isclab.task.entity.CodeEntity.ApprovalStatus;

import java.util.List;

public interface CodeRepository extends JpaRepository<CodeEntity, Long> {
    // 查询用户的所有代码
    Page<CodeEntity> findByCreatorId(Long creatorId, Pageable pageable);
    
    // 查询用户在特定节点上的代码
    Page<CodeEntity> findByCreatorIdAndNode_Id(Long creatorId, Long nodeId, Pageable pageable);
    
    // 查询节点所有者需要审批的代码
    Page<CodeEntity> findByNode_CreatorIdAndStatus(Long nodeOwnerId, ApprovalStatus status, Pageable pageable);
    
    // 查询节点所有者的所有代码申请
    Page<CodeEntity> findByNode_CreatorId(Long nodeOwnerId, Pageable pageable);
    
    // 按状态查询用户的代码
    Page<CodeEntity> findByCreatorIdAndStatus(Long creatorId, ApprovalStatus status, Pageable pageable);
    
    // 按状态查询用户在特定节点上的代码
    Page<CodeEntity> findByCreatorIdAndNode_IdAndStatus(Long creatorId, Long nodeId, ApprovalStatus status, Pageable pageable);

    boolean existsByCreatorIdAndNodeIdInAndStatusIn(Long creatorId, List<Long> nodeIds, List<CodeEntity.ApprovalStatus> statuses);
} 