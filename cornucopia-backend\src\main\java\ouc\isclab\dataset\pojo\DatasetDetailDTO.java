package ouc.isclab.dataset.pojo;

import lombok.Data;
import lombok.Builder;
import ouc.isclab.node.entity.NodeEntity;

import java.util.Date;
import java.util.Set;

@Data
@Builder
public class DatasetDetailDTO {
    private Long id;
    private String name;
    private String path;
    private String type;
    private String url;
    private Long size;
    private String description;
    private String mockData;
    private Long creatorId;
    private String creatorName;
    private Date timeCreated;
    private Date timeUpdated;
    private Set<NodeEntity> availableNodes;
} 