<template>
  <div class="role-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Lock /></el-icon>
          <h2>角色列表</h2>
        </div>
        <div class="sub-title">管理系统中的所有角色及其权限</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.name"
                placeholder="请输入角色名称"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="action-buttons">
            <el-button type="primary" @click="handleAdd" plain round>
              <el-icon><Plus /></el-icon>新增角色
            </el-button>
            <el-button 
              type="danger" 
              :disabled="!selectedIds.length"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        border
        stripe
        :default-sort="defaultSort"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        highlight-current-row
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="name" label="角色名称" align="center" min-width="120" />
        <el-table-column prop="description" label="描述" align="center" min-width="180" show-overflow-tooltip />
        <el-table-column prop="users" label="关联用户数" align="center" width="100">
          <template #default="scope">
            {{ scope.row.users?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column 
          prop="timeCreated" 
          label="创建时间" 
          align="center" 
          width="180" 
          show-overflow-tooltip
          sortable
          :formatter="formatDateTime"
          :sort-method="sortByTime"
        />
        <el-table-column 
          prop="timeUpdated" 
          label="更新时间" 
          align="center" 
          width="180" 
          show-overflow-tooltip
          sortable
          :formatter="formatDateTime"
          :sort-method="sortByTime"
        />
        <el-table-column prop="permissions" label="权限信息" width="180">
          <template #default="scope">
            <div class="permission-info">
              <el-tag type="info" effect="plain">
                {{ scope.row.permissions?.length || 0 }} 个权限
              </el-tag>
              <el-tooltip 
                v-if="scope.row.permissions?.length"
                placement="top"
                :content="getPermissionNames(scope.row.permissions)"
              >
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  type="info"
                  size="small"
                  @click.stop="handleDetail(scope.row)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑角色" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="handleEdit(scope.row)"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="权限管理" placement="top">
                <el-button
                  type="warning"
                  size="small"
                  @click.stop="handlePermission(scope.row)"
                >
                  <el-icon><Key /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除角色" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click.stop="handleDelete(scope.row)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Search, 
  Refresh, 
  Plus, 
  Delete, 
  Edit,
  List,
  View,
  Key,
  InfoFilled
} from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import service from '~/axios'
import { ElMessageBox, ElMessage } from 'element-plus'

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const selectedIds = ref([])

// 搜索表单
const searchForm = ref({
  name: ''
})

// 获取角色列表
const fetchRoles = async (page = 1) => {
  loading.value = true
  try {
    const response = await service.get('/api/v1.0/sys/roles', {
      params: {
        page,
        size: pageSize.value
      }
    })
    if (response.code === 10000) {
      tableData.value = response.data.roles
      total.value = response.data.pagination.total
      pageSize.value = response.data.pagination.size
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    toast('错误', '获取角色列表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = async () => {
  loading.value = true
  try {
    if (!searchForm.value.name) {
      fetchRoles(currentPage.value)
      return
    }
    
    const response = await service.get(`/api/v1.0/sys/role/search/${searchForm.value.name}`)
    if (response.code === 10000) {
      if (response.data === null) {
        // 处理搜索无结果的情况
        tableData.value = []
        total.value = 0
        currentPage.value = 1
        toast('提示', '未找到匹配的角色', 'info')
      } else {
        tableData.value = [response.data]
        total.value = 1
        currentPage.value = 1
      }
    } else {
      toast('错误', response.message || '搜索失败', 'error')
    }
  } catch (error) {
    console.error('搜索角色失败:', error)
    toast('错误', '搜索角色失败', 'error')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    name: ''
  }
  // 重置后获取所有角色列表
  fetchRoles(1)
}

// 新增角色
const handleAdd = () => {
  router.push('/user/role/create')
}

// 编辑角色
const handleEdit = (row) => {
  router.push({
    path: '/user/role/update',
    query: { id: row.id }
  })
}

// 查看详情
const handleDetail = (row) => {
  router.push({
    path: '/user/role/detail',
    query: { id: row.id }
  })
}

// 删除角色
const handleDelete = async (row) => {
  try {
    await showModal('确定要删除该角色吗？', 'warning', '提示')
    const response = await service.delete(`/api/v1.0/sys/role/${row.id}`)
    if (response.code === 10000) {
      toast('成功', '删除成功')
      fetchRoles(currentPage.value)
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '删除失败', 'error')
    }
  }
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.id)
}

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedIds.value.length) return
  
  try {
    await showModal(`确定要删除选中的 ${selectedIds.value.length} 个角色吗？`, 'warning', '提示')
    
    // 构造请求参数
    const queryParams = selectedIds.value.map(id => `ids=${id}`).join('&')
    const response = await service.delete(`/api/v1.0/sys/role/batch?${queryParams}`)
    
    if (response.code === 10000) {
      toast('成功', '批量删除成功')
      // 清空选中的ID
      selectedIds.value = []
      // 刷新列表
      fetchRoles(currentPage.value)
    } else {
      toast('错误', response.message || '批量删除失败', 'error')
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '批量删除失败', 'error')
    }
  }
}

// 页码变化
const handleCurrentChange = (val) => {
  fetchRoles(val)
}

// 每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchRoles(1)
}

// 添加时间格式化函数
const formatDateTime = (row, column) => {
  const value = row[column.property]
  if (!value) return '-'
  const date = new Date(value)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 添加时间排序函数
const sortByTime = (a, b) => {
  const timeA = new Date(a.timeCreated).getTime()
  const timeB = new Date(b.timeCreated).getTime()
  return timeA - timeB
}

// 添加默认排序配置
const defaultSort = {
  prop: 'timeCreated',
  order: 'descending'
}

// 添加权限管理处理函数
const handlePermission = (row) => {
  router.push({
    path: '/user/role/permission',
    query: { id: row.id }
  })
}

// 获取权限名称列表
const getPermissionNames = (permissions) => {
  return permissions
    .map(p => p.name)
    .join('\n')
}

onMounted(() => {
  fetchRoles()
})
</script>

<style scoped>
.role-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  flex-shrink: 0;
}

.title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  .title-icon {
    margin-right: 8px;
    font-size: 24px;
    color: var(--el-color-primary);
  }
  
  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
  }
}

.sub-title {
  color: #909399;
  font-size: 14px;
  margin-left: 32px;  /* 添加左边距，与图标对齐 */
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;  /* 改为居中对齐 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;  /* 强制居中 */
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

:deep(.el-button--primary) {
  --el-button-hover-bg-color: var(--el-color-primary-light-3);
  --el-button-hover-border-color: var(--el-color-primary-light-3);
  --el-button-active-bg-color: var(--el-color-primary-dark-2);
  --el-button-active-border-color: var(--el-color-primary-dark-2);
}

:deep(.el-button--default) {
  --el-button-hover-bg-color: var(--el-color-primary-light-9);
  --el-button-hover-border-color: var(--el-color-primary-light-7);
  --el-button-hover-text-color: var(--el-color-primary);
}

:deep(.el-button--danger) {
  --el-button-hover-bg-color: var(--el-color-danger-light-3);
  --el-button-hover-border-color: var(--el-color-danger-light-3);
  --el-button-active-bg-color: var(--el-color-danger-dark-2);
  --el-button-active-border-color: var(--el-color-danger-dark-2);
}

:deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

:deep(.el-table th) {
  font-weight: bold;
}

:deep(.el-table--border) {
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

.permission-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  color: #909399;
  cursor: help;
}
</style> 