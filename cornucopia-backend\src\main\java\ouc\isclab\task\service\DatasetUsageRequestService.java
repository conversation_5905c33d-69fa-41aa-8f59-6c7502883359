package ouc.isclab.task.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.dataset.entity.DatasetEntity;
import ouc.isclab.dataset.repository.DatasetRepository;
import ouc.isclab.dataset.service.DatasetService;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.task.entity.DatasetUsageRequestEntity;
import ouc.isclab.task.entity.DatasetUsageRequestEntity.ApprovalStatus;
import ouc.isclab.task.entity.NodeAccountRequestEntity;
import ouc.isclab.task.pojo.DatasetUsageRequestDTO;
import ouc.isclab.task.repository.DatasetUsageRequestRepository;
import ouc.isclab.task.repository.NodeAccountRequestRepository;
import ouc.isclab.node.repository.NodeRepository;

import java.util.List;

@Slf4j
@Service
public class DatasetUsageRequestService {

    @Autowired
    private DatasetUsageRequestRepository datasetUsageRequestRepository;
    
    @Autowired
    private DatasetRepository datasetRepository;
    
    @Autowired
    private DatasetService datasetService;
    
    @Autowired
    private NodeAccountRequestRepository nodeAccountRequestRepository;
    
    @Autowired
    private NodeAccountRequestService nodeAccountRequestService;
    
    @Autowired
    private NodeRepository nodeRepository;
    
    /**
     * 创建数据集使用申请
     */
    @Transactional
    public DatasetUsageRequestEntity createRequest(DatasetUsageRequestDTO requestDTO, Long applicantId) {
        // 检查数据集是否存在
        DatasetEntity dataset = datasetRepository.findById(requestDTO.getDatasetId())
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));
        
        // 检查节点是否存在
        NodeEntity node = nodeRepository.findById(requestDTO.getNodeId())
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));
        
        // 检查是否已经有针对该数据集的申请且未被拒绝
        if (datasetUsageRequestRepository.existsByApplicantIdAndDataset_IdAndNodeIdAndStatusNot(
                applicantId, requestDTO.getDatasetId(), requestDTO.getNodeId(), ApprovalStatus.REJECTED)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "您已经有针对该节点上此数据集的申请");
        }
        
        // 检查数据集是否可在该节点上使用
        boolean datasetAvailableOnNode = false;
        for (NodeEntity availableNode : dataset.getAvailableNodes()) {
            if (availableNode.getId().equals(node.getId())) {
                datasetAvailableOnNode = true;
                break;
            }
        }
        
        if (!datasetAvailableOnNode) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, 
                    "数据集 " + dataset.getName() + " 不可在该节点上使用");
        }
        
        // 检查用户是否有该节点的已批准账号
        if (!nodeAccountRequestService.hasApprovedNodeAccount(applicantId, node.getId())) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "您需要先申请该节点的账号");
        }
        
        // 获取节点账号申请ID
        NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                applicantId, node.getId(), NodeAccountRequestEntity.ApprovalStatus.APPROVED);
        
        // 创建数据集使用申请
        DatasetUsageRequestEntity request = new DatasetUsageRequestEntity();
        request.setDataset(dataset);
        request.setApplicantId(applicantId);
        request.setNodeId(node.getId());
        request.setPurpose(requestDTO.getPurpose());
        request.setStatus(ApprovalStatus.PENDING);
        request.setNodeAccountRequestId(nodeAccount.getId());
        
        return datasetUsageRequestRepository.save(request);
    }
    
    /**
     * 获取用户的所有数据集使用申请
     */
    public Page<DatasetUsageRequestEntity> getUserRequests(Long userId, Pageable pageable) {
        return datasetUsageRequestRepository.findByApplicantId(userId, pageable);
    }
    
    /**
     * 获取数据集所有者需要审批的申请
     */
    public Page<DatasetUsageRequestEntity> getPendingRequestsForDatasetOwner(Long datasetOwnerId, Pageable pageable) {
        return datasetUsageRequestRepository.findByDataset_CreatorIdAndStatus(
                datasetOwnerId, ApprovalStatus.PENDING, pageable);
    }
    
    /**
     * 审批数据集使用申请
     */
    @Transactional
    public DatasetUsageRequestEntity approveRequest(Long requestId, boolean approved, String rejectReason, Long approverId) {
        // 获取申请
        DatasetUsageRequestEntity request = datasetUsageRequestRepository.findById(requestId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "申请不存在"));
        
        // 检查是否是数据集所有者
        if (!datasetService.isDatasetOwner(request.getDataset().getId(), approverId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "您不是该数据集的所有者，无权审批");
        }
        
        // 更新申请状态
        if (approved) {
            request.setStatus(ApprovalStatus.APPROVED);
        } else {
            request.setStatus(ApprovalStatus.REJECTED);
            request.setRejectReason(rejectReason);
        }
        
        request.setApproverId(approverId);
        return datasetUsageRequestRepository.save(request);
    }
    
    /**
     * 获取用户已批准的数据集
     */
    public List<DatasetUsageRequestEntity> getUserApprovedDatasets(Long userId) {
        return datasetUsageRequestRepository.findByApplicantIdAndStatus(userId, ApprovalStatus.APPROVED);
    }
    
    /**
     * 检查用户是否有权限使用数据集
     */
    public boolean hasDatasetPermission(Long userId, Long datasetId) {
        DatasetEntity dataset = datasetRepository.findById(datasetId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "数据集不存在"));
        
        // 如果是数据集创建者，直接有权限
        if (dataset.getCreatorId().equals(userId)) {
            return true;
        }
        
        // 检查是否有已批准的使用申请
        List<DatasetUsageRequestEntity> approvedRequests = datasetUsageRequestRepository.findByApplicantIdAndStatus(
                userId, ApprovalStatus.APPROVED);
        
        for (DatasetUsageRequestEntity request : approvedRequests) {
            if (request.getDataset().getId().equals(datasetId)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取数据集所有者的所有申请
     */
    public Page<DatasetUsageRequestEntity> getAllRequestsByDatasetOwner(Long datasetOwnerId, Pageable pageable) {
        return datasetUsageRequestRepository.findByDataset_CreatorId(datasetOwnerId, pageable);
    }
    
    /**
     * 按状态获取数据集所有者的申请
     */
    public Page<DatasetUsageRequestEntity> getRequestsByDatasetOwnerAndStatus(Long datasetOwnerId, ApprovalStatus status, Pageable pageable) {
        return datasetUsageRequestRepository.findByDataset_CreatorIdAndStatus(datasetOwnerId, status, pageable);
    }
    
    /**
     * 检查用户是否向数据集所有者发起过请求
     */
    public boolean hasRequestFromUserToDatasetOwner(Long applicantId, Long datasetOwnerId) {
        // 获取数据集所有者拥有的所有数据集
        List<Long> ownerDatasetIds = datasetRepository.findDatasetIdsByCreatorId(datasetOwnerId);
        
        if (ownerDatasetIds.isEmpty()) {
            return false;
        }
        
        // 检查申请者是否向这些数据集发起过请求
        return datasetUsageRequestRepository.existsByApplicantIdAndDatasetIdIn(applicantId, ownerDatasetIds);
    }
} 