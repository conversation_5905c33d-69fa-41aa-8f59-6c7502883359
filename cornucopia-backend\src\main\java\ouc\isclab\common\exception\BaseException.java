package ouc.isclab.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import ouc.isclab.common.response.ResponseCode;

/**
 * 业务异常类，继承运行时异常，确保事务正常回滚
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BaseException extends RuntimeException {

    private ResponseCode code;
    private String message;

    public BaseException(ResponseCode code) {
        this.code = code;
        this.message = code.getMessage();
    }

    public BaseException(ResponseCode code, String message) {
        this.code = code;
        this.message = message;
    }

    public BaseException(Throwable cause, ResponseCode code) {
        super(cause);
        this.code = code;
        this.message = code.getMessage();
    }

    @Override
    public String getMessage() {
        return message;
    }
}
