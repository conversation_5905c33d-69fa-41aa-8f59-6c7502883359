package ouc.isclab.common.config;

import org.springframework.web.client.RestTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import ouc.isclab.common.interceptor.ServiceLogInterceptor;
import ouc.isclab.auth.interceptor.UserAuthInterceptor;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import ouc.isclab.common.resolver.CurrentUserIdResolver;
import java.util.List;
import ouc.isclab.common.interceptor.PermissionInterceptor;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import java.util.ArrayList;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 使用该方法注入，避免在拦截器中Service组件因为时机没有注入的问题
     */
    @Bean
    public ServiceLogInterceptor serviceLogInterceptor() {
        return new ServiceLogInterceptor();
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();  // 创建并返回 RestTemplate 实例
    }

    @Bean
    public UserAuthInterceptor userAuthInterceptor() {
        return new UserAuthInterceptor();
    }

    @Bean
    public PermissionInterceptor permissionInterceptor() {
        return new PermissionInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 用户认证拦截器
        registry.addInterceptor(userAuthInterceptor())
                .addPathPatterns("/api/**")
                .excludePathPatterns("/sso/login", "/sso/register");
                
        // 权限拦截器
        registry.addInterceptor(permissionInterceptor())
                .addPathPatterns("/api/**");
                
        // 日志拦截器
        registry.addInterceptor(serviceLogInterceptor());
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new CurrentUserIdResolver());
    }

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 添加支持 application/octet-stream 的转换器
        for (HttpMessageConverter<?> converter : converters) {
            if (converter instanceof MappingJackson2HttpMessageConverter) {
                MappingJackson2HttpMessageConverter jsonConverter = (MappingJackson2HttpMessageConverter) converter;
                List<MediaType> mediaTypes = new ArrayList<>(jsonConverter.getSupportedMediaTypes());
                mediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
                jsonConverter.setSupportedMediaTypes(mediaTypes);
                break;
            }
        }
    }
}
