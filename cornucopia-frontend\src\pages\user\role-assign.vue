<template>
  <div class="role-assign-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><UserFilled /></el-icon>
          <h2>分配角色</h2>
        </div>
        <div class="sub-title">为用户分配系统角色</div>
      </div>
      <div class="header-right">
        <el-button 
          @click="$router.push('/user/list')"
          plain
          round
        >
          <el-icon><Back /></el-icon>
          返回用户列表
        </el-button>
      </div>
    </div>

    <div class="main-content">
      <el-empty 
        v-if="!hasUserId" 
        description="未找到用户ID" 
        :image-size="200"
      >
        <el-button type="primary" @click="$router.push('/user/list')" round>
          返回用户列表
        </el-button>
      </el-empty>

      <template v-else>
        <el-card class="user-info-card" shadow="hover" v-if="userData">
          <template #header>
            <div class="card-header">
              <span class="user-name">{{ userData.username }}</span>
              <el-tag :type="userData.enable ? 'success' : 'danger'" effect="plain">
                {{ userData.enable ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </template>
          
          <div class="roles-container">
            <div class="section-title">
              <el-icon><Setting /></el-icon>
              <span>角色配置</span>
            </div>
            
            <el-form :model="roleForm" label-width="0">
              <el-form-item>
                <el-checkbox-group v-model="selectedRoles">
                  <div class="role-list">
                    <el-checkbox 
                      v-for="role in availableRoles" 
                      :key="role.id"
                      :label="role.id"
                      border
                    >
                      {{ role.name }}
                      <el-tooltip 
                        v-if="role.description"
                        :content="role.description"
                        placement="top"
                      >
                        <el-icon class="info-icon"><InfoFilled /></el-icon>
                      </el-tooltip>
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item>
                <div class="form-actions">
                  <el-button 
                    type="primary" 
                    @click="handleSave" 
                    :loading="saving"
                    round
                  >
                    <el-icon><Check /></el-icon>保存配置
                  </el-button>
                  <el-button 
                    @click="resetRoles" 
                    round
                  >
                    <el-icon><RefreshRight /></el-icon>重置
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  UserFilled, 
  Back, 
  Setting, 
  Check, 
  RefreshRight,
  InfoFilled 
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const route = useRoute()
const router = useRouter()
const pageLoading = ref(false)
const saving = ref(false)
const hasUserId = ref(false)
const userData = ref(null)
const selectedRoles = ref([])
const originalRoles = ref([])
const availableRoles = ref([])
const roleForm = ref({})

// 获取用户信息
const getUserDetail = async () => {
  try {
    pageLoading.value = true
    const userId = route.query.id
    if (!userId) {
      hasUserId.value = false
      return
    }
    hasUserId.value = true
    
    const res = await service.get(`/api/v1.0/sys/user/${userId}`)
    if (res.code === 10000) {
      userData.value = res.data
      selectedRoles.value = res.data.roles.map(role => role.id)
      originalRoles.value = [...selectedRoles.value]
    } else {
      throw new Error(res.message || '获取用户信息失败')
    }
  } catch (error) {
    toast('错误', error.message, 'error')
  } finally {
    pageLoading.value = false
  }
}

// 获取所有可用角色
const getAllRoles = async () => {
  try {
    const res = await service.get('/api/v1.0/sys/roles')
    if (res.code === 10000) {
      availableRoles.value = res.data.roles
    } else {
      throw new Error(res.message || '获取角色列表失败')
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    toast('错误', '获取角色列表失败', 'error')
  }
}

// 保存角色配置
const handleSave = async () => {
  if (!selectedRoles.value.length) {
    toast('警告', '请至少选择一个角色', 'warning')
    return
  }

  try {
    saving.value = true
    const res = await service.put(`/api/v1.0/sys/user/${route.query.id}/roles`, {
      roleIds: selectedRoles.value
    })
    
    if (res.code === 10000) {
      toast('成功', '角色分配成功', 'success')
      originalRoles.value = [...selectedRoles.value]
    } else {
      throw new Error(res.message || '保存失败')
    }
  } catch (error) {
    toast('错误', error.message, 'error')
  } finally {
    saving.value = false
  }
}

// 重置角色选择
const resetRoles = () => {
  selectedRoles.value = [...originalRoles.value]
}

onMounted(() => {
  if (!route.query.id) {
    hasUserId.value = false
    toast('错误', '未找到用户ID', 'error')
    return
  }
  hasUserId.value = true
  getUserDetail()
  getAllRoles()
})
</script>

<style scoped>
.role-assign-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  .title-icon {
    margin-right: 8px;
    font-size: 24px;
    color: var(--el-color-primary);
  }
  
  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
  }
}

.sub-title {
  color: #909399;
  font-size: 14px;
}

.user-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .user-name {
    font-size: 18px;
    font-weight: 600;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
}

.role-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.info-icon {
  margin-left: 4px;
  font-size: 14px;
  color: #909399;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
}
</style> 