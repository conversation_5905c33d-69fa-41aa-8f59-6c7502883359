from .scrip_slim import create_app, run_app, TemplateLoader
from pathlib import Path


def get_model():
    import os
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

    cache_dir = Path("./models")
    cache_dir.mkdir(parents=True, exist_ok=True)
    os.environ["TRANSFORMERS_CACHE"] = str(cache_dir)

    from transformers import pipeline
    from PIL import Image
    model_name = "facebook/convnext-tiny-224"
    classifier = pipeline("image-classification", model=model_name)

    class FoodClassifier:
        def __init__(self, classifier):
            self.classifier = classifier

        def pipe(self, file: str, top_k: int = 3, **kwargs):
            image = Image.open(file)
            predictions = self.classifier(image, top_k=top_k)
            return {pred["label"]: pred["score"] for pred in predictions}

    return FoodClassifier(classifier)


def main(port=8000):

    base_path = Path(__file__).parent

    app = create_app(
        get_model=get_model,
        input_template_config={
            "title": "Food Classification",
            "fields": [{
                "type": "img",
                "label": "Upload Image",
                "name": "file",
                "required":True
            }]
        },
        output_template_generate=TemplateLoader(
            base_path / "custom/clsfood_output.jinja2"),
        timeout_minutes=120
    )
    run_app(app, port)
