package ouc.isclab.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ouc.isclab.common.entity.BaseEntity;
import ouc.isclab.task.entity.TaskEntity;

/**
 * 模型实体
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_MODEL")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class ModelEntity extends BaseEntity {

    @Column(nullable = false)
    private String name; // 模型名称
    
    @Lob
    @Column(columnDefinition = "text")
    private String description; // 模型描述
    
    @Column(name = "creator_id", nullable = false)
    private Long creatorId; // 创建者ID

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id")
    private TaskEntity task; // 关联的任务

    @Column(nullable = false)
    private String modelPath; // 模型文件路径

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ModelStatus status; // 模型状态

    public enum ModelStatus {
        CREATED,    // 已创建
        TRAINING,   // 训练中
        COMPLETED,  // 已完成
        FAILED      // 失败
    }
} 