<style>
    .image-preview {
        max-width: 100%;
        max-height: 50vh;
        margin-top: var(--space-sm);
        display: none;
        border-radius: var(--radius-sm);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .drag-over {
        border-color: var(--primary-color) !important;
        background-color: rgba(66, 133, 244, 0.1) !important;
    }
</style>

<h2>{{ config.title if config.title is defined else 'Model Service' }}</h2>


{% macro form_field(field) %}

{% if field.get('label') and field.type !='checkbox' %}<label for="{{ field.name }}">{{ field.label }}</label>{% endif
%}

{% if field.type == 'file' %}
<div class="file-upload-wrapper" id="wrapper-{{ field.name }}">
    <label for="{{ field.name }}" class="file-upload-button">Select file</label>
    <input type="file" id="{{ field.name }}" name="{{ field.name }}" class="file-input" {% if field.accept
        %}accept="{{ field.accept }}" {% endif %}>
    <div class="file-name-display" id="display-{{ field.name }}">no file selected</div>
</div>

{% elif field.type == 'multifile' %}
<div class="file-upload-wrapper" id="wrapper-{{ field.name }}">
    <label for="{{ field.name }}" class="file-upload-button">Select files</label>
    <input type="file" id="{{ field.name }}" name="{{ field.name }}" class="file-input" multiple {% if field.accept
        %}accept="{{ field.accept }}" {% endif %}>
    <div class="file-name-display" id="display-{{ field.name }}">no files selected</div>
</div>

{% elif field.type == 'select' %}
<select id="{{ field.name }}" name="{{ field.name }}">
    {% for option in field.options %}
    <option value="{{ option.value }}" {% if option.get('selected') %}selected{% endif %}>
        {{ option.label }}
    </option>
    {% endfor %}
</select>

{% elif field.type == 'radio' %}
<div class="radio-group">
    {% for option in field.options %}
    <div class="radio-option">
        <input type="radio" id="{{ field.name }}-{{ loop.index }}" name="{{ field.name }}" value="{{ option.value }}" {%
            if option.get('checked') %}checked{% endif %}>
        <label for="{{ field.name }}-{{ loop.index }}">{{ option.label }}</label>
    </div>
    {% endfor %}
</div>

{% elif field.type == 'checkbox' %}
{% if 'options' in field %}
{% if field.get('label') %}<label for="{{ field.name }}">{{ field.label }}</label>{% endif %}
<div class="checkbox-group">
    {% for option in field.options %}
    <div class="checkbox-option">
        <input type="checkbox" id="{{ field.name }}-{{ loop.index }}" name="{{ field.name }}" value="{{ option.value }}"
            {% if option.get('checked') %}checked{% endif %}>
        <label for="{{ field.name }}-{{ loop.index }}">{{ option.label }}</label>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="checkbox-option">
    <input type="checkbox" id="{{ field.name }}" name="{{ field.name }}" value="{{ field.get('value', 'on') }}" {% if
        field.get('checked') %}checked{% endif %} {% if field.get('required') %}required{% endif %}>
    <label for="{{ field.name }}">{{ field.label }}</label>
</div>
{% endif %}

{% elif field.type == 'img' %}
{% if field.get('src') %}
<div style="text-align: center;">
    <img class="image-preview" alt="Image" src="{{field.src}}" style="display:inline-block;">
</div>
{% else %}
<div class="file-upload-wrapper" id="{{ field.name }}-wrapper" data-field-name="{{ field.name }}">
    <label for="{{ field.name }}" class="file-upload-button">
        {{ field.get('placeholder', 'Click to select or drag & drop an image') }}
    </label>
    <input type="file" id="{{ field.name }}" name="{{ field.name }}" class="file-input img-upload-field"
        accept="image/*">
    <div class="file-name-display" id="{{ field.name }}-name-display">
        {{ field.get('default_text', 'No image selected') }}
    </div>
</div>
<div style="text-align: center;">
    <img id="{{ field.name }}-preview" class="image-preview" alt="Image preview" style="display: none;">
</div>
{%endif%}

{% elif field.type == 'html' %}
<div>{{ field.content | safe }}</div>

{% elif field.type == 'table' %}
<div class="table-wrapper" id="{{ field.name }}-wrapper" data-field-name="{{ field.name }}">
    <table class="data-table">
        <thead>
            <tr>
                {% for header in field.headers %}
                <th>{{ header }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for row in field.rows %}
            <tr>
                {% for header in field.headers %}
                {% if row is mapping %}
                <td data-label="{{ header }}">{{ row[header] }}</td>
                {% else %}
                <td data-label="{{ header }}">{{ row[loop.index0] }}</td>
                {% endif %}
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>


{% else %}
{% if field.type == 'color' %}
<div class="color-picker-wrapper">
    <input type="color" id="{{ field.name }}" name="{{ field.name }}" value="{{ field.get('value', '#4285f4') }}"
        class="color-input">
</div>
{% else %}
<input type="{{ field.type }}" id="{{ field.name }}" name="{{ field.name }}" {% if field.placeholder
    %}placeholder="{{ field.placeholder }}" {% endif %} {% if field.value %}value="{{ field.value }}" {% endif %}>
{% endif %}
{% endif %}
{% endmacro %}




<form id="pipe-form">
    {% for field in config.fields %}
    <div class="form-group">
        {{ form_field(field) }}
    </div>
    {% endfor %}
    <button type="submit">Submit</button>
</form>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.file-input').forEach(input => {
            const wrapper = input.closest('.file-upload-wrapper')
            const display = wrapper.querySelector('.file-name-display')

            input.addEventListener('change', function (e) {
                if (this.files.length > 0) {
                    wrapper.classList.add('has-file')
                    if (this.hasAttribute('multiple')) {
                        display.textContent = `${this.files.length} files selected`
                    } else {
                        display.textContent = this.files[0].name
                    }
                } else {
                    wrapper.classList.remove('has-file')
                    display.textContent = this.hasAttribute('multiple') ? 'no files selected' : 'no file selected'
                }
            })

            wrapper.addEventListener('dragover', (e) => {
                e.preventDefault()
                wrapper.classList.add('drag-over')
            })

            wrapper.addEventListener('dragleave', () => {
                wrapper.classList.remove('drag-over')
            })

            wrapper.addEventListener('drop', (e) => {
                e.preventDefault()
                wrapper.classList.remove('drag-over')
                input.files = e.dataTransfer.files
                input.dispatchEvent(new Event('change'))
            })
        })



        document.querySelectorAll('.img-upload-field').forEach(function (fileInput) {
            const fieldName = fileInput.id
            const wrapper = document.getElementById(`${fieldName}-wrapper`)
            const display = document.getElementById(`${fieldName}-name-display`)
            const preview = document.getElementById(`${fieldName}-preview`)

            if (!wrapper || !display || !preview) return

            fileInput.addEventListener('change', function (e) {
                if (this.files.length > 0) {
                    wrapper.classList.add('has-file')
                    display.textContent = this.files[0].name

                    const file = this.files[0]
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader()
                        reader.onload = function (e) {
                            preview.src = e.target.result
                            preview.style.display = 'inline-block'
                        }
                        reader.readAsDataURL(file)
                    }
                } else {
                    wrapper.classList.remove('has-file')
                    display.textContent = wrapper.dataset.defaultText || 'No image selected'
                    preview.style.display = 'none'
                }
            })
            wrapper.addEventListener('dragover', (e) => {
                e.preventDefault()
                wrapper.classList.add('drag-over')
            })

            wrapper.addEventListener('dragleave', () => {
                wrapper.classList.remove('drag-over')
            })

            wrapper.addEventListener('drop', (e) => {
                e.preventDefault()
                wrapper.classList.remove('drag-over')

                if (e.dataTransfer.files.length) {
                    fileInput.files = e.dataTransfer.files
                    fileInput.dispatchEvent(new Event('change'))
                }
            })

            wrapper.dataset.defaultText = display.textContent
        })

        {% if config.get('history') %}

        function append_history(innerhtml) {
            const historyContainer = document.createElement('div')
            historyContainer.className = 'container results-container'
            document.getElementById('output-container').after(
                historyContainer
            )

            historyContainer.innerHTML = innerhtml
        }


        let currentContent = ''
        const outputContent = document.getElementById('output-content')

        const observer = new MutationObserver(function (mutations) {
            mutations.forEach(() => {
                if (outputContent.innerHTML !== currentContent && outputContent.innerHTML.trim().startsWith(`<div class="results">`)) {
                    if (currentContent) {
                        append_history(currentContent)
                    }

                    currentContent = outputContent.innerHTML.replace(/<h2>.*?<\/h2>/, "")
                }
            })
        })

        observer.observe(outputContent, {
            childList: true,
            subtree: true
        })

        {% endif %}

    });
</script>