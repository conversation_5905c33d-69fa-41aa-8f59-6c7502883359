package ouc.isclab.system.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "SYS_PERMISSION")
public class PermissionEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String code;        // 权限标识，如 "user:view"

    @Column(nullable = false)
    private String name;        // 权限名称，如 "查看用户"

    private String description; // 权限描述
} 