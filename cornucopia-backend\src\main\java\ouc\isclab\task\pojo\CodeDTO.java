package ouc.isclab.task.pojo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CodeDTO {
    private Long id;                // 代码ID
    private Long nodeId;            // 节点ID
    private String nodeName;        // 节点名称
    private String funcName;        // 函数名称
    private String codeContent;     // 代码内容
    private String description;     // 代码描述
    private Long creatorId;         // 创建者ID
    private String creatorName;     // 创建者名称
    private String status;          // 状态
    private String rejectReason;    // 拒绝原因
    private Long approverId;        // 审批人ID
    private String approverName;    // 审批人名称
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime; // 更新时间
    private String syceeCodeId;
    private String syceeRequestId;
    private String syceeJobId;
    private List<String> inputKwargs; // 函数输入参数列表
    private String nodeType; // 节点类型
    private String savedFiles; // 保存的文件，JSON格式
    private String pyxisTaskId; // Pyxis系统中的任务ID


} 