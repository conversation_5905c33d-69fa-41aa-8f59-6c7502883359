package ouc.isclab.auth.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import ouc.isclab.auth.entity.TokenEntity;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface TokenRepository extends JpaRepository<TokenEntity, Long> {
    Optional<TokenEntity> findByTokenAndExpireTimeAfter(String token, Date now);
    
    void deleteByToken(String token);
    
    @Query("SELECT t.userId FROM TokenEntity t WHERE t.token = :token AND t.expireTime > :now")
    Optional<Long> findUserIdByValidToken(String token, Date now);
    
    void deleteByUserId(Long userId);
    
    List<TokenEntity> findByUserId(Long userId);
    
    Optional<TokenEntity> findByToken(String token);
    
    void deleteByExpireTimeBefore(Date date);
} 