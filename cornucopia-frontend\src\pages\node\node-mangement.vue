<template>
  <div class="node-management-container">
    <el-empty 
      v-if="!hasNodeId" 
      description="未找到节点ID" 
      :image-size="200"
    >
      <el-button type="primary" @click="$router.push('/node/my')" round>
        返回节点列表
      </el-button>
    </el-empty>

    <div v-else class="management-content">
      <div class="page-header">
        <div class="header-left">
          <div class="title-wrapper">
            <el-icon class="title-icon"><Operation /></el-icon>
            <h2>操作节点</h2>
          </div>
          <div class="sub-title">Sycee控制台-对节点进行操作和管理</div>
        </div>
        <div class="header-right">
          <el-button 
            @click="$router.push('/node/my')"
            plain
            round
          >
            <el-icon><Back /></el-icon>
            返回节点列表
          </el-button>
        </div>
      </div>

      <!-- 添加tabs导航 -->
      <el-card class="content-card" shadow="hover">
        <el-tabs v-model="activeTab" class="management-tabs" type="border-card" @tab-click="handleTabClick">
          <el-tab-pane 
            v-for="(tab, name) in tabs" 
            :key="name"
            :name="name"
            :disabled="!isAdmin"
          >
            <template #label>
              <div class="tab-label">
                <el-icon><component :is="tab.icon" /></el-icon>
                <span>{{ tab.label }}</span>
              </div>
            </template>
          </el-tab-pane>
        </el-tabs>

        <!-- 统一的内容区域 -->
        <div class="tab-content" v-loading="loading">
          <template v-if="!currentComponent">
            <div class="welcome-page">
              <el-empty :image-size="200">
                <template #description>
                  <div class="welcome-desc">
                    <h3>欢迎使用Sycee控制台</h3>
                    <p>请先完成节点登录验证</p>
                    <p class="sub-desc">登录后可以访问更多功能</p>
                  </div>
                </template>
                <el-button 
                  type="primary" 
                  @click="handleLogin"
                  :loading="loading"
                  size="large"
                  round
                >
                  立即登录
                </el-button>
              </el-empty>
            </div>
          </template>
          <component 
            v-else
            :is="currentComponent"
            :node-id="route.query.id"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, shallowRef, defineAsyncComponent, nextTick } from 'vue'
import { 
  Operation, 
  Back,
  InfoFilled,
  DataLine,
  Message,
  Document,
  Timer,
  User,
  Monitor
} from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { toast } from '~/composables/util'
import sycee from '~/sycee.js'

// 异步加载组件
const components = {
  metadata: defineAsyncComponent(() => 
    import('./components/NodeMetadata.vue')
  ),
  datasets: defineAsyncComponent(() => 
    import('./components/NodeDatasets.vue')
  ),
  requests: defineAsyncComponent(() => 
    import('./components/NodeRequests.vue')
  ),
  codes: defineAsyncComponent(() => 
    import('./components/NodeCodes.vue')
  ),
  jobs: defineAsyncComponent(() => 
    import('./components/NodeJobs.vue')
  ),
  users: defineAsyncComponent(() => 
    import('./components/NodeUsers.vue')
  ),
  Workers: defineAsyncComponent(() => 
    import('./components/NodeWorkers.vue')
  ),
}

const route = useRoute()
const hasNodeId = ref(false)
const loading = ref(false)
const activeTab = ref('metadata')
const currentComponent = shallowRef(null)
const isAdmin = ref(false)

// 添加 tabs 配置对象
const tabs = {
  metadata: {
    label: '站点信息',
    icon: 'InfoFilled'
  },
  datasets: {
    label: '数据集',
    icon: 'DataLine'
  },
  requests: {
    label: '请求',
    icon: 'Message'
  },
  codes: {
    label: '代码',
    icon: 'Document'
  },
  jobs: {
    label: '作业',
    icon: 'Timer'
  },
  Workers:{
    label: '执行器',
    icon: 'Monitor'
  },
  users: {
    label: '用户',
    icon: 'User'
  }
}

// 登录处理函数
const handleLogin = async () => {
  try {
    loading.value = true
    const res = await sycee(route.query.id, 'user_role', {}, false)
    if (res.code === 10000 && res.data === 'ADMIN') {
      isAdmin.value = true
      toast('成功', '节点登录成功', 'success')
      // 登录成功后自动加载 metadata
      activeTab.value = 'metadata'
      currentComponent.value = components.metadata
    } else {
      toast('错误', '权限不足', 'error')
    }
  } catch (error) {
    console.error('登录失败:', error)
    toast('错误', '登录失败', 'error')
  } finally {
    loading.value = false
  }
}

const handleTabClick = async (tab) => {
  if (!isAdmin) {
    toast('警告', '请先登录', 'warning')
    return
  }
  
  if (!components[tab.props.name]) return
  
  loading.value = true
  try {
    currentComponent.value = null
    await nextTick()
    currentComponent.value = components[tab.props.name]
  } catch (error) {
    console.error('加载组件失败:', error)
    toast('错误', '加载组件失败', 'error')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  if (route.query.id) {
    hasNodeId.value = true
    // 不自动加载任何组件，直到用户登录
  } else {
    hasNodeId.value = false
    toast('错误', '未找到节点ID', 'error')
  }
})
</script>

<style scoped>
.node-management-container {
  padding: 20px;
}

.management-content {
  min-height: calc(100vh - 120px);
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  flex-shrink: 0;
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 内容区域样式 */
.content-card {
  :deep(.el-card__body) {
    padding: 0;
  }
}

/* Tab样式 */
:deep(.el-tabs--border-card) {
  border: none;
  background: transparent;
  
  > .el-tabs__header {
    background-color: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    margin: 0;
    height: auto;
  }
  
  > .el-tabs__content {
    padding: 0;
    border: none;
  }

  .el-tabs__active-bar {
    height: 3px;
    background-color: var(--el-color-primary);
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    bottom: 0;
    width: 100%;
  }
}

:deep(.el-tabs__nav) {
  width: 100%;
  display: flex;
}

:deep(.el-tabs__item) {
  flex: 1;
  text-align: center;
  padding: 0 8px;
  position: relative;
  
  &.is-active {
    color: var(--el-color-primary);
    background: var(--el-bg-color-page);
    border-bottom: none;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background-color: var(--el-color-primary);
      transition: all 0.3s;
    }
  }
  
  @media (max-width: 768px) {
    padding: 0 4px;
  }
}

.tab-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  .el-icon {
    font-size: 18px;
  }
  
  span {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
  }
}

/* 调整nav容器 */
:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

/* 调整内容区域 */
.tab-content {
  padding: 32px 24px;
  min-height: 600px;
  margin-top: 0;
}

/* 添加响应式布局样式 */
@media (max-width: 768px) {
  .node-management-container {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    
    .header-right {
      align-self: flex-start;
    }
  }
  
  .tab-content {
    padding: 16px 12px;
  }
}

.welcome-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  
  :deep(.el-empty__description) {
    margin: 20px 0;
  }

  .welcome-desc {
    text-align: center;
    
    h3 {
      font-size: 20px;
      color: var(--el-text-color-primary);
      margin-bottom: 16px;
    }
    
    p {
      margin: 8px 0;
      font-size: 16px;
      color: var(--el-text-color-regular);
      
      &.sub-desc {
        font-size: 14px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}
</style>