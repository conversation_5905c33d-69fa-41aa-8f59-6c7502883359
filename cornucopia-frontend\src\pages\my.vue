<template>
  <div class="my-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><UserFilled /></el-icon>
          <h2>我的信息</h2>
        </div>
        <div class="sub-title">查看和管理个人信息</div>
      </div>
    </div>

    <el-card class="info-card" shadow="hover" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span class="header-title">
            <el-icon><InfoFilled /></el-icon>
            基本信息
          </span>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户名">
          {{ userInfo.username }}
        </el-descriptions-item>
        <el-descriptions-item label="姓名">
          {{ userInfo.fullname || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          {{ userInfo.email || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="在线状态">
          <el-tag :type="userInfo.online ? 'success' : 'info'" size="small">
            {{ userInfo.online ? '在线' : '离线' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ userInfo.timeCreated }}
        </el-descriptions-item>
        <el-descriptions-item label="最后登录">
          {{ userInfo.lastLogin }}
        </el-descriptions-item>
        <el-descriptions-item label="角色" :span="2">
          <el-tag 
            v-for="role in userInfo.roles" 
            :key="role.id"
            class="role-tag"
            type="primary"
            effect="plain"
          >
            {{ role.name }}
            <el-tooltip 
              v-if="role.description" 
              effect="dark" 
              :content="role.description" 
              placement="top"
            >
              <el-icon class="info-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </el-tag>
          <span v-if="!userInfo.roles?.length" class="no-data">暂无角色</span>
        </el-descriptions-item>
      </el-descriptions>

      <div class="action-buttons">
        <el-button type="primary" @click="handleEdit" round>
          <el-icon><Edit /></el-icon>编辑信息
        </el-button>
        <el-button type="warning" @click="handleChangePwd" round>
          <el-icon><Lock /></el-icon>修改密码
        </el-button>
        <el-button type="danger" @click="handleDeactivate" round>
          <el-icon><Delete /></el-icon>注销账号
        </el-button>
      </div>
    </el-card>

    <el-dialog
      v-model="deactivateDialogVisible"
      title="注销账号确认"
      width="400px"
      center
    >
      <div class="deactivate-dialog-content">
        <el-icon class="warning-icon" :size="48"><Warning /></el-icon>
        <div class="warning-text">
          <p class="warning-title">确定要注销账号吗？</p>
          <p class="warning-desc">注销后账号将被永久删除，无法恢复！</p>
          <p class="warning-confirm">请输入 <span class="confirm-text">DEACTIVATE</span> 以确认操作</p>
          <el-input
            v-model="confirmText"
            placeholder="请输入 DEACTIVATE"
            size="large"
            class="confirm-input"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deactivateDialogVisible = false" round>取消</el-button>
          <el-button
            type="danger"
            @click="confirmDeactivate"
            :loading="deactivating"
            :disabled="confirmText !== 'DEACTIVATE'"
            round
          >
            确认注销
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { UserFilled, InfoFilled, Edit, Lock, Delete, Warning } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const router = useRouter()
const loading = ref(false)
const userInfo = ref({
  username: '',
  fullname: '',
  email: '',
  online: false,
  lastLogin: '',
  timeCreated: '',
  timeUpdated: '',
  roles: []
})

const confirmText = ref('')
const deactivateDialogVisible = ref(false)
const deactivating = ref(false)

// 获取用户信息
const getUserInfo = async () => {
  try {
    loading.value = true
    const res = await service.get('/api/v1.0/sys/user')
    if (res.code === 10000) {
      const formatTime = (timeStr) => {
        if (!timeStr) return '-'
        const date = new Date(timeStr)
        return new Date(date.getTime()).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        })
      }
      
      userInfo.value = {
        username: res.data.username,
        fullname: res.data.fullname,
        email: res.data.email,
        online: res.data.enable,
        lastLogin: formatTime(res.data.lastLogin),
        timeCreated: formatTime(res.data.createTime),
        roles: res.data.roles || []
      }
    } else {
      toast('获取用户信息失败', res.message, 'error')
    }
  } catch (error) {
    toast('获取用户信息失败', error.message, 'error')
  } finally {
    loading.value = false
  }
}

// 编辑信息
const handleEdit = () => {
  router.push('/my/update')
}

// 修改密码
const handleChangePwd = () => {
  router.push('/my/password')
}

// 处理注销按钮点击
const handleDeactivate = () => {
  deactivateDialogVisible.value = true
  confirmText.value = ''
}

// 确认注销
const confirmDeactivate = async () => {
  try {
    deactivating.value = true
    const res = await service.get('/sso/deactivate')
    
    if (res.code === 10007) {
      toast('成功', '账号已注销', 'success')
      router.push('/login')
    } else {
      toast('注销失败', res.message, 'error')
    }
  } catch (error) {
    toast('错误', error.message, 'error')
  } finally {
    deactivating.value = false
    deactivateDialogVisible.value = false
  }
}

onMounted(() => {
  getUserInfo()
})
</script>

<style scoped>
.my-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.title-icon {
  margin-right: 8px;
  font-size: 24px;
  color: var(--el-color-primary);
}

h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.sub-title {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.info-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.header-title .el-icon {
  margin-right: 6px;
  font-size: 18px;
  color: var(--el-color-primary);
}

.role-tag {
  margin-right: 8px;
  margin-bottom: 4px;
  display: inline-flex;
  align-items: center;
}

.info-icon {
  margin-left: 4px;
  font-size: 14px;
  cursor: help;
}

.no-data {
  color: var(--el-text-color-secondary);
  font-size: 13px;
  font-style: italic;
}

.action-buttons {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

:deep(.el-descriptions__label) {
  width: 120px;
  justify-content: flex-end;
}

.deactivate-dialog-content {
  display: flex;
  align-items: center;
  padding: 20px 0;
}

.warning-icon {
  color: var(--el-color-danger);
  margin-right: 16px;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 8px;
  color: var(--el-text-color-primary);
}

.warning-desc {
  font-size: 14px;
  margin: 0;
  color: var(--el-text-color-secondary);
}

.warning-confirm {
  font-size: 14px;
  margin: 16px 0 8px;
  color: var(--el-text-color-regular);
}

.confirm-text {
  color: var(--el-color-danger);
  font-weight: 600;
  font-family: monospace;
  background-color: var(--el-fill-color-light);
  padding: 2px 6px;
  border-radius: 4px;
}

.confirm-input {
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
}

/* 深色模式适配 */
html.dark .info-card {
  border-color: var(--el-border-color-darker);
}

html.dark .warning-title {
  color: var(--el-text-color-primary);
}

html.dark .warning-desc {
  color: var(--el-text-color-secondary);
}

html.dark .confirm-text {
  background-color: var(--el-fill-color-darker);
}
</style>