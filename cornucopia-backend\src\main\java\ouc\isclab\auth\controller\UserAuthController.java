package ouc.isclab.auth.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.common.response.ResponseResult;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.system.pojo.UserInfo;
import ouc.isclab.auth.service.UserAuthService;
import ouc.isclab.system.service.UserService;
import ouc.isclab.auth.service.TokenService;
import ouc.isclab.system.pojo.RoleInfo;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/sso")
public class UserAuthController {
    @Autowired
    private UserAuthService userAuthService;

    @Autowired
    private UserService userService;

    @Autowired
    private TokenService tokenService;

    @PostMapping("/login")
    public Object login(@RequestBody Map<String, String> userLoginInfo) {
        log.info(userLoginInfo.toString());

        UserEntity userEntity = userAuthService.validate(userLoginInfo.get("username"), userLoginInfo.get("password"));

        if(userEntity == null) {
            throw new BaseException(ResponseCode.LOGIN_FAILED, "用户名或密码错误");
        }

        // 生成token
        String token = tokenService.createToken(userEntity.getId());
        
        // 更新用户登录状态
        userService.updateUserLoginStatus(userEntity.getUsername(), true);

        // 构建返回数据
        Map<String, Object> data = new HashMap<>();
        data.put("token", token);
        data.put("userInfo", UserInfo.builder()
                .userId(userEntity.getId())
                .username(userEntity.getUsername())
                .fullname(userEntity.getFullname())
                .email(userEntity.getEmail())
                .enable(userEntity.isEnable())
                .roles(userEntity.getRoles().stream()
                        .map(role -> RoleInfo.builder()
                                .id(role.getId())
                                .name(role.getName())
                                .permissions(role.getPermissions().stream()
                                        .map(permission -> permission.getCode())
                                        .collect(Collectors.toSet()))
                                .build())
                        .collect(Collectors.toSet()))
                .build());

        return new ResponseResult(ResponseCode.LOGIN_SUCCESS.getCode(),
                ResponseCode.LOGIN_SUCCESS.getMessage(),
                data);
    }

    @GetMapping("/logout")
    public Object logout(@RequestHeader("token") String token) {
        try {
            tokenService.removeToken(token);
            return new ResponseResult(ResponseCode.LOGOUT_SUCCESS.getCode(),
                    ResponseCode.LOGOUT_SUCCESS.getMessage(),
                    null);
        } catch (Exception e) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "登出失败");
        }
    }

    @PostMapping("/register")
    public Object register(@RequestBody Map<String, Object> userRegisterInfo) {
        // 提取必填字段
        String username = (String) userRegisterInfo.get("username");
        String password = (String) userRegisterInfo.get("password");
        
        // 设置默认角色ID为1
        Long roleId = 1L;
        Set<Long> validRoleIds = Set.of(1L, 2L, 3L, 4L);
        
        String roleIdStr = (String) userRegisterInfo.get("roleId");
        if (roleIdStr != null && !roleIdStr.isEmpty()) {
            try {
                Long parsedRoleId = Long.parseLong(roleIdStr);
                if (validRoleIds.contains(parsedRoleId)) {
                    roleId = parsedRoleId;
                }
            } catch (NumberFormatException e) {
                log.warn("Invalid roleId format: {}", roleIdStr);
            }
        }

        // 校验必填字段
        if (username == null || username.isEmpty() || password == null || password.isEmpty()) {
            return new ResponseResult(ResponseCode.REGISTER_FAILED.getCode(),
                    "用户名或密码不能为空",
                    null);
        }

        // 提取可选字段
        String fullname = (String) userRegisterInfo.getOrDefault("fullname", null);
        String email = (String) userRegisterInfo.getOrDefault("email", null);


        UserEntity userEntity = userService.createUser(username, fullname, email, password, roleId);

        return new ResponseResult(ResponseCode.REGISTER_SUCCESS.getCode(),
                ResponseCode.REGISTER_SUCCESS.getMessage(),
                userEntity);
    }

    @GetMapping("/deactivate")
    public Object deactivate(@RequestHeader("token") String token) {
        try {
            Optional<Long> userId = tokenService.validateToken(token);
            if (userId.isPresent()) {
                userService.deleteUserById(userId.get());
                tokenService.removeToken(token);
                return new ResponseResult(ResponseCode.DELETE_ACCOUNT_SUCCESS.getCode(),
                        ResponseCode.DELETE_ACCOUNT_SUCCESS.getMessage(),
                        null);
            } else {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "无效的token");
            }
        } catch (Exception e) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "注销账号失败");
        }
    }
}
