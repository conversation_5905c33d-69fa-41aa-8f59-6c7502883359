<template>
  <div class="model-overview-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Monitor /></el-icon>
          <h2>应用总览</h2>
        </div>
        <div class="sub-title">查看系统应用部署的整体统计信息</div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="goToMyApplications" round>
          <el-icon><Files /></el-icon>
          我的应用
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="stat in statistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
      
    <!-- 趋势图表区域 -->
    <el-row :gutter="20">
      <!-- 应用部署趋势图表 -->
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><TrendCharts /></el-icon>
                <span>应用部署趋势</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="appTrendChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Monitor,
  Files,
  StarFilled,
  Loading,
  SuccessFilled,
  CircleCloseFilled,
  TrendCharts
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'
import * as echarts from 'echarts'

const router = useRouter()
const pageLoading = ref(false)
const appTrendChartRef = ref(null)
let appTrendChart = null

// 统计数据
const statistics = ref([
  {
    title: '总应用数',
    value: 0,
    type: 'primary',
    icon: StarFilled
  },
  {
    title: '部署中应用',
    value: 0,
    type: 'success',
    icon: Loading
  },
  {
    title: '已完成应用',
    value: 0,
    type: 'warning',
    icon: SuccessFilled
  },
  {
    title: '失败应用',
    value: 0,
    type: 'danger',
    icon: CircleCloseFilled
  }
])

// 获取应用统计数据
const fetchStatistics = async () => {
  pageLoading.value = true
  try {
    // 使用应用统计API
    const res = await service.get('/api/v1.0/sys/application/statistics')
    if (res.code === 10000) {
      const data = res.data || {}

      // 更新统计卡片数据
      statistics.value[0].value = data.totalApplications || 0
      statistics.value[1].value = data.runningApplications || 0
      statistics.value[2].value = data.stoppedApplications || 0
      statistics.value[3].value = data.failedApplications || 0

      // 更新图表
      updateAppTrendChart(data.appTrend || [])
    } else {
      // 请求不成功时使用默认数据
      statistics.value[0].value = 85
      statistics.value[1].value = 42
      statistics.value[2].value = 28
      statistics.value[3].value = 15

      // 手动更新图表
      updateAppTrendChart()
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    toast('错误', '获取统计数据失败', 'error')

    // 出错时也使用默认数据
    statistics.value[0].value = 85
    statistics.value[1].value = 42
    statistics.value[2].value = 28
    statistics.value[3].value = 15

    // 手动更新图表
    updateAppTrendChart()
  } finally {
    pageLoading.value = false
  }
}

// 初始化应用部署趋势图表
const initAppTrendChart = () => {
  if (appTrendChartRef.value) {
    appTrendChart = echarts.init(appTrendChartRef.value)
    // 设置初始空白图表，确保容器被渲染
    appTrendChart.setOption({
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          type: 'bar',
          data: []
        }
      ]
    })
    console.log('应用部署趋势图表初始化完成', appTrendChartRef.value)
  } else {
    console.error('appTrendChartRef元素不存在')
  }
}

// 更新应用部署趋势图表数据
const updateAppTrendChart = (trendData) => {
  if (!appTrendChart) {
    console.error('应用部署趋势图表实例不存在，尝试重新初始化')
    initAppTrendChart()
    if (!appTrendChart) return
  }

  console.log('更新应用部署趋势图表数据', trendData)

  // 使用API数据或默认数据
  const data = trendData && trendData.length > 0 ? trendData : [
    { date: '周一', count: 5 },
    { date: '周二', count: 8 },
    { date: '周三', count: 12 },
    { date: '周四', count: 6 },
    { date: '周五', count: 10 },
    { date: '周六', count: 3 },
    { date: '周日', count: 2 }
  ]

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date),
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        interval: 0,
        rotate: 30,
        fontSize: 12,
        margin: 8
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '新建应用数',
        type: 'bar',
        barWidth: '50%',
        data: data.map(item => item.count),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#91cc75' },
            { offset: 0.5, color: '#67c23a' },
            { offset: 1, color: '#4d9e2d' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#67c23a' },
              { offset: 1, color: '#4d9e2d' }
            ])
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}'
        }
      }
    ]
  }

  console.log('设置应用部署趋势图表选项', option)
  appTrendChart.setOption(option)
}



// 跳转到我的应用页面
const goToMyApplications = () => {
  router.push('/application/deploy/task')
}

onMounted(async () => {
  console.log('组件已挂载')

  // 确保DOM已完全渲染
  await nextTick()

  // 初始化图表
  initAppTrendChart()

  // 再次确保图表容器已渲染
  await nextTick()

  // 获取数据并更新图表
  await fetchStatistics()

  // 窗口调整大小时重新调整图表
  window.addEventListener('resize', () => {
    if (appTrendChart) appTrendChart.resize()
  })
})

onUnmounted(() => {
  // 销毁图表实例
  appTrendChart?.dispose()

  // 移除事件监听器
  window.removeEventListener('resize', () => {
    appTrendChart?.resize()
  })
})
</script>

<style scoped>
.model-overview-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  display: flex;
  align-items: center;
}

.stat-card {
  height: 120px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 8px;
}

.stat-icon.primary { background-color: var(--el-color-primary-light-9); }
.stat-icon.success { background-color: var(--el-color-success-light-9); }
.stat-icon.warning { background-color: var(--el-color-warning-light-9); }
.stat-icon.danger { background-color: var(--el-color-danger-light-9); }
.stat-icon.info { background-color: var(--el-color-info-light-9); }

.stat-info {
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  
  .stat-title {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.chart-card {
  height: 450px;
  overflow: hidden;
  margin-bottom: 20px;
}

.chart-container {
  height: 390px;
  padding: 10px;
  width: 100%;
  min-height: 300px;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  
  .icon {
    margin-right: 8px;
    font-size: 18px;
    color: var(--el-color-primary);
  }
}

/* 深色模式适配 */
html.dark {
  .stat-card,
  .chart-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style>