import os
import sys
import logging
from pathlib import Path
import json
import time

def init_logging():
    log_dir = Path("/runtime/logs")
    log_dir.mkdir(exist_ok=True)

    logging.basicConfig(
        handlers=[
            logging.FileHandler(log_dir / "execution.log"),
            logging.StreamHandler(sys.stdout)
        ],
        level=logging.INFO,
        format="[%(levelname)s] %(asctime)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )


def _update_status(status_data: dict):
    status_file = Path("/runtime/STATUS.json")
    status_data["last_update"]= time.strftime("%Y-%m-%d %H:%M:%S")
    old_data = {}
    if status_file.exists():
        try:
            with open(status_file, "r") as f:
                old_data = json.load(f)
        except json.JSONDecodeError:
            old_data = {}

    new_data = {**old_data, **status_data}

    with open(status_file, "w") as f:
        json.dump(new_data, f, indent=2)


def execute_task():
    try:
        sys.path.insert(0, "/runtime/framework")
        from framework import execute_in_framework  # type:ignore

        _update_status({"status": "running"})

        execute_in_framework(
            user_code_path="/runtime/user_code",
            workspace_path="/runtime/workspace"
        )

        logging.info("Task completed")
        return 0
    except Exception as e:
        logging.error(f"Task failed: {str(e)}", exc_info=True)

        _update_status({
            "status": "error",
            "error": f"{str(e)}"
        })

        return 1


if __name__ == "__main__":
    init_logging()
    logging.info(f"Starting task {os.getenv('TASK_ID')}")
    exitcode = execute_task()

    _update_status({"exit_code": exitcode})

    sys.exit(exitcode)
