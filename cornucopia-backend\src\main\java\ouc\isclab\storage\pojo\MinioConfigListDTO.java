package ouc.isclab.storage.pojo;

import lombok.Data;
import ouc.isclab.storage.entity.MinioConfigEntity;
import java.util.Date;

@Data
public class MinioConfigListDTO {
    private Long id;
    private String name;
    private String endpoint;
    private String bucket;
    private MinioConfigEntity.ConfigType type;
    private Boolean active;
    private Date timeCreated;

    public static MinioConfigListDTO fromEntity(MinioConfigEntity entity) {
        MinioConfigListDTO dto = new MinioConfigListDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setEndpoint(entity.getEndpoint());
        dto.setBucket(entity.getBucket());
        dto.setType(entity.getType());
        dto.setActive(entity.getActive());
        dto.setTimeCreated(entity.getTimeCreated());
        return dto;
    }
} 