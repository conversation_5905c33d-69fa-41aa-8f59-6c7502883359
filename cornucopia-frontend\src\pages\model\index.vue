<template>
  <div class="model-overview-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />

    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><CollectionTag /></el-icon>
          <h2>模型总览</h2>
        </div>
        <div class="sub-title">查看系统模型的整体统计信息</div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="goToMyModels" round>
          <el-icon><Files /></el-icon>
          我的模型
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="stat in statistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <!-- 模型创建趋势图表 -->
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><TrendCharts /></el-icon>
                <span>模型创建趋势</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="modelTrendChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  CollectionTag,
  Files,
  StarFilled,
  Loading,
  SuccessFilled,
  CircleCloseFilled,
  TrendCharts
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'
import * as echarts from 'echarts'

const router = useRouter()
const pageLoading = ref(false)
const modelTrendChartRef = ref(null)
let modelTrendChart = null

// 统计数据
const statistics = ref([
  {
    title: '总模型数',
    value: 0,
    type: 'primary',
    icon: StarFilled
  },
  {
    title: '已完成模型',
    value: 0,
    type: 'success',
    icon: SuccessFilled
  },
  {
    title: '训练中模型',
    value: 0,
    type: 'warning',
    icon: Loading
  },
  {
    title: '失败模型',
    value: 0,
    type: 'danger',
    icon: CircleCloseFilled
  }
])

// 获取模型统计数据
const fetchStatistics = async () => {
  pageLoading.value = true
  try {
    // 使用模型统计API
    const res = await service.get('/api/v1.0/sys/model/statistics')
    if (res.code === 10000) {
      const data = res.data || {}

      // 更新统计卡片数据
      statistics.value[0].value = data.totalModels || 0
      statistics.value[1].value = data.completedModels || 0
      statistics.value[2].value = data.trainingModels || 0
      statistics.value[3].value = data.failedModels || 0

      // 更新图表
      updateModelTrendChart(data.modelTrend || [])
    } else {
      // 请求不成功时使用默认数据
      statistics.value[0].value = 120
      statistics.value[1].value = 36
      statistics.value[2].value = 15
      statistics.value[3].value = 8

      // 手动更新图表
      updateModelTrendChart()
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    toast('错误', '获取统计数据失败', 'error')

    // 出错时也使用默认数据
    statistics.value[0].value = 120
    statistics.value[1].value = 36
    statistics.value[2].value = 15
    statistics.value[3].value = 8

    // 手动更新图表
    updateModelTrendChart()
  } finally {
    pageLoading.value = false
  }
}

// 初始化模型创建趋势图表
const initModelTrendChart = () => {
  if (modelTrendChartRef.value) {
    modelTrendChart = echarts.init(modelTrendChartRef.value)
    // 设置初始空白图表，确保容器被渲染
    modelTrendChart.setOption({
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          type: 'bar',
          data: []
        }
      ]
    })
    console.log('模型创建趋势图表初始化完成', modelTrendChartRef.value)
  } else {
    console.error('modelTrendChartRef元素不存在')
  }
}

// 更新模型创建趋势图表数据
const updateModelTrendChart = (trendData) => {
  if (!modelTrendChart) {
    console.error('模型创建趋势图表实例不存在，尝试重新初始化')
    initModelTrendChart()
    if (!modelTrendChart) return
  }
  
  console.log('更新模型创建趋势图表数据', trendData)
  
  // 使用API数据或默认数据
  const data = trendData && trendData.length > 0 ? trendData : [
    { date: '周一', count: 3 },
    { date: '周二', count: 5 },
    { date: '周三', count: 8 },
    { date: '周四', count: 4 },
    { date: '周五', count: 7 },
    { date: '周六', count: 2 },
    { date: '周日', count: 1 }
  ]
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date),
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        interval: 0,
        rotate: 30,
        fontSize: 12,
        margin: 8
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '新建模型数',
        type: 'bar',
        barWidth: '50%',
        data: data.map(item => item.count),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#5470c6' },
              { offset: 1, color: '#3c5fbe' }
            ])
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}'
        }
      }
    ]
  }
  
  console.log('设置模型创建趋势图表选项', option)
  modelTrendChart.setOption(option)
}



// 跳转到我的模型页面
const goToMyModels = () => {
  router.push('/model/my')
}

onMounted(async () => {
  console.log('组件已挂载')

  // 确保DOM已完全渲染
  await nextTick()

  // 初始化图表
  initModelTrendChart()

  // 再次确保图表容器已渲染
  await nextTick()

  // 获取数据并更新图表
  await fetchStatistics()

  // 窗口调整大小时重新调整图表
  window.addEventListener('resize', () => {
    if (modelTrendChart) modelTrendChart.resize()
  })
})

onUnmounted(() => {
  // 销毁图表实例
  modelTrendChart?.dispose()

  // 移除事件监听器
  window.removeEventListener('resize', () => {
    modelTrendChart?.resize()
  })
})
</script>

<style scoped>
.model-overview-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  display: flex;
  align-items: center;
}

.stat-card {
  height: 120px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 8px;
}

.stat-icon.primary { background-color: var(--el-color-primary-light-9); }
.stat-icon.success { background-color: var(--el-color-success-light-9); }
.stat-icon.warning { background-color: var(--el-color-warning-light-9); }
.stat-icon.danger { background-color: var(--el-color-danger-light-9); }
.stat-icon.info { background-color: var(--el-color-info-light-9); }

.stat-info {
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  
  .stat-title {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.chart-card {
  height: 450px;
  overflow: hidden;
  margin-bottom: 20px;
}

.chart-container {
  height: 390px;
  padding: 10px;
  width: 100%;
  min-height: 300px;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  
  .icon {
    margin-right: 8px;
    font-size: 18px;
    color: var(--el-color-primary);
  }
}

/* 深色模式适配 */
html.dark {
  .stat-card,
  .chart-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style>