package ouc.isclab.storage.config;

import io.minio.MinioClient;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import ouc.isclab.storage.entity.MinioConfigEntity;
import ouc.isclab.storage.repository.MinioConfigRepository;

import java.util.Optional;

@Configuration
@ConfigurationProperties(prefix = "storage.minio")
@Data
public class MinioConfiguration {
    private String accessKey;
    private String secretKey;
    private String endpoint;
    private String bucket;
    private MinioConfigEntity.ConfigType type = MinioConfigEntity.ConfigType.DATASET;

    @Autowired
    private MinioConfigRepository minioConfigRepository;

    private MinioClient minioClient;

    /**
     * 初始化时从数据库加载配置
     */
    @PostConstruct
    public void init() {
        // 尝试从数据库加载配置
        Optional<MinioConfigEntity> configOpt = minioConfigRepository.findByTypeAndActiveTrue(type);
        
        if (configOpt.isPresent()) {
            // 如果数据库中有配置，使用数据库中的配置
            MinioConfigEntity config = configOpt.get();
            this.endpoint = config.getEndpoint();
            this.accessKey = config.getAccessKey();
            this.secretKey = config.getSecretKey();
            this.bucket = config.getBucket();
        } else if (this.endpoint != null && this.accessKey != null && this.secretKey != null) {
            // 如果数据库中没有配置，但配置文件中有配置，则保存到数据库
            saveConfigToDatabase();
        } else {
            // 如果既没有数据库配置，也没有配置文件配置，则使用默认值
            this.endpoint = "http://localhost:9000";
            this.accessKey = "minioadmin";
            this.secretKey = "minioadmin";
            this.bucket = "default";
            saveConfigToDatabase();
        }

        // 初始化MinIO客户端
        this.minioClient = MinioClient.builder()
                .endpoint(this.endpoint)
                .credentials(this.accessKey, this.secretKey)
                .build();
    }

    /**
     * 将当前配置保存到数据库
     */
    public void saveConfigToDatabase() {
        // 将同类型的配置设置为非活动
        minioConfigRepository.findByTypeAndActiveTrue(type).ifPresent(config -> {
            config.setActive(false);
            minioConfigRepository.save(config);
        });

        // 创建新配置
        MinioConfigEntity newConfig = new MinioConfigEntity();
        newConfig.setName("Default " + type.name() + " Storage");
        newConfig.setEndpoint(this.endpoint);
        newConfig.setAccessKey(this.accessKey);
        newConfig.setSecretKey(this.secretKey);
        newConfig.setBucket(this.bucket);
        newConfig.setType(this.type);
        newConfig.setActive(true);
        
        minioConfigRepository.save(newConfig);
    }

    @Bean
    @DependsOn("minioConfigRepository")
    public MinioClient minioClient() {
        return this.minioClient;
    }
}
