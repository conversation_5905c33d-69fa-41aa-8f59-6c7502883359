<template>
  <div class="metadata-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><InfoFilled /></el-icon>
          <h2>节点元数据</h2>
        </div>
        <div class="sub-title">查看节点的基本信息和配置</div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleEdit" plain round>
          <el-icon><Edit /></el-icon>编辑设置
        </el-button>
      </div>
    </div>

    <el-card class="metadata-card" shadow="hover" v-loading="loading">
      <div class="metadata-grid" v-if="metadata">
        <div class="metadata-section">
          <h3>基本信息</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="节点名称">{{ metadata.name }}</el-descriptions-item>
            <el-descriptions-item label="描述">{{ metadata.description }}</el-descriptions-item>
            <el-descriptions-item label="组织">{{ metadata.organization }}</el-descriptions-item>
            <el-descriptions-item label="服务器类型">{{ metadata.server_type }}</el-descriptions-item>
            <el-descriptions-item label="管理员邮箱">
              {{ metadata.admin_email || '未设置' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="metadata-section">
          <h3>技术参数</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="Syft 版本">{{ metadata.syft_version }}</el-descriptions-item>
            <el-descriptions-item label="验证密钥">
              <el-tooltip :content="metadata.verify_key" placement="top">
                <el-tag size="small" type="info" class="key-tag">
                  {{ metadata.verify_key.slice(0, 20) }}...
                </el-tag>
              </el-tooltip>
              <el-button 
                type="primary" 
                link 
                size="small"
                v-copy="metadata.verify_key"
              >
                复制
              </el-button>
            </el-descriptions-item>
            <el-descriptions-item label="支持的协议">
              <div class="protocol-list">
                <el-tag 
                  v-for="protocol in metadata.supported_protocols" 
                  :key="protocol"
                  size="small"
                  type="success"
                  class="protocol-tag"
                >
                  v{{ protocol }}
                </el-tag>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="metadata-section">
          <h3>版本信息</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="元数据版本">{{ metadata.metadata_version }}</el-descriptions-item>
            <el-descriptions-item label="最高对象版本">{{ metadata.highest_object_version }}</el-descriptions-item>
            <el-descriptions-item label="最低对象版本">{{ metadata.lowest_object_version }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="metadata-section">
          <h3>系统配置</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="最小 Blob 存储大小">
              {{ metadata.min_size_blob_storage_mb }} MB
            </el-descriptions-item>
            <el-descriptions-item label="显示警告">
              <el-tag :type="metadata.show_warnings ? 'warning' : 'info'" size="small">
                {{ metadata.show_warnings ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="注册启用">
              <el-tag :type="metadata.signup_enabled ? 'success' : 'danger'" size="small">
                {{ metadata.signup_enabled ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="急切执行启用">
              <el-tag :type="metadata.eager_execution_enabled ? 'success' : 'danger'" size="small">
                {{ metadata.eager_execution_enabled ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>

    <el-dialog
      v-model="editDialogVisible"
      title="编辑设置"
      width="500px"
      center
    >
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="节点名称" required>
          <el-input v-model="editForm.name" placeholder="请输入节点名称" />
        </el-form-item>
        <el-form-item label="组织" required>
          <el-input v-model="editForm.organization" placeholder="请输入组织名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入节点描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false" round>取消</el-button>
          <el-button type="primary" @click="confirmEdit" :loading="editLoading" round>
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { InfoFilled, Edit } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import sycee from '~/sycee.js'

const route = useRoute()
const nodeId = ref('')
const metadata = ref(null)
const loading = ref(false)
const editDialogVisible = ref(false)
const editLoading = ref(false)
const editForm = ref({
  name: '',
  organization: '',
  description: ''
})

const handleGetMetadata = async () => {
  loading.value = true
  try {
    const res = await sycee(nodeId.value, 'metadata', {}, false)
    if (res.code === 10000) {
      metadata.value = res.data
    } else {
      toast("错误", res.data, "error")
    }
  } catch (error) {
    toast("错误", "获取元数据失败", "error")
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  nodeId.value = route.query.id
  handleGetMetadata()
})

const vCopy = {
  mounted(el, { value }) {
    el.onclick = () => {
      navigator.clipboard.writeText(value).then(() => {
        toast('成功', '已复制到剪贴板', 'success')
      }).catch(() => {
        toast('错误', '复制失败', 'error')
      })
    }
  }
}

const handleEdit = () => {
  editForm.value = {
    name: metadata.value.name,
    organization: metadata.value.organization,
    description: metadata.value.description
  }
  editDialogVisible.value = true
}

const confirmEdit = async () => {
  if (!editForm.value.name || !editForm.value.organization) {
    toast('警告', '请填写必填项', 'warning')
    return
  }

  editLoading.value = true
  try {
    const res = await sycee(nodeId.value, 'settings/update', editForm.value)
    if (res.code === 10000) {
      toast('成功', '设置已更新')
      editDialogVisible.value = false
      handleGetMetadata()
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    toast('错误', '更新设置失败', 'error')
  } finally {
    editLoading.value = false
  }
}
</script>

<style>
.metadata-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.title-icon {
  margin-right: 8px;
  font-size: 24px;
  color: var(--el-color-primary);
}

h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.sub-title {
  color: #909399;
  font-size: 14px;
  margin-left: 32px;
}

.metadata-card {
  border-radius: 8px;
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 16px;
}

.metadata-section {
  h3 {
    margin: 0 0 16px;
    font-size: 18px;
    color: var(--el-text-color-primary);
  }
}

.protocol-tag {
  margin-right: 8px;
}

:deep(.el-descriptions__label) {
  width: 140px;
  font-weight: 500;
}

:deep(.el-descriptions__content) {
  word-break: break-all;
}

.key-tag {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}

.protocol-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.protocol-tag {
  margin: 0;
}

:deep(.el-descriptions__cell) {
  padding: 12px 16px !important;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style> 
