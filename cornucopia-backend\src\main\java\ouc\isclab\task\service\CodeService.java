package ouc.isclab.task.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.repository.NodeRepository;
import ouc.isclab.node.service.NodeService;
import ouc.isclab.system.entity.UserEntity;
import ouc.isclab.system.repository.UserRepository;
import ouc.isclab.task.entity.CodeEntity;
import ouc.isclab.task.entity.CodeEntity.ApprovalStatus;
import ouc.isclab.task.entity.NodeAccountRequestEntity;
import ouc.isclab.task.pojo.CodeDTO;
import ouc.isclab.task.repository.CodeRepository;
import ouc.isclab.task.repository.NodeAccountRequestRepository;
import ouc.isclab.task.service.NodeAccountRequestService;
import ouc.isclab.sycee.service.SyceeService;
import ouc.isclab.task.service.TaskService;
import ouc.isclab.task.entity.TaskEntity;
import ouc.isclab.task.repository.TaskRepository;
import ouc.isclab.pyxis.service.PyxisService;
import org.springframework.http.ResponseEntity;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CodeService {

    @Autowired
    private CodeRepository codeRepository;
    
    @Autowired
    private NodeRepository nodeRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private NodeAccountRequestRepository nodeAccountRequestRepository;
    
    @Autowired
    private NodeAccountRequestService nodeAccountRequestService;
    
    @Autowired
    private NodeService nodeService;
    
    @Autowired
    private SyceeService syceeService;

    @Autowired
    private PyxisService pyxisService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskRepository taskRepository;

    /**
     * 上传代码字符串
     */
    @Transactional
    public Map<String, Object> uploadCodeString(Long nodeId, Long resourceId, String funcName, String codeContent, String description, Long userId, String customModelPath) {
        try {
            // 检查节点是否存在
            NodeEntity node = nodeRepository.findById(nodeId)
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));

            // 获取节点账号请求信息
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }

            // 创建代码实体
            CodeEntity codeEntity = new CodeEntity();
            codeEntity.setNode(node);
            codeEntity.setFuncName(funcName);
            codeEntity.setCodeContent(codeContent);
            codeEntity.setDescription(description);
            codeEntity.setCreatorId(userId);
            codeEntity.setStatus(ApprovalStatus.PENDING);

            // 调用Sycee服务上传代码
            Map<String, Object> syceeResult = syceeService.uploadCodeStr(
                    nodeId,
                    userId,
                    codeEntity.getFuncName(),
                    codeEntity.getCodeContent()
            );

            // 检查Sycee服务返回结果
            if (syceeResult != null && syceeResult.containsKey("codeId") && syceeResult.containsKey("requestId")) {
                // 保存Sycee代码ID
                codeEntity.setSyceeCodeId(syceeResult.get("codeId").toString());
                codeEntity.setSyceeRequestId(syceeResult.get("requestId").toString());
                
                // 保存输入参数
                if (syceeResult.containsKey("inputKwargs")) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    codeEntity.setInputKwargs(objectMapper.writeValueAsString(syceeResult.get("inputKwargs")));
                }
            } else {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "上传代码到节点失败");
            }

            // 保存代码实体
            CodeEntity savedCode = codeRepository.save(codeEntity);

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", savedCode.getId());
            result.put("funcName", savedCode.getFuncName());
            result.put("syceeCodeId", savedCode.getSyceeCodeId());
            result.put("syceeRequestId", savedCode.getSyceeRequestId());
            result.put("status", savedCode.getStatus().name());
            
            // 添加参数信息到返回结果
            if (savedCode.getInputKwargs() != null && !savedCode.getInputKwargs().isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                result.put("inputKwargs", objectMapper.readValue(savedCode.getInputKwargs(), List.class));
            }
            
            return result;
            
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("上传代码字符串失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "上传代码字符串失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户上传的代码列表
     */
    public Map<String, Object> getUserCodes(Long nodeId, Long userId, int page, int size) {
        Page<CodeEntity> codePage;
        Pageable pageable = PageRequest.of(page - 1, size);
        
        if (nodeId != null) {
            codePage = codeRepository.findByCreatorIdAndNode_Id(userId, nodeId, pageable);
        } else {
            codePage = codeRepository.findByCreatorId(userId, pageable);
        }
        
        List<Map<String, Object>> codeList = new ArrayList<>();
        for (CodeEntity code : codePage.getContent()) {
            Map<String, Object> codeMap = new HashMap<>();
            codeMap.put("id", code.getId());
            codeMap.put("nodeId", code.getNode().getId());
            codeMap.put("nodeName", code.getNode().getName());
            codeMap.put("funcName", code.getFuncName());
            codeMap.put("inputKwargs", code.getInputKwargs());
            // 不返回完整代码内容，只返回简要信息
            codeMap.put("description", code.getDescription());
            codeMap.put("status", code.getStatus().name());
            codeMap.put("creatorId", code.getCreatorId());
            
            // 添加节点IP和端口信息
            codeMap.put("nodeIp", code.getNode().getIpAddress());
            codeMap.put("nodePort", code.getNode().getPort());
            
            // 添加Sycee任务ID
            codeMap.put("syceeJobId", code.getSyceeJobId());

            // 添加Pyxis任务ID
            codeMap.put("pyxisTaskId", code.getPyxisTaskId());
            
            // 获取关联的任务ID列表
            List<TaskEntity> relatedTasks = taskRepository.findByCodesContaining(code);
            List<Long> taskIds = new ArrayList<>();
            List<String> taskNames = new ArrayList<>();
            for (TaskEntity task : relatedTasks) {
                taskIds.add(task.getId());
                taskNames.add(task.getName());
            }
            codeMap.put("taskIds", taskIds);
            codeMap.put("taskNames", taskNames);
            
            // 获取创建者名称
            userRepository.findById(code.getCreatorId()).ifPresent(user -> {
                codeMap.put("creatorName", user.getUsername());
            });
            
            codeMap.put("createTime", code.getTimeCreated().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            codeMap.put("updateTime", code.getTimeUpdated().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            
            codeList.add(codeMap);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("codes", codeList);
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("total", codePage.getTotalElements());
        pagination.put("size", size);
        pagination.put("page", page);
        result.put("pagination", pagination);
        
        return result;
    }
    
    /**
     * 获取代码详情
     */
    public CodeDTO getCodeDetail(Long codeId, Long userId) {
        try {
            // 获取代码实体
            CodeEntity codeEntity = codeRepository.findById(codeId)
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));
            
            // 检查权限（创建者或节点所有者可以查看）
            if (!codeEntity.getCreatorId().equals(userId) && !nodeService.isNodeOwner(codeEntity.getNode().getId(), userId)) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "您没有权限查看该代码");
            }
            
            // 转换为DTO，包含完整信息
            CodeDTO codeDTO = new CodeDTO();
            codeDTO.setId(codeEntity.getId());
            codeDTO.setNodeId(codeEntity.getNode().getId());
            codeDTO.setNodeName(codeEntity.getNode().getName());
            codeDTO.setNodeType(codeEntity.getNodeType().name());
            codeDTO.setDescription(codeEntity.getDescription());
            codeDTO.setCreatorId(codeEntity.getCreatorId());
            codeDTO.setStatus(codeEntity.getStatus().name());
            codeDTO.setRejectReason(codeEntity.getRejectReason());
            codeDTO.setApproverId(codeEntity.getApproverId());
            codeDTO.setCreateTime(codeEntity.getTimeCreated().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            codeDTO.setUpdateTime(codeEntity.getTimeUpdated().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            if (codeEntity.getNodeType() == CodeEntity.NodeType.Sycee) {
                codeDTO.setFuncName(codeEntity.getFuncName());
                codeDTO.setCodeContent(codeEntity.getCodeContent());  // 详情接口返回完整代码内容
                codeDTO.setSyceeCodeId(codeEntity.getSyceeCodeId());
                codeDTO.setSyceeRequestId(codeEntity.getSyceeRequestId());
                codeDTO.setSyceeJobId(codeEntity.getSyceeJobId());
            }
            if (codeEntity.getNodeType() == CodeEntity.NodeType.Pyxis) {
                codeDTO.setPyxisTaskId(codeEntity.getPyxisTaskId());
                codeDTO.setSavedFiles(codeEntity.getSavedFiles());
            }
            // 获取创建者名称
            userRepository.findById(codeEntity.getCreatorId()).ifPresent(user -> {
                codeDTO.setCreatorName(user.getUsername());
            });
            
            // 获取审批者名称
            if (codeEntity.getApproverId() != null) {
                userRepository.findById(codeEntity.getApproverId()).ifPresent(user -> {
                    codeDTO.setApproverName(user.getUsername());
                });
            }
            
            // 添加输入参数
            if (codeEntity.getInputKwargs() != null && !codeEntity.getInputKwargs().isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                codeDTO.setInputKwargs(objectMapper.readValue(codeEntity.getInputKwargs(), List.class));
            }
            
            return codeDTO;
        } catch (Exception e) {
            log.error("获取代码详情失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取代码详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 审批代码
     */
    @Transactional
    public CodeDTO approveCode(Long codeId, boolean approved, String rejectReason, Long approverId) {
        try {
            // 获取代码实体
            CodeEntity codeEntity = codeRepository.findById(codeId)
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));

            // 检查是否是节点所有者
            if (!nodeService.isNodeOwner(codeEntity.getNode().getId(), approverId)) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "您不是该节点的所有者，无权审批");
            }

            // 更新审批状态
            if (approved) {
                // 调用Sycee服务批准代码
                if (codeEntity.getSyceeRequestId() != null) {
                    syceeService.approveCode(codeEntity.getNode().getId(), codeEntity.getSyceeRequestId());
                }

                codeEntity.setStatus(ApprovalStatus.APPROVED);
            } else {
                // 调用Sycee服务拒绝代码
                if (codeEntity.getSyceeRequestId() != null) {
                    syceeService.rejectCode(codeEntity.getNode().getId(), codeEntity.getSyceeRequestId(), rejectReason);
                }

                codeEntity.setStatus(ApprovalStatus.REJECTED);
                codeEntity.setRejectReason(rejectReason);
            }

            codeEntity.setApproverId(approverId);
            CodeEntity updatedCode = codeRepository.save(codeEntity);

            // 返回更新后的代码详情
            return getCodeDetail(updatedCode.getId(), approverId);

        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("审批代码失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "审批代码失败: " + e.getMessage());
        }
    }

    /**
     * 获取节点所有者的所有代码
     */
    public Map<String, Object> getAllCodesByNodeOwner(Long nodeOwnerId, Pageable pageable) {
        Page<CodeEntity> codePage = codeRepository.findByNode_CreatorId(nodeOwnerId, pageable);
        
        List<Map<String, Object>> codeList = new ArrayList<>();
        for (CodeEntity code : codePage.getContent()) {
            Map<String, Object> codeMap = new HashMap<>();
            codeMap.put("id", code.getId());
            codeMap.put("nodeId", code.getNode().getId());
            codeMap.put("nodeName", code.getNode().getName());
            codeMap.put("funcName", code.getFuncName());
            codeMap.put("inputKwargs", code.getInputKwargs());
            // 不返回完整代码内容，只返回简要信息
            codeMap.put("description", code.getDescription());
            codeMap.put("status", code.getStatus().name());
            codeMap.put("creatorId", code.getCreatorId());
            codeMap.put("nodeType", code.getNodeType().name());
            // 添加节点IP和端口信息
            codeMap.put("nodeIp", code.getNode().getIpAddress());
            codeMap.put("nodePort", code.getNode().getPort());
            
            // 添加Sycee任务ID
            codeMap.put("syceeJobId", code.getSyceeJobId());
            
            // 获取关联的任务ID列表
            List<TaskEntity> relatedTasks = taskRepository.findByCodesContaining(code);
            List<Long> taskIds = new ArrayList<>();
            List<String> taskNames = new ArrayList<>();
            for (TaskEntity task : relatedTasks) {
                taskIds.add(task.getId());
                taskNames.add(task.getName());
            }
            codeMap.put("taskIds", taskIds);
            codeMap.put("taskNames", taskNames);
            
            // 获取创建者名称
            userRepository.findById(code.getCreatorId()).ifPresent(user -> {
                codeMap.put("creatorName", user.getUsername());
            });
            
            codeMap.put("createTime", code.getTimeCreated().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            codeMap.put("updateTime", code.getTimeUpdated().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            
            codeList.add(codeMap);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("codes", codeList);
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("total", codePage.getTotalElements());
        pagination.put("size", codePage.getSize());
        pagination.put("page", codePage.getNumber() + 1);
        result.put("pagination", pagination);
        
        return result;
    }

    /**
     * 按状态获取节点所有者的代码
     */
    public Map<String, Object> getCodesByNodeOwnerAndStatus(Long nodeOwnerId, CodeEntity.ApprovalStatus status, Pageable pageable) {
        Page<CodeEntity> codePage = codeRepository.findByNode_CreatorIdAndStatus(nodeOwnerId, status, pageable);
        
        List<Map<String, Object>> codeList = new ArrayList<>();
        for (CodeEntity code : codePage.getContent()) {
            Map<String, Object> codeMap = new HashMap<>();
            codeMap.put("id", code.getId());
            codeMap.put("nodeId", code.getNode().getId());
            codeMap.put("nodeName", code.getNode().getName());
            codeMap.put("funcName", code.getFuncName());
            codeMap.put("inputKwargs", code.getInputKwargs());
            // 不返回完整代码内容，只返回简要信息
            codeMap.put("description", code.getDescription());
            codeMap.put("status", code.getStatus().name());
            codeMap.put("creatorId", code.getCreatorId());
            codeMap.put("nodeType", code.getNodeType().name());
            // 添加节点IP和端口信息
            codeMap.put("nodeIp", code.getNode().getIpAddress());
            codeMap.put("nodePort", code.getNode().getPort());
            
            // 添加Sycee任务ID
            codeMap.put("syceeJobId", code.getSyceeJobId());
            
            // 获取关联的任务ID列表
            List<TaskEntity> relatedTasks = taskRepository.findByCodesContaining(code);
            List<Long> taskIds = new ArrayList<>();
            List<String> taskNames = new ArrayList<>();
            for (TaskEntity task : relatedTasks) {
                taskIds.add(task.getId());
                taskNames.add(task.getName());
            }
            codeMap.put("taskIds", taskIds);
            codeMap.put("taskNames", taskNames);
            
            // 获取创建者名称
            userRepository.findById(code.getCreatorId()).ifPresent(user -> {
                codeMap.put("creatorName", user.getUsername());
            });
            
            codeMap.put("createTime", code.getTimeCreated().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            codeMap.put("updateTime", code.getTimeUpdated().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            
            codeList.add(codeMap);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("codes", codeList);
        
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("total", codePage.getTotalElements());
        pagination.put("size", codePage.getSize());
        pagination.put("page", codePage.getNumber() + 1);
        result.put("pagination", pagination);
        
        return result;
    }

    /**
     * 运行代码
     */
    @Transactional
    public Map<String, Object> runCode(Long codeId, Map<String, Object> args, Long userId) {
        try {
            // 获取代码实体
            CodeEntity codeEntity = codeRepository.findById(codeId)
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));
            
            // 检查代码状态是否已批准
            if (codeEntity.getStatus() != ApprovalStatus.APPROVED) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "代码未获批准或已经运行");
            }
            
            // 检查权限（创建者可以运行）
            if (!codeEntity.getCreatorId().equals(userId)) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "您没有权限运行该代码");
            }
            
            // 获取节点账号请求信息
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, codeEntity.getNode().getId(), NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }
            
            // 调用Sycee服务运行代码
            Map<String, Object> syceeResult = syceeService.runCode(
                    codeEntity.getNode().getId(),
                    userId,
                    codeEntity.getSyceeCodeId(),
                    args
            );
            
            // 检查Sycee服务返回结果
            if (syceeResult != null && syceeResult.containsKey("jobId")) {
                // 保存Sycee任务ID
                codeEntity.setSyceeJobId(syceeResult.get("jobId").toString());
                codeEntity.setStatus(ApprovalStatus.RUNNING);
                codeRepository.save(codeEntity);
                
                // 更新关联任务的状态
                updateRelatedTasksStatus(codeEntity);
                
                // 返回结果
                Map<String, Object> result = new HashMap<>();
                result.put("id", codeEntity.getId());
                result.put("funcName", codeEntity.getFuncName());
                result.put("syceeCodeId", codeEntity.getSyceeCodeId());
                result.put("syceeJobId", codeEntity.getSyceeJobId());
                
                return result;
            } else {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "运行代码失败");
            }
            
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("运行代码失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "运行代码失败: " + e.getMessage());
        }
    }

    /**
     * 获取代码运行结果
     */
    public Map<String, Object> getCodeResult(Long codeId, Long userId) {
        try {
            // 获取代码实体
            CodeEntity codeEntity = codeRepository.findById(codeId)
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));
            
            // 检查权限（创建者可以查看结果）
            if (!codeEntity.getCreatorId().equals(userId) && !nodeService.isNodeOwner(codeEntity.getNode().getId(), userId)) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "您没有权限查看该代码结果");
            }
            
            // 检查代码是否已运行
            if (codeEntity.getSyceeJobId() == null || codeEntity.getSyceeJobId().isEmpty()) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "代码尚未运行");
            }
            
            // 调用Sycee服务获取结果
            Map<String, Object> syceeResult = syceeService.getJobResult(
                    codeEntity.getNode().getId(),
                    userId,
                    codeEntity.getSyceeJobId()
            );
            
            // 检查Sycee服务返回结果
            if (syceeResult != null && syceeResult.containsKey("job_status")) {
                // 更新代码状态
                String jobStatus = syceeResult.get("job_status").toString();
                if (jobStatus.equals("COMPLETED")) {
                    codeEntity.setStatus(ApprovalStatus.COMPLETED);
                } else if (jobStatus.equals("ERROR") || jobStatus.equals("FAILED")) {
                    codeEntity.setStatus(ApprovalStatus.ERROR);
                }
                codeRepository.save(codeEntity);
                
                // 更新关联任务的状态
                updateRelatedTasksStatus(codeEntity);
            }
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", codeEntity.getId());
            result.put("funcName", codeEntity.getFuncName());
            result.put("syceeCodeId", codeEntity.getSyceeCodeId());
            result.put("syceeJobId", codeEntity.getSyceeJobId());
            result.put("result", syceeResult);
            result.put("status", syceeResult.get("job_status"));
            
            return result;
            
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取代码运行结果失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取代码运行结果失败: " + e.getMessage());
        }
    }

    /**
     * 获取代码运行日志
     */
    public Map<String, Object> getCodeLog(Long codeId, Long userId) {
        try {
            // 获取代码实体
            CodeEntity codeEntity = codeRepository.findById(codeId)
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));
            
            // 检查权限（创建者可以查看日志）
            if (!codeEntity.getCreatorId().equals(userId) && !nodeService.isNodeOwner(codeEntity.getNode().getId(), userId)) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "您没有权限查看该代码日志");
            }
            
            // 检查代码是否已运行
            if (codeEntity.getSyceeJobId() == null || codeEntity.getSyceeJobId().isEmpty()) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "代码尚未运行");
            }
            
            // 调用Sycee服务获取日志
            Map<String, Object> syceeResult = syceeService.getJobLog(
                    codeEntity.getNode().getId(),
                    userId,
                    codeEntity.getSyceeJobId()
            );
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", codeEntity.getId());
            result.put("funcName", codeEntity.getFuncName());
            result.put("syceeCodeId", codeEntity.getSyceeCodeId());
            result.put("syceeJobId", codeEntity.getSyceeJobId());
            result.put("log", syceeResult);
            
            return result;
            
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取代码运行日志失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取代码运行日志失败: " + e.getMessage());
        }
    }

    // 添加一个辅助方法来更新与代码相关的所有任务状态
    private void updateRelatedTasksStatus(CodeEntity codeEntity) {
        // 查询所有包含此代码的任务
        List<TaskEntity> relatedTasks = taskRepository.findByCodesContaining(codeEntity);
        for (TaskEntity task : relatedTasks) {
            taskService.updateTaskStatus(task.getId());
        }
    }

    /**
     * 删除代码
     */
    @Transactional
    public void deleteCode(Long codeId, Long userId) {
        // 1. 获取代码实体
        CodeEntity code = codeRepository.findById(codeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));
        
        // 2. 检查权限（只有创建者可以删除）
        if (!code.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无权删除此代码");
        }
        
        // 3. 检查代码状态（不能删除正在运行的代码）
        if (code.getStatus() == CodeEntity.ApprovalStatus.RUNNING) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "无法删除正在运行的代码");
        }
        
        // 4. 查找并解除与任务的关联
        List<TaskEntity> relatedTasks = taskRepository.findByCodesContaining(code);
        for (TaskEntity task : relatedTasks) {
            task.getCodes().remove(code);
            taskRepository.save(task);
        }
        
        // 5. 如果代码已经上传到Sycee，尝试从Sycee删除
        // if (code.getSyceeCodeId() != null) {
        //     try {
        //         syceeService.deleteCode(code.getNode().getId(), code.getSyceeCodeId());
        //     } catch (Exception e) {
        //         log.error("从Sycee删除代码失败", e);
        //         // 继续删除数据库记录，即使Sycee删除失败
        //     }
        // }
        
        // 6. 删除数据库记录
        codeRepository.delete(code);
    }

    /**
     * 批量删除代码
     */
    @Transactional
    public void batchDeleteCodes(List<Long> ids, Long userId) {
        for (Long id : ids) {
            deleteCode(id, userId);
        }
    }

    /**
     * 检查用户是否向节点所有者发起过代码审批请求
     */
    public boolean hasApprovalRequestFromUserToNodeOwner(Long creatorId, Long nodeOwnerId) {
        // 获取节点所有者拥有的所有节点
        List<Long> ownerNodeIds = nodeRepository.findNodeIdsByCreatorId(nodeOwnerId);
        
        if (ownerNodeIds.isEmpty()) {
            return false;
        }
        
        // 检查用户是否在这些节点上提交过需要审批的代码
        return codeRepository.existsByCreatorIdAndNodeIdInAndStatusIn(
            creatorId, 
            ownerNodeIds, 
            List.of(CodeEntity.ApprovalStatus.PENDING, CodeEntity.ApprovalStatus.APPROVED, CodeEntity.ApprovalStatus.REJECTED)
        );
    }

    /**
     * 刷新代码状态
     */

    public Map<String, Object> refreshCodeStatus(Long codeId, Long userId) {
        try {
            // 获取代码实体
            CodeEntity codeEntity = codeRepository.findById(codeId)
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));
            
            // 检查权限（创建者可以查看结果）
            if (!codeEntity.getCreatorId().equals(userId) && !nodeService.isNodeOwner(codeEntity.getNode().getId(), userId)) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "您没有权限刷新代码状态");
            }
            
            // 检查代码是否已运行
            if (codeEntity.getSyceeJobId() == null || codeEntity.getSyceeJobId().isEmpty()) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "代码尚未运行");
            }
            
            // 调用Sycee服务获取结果
            Map<String, Object> syceeResult = syceeService.refreshJobStatus(
                    codeEntity.getNode().getId(),
                    userId,
                    codeEntity.getSyceeJobId()
            );
            
            // 检查Sycee服务返回结果
            if (syceeResult != null && syceeResult.containsKey("status")) {
                // 更新代码状态
                String jobStatus = syceeResult.get("status").toString();
                if (jobStatus.equals("completed")) {
                    codeEntity.setStatus(ApprovalStatus.COMPLETED);
                } else if (jobStatus.equals("errored") || jobStatus.equals("FAILED")) {
                    codeEntity.setStatus(ApprovalStatus.ERROR);
                }
                codeRepository.save(codeEntity);
                
                // 更新关联任务的状态
                updateRelatedTasksStatus(codeEntity);
            }
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", codeEntity.getId());
            result.put("status", syceeResult.get("status"));
            
            return result;
            
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取代码运行结果失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "获取代码运行结果失败: " + e.getMessage());
        }
    }

    public Boolean isSycee(Long nodeId) {
        NodeEntity node = nodeRepository.findById(nodeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));
        return node.getNodeType() == NodeEntity.NodeType.Sycee;
    }

    public Boolean isSyceeCode(Long codeId) {
        CodeEntity code = codeRepository.findById(codeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));
        return code.getNodeType() == CodeEntity.NodeType.Sycee;
    }

    /**
     * 上传代码文件夹
     */
    public Map<String, Object> uploadFile(Long nodeId, Long userId, MultipartFile[] files, String folderName) {
        try {
            // 检查节点是否存在
            NodeEntity node = nodeRepository.findById(nodeId)
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "节点不存在"));

            // 获取节点账号请求信息
            NodeAccountRequestEntity nodeAccount = nodeAccountRequestRepository.findByApplicantIdAndNode_IdAndStatus(
                    userId, nodeId, NodeAccountRequestEntity.ApprovalStatus.APPROVED);
            if (nodeAccount == null) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "用户没有该节点的账号");
            }
            
            Map<String, Object> result = pyxisService.createPyxisTask(nodeId, userId, files, folderName);
            //{"task_id":"09E8FC3133C00000_deploy","saved_files":["minio_model_deploy.py","base_template.jinja2","input_template.jinja2","output_template.jinja2","scrip_slim.py","template_config_builder.py","template_loader.py","__init__.py","scrip_slim.cpython-312.pyc","template_config_builder.cpython-312.pyc","template_loader.cpython-312.pyc","__init__.cpython-312.pyc","__init__.py"]}
                
            // 创建代码实体
            CodeEntity codeEntity = new CodeEntity();
            codeEntity.setNode(node);
            codeEntity.setNodeType(CodeEntity.NodeType.Pyxis);
            codeEntity.setCreatorId(userId);
            codeEntity.setStatus(ApprovalStatus.PENDING);

            // 检查返回结果
            if (result != null && result.containsKey("task_id") && result.containsKey("saved_files")) {
                codeEntity.setPyxisTaskId(result.get("task_id").toString());
                ObjectMapper objectMapper = new ObjectMapper();
                codeEntity.setSavedFiles(objectMapper.writeValueAsString(result.get("saved_files")));
            } else {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "创建Pyxis任务失败");
            }

            // 保存代码实体
            CodeEntity savedCode = codeRepository.save(codeEntity);

            // 返回结果
            Map<String, Object> result2 = new HashMap<>();
            result2.put("id", savedCode.getId());
            result2.put("task_id", savedCode.getPyxisTaskId());
            ObjectMapper objectMapper = new ObjectMapper();
            result2.put("saved_files", objectMapper.readValue(savedCode.getSavedFiles(), List.class)); 
            
            return result2;
        } catch (Exception e) {
            log.error("上传代码文件夹失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "上传代码文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 审批代码
     */
    @Transactional
    public CodeDTO approveCodePyxis(Long codeId, boolean approved, String rejectReason, Long approverId) {
        try {
            // 获取代码实体
            CodeEntity codeEntity = codeRepository.findById(codeId)
                    .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));

            // 检查是否是节点所有者
            if (!nodeService.isNodeOwner(codeEntity.getNode().getId(), approverId)) {
                throw new BaseException(ResponseCode.SERVICE_ERROR, "您不是该节点的所有者，无权审批");
            }
            // 更新审批状态
            if (approved) {
                // 调用Pyxis服务批准代码
                if (codeEntity.getPyxisTaskId() != null) {
                    pyxisService.approvePyxisTask(codeEntity.getNode().getId(), codeEntity.getPyxisTaskId(),"",true);
                }

                codeEntity.setStatus(ApprovalStatus.APPROVED);
            } else {
                // 调用Pyxis服务拒绝代码
                if (codeEntity.getPyxisTaskId() != null) {
                    pyxisService.approvePyxisTask(codeEntity.getNode().getId(), codeEntity.getPyxisTaskId(), rejectReason,false);
                }

                codeEntity.setStatus(ApprovalStatus.REJECTED);
                codeEntity.setRejectReason(rejectReason);
            }

            codeEntity.setApproverId(approverId);
            codeEntity.setExecuted(false);
            CodeEntity updatedCode = codeRepository.save(codeEntity);

            // 返回更新后的代码详情
            return getCodeDetail(updatedCode.getId(), approverId);

        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("审批代码失败", e);
            throw new BaseException(ResponseCode.SERVICE_ERROR, "审批代码失败: " + e.getMessage());
        }
    }

    
    /**
     * 执行Pyxis任务
     */
    public Map<String, Object> executePyxisTask(Long codeId, Long userId) {
        // 获取任务实体
        CodeEntity code = codeRepository.findById(codeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));

        // 检查代码状态
        if (code.getStatus() == ApprovalStatus.PENDING) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "代码未被批准，无法执行");
        }

        // 检查代码是否已执行过且未重新申请审批
        if (code.getExecuted() != null && code.getExecuted() && code.getStatus() == ApprovalStatus.APPROVED) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "代码已执行过，请重新申请审批后再执行");
        }

        // 执行Pyxis任务
        Map<String, Object> result = pyxisService.executePyxisTask(code.getNode().getId(), code.getPyxisTaskId(), userId);
        
        // 标记任务已执行，但不改变审批状态
        code.setExecuted(true);
        codeRepository.save(code);
        
        return result;
    }

    /**
     * 停止部署任务
     */
    public Map<String, Object> stopTask(Long codeId, Long userId) {
        // 获取任务实体
        CodeEntity code = codeRepository.findById(codeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));

        // 检查代码状态
        // if (code.getStatus() == ApprovalStatus.PENDING) {
        //     throw new BaseException(ResponseCode.SERVICE_ERROR, "代码未被批准，无法停止");
        // }

        // 停止Pyxis任务
        Map<String, Object> result = pyxisService.stopPyxisTask(code.getNode().getId(), code.getPyxisTaskId(), userId);
        
        // 标记任务已执行操作，但不改变审批状态
        code.setExecuted(true);
        codeRepository.save(code);
        
        return result;
    }

    /**
     * 强制终止部署任务
     */
    public Map<String, Object> killTask(Long codeId, Long userId) {
        // 获取任务实体
        CodeEntity code = codeRepository.findById(codeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));

        // 检查代码状态
        // if (code.getStatus() == ApprovalStatus.PENDING) {
        //     throw new BaseException(ResponseCode.SERVICE_ERROR, "代码未被批准，无法终止");
        // }

        // 强制终止Pyxis任务
        Map<String, Object> result = pyxisService.killPyxisTask(code.getNode().getId(), code.getPyxisTaskId(), userId);
        
        // 标记任务已执行操作，但不改变审批状态
        code.setExecuted(true);
        codeRepository.save(code);
        
        return result;
    }

    /**
     * 获取部署任务状态
     */
    public Map<String, Object> getTaskStatus(Long codeId, Long userId) {
        // 获取任务实体
        CodeEntity code = codeRepository.findById(codeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));

        // 获取Pyxis任务状态（不需要更新任务状态）
        return pyxisService.getPyxisTaskStatus(code.getNode().getId(), code.getPyxisTaskId(), userId);
    }

    /**
     * 浏览部署任务文件目录
     * @param codeId 代码ID
     * @param userId 用户ID
     * @param path 要浏览的目录路径，如果为null则浏览根目录
     * @return 目录内容
     */
    public Map<String, Object> browseTaskFiles(Long codeId, Long userId, String path) {
        // 获取任务实体
        CodeEntity code = codeRepository.findById(codeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));

        // 检查任务状态（不需要检查是否为APPROVED状态，因为浏览文件不需要任务处于特定状态）
        
        // 浏览Pyxis任务文件目录
        return pyxisService.browsePyxisTaskFiles(code.getNode().getId(), code.getPyxisTaskId(), userId, path);
    }

    /**
     * 下载任务文件
     * @param codeId 代码ID
     * @param userId 用户ID
     * @param filePath 文件路径
     * @return ResponseEntity<byte[]> 包含文件内容的响应实体
     */
    public ResponseEntity<byte[]> downloadTaskFile(Long codeId, Long userId, String filePath) {
        // 获取任务信息
        CodeEntity code = codeRepository.findById(codeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));

        // // 检查任务状态
        // if (!task.getStatus().equals(ModelDeployTaskEntity.TaskStatus.APPROVED)) {
        //     throw new BaseException(ResponseCode.SERVICE_ERROR, "任务未审批，无法下载文件");
        // }

        log.info("下载任务文件: taskId={}, filePath={}, userId={}", codeId, filePath, userId);
        return pyxisService.downloadPyxisTaskFile(code.getNode().getId(), code.getPyxisTaskId(), userId, filePath);
    }

    /**
     * 预览任务文本文件
     * @param codeId 代码ID
     * @param userId 用户ID
     * @param filePath 文件路径
     * @return String 文件内容文本
     */
    public String previewTaskFile(Long codeId, Long userId, String filePath) {
        // 获取任务信息
        CodeEntity code = codeRepository.findById(codeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));

        log.info("预览任务文件: taskId={}, filePath={}, userId={}", codeId, filePath, userId);
        return pyxisService.previewPyxisTaskFile(code.getNode().getId(), code.getPyxisTaskId(), userId, filePath);
    }

    /**
     * 申请重新审批
     */
    public Map<String, Object> requestReapproval(Long codeId, Long userId) {
        // 获取任务实体
        CodeEntity code = codeRepository.findById(codeId)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "代码不存在"));
        
        // 检查是否是代码的所有者
        if (!code.getCreatorId().equals(userId)) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "只有代码所有者才能申请重新审批");
        }
        
        // 更新代码状态为待审批
        code.setStatus(ApprovalStatus.PENDING);
        code.setExecuted(false);
        codeRepository.save(code);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "已成功申请重新审批");
        return result;
    }

} 