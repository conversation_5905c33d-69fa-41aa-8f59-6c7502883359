package ouc.isclab.common.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ouc.isclab.common.entity.ServiceLogEntity;
import ouc.isclab.common.pojo.ServiceLog;
import ouc.isclab.common.repository.ServiceLogRepository;
import org.springframework.transaction.annotation.Transactional;


import java.util.Date;
import java.util.List;

@Service
public class ServiceLogService {
    @Autowired
    private ServiceLogRepository serviceLogRepository;

    /**
     * 创建服务日志
     */
    public ServiceLogEntity create(ServiceLog serviceLog) {
        ServiceLogEntity serviceLogEntity = new ServiceLogEntity();
        serviceLogEntity.setClientIp(serviceLog.getClientIp());
        serviceLogEntity.setClientPort(serviceLog.getClientPort());
        serviceLogEntity.setHttpMethod(serviceLog.getHttpMethod());
        serviceLogEntity.setRequestUri(serviceLog.getRequestUri());
        serviceLogEntity.setDuration(serviceLog.getDuration());

        return serviceLogRepository.save(serviceLogEntity);
    }

    public List<ServiceLogEntity> findLogs(int page, int size, String ip, String method, Date startTime, Date endTime) {
        return serviceLogRepository.findLogs((page - 1) * size, size, ip, method, startTime, endTime);
    }
    
    public long countLogs(String ip, String method, Date startTime, Date endTime) {
        return serviceLogRepository.countLogs(ip, method, startTime, endTime);
    }

    @Transactional
    public int deleteLogs(String ip, String method, Date startTime, Date endTime) {

        return serviceLogRepository.deleteLogs(ip, method, startTime, endTime);
    }

}
