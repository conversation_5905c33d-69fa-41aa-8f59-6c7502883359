package ouc.isclab.common.interceptor;

import com.google.gson.JsonObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import ouc.isclab.common.annotation.RequirePermission;
import ouc.isclab.system.service.UserService;
import java.io.IOException;
import java.util.Arrays;

@Slf4j
public class PermissionInterceptor implements HandlerInterceptor {
    
    @Autowired
    private UserService userService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        RequirePermission permission = handlerMethod.getMethodAnnotation(RequirePermission.class);
        
        if (permission == null) {
            return true;
        }
        
        // 获取当前用户ID
        Long userId = (Long) request.getAttribute("userId");
        String[] requiredPermissions = permission.value();
        boolean logical = permission.logical();
        
        log.info("权限检查 - 用户ID: {}, 请求路径: {}, 需要权限: {}, 逻辑类型: {}", 
            userId, 
            request.getRequestURI(),
            Arrays.toString(requiredPermissions),
            logical ? "AND" : "OR"
        );
        
        boolean hasPermission = checkPermission(userId, requiredPermissions, logical);
        
        if (!hasPermission) {
            log.warn("权限不足 - 用户ID: {}, 请求路径: {}, 所需权限: {}", 
                userId,
                request.getRequestURI(),
                Arrays.toString(requiredPermissions)
            );
            handleNoPermission(response, requiredPermissions);
            return false;
        }
        
        log.info("权限验证通过 - 用户ID: {}, 请求路径: {}", 
            userId,
            request.getRequestURI()
        );
        return true;
    }
    
    private boolean checkPermission(Long userId, String[] permissions, boolean logical) {
        if (logical) { // AND
            boolean result = userService.hasAllPermissions(userId, permissions);
            log.debug("权限检查(AND) - 用户ID: {}, 权限: {}, 结果: {}", 
                userId, 
                Arrays.toString(permissions),
                result
            );
            return result;
        } else { // OR
            boolean result = userService.hasAnyPermission(userId, permissions);
            log.debug("权限检查(OR) - 用户ID: {}, 权限: {}, 结果: {}", 
                userId, 
                Arrays.toString(permissions),
                result
            );
            return result;
        }
    }
    
    private void handleNoPermission(HttpServletResponse response, String[] permissions) throws IOException {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("application/json;charset=utf-8");
        
        JsonObject result = new JsonObject();
        result.addProperty("code", 403);
        result.addProperty("msg", "权限不足，需要权限：" + Arrays.toString(permissions));
        
        response.getWriter().write(result.toString());
    }
} 