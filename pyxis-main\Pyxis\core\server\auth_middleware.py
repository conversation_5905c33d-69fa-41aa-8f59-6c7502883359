from contextlib import contextmanager
import re
from typing import Callable, Dict, Any
import os

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse, RedirectResponse
from jose import JW<PERSON>rror, jwt
from starlette.middleware.base import BaseHTTPMiddleware

from ..stash.models import SessionLocal, Task
from .users import SECRET_KEY, ALGORITH<PERSON>
from ..utils.response_utils import safely_read_json_response

# ----------------------------
# Utils Functions
# ----------------------------


@contextmanager
def get_db_session():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def extract_token(request: Request) -> str:
    """Extract JWT token from authorization header"""
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid authorization header"
        )
    return auth_header.split(" ")[1]


async def validate_token(token: str) -> dict:
    """Validate JWT token and return user info"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("user_id")
        role = payload.get("role")

        if not user_id or not role:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload"
            )

        return {"user_id": user_id, "role": role}
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )


def paths_match(pattern: str, request: Request) -> bool:
    """
    Check if the request path matches the given pattern with {param} placeholders.
    If matched, extracts parameters and stores them in `request.state.path_params`.

    Example:
        - Pattern: `/task/{task_id}/launch`
        - Path: `/task/123/launch`
        - Extracted: `{"task_id": "123"}`
    """
    path = request.url.path
    escaped_pattern = re.escape(pattern)

    # Replace `{param}` with named regex group `(?P<param>[^/]+)`
    regex_pattern = re.sub(r'\\{([^}]+)\\}', r'(?P<\1>[^/]+)', escaped_pattern)
    regex_pattern = f'^{regex_pattern}$'

    match = re.fullmatch(regex_pattern, path)
    if not match:
        return False

    # Store extracted params in request.state
    if not hasattr(request.state, "path_params"):
        request.state.path_params = {}

    request.state.path_params.update(match.groupdict())
    return True

# ----------------------------
# Handler Functions
# ----------------------------


async def default_handler(
    request: Request,
    call_next: Callable
) -> Any:
    """Default handler that passes through"""
    return await call_next(request)


async def read_only_handler(
    request: Request,
    call_next: Callable
) -> Any:
    """Handler for read-only endpoints"""
    allowed_roles = ["admin", "user", "auditor"]
    user_info = request.state.user
    if user_info["role"] not in allowed_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    return await call_next(request)


async def tasks_list_handler(
    request: Request,
    call_next: Callable
) -> Any:
    """Handler that filters tasks response based on user permissions"""
    # Admin and auditor can see all tasks - pass through
    user_info = request.state.user
    if user_info["role"] in ("admin", "auditor"):
        return await call_next(request)

    user_id = user_info["user_id"]

    try:
        with get_db_session() as session:
            user_tasks = session.query(Task).filter(
                Task.owner_id == user_id).all()

            filtered_tasks = [
                {
                    "task_id": task.task_id,
                    "path": f"{task.task_id}",
                    "runable": task.allowed_times,
                    "audit_message": task.audit_message
                } for task in user_tasks
            ]

            return JSONResponse(content=filtered_tasks)

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Invalid task data format"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Task filtering failed: {str(e)}"
        )


async def root_path_handler(
    request: Request,
    call_next: Callable
) -> Any:
    try:
        user_info = request.state.user

        if user_info["role"] == "admin":
            return RedirectResponse(url="/static/users.html")
        elif user_info["role"] == "auditor":
            return RedirectResponse(url="/static/auditor.html")
        elif user_info["role"] == "user":
            return await call_next(request)
        return RedirectResponse(url="/static/login.html")
    except Exception:
        return RedirectResponse(url="/static/login.html")


async def task_creation_handler(
    request: Request,
    call_next: Callable
) -> Any:
    """Handler for task creation"""
    user_info = request.state.user

    response = await call_next(request)

    if response.status_code == status.HTTP_201_CREATED:
        response_data, response = await safely_read_json_response(response)

        task_id = response_data.get("task_id")
        if task_id:
            with get_db_session() as session:
                try:
                    db_task = Task(
                        task_id=task_id,
                        owner_id=user_info["user_id"],
                        allowed_times=0,
                        audit_message="Pending approval"
                    )
                    session.add(db_task)
                    session.commit()
                except Exception:
                    session.rollback()
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="database error"
                    )

    return response


async def task_control_handler(
    request: Request,
    call_next: Callable
) -> Any:
    """Handler for task control operations (stop/kill)"""
    user_info = request.state.user
    task_id = request.state.path_params["task_id"]
    with get_db_session() as session:
        task = session.query(Task).filter(Task.task_id == task_id).first()

        if not task and user_info["role"] != "admin":
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )

        if task is not None and user_info["role"] != "admin" and task.owner_id != user_info["user_id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to control this task"
            )

        return await call_next(request)


async def task_launch_handler(
    request: Request,
    call_next: Callable
) -> Any:
    """Handler for task launch"""
    user_info = request.state.user
    task_id = request.state.path_params["task_id"]

    with get_db_session() as session:
        task = session.query(Task).filter(Task.task_id == task_id).first()

        if task is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )

        if user_info["role"] != "admin" and user_info["user_id"] != task.owner_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Task can only be launched by owner or admin"
            )

        if task.allowed_times <= 0 and user_info["role"] != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Task not approved or run limit reached"
            )

        if user_info["role"] != "admin":
            task.allowed_times -= 1
            session.commit()

        return await call_next(request)


async def task_deletion_handler(
    request: Request,
    call_next: Callable
) -> Any:
    """Handler for task deletion"""
    user_info = request.state.user
    task_id = request.state.path_params["task_id"]

    with get_db_session() as session:
        task = session.query(Task).filter(Task.task_id == task_id).first()

        # if admin, ignore not found
        if task is None and user_info["role"] != "admin":
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )

        if task is not None and user_info["role"] != "admin" and user_info["user_id"] != task.owner_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Task can only be deleted by owner or admin"
            )

        try:
            if task is not None:
                session.delete(task)
            session.commit()
            response = await call_next(request)
        except HTTPException:
            session.rollback()
            raise
        except Exception:
            session.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete task record from database"
            )

        return response

# ----------------------------
# Middleware Class
# ----------------------------


class AuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        """Initialize middleware with API handlers"""
        super().__init__(app)
        self.handler_map = self._build_handler_map()

    def _build_handler_map(self) -> Dict[tuple, Callable]:
        """Build mapping of (method, path) to handler functions"""
        return {
            ("POST", "/task"): task_creation_handler,
            ("POST", "/task/{task_id}/launch"): task_launch_handler,
            ("POST", "/task/{task_id}/stop"): task_control_handler,
            ("POST", "/task/{task_id}/kill"): task_control_handler,
            ("DELETE", "/task/{task_id}"): task_deletion_handler,
            ("GET", "/tasks"): tasks_list_handler,
            ("GET", "/task/{task_id}/status"): read_only_handler,
            ("GET", "/"): root_path_handler,
            ("*", "*"): default_handler
        }

    async def dispatch(self, request: Request, call_next: Callable):
        """Middleware entry point - routes to appropriate handler"""
        try:
            if not self._auth_enabled():
                request.state.user = {"user_id": "anonymous", "role": "admin"}
                return await default_handler(
                    request,
                    call_next
                )

            if request.url.path in ('/static/login.html', '/users/token', '/favicon.ico'):
                return await call_next(request)

            if request.url.path.startswith('/service'):
                return await call_next(request)

            if request.url.path == "/":
                return await root_path_handler(request, call_next)

            if request.url.path.startswith("/static/"):
                return await call_next(request)

            token = extract_token(request)
            user_info = await validate_token(token)
            request.state.user = user_info

            handler = self._resolve_handler(request)
            return await handler(
                request,
                call_next
            )

        except HTTPException as e:
            raise e
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=str(e)
            )

    def _resolve_handler(self, request: Request) -> Callable:
        """Find the appropriate handler for the request"""
        for (method, path), handler in self.handler_map.items():
            if method != "*" and method != request.method:
                continue
            if paths_match(path, request):  # Extracts all {param} values
                return handler

        return default_handler

    def _auth_enabled(self):
        return (os.getenv("AUTH_ENABLE", "False") == "True")
