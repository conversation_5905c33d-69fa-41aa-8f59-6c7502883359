package ouc.isclab.node.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.node.entity.NodeEntity;
import ouc.isclab.node.entity.NodeResourceEntity;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.node.service.NodeResourceService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys")
public class NodeResourceController {
    @Autowired
    private NodeResourceService nodeResourceService;

    /**
     * 查看节点状态
     */
    @GetMapping("/nodeResource/{nodeId}")
    public NodeResourceEntity getLatest(@PathVariable long nodeId) {
        return nodeResourceService.getNodeResources(nodeId);
    }

//    @RequestMapping(value = "/list",method = RequestMethod.GET)
//    public List<NodeResourceEntity> list(@RequestParam long nodeId) {
//        return nodeResourceService.getNodeResources(nodeId);
//    }

    /**
     * 更新节点状态
     */
    @PutMapping("/nodeResource/{nodeId}")
    public NodeResourceEntity check(@PathVariable long nodeId) {
        return nodeResourceService.updateNodeResource(nodeId);
    }

    /**
     * 查看所有节点状态
     */
    @GetMapping("/nodeResources")
    public Map<String, Object> listNodeResources(
            @RequestParam(defaultValue = "1") int page,  // 默认页码为 1
            @RequestParam(defaultValue = "10") int size // 默认每页大小为 10
    ) {
        Pageable pageable = PageRequest.of(page - 1, size);  // 页码从 0 开始，所以减 1
        Page<NodeResourceEntity> nodeResourcesPage = nodeResourceService.getAllNodeResources(pageable);
        Map<String, Object> data = new HashMap<>();
        data.put("nodeResources", nodeResourcesPage.getContent());  // 当前页的数据
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);  // 当前页码
        pagination.put("size", size);  // 每页大小
        pagination.put("total", nodeResourcesPage.getTotalElements());  // 总记录数
        data.put("pagination", pagination);  // 将pagination放入data中
        return data;
    }

    /**
     * 更新所有节点状态
     */
    @PutMapping(value = "/nodeResources")
    public List<NodeResourceEntity> checkAll() {
        return nodeResourceService.checkAllNodeStatuses();
    }

    /**
     * 删除节点状态信息
     */
    @DeleteMapping(value = "/nodeResource/{nodeId}")
    public void deleteResource(@PathVariable long nodeId) {
        nodeResourceService.deleteNodeResources(nodeId);
    }

    /**
     * 根据节点名称搜索节点资源
     */
    @GetMapping(value = "/nodeResource/search/{name}")
    public NodeResourceEntity searchNodeResourceByNodeName(@PathVariable String name) {
        return nodeResourceService.findNodeResourceByNodeName(name);
    }

    /**
     * 批量删除节点资源
     */
    @Transactional
    @DeleteMapping(value = "/nodeResource/batch")
    public void batchDeleteNodeResources(@RequestParam List<Long> ids) {
        nodeResourceService.batchDeleteNodeResources(ids);
    }
}