import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import store from './store'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import permission from './directives/permission'
import HelpDrawer from './components/HelpDrawer.vue'

const app = createApp(App)
app.use(store)
app.use(router)

app.use(ElementPlus)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
import 'virtual:windi.css'

//引入全局守卫
import "./permission"

import "nprogress/nprogress.css"

// 确保引入了 Element Plus 的暗色主题样式
import 'element-plus/theme-chalk/dark/css-vars.css'

app.directive('permission', permission)

app.component('HelpDrawer', HelpDrawer)

app.mount('#app')
