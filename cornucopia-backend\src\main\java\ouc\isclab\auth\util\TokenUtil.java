package ouc.isclab.auth.util;

import java.util.Calendar;
import java.util.Date;
import java.util.UUID;

public class TokenUtil {
    private static final int TOKEN_EXPIRE_HOURS = 24;

    public static String generateToken() {
        return UUID.randomUUID().toString();
    }

    public static Date generateExpireTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, TOKEN_EXPIRE_HOURS);
        return calendar.getTime();
    }
} 