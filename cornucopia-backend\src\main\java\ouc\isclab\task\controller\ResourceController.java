package ouc.isclab.task.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.annotation.CurrentUserId;
import ouc.isclab.common.response.BaseResponse;
import ouc.isclab.task.service.UserResourceService;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys")
public class ResourceController {

    @Autowired
    private UserResourceService userResourceService;
    
    /**
     * 获取用户可访问的资源列表（包含节点完整信息）
     */
    @GetMapping("/user/resources")
    public Map<String, Object> getUserResources(
            @RequestParam(required = false) String resourceType,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @CurrentUserId Long userId) {

        Pageable pageable = PageRequest.of(page - 1, size);
        Page<Map<String, Object>> resourcesPage = userResourceService.getUserResourcesWithNodeInfo(userId, resourceType, pageable);

        Map<String, Object> result = new HashMap<>();
        result.put("resources", resourcesPage.getContent());

        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", resourcesPage.getTotalElements());
        result.put("pagination", pagination);

        return result;
    }

    /**
     * 检查用户是否有权限访问特定资源
     */
    @GetMapping("/user/resource/check")
    public Map<String, Boolean> checkResourceAccess(
            @RequestParam String resourceType,
            @RequestParam Long resourceId,
            @RequestParam Long nodeId,
            @CurrentUserId Long userId) {

        boolean hasAccess = userResourceService.hasResourceAccess(
                userId, resourceType, resourceId, nodeId
        );

        Map<String, Boolean> result = new HashMap<>();
        result.put("hasAccess", hasAccess);

        return result;
    }
    
    /**
     * 获取资源的Minio配置
     * 根据资源ID和可选的模型路径，返回相应的Minio配置信息
     */
    @GetMapping("/resource/minio-config")
    public Map<String, Object> getResourceMinioConfig(
            @RequestParam Long resourceId,
            @RequestParam(required = false) String modelPath,
            @CurrentUserId Long userId) {
        
        // 直接调用UserResourceService的方法获取MinIO配置
        return userResourceService.getResourceMinioConfig(userId, resourceId, modelPath);
    }
}