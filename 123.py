import requests
import time
import re
import random

def getCode():
    code = ''.join([str(random.randint(0, 9)) for _ in range(5)])
    url = "https://www.perplexity.ai/account/pro-perks?_rsc=" + code

    payload = {}
    headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'next-url': '/account/pro-perks',
    'priority': 'u=1, i',
    'referer': 'https://www.perplexity.ai/account/pro-perks?_rsc=' +code,
    'rsc': '1',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-arch': '"x86"',
    'sec-ch-ua-bitness': '"64"',
    'sec-ch-ua-full-version': '"136.0.7103.114"',
    'sec-ch-ua-full-version-list': '"Chromium";v="136.0.7103.114", "Google Chrome";v="136.0.7103.114", "Not.A/Brand";v="99.0.0.0"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-model': '""',
    'sec-ch-ua-platform': '"Windows"',
    'sec-ch-ua-platform-version': '"19.0.0"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'Cookie': 'pplx.visitor-id=2714993e-46e7-4191-a772-a32b97183f08; pplx.session-id=933e698a-261e-48f5-b69e-c2246bc788f3; __cflb=02DiuDyvFMmK5p9jVbVnMNSKYZhUL9aGmNXXBG4tXgXCx; pplx.search-mode-layout-variant=enabled; next-auth.csrf-token=80771d846e1266ea7271b426428356bde3d2578bc083f87fa155d2a318dcbae3%7Cb3d9f84cc1a989e1d1352b5959eac727e70f111513d8cadc3e9629e63d16e92e; sidebarHiddenHubs=[]; pplx.source-selection-v3-space-=[]; pplx.source-selection-v3-space-7c677d48-b8f8-4fb7-b66e-93278a3cf4f4=[]; _fbp=fb.1.1747904697681.857488054657784397; intercom-device-id-l2wyozh0=e15caf75-bf03-41ca-8bdf-9cd8d8c80d36; __cf_bm=WsTvdoM8HzOlSz828su5MB3HmVVWau3DDsI4EIBlYz4-1747910121-*******-dMwv_MXMAwwa0If5YpoIpm0wnChlbQtFu91i9Oa4jX6U3QR_RqjKdAKXhgNKuA9sTPG_B0NFB3X8JhHh2rhsreI5M_i_CFxBiIXSFBBWKMU; intercom-session-l2wyozh0=Q0pzUHJDMW1NTVNNV3VlODVudzZOM0FwOUZzMDd0RWpkWThZMklTL0k4SFd4bVE4SkEvTVJab2xidWtrQlZzdTlSUnZsbmI3T0lscHZIT3hwTEd1YmEzZEg0ejEzcnY3ditpZGRyLzV1K2s9LS03eEx4c0p2L3d6Tkk3VEF6NzdmZkFnPT0=--468299fced9981932590d5dc8a9f01d37bfcbf23; pplx.is-incognito=false; next-auth.callback-url=https%3A%2F%2Fwww.perplexity.ai%2Fapi%2Fauth%2Fsignin-callback%3Fredirect%3Dhttps%253A%252F%252Fwww.perplexity.ai%252F%253Flogin-source%253DfloatingSignup; cf_clearance=mW27bDB8aQz4GxIjdK9avrgdCNOBHTcTcWMSilpweMA-1747910163-*******-E25ed6qfETGcofdpohPvQ47hxwffNTQvqLcU1v16ytVCsKjoF3u8FxCL1O_.sYIXYvGTpmIfO16r60SI4RR4B2BJQiVOfDMCzrf0tPBAD5aZckoGy8A0Q4EPkmpXXKBIlacZ7JzCW.IpXryOIPX4ktXPiIRbcE13VmAVW1cAaIsZlR7dH7Lebm3TJGgMDRuD78hJ24Hl9BgPzHncDXWCq5yYQvXdYPptmknBQqJU16socf4GSRvhaC4d9sUNYMJZ7P0h_ge7zvy3f.i.YJ7hwJqPnlfqmCvJYZ6DFnI9iqXFULIeHsRuNMJgrg_qTQCECHXPOUwoXVS7aTfgP9bRkpwsc9VRpjBjGUA74gZlhgbD3gbX8yezo26SnCdeZVZb; __Secure-next-auth.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..tdOPV3MlHsTo-iRH.Xupb5fIn5TeTJO_ziKNdkuWIW71Dcwzs61atnFfBW7nortFXSM0J6jThLJBUgeSAy3MEjQ4zi49CQ8fc2zPvHSo_E8lyyeuUrBbCkNQUmnmdEMOC20aiqCGqZ3V9S1tZZ0K7r08bPmpWQ-XJukdzWCDPg3X-Td-X81ZodbE9LL6n5OS5ksaLqBIl6763_Bs-RvIxSt7ZuoS3sGlVkZnXc5j_ZZ3lh5dYm09cEWAgT2seE3knO9yqn6aWndN7xW6C.q7ld4hJfgifz7rPNnygSqA; _rdt_uuid=1747904697155.90e0393a-3b86-41eb-80e5-6fd2f5a85c53; pplx.metadata={%22qc%22:7%2C%22qcu%22:0%2C%22qcm%22:0%2C%22qcc%22:0%2C%22qcr%22:0%2C%22qcdr%22:0%2C%22qcs%22:0%2C%22qcd%22:0%2C%22hli%22:true%2C%22hcga%22:false%2C%22hcds%22:false%2C%22hso%22:true%2C%22hfo%22:true}; AWSALB=MNF2WKSV7Rar9v5a59FheTi+f7MV9O03/639FuaZhFBvYF0ApkysgreI2ts/JamWOhfYAINLATrVr0yrlMDbNGTeBC0OfL0HqWkz1Wb92HJGz7dkesRwFNDQutAX; AWSALBCORS=MNF2WKSV7Rar9v5a59FheTi+f7MV9O03/639FuaZhFBvYF0ApkysgreI2ts/JamWOhfYAINLATrVr0yrlMDbNGTeBC0OfL0HqWkz1Wb92HJGz7dkesRwFNDQutAX; _dd_s=aid=0ea7699f-4df7-4f6a-afed-d37ecbf21ead&rum=0&expire=1747911103884&logs=0'
    }

    response = requests.request("GET", url, headers=headers, data=payload)
    return response.text

target_pattern = r'https://cursor.com/referral\?code=(.*?)"'

def extract_url(text):
    matches = re.findall(target_pattern, text)
    return matches

for i in range(1000):
    jsonData = getCode()
    matches = extract_url(jsonData)
    
    if matches:
        for match in matches:
            print(f"Code: {match}")
    time.sleep(0.5)