from typing import Dict, List, Union, Literal, Optional, TypedDict
from dataclasses import dataclass


class InputFieldOption(TypedDict):
    """Option for select/radio/checkbox fields."""
    value: str
    label: str
    selected: Optional[bool]
    checked: Optional[bool]


@dataclass
class InputFieldConfig:
    """Base configuration for a form field."""
    name: str
    type: Literal[
        'text', 'number', 'email', 'password', 'file', 'multifile',
        'select', 'radio', 'checkbox', 'img', 'html', 'table', 'color'
    ]
    label: Optional[str] = None
    placeholder: Optional[str] = None
    value: Optional[Union[str, int, bool]] = None
    required: Optional[bool] = None
    accept: Optional[str] = None
    description: Optional[str] = None


class InputTemplateConfig:
    """Builder for creating input template configurations.

    This builder helps construct configuration dictionaries for dynamic form generation
    using a type-safe and IDE-friendly interface.

    Example:
        >>> config = (
        ...     InputTemplateConfig()
        ...     .set_title("User Registration")
        ...     .add_text_field(name="username", label="Username", required=True)
        ...     .add_email_field(name="email", placeholder="<EMAIL>")
        ...     .add_select_field(
        ...         name="gender",
        ...         options=[
        ...             {"value": "male", "label": "Male"},
        ...             {"value": "female", "label": "Female"}
        ...         ]
        ...     )
        ...     .build()
        ... )

    Attributes:
        _config (Dict): The internal configuration dictionary being built.
    """

    def __init__(self) -> None:
        """Initialize a new empty configuration builder."""
        self._config: Dict = {
            "title": "Model Service",
            "fields": []
        }

    def set_title(self, title: str) -> 'InputTemplateConfig':
        """Set the title for the form.

        Args:
            title: The title to display at the top of the form.

        Returns:
            The builder instance for method chaining.
        """
        self._config["title"] = title
        return self

    def add_custom_field(self, **kwargs) -> 'InputTemplateConfig':
        """Add a completely custom field configuration.

        This method allows adding fields with custom configurations that may not be
        covered by the standard field methods. All provided keyword arguments will
        be directly added to the field configuration.

        Example:
            >>> config.add_custom_field(
            ...     type="custom-type",
            ...     name="custom_field",
            ...     custom_property="value",
            ...     another_property=123
            ... )

        Args:
            **kwargs: Key-value pairs to include in the field configuration.

        Returns:
            The builder instance for method chaining.

        Raises:
            ValueError: If neither 'type' nor 'name' is provided in kwargs.
        """
        if 'type' not in kwargs or 'name' not in kwargs:
            raise ValueError(
                "Custom field must include both 'type' and 'name'")
        self._config["fields"].append(kwargs)
        return self

    def add_text_field(
        self,
        name: str,
        label: Optional[str] = None,
        placeholder: Optional[str] = None,
        value: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a text input field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            placeholder: Example text shown when the field is empty.
            value: Default value for the field.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "text",
            "name": name,
            "label": label,
            "placeholder": placeholder,
            "value": value,
            "required": required,
            "description": description
        })
        return self

    def add_file_field(
        self,
        name: str,
        label: Optional[str] = None,
        accept: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a file upload field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            accept: File types to accept (e.g. "image/*", ".pdf,.docx").
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "file",
            "name": name,
            "label": label,
            "accept": accept,
            "required": required,
            "description": description
        })
        return self

    def add_select_field(
        self,
        name: str,
        options: List[InputFieldOption],
        label: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a dropdown select field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            options: List of available options.
            label: User-facing label displayed with the field.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "select",
            "name": name,
            "label": label,
            "options": options,
            "required": required,
            "description": description
        })
        return self

    def add_checkbox_field(
        self,
        name: str,
        label: str,
        value: str = "on",
        checked: bool = False,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a single checkbox field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the checkbox.
            value: Value to submit when checked.
            checked: Whether the checkbox is initially checked.
            required: Whether the field must be checked before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "checkbox",
            "name": name,
            "label": label,
            "value": value,
            "checked": checked,
            "required": required,
            "description": description
        })
        return self

    def add_image_field(
        self,
        name: str,
        label: Optional[str] = None,
        placeholder: Optional[str] = None,
        default_text: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add an image upload field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            placeholder: Placeholder text for the upload button.
            default_text: Text to show when no image is selected.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "img",
            "name": name,
            "label": label,
            "placeholder": placeholder or "Click to select or drag & drop an image",
            "default_text": default_text or "No image selected",
            "required": required,
            "description": description
        })
        return self

    def add_html_content(
        self,
        content: str
    ) -> 'InputTemplateConfig':
        """Add static HTML content to the form.

        Args:
            content: HTML string to include in the form.

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "html",
            "content": content
        })
        return self

    def add_number_field(
        self,
        name: str,
        label: Optional[str] = None,
        placeholder: Optional[str] = None,
        value: Optional[Union[int, float]] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a number input field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            placeholder: Example text shown when the field is empty.
            value: Default numeric value for the field.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "number",
            "name": name,
            "label": label,
            "placeholder": placeholder,
            "value": value,
            "required": required,
            "description": description
        })
        return self

    def add_email_field(
        self,
        name: str,
        label: Optional[str] = None,
        placeholder: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add an email input field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            placeholder: Example text shown when the field is empty.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "email",
            "name": name,
            "label": label,
            "placeholder": placeholder,
            "required": required,
            "description": description
        })
        return self

    def add_password_field(
        self,
        name: str,
        label: Optional[str] = None,
        placeholder: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a password input field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            placeholder: Example text shown when the field is empty.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "password",
            "name": name,
            "label": label,
            "placeholder": placeholder,
            "required": required,
            "description": description
        })
        return self

    def add_multi_file_field(
        self,
        name: str,
        label: Optional[str] = None,
        help_text: Optional[str] = None,
        accept: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a multiple file upload field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            help_text: Additional instructions shown to users below the field.
            accept: File types to accept (e.g. "image/*", ".pdf,.docx").
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "multifile",
            "name": name,
            "label": label,
            "help_text": help_text,
            "accept": accept,
            "required": required,
            "description": description
        })
        return self

    def add_image_display(
        self,
        label: str,
        src: str
    ) -> 'InputTemplateConfig':
        """Add a static image display to the form.

        Args:
            label: User-facing label displayed with the image.
            src: Image source URL or base64 encoded data.
            description: Developer documentation explaining the image's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "img",
            "label": label,
            "src": src
        })
        return self

    def add_radio_field(
        self,
        name: str,
        options: List[InputFieldConfig],
        label: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a radio button group to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            options: List of radio button options.
            label: User-facing label displayed with the field.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "radio",
            "name": name,
            "label": label,
            "options": options,
            "required": required,
            "description": description
        })
        return self

    def add_checkbox_group(
        self,
        name: str,
        options: List[InputFieldOption],
        label: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a checkbox group to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            options: List of checkbox options.
            label: User-facing label displayed with the field.
            required: Whether at least one checkbox must be selected.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "checkbox",
            "name": name,
            "label": label,
            "options": options,
            "required": required,
            "description": description
        })
        return self

    def add_date_field(
        self,
        name: str,
        label: Optional[str] = None,
        value: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a date picker field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            value: Default date value in YYYY-MM-DD format.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "date",
            "name": name,
            "label": label,
            "value": value,
            "required": required,
            "description": description
        })
        return self

    def add_datetime_field(
        self,
        name: str,
        label: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a datetime picker field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "datetime-local",
            "name": name,
            "label": label,
            "required": required,
            "description": description
        })
        return self

    def add_color_field(
        self,
        name: str,
        label: Optional[str] = None,
        value: str = "#4285f4",
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a color picker field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            value: Default color value in HEX format.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "color",
            "name": name,
            "label": label,
            "value": value,
            "required": required,
            "description": description
        })
        return self

    def add_range_field(
        self,
        name: str,
        label: Optional[str] = None,
        min: int = 0,
        max: int = 100,
        step: int = 1,
        value: Optional[int] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a range slider field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            min: Minimum value of the range.
            max: Maximum value of the range.
            step: Increment step size.
            value: Default value (defaults to midpoint between min and max).
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "range",
            "name": name,
            "label": label,
            "min": min,
            "max": max,
            "step": step,
            "value": value or ((max - min) // 2),
            "required": required,
            "description": description
        })
        return self

    def add_url_field(
        self,
        name: str,
        label: Optional[str] = None,
        placeholder: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a URL input field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            placeholder: Example text shown when the field is empty.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "url",
            "name": name,
            "label": label,
            "placeholder": placeholder,
            "required": required,
            "description": description
        })
        return self

    def add_phone_field(
        self,
        name: str,
        label: Optional[str] = None,
        pattern: Optional[str] = None,
        required: bool = False,
        description: Optional[str] = None
    ) -> 'InputTemplateConfig':
        """Add a telephone number input field to the form.

        Args:
            name: Unique identifier for the field (used in form submission data).
            label: User-facing label displayed with the field.
            pattern: Regex pattern for validation.
            required: Whether the field must be filled before submission.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "tel",
            "name": name,
            "label": label,
            "pattern": pattern,
            "required": required,
            "description": description
        })
        return self

    def build(self) -> Dict:
        """Finalize and return the built configuration.

        Returns:
            A dictionary containing the complete form configuration.
        """
        return self._config.copy()


class TableHeader(TypedDict):
    """Configuration for table headers."""
    name: str
    label: Optional[str]


@dataclass
class OutputFieldConfig:
    """Base configuration for an output display field."""
    name: str
    type: Literal[
        'text', 'list', 'json', 'img', 'svg', 'html', 'table'
    ]
    label: Optional[str] = None
    format: Optional[str] = None  # For formatting values (e.g. date format)
    description: Optional[str] = None


class OutputTemplateConfig:
    """Builder for creating output display configurations.

    This builder helps construct configuration dictionaries for dynamic result 
    rendering using a type-safe and IDE-friendly interface.

    Example:
        >>> config = (
        ...     OutputTemplateConfig()
        ...     .set_title("Analysis Results")
        ...     .add_text_field(name="summary", label="Summary")
        ...     .add_table_field(
        ...         name="statistics",
        ...         headers=[
        ...             {"name": "metric", "label": "Metric"},
        ...             {"name": "value", "label": "Value"}
        ...         ]
        ...     )
        ...     .add_image_field(name="chart", label="Result Chart")
        ...     .build()
        ... )

    Attributes:
        _config (Dict): The internal configuration dictionary being built.
    """

    def __init__(self) -> None:
        """Initialize a new empty configuration builder."""
        self._config: Dict = {
            "title": "Results",
            "fields": []
        }

    def set_title(self, title: str) -> 'OutputTemplateConfig':
        """Set the title for the results display.

        Args:
            title: The title to display at the top of the results.

        Returns:
            The builder instance for method chaining.
        """
        self._config["title"] = title
        return self

    def add_custom_field(self, **kwargs) -> 'OutputTemplateConfig':
        """Add a completely custom field configuration.

        This method allows adding fields with custom configurations that may not be
        covered by the standard field methods. All provided keyword arguments will
        be directly added to the field configuration.

        Example:
            >>> config.add_custom_field(
            ...     type="custom-type",
            ...     name="custom_field",
            ...     custom_property="value",
            ...     another_property=123
            ... )

        Args:
            **kwargs: Key-value pairs to include in the field configuration.

        Returns:
            The builder instance for method chaining.

        Raises:
            ValueError: If neither 'type' nor 'name' is provided in kwargs.
        """
        if 'type' not in kwargs or 'name' not in kwargs:
            raise ValueError(
                "Custom field must include both 'type' and 'name'")
        self._config["fields"].append(kwargs)
        return self

    def add_text_field(
        self,
        name: str,
        label: Optional[str] = None,
        format: Optional[str] = None,
        description: Optional[str] = None
    ) -> 'OutputTemplateConfig':
        """Add a text display field to the results.

        Args:
            name: Field name matching the result data key.
            label: User-facing label displayed with the field.
            format: Format string for values (e.g. for dates/numbers).
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "text",
            "name": name,
            "label": label,
            "format": format,
            "description": description
        })
        return self

    def add_list_field(
        self,
        name: str,
        label: Optional[str] = None,
        item_format: Optional[str] = None,
        description: Optional[str] = None
    ) -> 'OutputTemplateConfig':
        """Add a list display field to the results.

        Args:
            name: Field name matching the result data key.
            label: User-facing label displayed with the field.
            item_format: Format string for list items.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "list",
            "name": name,
            "label": label,
            "item_format": item_format,
            "description": description
        })
        return self

    def add_json_field(
        self,
        name: str,
        label: Optional[str] = None,
        indent: int = 2,
        description: Optional[str] = None
    ) -> 'OutputTemplateConfig':
        """Add a JSON display field to the results.

        Args:
            name: Field name matching the result data key.
            label: User-facing label displayed with the field.
            indent: Indentation level for pretty-printing.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "json",
            "name": name,
            "label": label,
            "indent": indent,
            "description": description
        })
        return self

    def add_image_field(
        self,
        name: str,
        label: Optional[str] = None,
        description: Optional[str] = None
    ) -> 'OutputTemplateConfig':
        """Add an image display field to the results.

        Args:
            name: Field name matching the result data key.
            label: User-facing label displayed with the field.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "img",
            "name": name,
            "label": label,
            "description": description
        })
        return self

    def add_svg_field(
        self,
        name: str,
        label: Optional[str] = None,
        description: Optional[str] = None
    ) -> 'OutputTemplateConfig':
        """Add an SVG display field to the results.

        Args:
            name: Field name matching the result data key.
            label: User-facing label displayed with the field.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "svg",
            "name": name,
            "label": label,
            "description": description
        })
        return self

    def add_html_field(
        self,
        name: str,
        label: Optional[str] = None,
        description: Optional[str] = None
    ) -> 'OutputTemplateConfig':
        """Add an HTML content field to the results.

        Args:
            name: Field name matching the result data key.
            label: User-facing label displayed with the field.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "html",
            "name": name,
            "label": label,
            "description": description
        })
        return self

    def add_table_field(
        self,
        name: str,
        headers: List[str],
        label: Optional[str] = None,
        description: Optional[str] = None
    ) -> 'OutputTemplateConfig':
        """Add a table display field to the results.

        Args:
            name: Field name matching the result data key.
            headers: List of header names.
            label: User-facing label displayed with the table.
            description: Developer documentation explaining the field's purpose 
                       (appears in API documentation, not shown to end-users).

        Returns:
            The builder instance for method chaining.
        """
        self._config["fields"].append({
            "type": "table",
            "name": name,
            "label": label,
            "headers": headers,
            "description": description
        })
        return self

    def build(self) -> Dict:
        """Finalize and return the built configuration.

        Returns:
            A dictionary containing the complete output display configuration.
        """
        return self._config.copy()
