<template>
    <div class="code-pending-container">
      <div class="page-header">
        <div class="header-left">
          <div class="title-wrapper">
            <el-icon class="title-icon"><Document /></el-icon>
            <h2>代码审批</h2>
          </div>
          <div class="sub-title">审批用户提交的代码</div>
        </div>
        
      </div>

      <el-card class="list-card" shadow="hover">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          border
          stripe
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
          highlight-current-row
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column label="名称/函数" min-width="150" align="center">
            <template #default="scope">
              <div style="display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <el-icon class="file-icon" style="margin-right: 5px;">
                  <Document v-if="scope.row.nodeType !== 'Pyxis'" />
                  <Folder v-else />
                </el-icon>
                <span v-if="scope.row.nodeType !== 'Pyxis'">{{ scope.row.funcName }}</span>
                <span v-else-if="scope.row.pyxisTaskId">Pyxis任务: {{ scope.row.pyxisTaskId }}</span>
                <span v-else>Pyxis代码任务</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="代码节点类型" width="120" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.nodeType === 'Pyxis' ? 'success' : 'primary'">
                {{ scope.row.nodeType || '未知' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="节点" min-width="150" align="center">
            <template #default="scope">
              <el-tag>{{ scope.row.nodeIp }}:{{ scope.row.nodePort }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="所属任务" min-width="200" align="center">
            <template #default="scope">
              <div v-if="scope.row.taskNames && scope.row.taskNames.length > 0">
                <el-tag 
                  v-for="(taskName, index) in scope.row.taskNames" 
                  :key="index"
                  type="success" 
                  size="small"
                  style="margin: 2px;"
                >
                  {{ taskName }}
                </el-tag>
              </div>
              <span v-else style="color: #909399;">无关联任务</span>
            </template>
          </el-table-column>
          <el-table-column label="提交用户" min-width="120" align="center">
            <template #default="scope">
              <el-button 
                type="primary" 
                link 
                @click="viewUserInfo(scope.row.creatorId)"
              >
                {{ scope.row.creatorName || `用户${scope.row.creatorId}` }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="updateTime" 
            label="提交时间" 
            width="180" 
            align="center"
            sortable
          >
            <template #default="scope">
              {{ formatDateTime(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="scope">
              <div class="operation-buttons">
                <el-button-group v-if="scope.row.status === 'PENDING'">
                  <el-tooltip content="批准" placement="top">
                    <el-button type="success" size="small" @click="approveCode(scope.row)">
                      <el-icon><Check /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="拒绝" placement="top">
                    <el-button type="danger" size="small" @click="showRejectDialog(scope.row)">
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="查看代码" placement="top">
                    <el-button type="primary" size="small" @click="viewCodeDetail(scope.row)">
                      <el-icon><View /></el-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
                <el-tooltip content="查看详情" placement="top" v-else>
                  <el-button type="info" size="small" @click="viewCodeDetail(scope.row)">
                    <el-icon><View /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
  
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
  
      <!-- 拒绝理由对话框 -->
      <el-dialog
        v-model="rejectDialogVisible"
        title="拒绝代码"
        width="500px"
        destroy-on-close
      >
        <el-form :model="rejectForm" :rules="rejectRules" ref="rejectFormRef">
          <el-form-item label="拒绝理由" prop="reason" label-width="100px">
            <el-input
              v-model="rejectForm.reason"
              type="textarea"
              :rows="4"
              placeholder="请输入拒绝理由"
            ></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="rejectDialogVisible = false">取消</el-button>
            <el-button type="danger" @click="rejectCode" :loading="rejectLoading">
              确认拒绝
            </el-button>
          </span>
        </template>
      </el-dialog>
  
      <!-- 代码详情对话框 -->
      <el-dialog
        v-model="codeDetailVisible"
        title="代码详情"
        width="800px"
        destroy-on-close
      >
        <div v-loading="codeDetailLoading">
          <el-descriptions v-if="codeDetail" :column="1" border label-width="120px">
            <el-descriptions-item label="函数名称" v-if="codeDetail.nodeType !== 'Pyxis'">{{ codeDetail.funcName || '无' }}</el-descriptions-item>
            <el-descriptions-item label="节点">
              {{ codeDetail.nodeName }} ({{ codeDetail.nodeType || '未知类型' }})
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(codeDetail.status)">{{ getStatusText(codeDetail.status) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ codeDetail.createTime }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ codeDetail.updateTime }}</el-descriptions-item>
            <el-descriptions-item label="创建者">{{ codeDetail.creatorName }}</el-descriptions-item>
            <el-descriptions-item label="描述" v-if="codeDetail.description">{{ codeDetail.description }}</el-descriptions-item>
            <el-descriptions-item v-if="codeDetail.status === 'REJECTED'" label="拒绝原因">
              <span style="color: #f56c6c">{{ codeDetail.rejectReason }}</span>
            </el-descriptions-item>
            <el-descriptions-item v-if="codeDetail.status === 'APPROVED'" label="审批人">
              {{ codeDetail.approverName }}
            </el-descriptions-item>
            <el-descriptions-item label="Pyxis任务ID" v-if="codeDetail.nodeType === 'Pyxis' && codeDetail?.pyxisTaskId">
              {{ codeDetail.pyxisTaskId }}
            </el-descriptions-item>
            <el-descriptions-item label="Sycee代码ID" v-if="codeDetail.nodeType !== 'Pyxis' && codeDetail?.syceeCodeId">
              {{ codeDetail.syceeCodeId }}
            </el-descriptions-item>
            <el-descriptions-item label="Sycee请求ID" v-if="codeDetail.nodeType !== 'Pyxis' && codeDetail?.syceeRequestId">
              {{ codeDetail.syceeRequestId }}
            </el-descriptions-item>
            <el-descriptions-item label="Sycee任务ID" v-if="codeDetail.nodeType !== 'Pyxis' && codeDetail?.syceeJobId">
              {{ codeDetail.syceeJobId }}
            </el-descriptions-item>
            <el-descriptions-item label="保存的文件" v-if="parsedSavedFiles.length > 0">
              <div class="saved-files-list">
                <el-tag 
                  v-for="file in parsedSavedFiles" 
                  :key="file"
                  class="file-tag"
                  type="info"
                >
                  {{ file }}
                </el-tag>
              </div>
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="code-content-wrapper" v-if="codeDetail?.codeContent">
            <div class="code-header">
              <span>代码内容</span>
            </div>
            <pre class="code-pre">{{ codeDetail?.codeContent }}</pre>
          </div>
          
          <!-- 修改文件操作按钮区域，添加下载按钮 -->
          <div class="file-actions" v-if="codeDetail && codeDetail.nodeType === 'Pyxis'">
            <el-button type="primary" @click="browseFiles(codeDetail)">
              <el-icon><Folder /></el-icon> 浏览文件
            </el-button>
            <el-button type="success" @click="downloadFiles(codeDetail)">
              <el-icon><Download /></el-icon> 下载文件
            </el-button>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="codeDetailVisible = false">关闭</el-button>
            <el-button-group v-if="codeDetail && codeDetail.status === 'PENDING'">
              <el-button type="success" @click="approveFromDetail">批准</el-button>
              <el-button type="danger" @click="showRejectDialogFromDetail">拒绝</el-button>
            </el-button-group>
          </span>
        </template>
      </el-dialog>
  
      <!-- 用户信息对话框 -->
      <el-dialog
        v-model="userInfoDialogVisible"
        title="代码创建者信息"
        width="500px"
      >
        <div v-loading="userInfoLoading">
          <el-descriptions :column="1" border v-if="userInfo">
            <el-descriptions-item label="用户ID">{{ userInfo.userId }}</el-descriptions-item>
            <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
            <el-descriptions-item label="姓名">{{ userInfo.fullname || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{ userInfo.email || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="状态">{{ userInfo.enable ? '启用' : '禁用' }}</el-descriptions-item>
            <el-descriptions-item label="最后登录">
              {{ formatUserDateTime(userInfo.lastLogin) }}
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatUserDateTime(userInfo.createTime) }}
            </el-descriptions-item>
          </el-descriptions>
          <el-empty v-else description="无法获取用户信息"></el-empty>
        </div>
      </el-dialog>

      <!-- 文件浏览对话框 -->
      <el-dialog
        v-model="fileBrowserVisible"
        title="文件浏览器"
        width="700px"
        destroy-on-close
      >
        <div v-loading="fileBrowserLoading" class="file-browser">
          <div class="file-browser-header">
            <div class="current-path">{{ currentPath || '/' }}</div>
            <el-button size="small" @click="navigateUp" :disabled="currentPath === ''">
              <el-icon><Back /></el-icon> 返回上级
            </el-button>
          </div>
          
          <el-table :data="browserFiles" size="small" border style="width: 100%">
            <el-table-column prop="name" label="名称">
              <template #default="scope">
                <div class="file-item" @click="navigateToFile(scope.row)">
                  <el-icon class="file-icon">
                    <Folder v-if="scope.row.type === 'directory'" />
                    <Document v-else />
                  </el-icon>
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="大小" width="100" />
            <el-table-column label="修改时间" width="180">
              <template #default="scope">
                {{ formatFileTime(scope.row.modified) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button-group>
                  <el-button 
                    size="small" 
                    type="primary" 
                    @click.stop="downloadBrowserFile(scope.row)"
                    :disabled="scope.row.type === 'directory'"
                  >
                    <el-icon><Download /></el-icon>
                  </el-button>
                  <el-button
                    size="small"
                    type="info"
                    @click.stop="previewFile(scope.row)"
                    :disabled="scope.row.type === 'directory' || !isTextFile(scope.row.name)"
                  >
                    <el-icon><ZoomIn /></el-icon>
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-dialog>

      <!-- 文件预览对话框 -->
      <el-dialog
        v-model="filePreviewVisible"
        :title="`文件预览: ${previewFileName}`"
        width="700px"
        destroy-on-close
      >
        <div v-loading="filePreviewLoading" class="file-preview">
          <div class="preview-actions">
            <el-button type="primary" @click="downloadPreviewFile">
              <el-icon><Download /></el-icon> 下载文件
            </el-button>
            <el-button type="info" @click="copyPreviewContent">
              <el-icon><DocumentCopy /></el-icon> 复制内容
            </el-button>
          </div>
          <div class="preview-content">
            <pre>{{ filePreviewContent }}</pre>
          </div>
        </div>
      </el-dialog>

      <!-- 添加下载文件列表对话框 -->
      <el-dialog
        v-model="fileListDialogVisible"
        title="文件管理"
        width="700px"
        destroy-on-close
      >
        <div v-loading="fileBrowserLoading" class="file-list-container">
          <!-- 文件列表 -->
          <div class="file-list" v-if="fileList.length > 0">
            <div class="section-subtitle">文件列表</div>
            <el-table :data="fileList" size="small" border style="width: 100%">
              <el-table-column prop="name" label="文件名" />
              <el-table-column prop="size" label="大小" width="100" />
              <el-table-column label="修改时间" width="180">
                <template #default="scope">
                  {{ formatFileTime(scope.row.modified) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template #default="scope">
                  <el-button size="small" type="primary" @click="downloadFile(scope.row)">
                    <el-icon><Download /></el-icon>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-empty v-else description="没有可下载的文件" />
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="fileListDialogVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { 
    Search, 
    Refresh, 
    Check, 
    Close, 
    View, 
    Document, 
    Back,
    Download,
    Folder,
    DocumentCopy,
    ZoomIn
  } from '@element-plus/icons-vue';
  import service from '~/axios';
  import { toast, showModal } from '~/composables/util';
  import { ElLoading } from 'element-plus';
  
  // 数据加载状态
  const loading = ref(false);
  const rejectLoading = ref(false);
  const codeDetailLoading = ref(false);
  const userInfoLoading = ref(false);
  
  // 分页相关
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  
  // 表格数据
  const tableData = ref([]);
  
  // 对话框控制
  const rejectDialogVisible = ref(false);
  const codeDetailVisible = ref(false);
  const userInfoDialogVisible = ref(false);
  const rejectFormRef = ref(null);
  const codeDetail = ref(null);
  const userInfo = ref(null);
  
  // 拒绝表单
  const rejectForm = reactive({
    id: null,
    reason: ''
  });
  
  // 表单验证规则
  const rejectRules = {
    reason: [{ required: true, message: '请输入拒绝理由', trigger: 'blur' }]
  };
  
  // 文件浏览相关
  const fileBrowserVisible = ref(false);
  const fileBrowserLoading = ref(false);
  const browserFiles = ref([]);
  const currentPath = ref('');
  const currentCodeId = ref(null);
  
  // 文件预览相关
  const filePreviewVisible = ref(false);
  const filePreviewLoading = ref(false);
  const filePreviewContent = ref('');
  const previewFileName = ref('');
  
  // 解析savedFiles
  const parsedSavedFiles = ref([]);
  
  // 文件列表相关
  const fileListDialogVisible = ref(false);
  const fileList = ref([]);
  
  // 获取代码列表
  const loadCodes = async () => {
    loading.value = true;
    try {
      const params = {
        page: currentPage.value,
        size: pageSize.value,
      };
      
      const res = await service.get('/api/v1.0/sys/code/examine', { params });
      if (res.code === 10000) {
        // 直接使用后端返回的数据，无需再获取节点信息
        tableData.value = res.data.codes || [];
        total.value = res.data.pagination?.total || 0;
      } else {
        toast('错误', res.message || '获取代码列表失败', 'error');
      }
    } catch (error) {
      console.error('获取代码列表失败:', error);
      toast('错误', '获取代码列表失败', 'error');
    } finally {
      loading.value = false;
    }
  };
  
  // 批准代码
  const approveCode = async (row) => {
    try {
      // 根据代码类型显示不同的确认消息
      let confirmMessage;
      if (row.nodeType === 'Pyxis') {
        confirmMessage = `确定要批准用户 ${row.creatorName || row.creatorId} 提交的Pyxis代码吗？`;
      } else {
        confirmMessage = `确定要批准用户 ${row.creatorName || row.creatorId} 提交的代码 "${row.funcName}" 吗？`;
      }
      
      await showModal(confirmMessage, 'warning', '确认批准');
      
      loading.value = true;
      const res = await service.post(`/api/v1.0/sys/code/approve/${row.id}`, null, {
        params: {
          approved: true
        }
      });
      
      if (res.code === 10000) {
        toast('成功', '已批准代码', 'success');
        loadCodes(); // 刷新列表
      } else {
        toast('错误', res.message || '批准代码失败', 'error');
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批准代码失败:', error);
        toast('错误', error.response?.data?.message || '批准代码失败', 'error');
      }
    } finally {
      loading.value = false;
    }
  };
  
  // 从详情对话框批准代码
  const approveFromDetail = async () => {
    if (!codeDetail.value) return;
    
    try {
      // 根据代码类型显示不同的确认消息
      let confirmMessage;
      if (codeDetail.value.nodeType === 'Pyxis') {
        confirmMessage = `确定要批准用户 ${codeDetail.value.creatorName || codeDetail.value.creatorId} 提交的 Pyxis 部署任务吗？`;
      } else {
        confirmMessage = `确定要批准用户 ${codeDetail.value.creatorName || codeDetail.value.creatorId} 提交的代码 "${codeDetail.value.funcName}" 吗？`;
      }
      
      await showModal(confirmMessage, 'warning', '确认批准');
      
      const res = await service.post(`/api/v1.0/sys/code/approve/${codeDetail.value.id}`, null, {
        params: {
          approved: true
        }
      });
      
      if (res.code === 10000) {
        toast('成功', '已批准代码', 'success');
        codeDetailVisible.value = false;
        loadCodes(); // 刷新列表
      } else {
        toast('错误', res.message || '批准代码失败', 'error');
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批准代码失败:', error);
        toast('错误', error.response?.data?.message || '批准代码失败', 'error');
      }
    }
  };
  
  // 显示拒绝对话框
  const showRejectDialog = (row) => {
    rejectForm.codeId = row.id;
    rejectForm.reason = '';
    rejectDialogVisible.value = true;
  };
  
  // 从详情对话框显示拒绝对话框
  const showRejectDialogFromDetail = () => {
    if (!codeDetail.value) return;
    
    codeDetailVisible.value = false;
    rejectForm.codeId = codeDetail.value.id;
    rejectForm.reason = '';
    rejectDialogVisible.value = true;
  };
  
  // 拒绝代码
  const rejectCode = async () => {
    if (!rejectFormRef.value) return;
    
    await rejectFormRef.value.validate(async (valid) => {
      if (!valid) return;
      
      rejectLoading.value = true;
      try {
        const res = await service.post(`/api/v1.0/sys/code/approve/${rejectForm.codeId}`, null, {
          params: {
            approved: false,
            rejectReason: rejectForm.reason
          }
        });
        
        if (res.code === 10000) {
          toast('成功', '已拒绝代码', 'success');
          rejectDialogVisible.value = false;
          loadCodes(); // 刷新列表
        } else {
          toast('错误', res.message || '拒绝代码失败', 'error');
        }
      } catch (error) {
        console.error('拒绝代码失败:', error);
        toast('错误', error.response?.data?.message || '拒绝代码失败', 'error');
      } finally {
        rejectLoading.value = false;
      }
    });
  };
  
  // 查看代码详情
  const viewCodeDetail = async (row) => {
    codeDetailVisible.value = true;
    codeDetailLoading.value = true;
    parsedSavedFiles.value = []; // 重置文件列表
    
    try {
      const response = await service.get(`/api/v1.0/sys/code/${row.id}`);
      if (response.code === 10000) {
        codeDetail.value = response.data;
        
        // 解析savedFiles字段
        if (codeDetail.value.savedFiles) {
          try {
            if (typeof codeDetail.value.savedFiles === 'string') {
              parsedSavedFiles.value = JSON.parse(codeDetail.value.savedFiles);
            } else if (Array.isArray(codeDetail.value.savedFiles)) {
              parsedSavedFiles.value = codeDetail.value.savedFiles;
            }
          } catch (error) {
            console.error('解析savedFiles失败:', error);
            parsedSavedFiles.value = [];
          }
        }
      } else {
        toast('错误', response.message || '获取代码详情失败', 'error');
      }
    } catch (error) {
      console.error('获取代码详情失败:', error);
      toast('错误', '获取代码详情失败', 'error');
    } finally {
      codeDetailLoading.value = false;
    }
  };
  
  // 处理页码变化
  const handleCurrentChange = (val) => {
    currentPage.value = val;
    loadCodes();
  };
  
  // 处理每页条数变化
  const handleSizeChange = (val) => {
    pageSize.value = val;
    currentPage.value = 1;
    loadCodes();
  };
  
  // 获取状态类型
  const getStatusType = (status) => {
    const statusMap = {
      'PENDING': 'warning',
      'APPROVED': 'success',
      'REJECTED': 'danger',
      'RUNNING': 'primary',
      'COMPLETED': 'success',
      'ERROR': 'danger'
    };
    return statusMap[status] || 'info';
  };
  
  // 获取状态文本
  const getStatusText = (status) => {
    const statusMap = {
      'PENDING': '待审批',
      'APPROVED': '已批准',
      'REJECTED': '已拒绝',
      'RUNNING': '运行中',
      'COMPLETED': '已完成',
      'ERROR': '错误'
    };
    return statusMap[status] || status;
  };
  
  // 查看用户信息
  const viewUserInfo = async (userId) => {
    userInfoDialogVisible.value = true;
    userInfoLoading.value = true;
    userInfo.value = null;
    
    try {
      const response = await service.get(`/api/v1.0/sys/code/approval/user/${userId}`);
      if (response.code === 10000) {
        userInfo.value = response.data;
      } else {
        toast('错误', response.message || '获取用户信息失败', 'error');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      toast('错误', '获取用户信息失败', 'error');
    } finally {
      userInfoLoading.value = false;
    }
  };
  
    // 格式化用户信息中的日期时间
  const formatUserDateTime = (dateStr) => {
    if (!dateStr) return '未设置';
    const date = new Date(dateStr);
    return date.toLocaleString();
  };
  
  // 修改时间格式化函数
  const formatDateTime = (timestamp) => {
    if (!timestamp) return '-';
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return timestamp;
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };
  
  // 打开文件浏览器
  const browseFiles = async (row) => {
    currentCodeId.value = row.id;
    fileBrowserVisible.value = true;
    fileBrowserLoading.value = true;
    currentPath.value = '';
    
    try {
      await fetchDirectoryContents('');
    } catch (error) {
      console.error('获取文件列表失败:', error);
      toast('错误', '获取文件列表失败', 'error');
    } finally {
      fileBrowserLoading.value = false;
    }
  };
  
  // 获取目录内容
  const fetchDirectoryContents = async (path) => {
    if (!currentCodeId.value) return;
    
    fileBrowserLoading.value = true;
    try {
      const response = await service.get(`/api/v1.0/sys/code/browse/${currentCodeId.value}`, {
        params: { path: path || '' }
      });
      
      if (response.code === 10000) {
        // 处理API返回的数据结构
        const data = response.data;
        if (data.children && Array.isArray(data.children)) {
          browserFiles.value = data.children;
        } else if (data.type === 'directory' && data.children && Array.isArray(data.children)) {
          browserFiles.value = data.children;
        } else {
          browserFiles.value = [];
        }
        currentPath.value = path;
      } else {
        toast('错误', response.message || '获取目录内容失败', 'error');
      }
    } catch (error) {
      console.error('获取目录内容失败:', error);
      toast('错误', '获取目录内容失败', 'error');
    } finally {
      fileBrowserLoading.value = false;
    }
  };
  
  // 判断是否是文本文件
  const isTextFile = (filename) => {
    const textExtensions = ['.txt', '.py', '.js', '.html', '.css', '.json', '.md', '.xml', '.csv', '.log', '.ini', '.conf', '.sh', '.bat', '.yaml', '.yml', '.env'];
    const ext = '.' + filename.split('.').pop().toLowerCase();
    return textExtensions.includes(ext);
  };
  
  // 导航到文件或目录
  const navigateToFile = (file) => {
    if (file.type === 'directory') {
      const newPath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name);
      fetchDirectoryContents(newPath);
    } else {
      // 判断是否是文本文件
      if (isTextFile(file.name)) {
        previewFile(file);
      } else {
        downloadBrowserFile(file);
      }
    }
  };
  
  // 导航到上级目录
  const navigateUp = () => {
    if (!currentPath.value) return;
    
    const pathParts = currentPath.value.split('/');
    pathParts.pop();
    const newPath = pathParts.join('/');
    fetchDirectoryContents(newPath);
  };
  
  // 下载文件
  const downloadBrowserFile = (file) => {
    if (file.type === 'directory') return;
    
    const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name);
    downloadSpecificFile(currentCodeId.value, filePath);
  };
  
  // 下载特定文件
  const downloadSpecificFile = async (codeId, filePath) => {
    try {
      // 显示加载动画
      const loading = ElLoading.service({
        lock: true,
        text: '正在下载文件...',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      // 获取文件名
      const filename = filePath.split('/').pop() || 'downloaded_file';
      
      // 使用service发送请求
      const response = await service.get(`/api/v1.0/sys/code/download/${codeId}`, {
        params: { filePath },
        responseType: 'blob'
      });
      
      // 创建 Blob URL
      const blob = new Blob([response]);
      const url = window.URL.createObjectURL(blob);
      
      // 创建下载链接
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      
      // 清理
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }, 100);
      
      // 关闭加载动画
      loading.close();
      
      toast('成功', `文件 ${filename} 下载成功`, 'success');
    } catch (error) {
      console.error('下载文件失败:', error);
      toast('错误', `下载文件失败: ${error.message}`, 'error');
    }
  };
  
  // 预览文件
  const previewFile = async (file) => {
    filePreviewLoading.value = true;
    filePreviewVisible.value = true;
    previewFileName.value = file.name;
    
    try {
      const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name);
      
      // 获取文件内容
      const response = await service.get(`/api/v1.0/sys/code/preview/${currentCodeId.value}`, {
        params: { filePath },
        responseType: 'text'
      });
      
      filePreviewContent.value = response;
    } catch (error) {
      console.error('预览文件失败:', error);
      toast('错误', `预览文件失败: ${error.message}`, 'error');
      filePreviewContent.value = '文件内容加载失败';
    } finally {
      filePreviewLoading.value = false;
    }
  };
  
  // 下载当前预览的文件
  const downloadPreviewFile = () => {
    const filePath = currentPath.value ? `${currentPath.value}/${previewFileName.value}` : previewFileName.value;
    downloadSpecificFile(currentCodeId.value, filePath);
  };
  
  // 复制预览内容到剪贴板
  const copyPreviewContent = async () => {
    try {
      // 确保内容是字符串格式
      let contentToCopy = filePreviewContent.value
      if (typeof contentToCopy === 'object') {
        contentToCopy = JSON.stringify(contentToCopy, null, 2)
      } else if (contentToCopy === null || contentToCopy === undefined) {
        contentToCopy = ''
      } else {
        contentToCopy = String(contentToCopy)
      }

      await navigator.clipboard.writeText(contentToCopy);
      toast('成功', '内容已复制到剪贴板', 'success');
    } catch (error) {
      console.error('复制失败:', error);
      toast('错误', '复制失败', 'error');
    }
  };
  
  // 添加专用于文件的时间格式化函数
  const formatFileTime = (timestamp) => {
    if (!timestamp) return '无';
    
    try {
      // 处理科学计数法格式的时间戳 (1.7474714980392427E9)
      const milliseconds = parseFloat(timestamp) * 1000;
      if (!isNaN(milliseconds)) {
        const date = new Date(milliseconds);
        if (!isNaN(date.getTime())) {
          return date.toLocaleString('zh-CN', { hour12: false });
        }
      }
      
      // 如果不是时间戳格式，尝试普通日期转换
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return date.toLocaleString('zh-CN', { hour12: false });
      }
      
      return String(timestamp);
    } catch (e) {
      console.error('文件时间格式化错误:', e, timestamp);
      return String(timestamp);
    }
  };
  
  // 下载文件列表
  const downloadFiles = async (row) => {
    if (!row || !row.id) return;
    
    currentCodeId.value = row.id;
    fileListDialogVisible.value = true;
    fileBrowserLoading.value = true;
    fileList.value = [];
    
    try {
      const response = await service.get(`/api/v1.0/sys/code/browse/${row.id}`);
      
      if (response.code === 10000) {
        // 处理API返回的数据结构
        const data = response.data;
        if (data.children && Array.isArray(data.children)) {
          // 收集所有非目录文件
          const collectFiles = (items, parentPath = '') => {
            let result = [];
            for (const item of items) {
              if (item.type === 'file') {
                result.push({
                  name: item.name,
                  path: item.path || (parentPath ? `${parentPath}/${item.name}` : item.name),
                  size: item.size,
                  modified: item.modified
                });
              } else if (item.type === 'directory' && item.children) {
                const dirPath = parentPath ? `${parentPath}/${item.name}` : item.name;
                result = result.concat(collectFiles(item.children, dirPath));
              }
            }
            return result;
          };
          
          fileList.value = collectFiles(data.children);
        } else {
          fileList.value = [];
        }
      } else {
        toast('错误', response.message || '获取文件列表失败', 'error');
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      toast('错误', '获取文件列表失败', 'error');
    } finally {
      fileBrowserLoading.value = false;
    }
  };
  
  // 下载单个文件
  const downloadFile = (file) => {
    if (!file || !file.path) return;
    downloadSpecificFile(currentCodeId.value, file.path);
  };
  
  // 页面加载时执行
  onMounted(() => {
    loadCodes();
  });
  </script>
  
  <style scoped>
  .code-pending-container {
    padding: 20px;
  }
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .header-left {
    .title-wrapper {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .title-icon {
        margin-right: 8px;
        font-size: 24px;
        color: var(--el-color-primary);
      }
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
    }
    
    .sub-title {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .header-right {
    flex: 1;
    display: flex;
    justify-content: center;
  }
  
  .header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }
  
  .search-form {
    display: flex;
    align-items: center;
    margin: 0;
  }
  
  .search-input {
    width: 240px;
  }
  
  :deep(.el-form--inline .el-form-item) {
    margin-right: 12px;
    margin-bottom: 0;
  }
  
  :deep(.el-form--inline .el-form-item:last-child) {
    margin-right: 0;
  }
  
  :deep(.el-input__wrapper) {
    border-radius: 20px;
  }
  
  :deep(.el-button.is-round) {
    height: 36px;
    padding: 0 20px;
  }
  
  .list-card {
    border: none;
    border-radius: 8px;
    margin-bottom: 0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }
  
  .list-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  :deep(.el-pagination) {
    justify-content: center !important;
  }
  
  .operation-buttons {
    display: flex;
    justify-content: center;
  }
  
  .file-icon {
    margin-right: 5px;
    font-size: 16px;
  }
  
  .code-content-wrapper {
    margin-top: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .code-header {
    background-color: #f5f7fa;
    padding: 8px 12px;
    font-weight: bold;
    border-bottom: 1px solid #dcdfe6;
  }
  
  .code-pre {
    background-color: #f8f8f8;
    padding: 12px;
    margin: 0;
    max-height: 400px;
    overflow: auto;
    font-family: monospace;
    white-space: pre-wrap;
    word-break: break-all;
  }
  
  /* 深色模式适配 */
  html.dark .list-card {
    background-color: var(--el-bg-color-overlay);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
  
  html.dark .list-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  html.dark .code-pre {
    background-color: #1e1e1e;
    color: #d4d4d4;
  }
  
  html.dark .code-header {
    background-color: #2d2d2d;
    border-bottom: 1px solid #3e3e3e;
  }
  
  html.dark .code-content-wrapper {
    border: 1px solid #3e3e3e;
  }
  
  /* 文件浏览相关样式 */
  .file-browser {
    padding: 10px;
  }
  
  .file-browser-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .current-path {
    font-size: 16px;
    font-weight: 600;
    padding: 4px 8px;
    background-color: var(--el-fill-color);
    border-radius: 4px;
    font-family: monospace;
  }
  
  .file-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 4px;
  }
  
  .file-item:hover {
    color: var(--el-color-primary);
  }
  
  .file-actions {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  /* 文件预览相关样式 */
  .file-preview {
    max-height: 400px;
    overflow-y: auto;
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 12px;
  }
  
  .preview-actions {
    margin-bottom: 10px;
  }
  
  .preview-content {
    white-space: pre-wrap;
    word-break: break-all;
    font-family: monospace;
  }
  
  /* 深色模式下的文件预览样式 */
  html.dark .file-preview {
    background-color: #1e1e1e;
    color: #e6e6e6;
  }
  
  .file-tag {
    margin: 2px;
    font-family: monospace;
  }
  
  .saved-files-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  /* 调整描述列表标签宽度 */
  :deep(.el-descriptions__label) {
    width: 120px !important;
    min-width: 120px !important;
  }
  
  .file-list-container {
    padding: 10px;
  }
  
  .section-subtitle {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  
  .file-list {
    margin-top: 10px;
  }
  </style>