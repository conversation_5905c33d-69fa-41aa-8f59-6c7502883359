{"name": "cornucopia-ui", "private": true, "version": "0.0.2", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^1.1.4", "@vueuse/core": "^12.2.0", "@vueuse/integrations": "^8.4.1", "axios": "^0.27.2", "echarts": "^5.5.1", "element-plus": "^2.1.11", "highlight.js": "^11.11.1", "nprogress": "^0.2.0", "universal-cookie": "^4.0.4", "vue": "^3.2.25", "vue-router": "^4.0.15", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^2.3.1", "vite": "^2.9.7", "vite-plugin-windicss": "^1.8.4", "windicss": "^3.5.1"}}