<template>
  <div class="model-deploy-pending-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Connection /></el-icon>
          <h2>应用部署任务审批</h2>
        </div>
        <div class="sub-title">审批用户的应用部署任务请求</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.keyword"
                placeholder="搜索申请人或模型"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
        highlight-current-row
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column label="申请人" min-width="120" align="center">
          <template #default="scope">
            <el-button 
              type="primary" 
              link 
              @click="viewUserInfo(scope.row.creatorId)"
            >
              {{ scope.row.creatorName || `用户${scope.row.creatorId}` }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="任务名称" min-width="150" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="viewTaskDetail(scope.row)">
              {{ scope.row.name }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="模型" min-width="150" align="center">
          <template #default="scope">
            <template v-if="scope.row.model">
              <el-tag type="success">{{ scope.row.model.name }}</el-tag>
            </template>
            <template v-else>
              <el-tag type="info">外部模型</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="节点" min-width="150" align="center">
          <template #default="scope">
            <el-tag>{{ scope.row.node?.name || '未知节点' }} ({{ scope.row.node?.ipAddress }}:{{ scope.row.node?.port }})</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          prop="timeUpdated" 
          label="申请时间" 
          width="180" 
          align="center"
          sortable
        >
          <template #default="scope">
            {{ formatDateTime(scope.row.timeUpdated) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-buttons">
              <el-button-group v-if="scope.row.status === 'PENDING'">
                <el-tooltip content="批准" placement="top">
                  <el-button type="success" size="small" @click="approveTask(scope.row)">
                    <el-icon><Check /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="拒绝" placement="top">
                  <el-button type="danger" size="small" @click="showRejectDialog(scope.row)">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </el-tooltip>
              </el-button-group>
              <el-button-group>
                <el-tooltip content="查看详情" placement="top">
                  <el-button type="info" size="small" @click="viewTaskDetail(scope.row)">
                    <el-icon><View /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="查看代码" placement="top">
                  <el-button type="primary" size="small" @click="viewCodeFiles(scope.row)">
                    <el-icon><Document /></el-icon>
                  </el-button>
                </el-tooltip>
              </el-button-group>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 拒绝对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="拒绝部署任务"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="rejectFormRef"
        :model="rejectForm"
        :rules="rejectRules"
        label-width="100px"
      >
        <el-form-item label="拒绝理由" prop="reason">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝理由"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button
            type="danger"
            :loading="rejectLoading"
            @click="rejectTask"
          >
            确认拒绝
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批准对话框 -->
    <el-dialog
      v-model="approveDialogVisible"
      title="批准部署任务"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="approveFormRef"
        :model="approveForm"
        :rules="approveRules"
        label-width="100px"
      >
        <el-form-item label="批准理由" prop="reason">
          <el-input
            v-model="approveForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入批准理由"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="approveDialogVisible = false">取消</el-button>
          <el-button
            type="success"
            :loading="loading"
            @click="confirmApproveTask"
          >
            确认批准
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="部署任务详情"
      width="700px"
      destroy-on-close
    >
      <div v-if="currentTask" class="task-detail">
        <el-descriptions :column="1" border label-width="100px">
          <el-descriptions-item label="任务ID">{{ currentTask.id }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ currentTask.name }}</el-descriptions-item>
          <el-descriptions-item label="任务描述">{{ currentTask.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentTask.creatorId }}</el-descriptions-item>
          <el-descriptions-item label="模型">
            <template v-if="currentTask.model">
              <el-tag type="success">{{ currentTask.model.name }}</el-tag>
            </template>
            <template v-else>
              <el-tag type="info">外部模型</el-tag>
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="节点">{{ currentTask.node?.name || '未知节点' }} ({{ currentTask.node?.ipAddress }}:{{ currentTask.node?.port }})</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentTask.status)">
              {{ getStatusText(currentTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="模型路径">
            {{ currentTask.modelPath || '外部路径' }}
          </el-descriptions-item>
          <el-descriptions-item label="部署任务ID">{{ currentTask.taskId || '无' }}</el-descriptions-item>
          <el-descriptions-item label="保存的文件">
            <div v-if="Array.isArray(currentTask.savedFiles) && currentTask.savedFiles.length">
              <el-tag 
                v-for="file in currentTask.savedFiles" 
                :key="file"
                class="file-tag"
                type="info"
              >
                {{ file }}
              </el-tag>
            </div>
            <span v-else>无</span>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime(currentTask.timeCreated) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间" v-if="currentTask.timeUpdated">
            {{ formatDateTime(currentTask.timeUpdated) }}
          </el-descriptions-item>
          <el-descriptions-item label="拒绝理由" v-if="currentTask.rejectReason">
            {{ currentTask.rejectReason }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 用户信息对话框 -->
    <el-dialog
      v-model="userInfoDialogVisible"
      title="申请人信息"
      width="500px"
      destroy-on-close
    >
      <div v-if="userInfo" class="user-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="用户ID">{{ userInfo.userId }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ userInfo.fullname }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ userInfo.email }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="userInfo.enable ? 'success' : 'danger'">
              {{ userInfo.enable ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag 
              v-for="role in userInfo.roles" 
              :key="role.id"
              class="role-tag"
              type="info"
            >
              {{ role.name }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最后登录时间">
            {{ formatDateTime(userInfo.lastLogin) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(userInfo.createTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div v-else-if="userInfoLoading" class="loading-container">
        <el-skeleton :rows="6" animated />
      </div>
    </el-dialog>

    <!-- 文件浏览对话框 -->
    <el-dialog
      v-model="fileBrowserVisible"
      title="文件浏览器"
      width="700px"
      destroy-on-close
    >
      <div v-loading="fileBrowserLoading" class="file-browser">
        <div class="file-browser-header">
          <div class="current-path">{{ currentPath || '/' }}</div>
          <el-button size="small" @click="navigateUp" :disabled="currentPath === ''">
            <el-icon><Back /></el-icon> 返回上级
          </el-button>
        </div>
        
        <el-table :data="browserFiles" size="small" border style="width: 100%">
          <el-table-column prop="name" label="名称">
            <template #default="scope">
              <div class="file-item" @click="navigateToFile(scope.row)">
                <el-icon class="file-icon">
                  <Folder v-if="scope.row.type === 'directory'" />
                  <Document v-else />
                </el-icon>
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" width="100" />
          <el-table-column label="修改时间" width="180">
            <template #default="scope">
              {{ formatFileTime(scope.row.modified) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button-group>
                <el-button 
                  size="small" 
                  type="primary" 
                  @click.stop="downloadBrowserFile(scope.row)"
                  :disabled="scope.row.type === 'directory'"
                >
                  <el-icon><Download /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  @click.stop="previewFile(scope.row)"
                  :disabled="scope.row.type === 'directory' || !isTextFile(scope.row.name)"
                >
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 文件列表对话框 -->
    <el-dialog
      v-model="fileListDialogVisible"
      title="文件管理"
      width="700px"
      destroy-on-close
    >
      <div class="file-actions">
        <el-button type="primary" @click="browseFiles" :disabled="!currentTask">
          <el-icon><Folder /></el-icon> 浏览文件
        </el-button>
        <el-button type="success" @click="downloadFiles" :disabled="!currentTask">
          <el-icon><Download /></el-icon> 下载文件
        </el-button>
      </div>
      
      <!-- 文件列表 -->
      <div class="file-list" v-if="fileList.length > 0">
        <div class="section-subtitle">文件列表</div>
        <el-table :data="fileList" size="small" border style="width: 100%">
          <el-table-column prop="name" label="文件名" />
          <el-table-column prop="size" label="大小" width="100" />
          <el-table-column label="修改时间" width="180">
            <template #default="scope">
              {{ formatFileTime(scope.row.modified) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button size="small" type="primary" @click="downloadFile(scope.row)">
                <el-icon><Download /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileListDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加文件预览对话框 -->
    <el-dialog
      v-model="filePreviewVisible"
      :title="`文件预览: ${previewFileName}`"
      width="700px"
      destroy-on-close
    >
      <div v-loading="filePreviewLoading" class="file-preview">
        <div class="preview-actions">
          <el-button type="primary" @click="downloadPreviewFile">
            <el-icon><Download /></el-icon> 下载文件
          </el-button>
          <el-button type="info" @click="copyPreviewContent">
            <el-icon><DocumentCopy /></el-icon> 复制内容
          </el-button>
        </div>
        <div class="preview-content">
          <pre>{{ filePreviewContent }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { 
  Search, 
  Refresh, 
  Check, 
  Close, 
  View, 
  Connection, 
  Document, 
  Back, 
  Download, 
  Folder, 
  DocumentCopy,
  ZoomIn
} from '@element-plus/icons-vue';
import service from '~/axios';
import { toast, showModal } from '~/composables/util';
import { ElLoading } from 'element-plus';

// 数据加载状态
const loading = ref(false);
const rejectLoading = ref(false);
const userInfoLoading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索表单
const searchForm = reactive({
  keyword: ''
});

// 表格数据
const tableData = ref([]);

// 对话框控制
const rejectDialogVisible = ref(false);
const approveDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const userInfoDialogVisible = ref(false);
const rejectFormRef = ref(null);
const approveFormRef = ref(null);
const currentTask = ref(null);
const userInfo = ref(null);

// 文件浏览和下载相关
const fileBrowserVisible = ref(false);
const fileBrowserLoading = ref(false);
const browserFiles = ref([]);
const currentPath = ref('');
const fileListDialogVisible = ref(false);
const fileList = ref([]);

// 文件预览相关
const filePreviewVisible = ref(false);
const filePreviewLoading = ref(false);
const filePreviewContent = ref('');
const previewFileName = ref('');

// 拒绝表单
const rejectForm = reactive({
  taskId: null,
  reason: ''
});

// 批准表单
const approveForm = reactive({
  taskId: null,
  reason: ''
});

// 表单验证规则
const rejectRules = {
  reason: [{ required: true, message: '请输入拒绝理由', trigger: 'blur' }]
};

const approveRules = {
  reason: [{ required: true, message: '请输入批准理由', trigger: 'blur' }]
};

// 获取任务列表
const loadTasks = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
    };
    
    if (searchForm.keyword) {
      params.keyword = searchForm.keyword;
    }
    
    const res = await service.get('/api/v1.0/sys/application/examine', { params });
    if (res.code === 10000) {
      // 处理任务数据
      tableData.value = res.data.tasks || [];
      
      // 处理每个任务的 savedFiles 和时间字段
      tableData.value.forEach(task => {
        // 处理 savedFiles
        if (task.savedFiles) {
          try {
            if (typeof task.savedFiles === 'string' && task.savedFiles.trim().startsWith('[')) {
              task.savedFiles = JSON.parse(task.savedFiles);
            }
            if (!Array.isArray(task.savedFiles)) {
              task.savedFiles = [];
            }
          } catch (e) {
            console.error('解析savedFiles失败:', e);
            task.savedFiles = [];
          }
        } else {
          task.savedFiles = [];
        }
      });
      
      total.value = res.data.pagination?.total || 0;
    } else {
      toast('错误', res.message || '获取部署任务列表失败', 'error');
    }
  } catch (error) {
    console.error('获取部署任务列表失败:', error);
    toast('错误', '获取部署任务列表失败', 'error');
  } finally {
    loading.value = false;
  }
};

// 批准任务
const approveTask = (row) => {
  approveForm.taskId = row.id;
  approveForm.reason = '';
  approveDialogVisible.value = true;
};

// 确认批准任务
const confirmApproveTask = async () => {
  if (!approveFormRef.value) return;
  
  await approveFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    loading.value = true;
    try {
      const res = await service.post(`/api/v1.0/sys/application/approve/${approveForm.taskId}`, null, {
        params: {
          approved: true,
          message: approveForm.reason
        }
      });
      
      if (res.code === 10000) {
        toast('成功', '已批准部署任务', 'success');
        approveDialogVisible.value = false;
        loadTasks(); // 刷新列表
      } else {
        toast('错误', res.message || '批准部署任务失败', 'error');
      }
    } catch (error) {
      console.error('批准部署任务失败:', error);
      toast('错误', error.response?.data?.message || '批准部署任务失败', 'error');
    } finally {
      loading.value = false;
    }
  });
};

// 显示拒绝对话框
const showRejectDialog = (row) => {
  rejectForm.taskId = row.id;
  rejectForm.reason = '';
  rejectDialogVisible.value = true;
};

// 拒绝任务
const rejectTask = async () => {
  if (!rejectFormRef.value) return;
  
  await rejectFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    rejectLoading.value = true;
    try {
      const res = await service.post(`/api/v1.0/sys/application/approve/${rejectForm.taskId}`, null, {
        params: {
          approved: false,
          message: rejectForm.reason
        }
      });
      
      if (res.code === 10000) {
        toast('成功', '已拒绝部署任务', 'success');
        rejectDialogVisible.value = false;
        loadTasks(); // 刷新列表
      } else {
        toast('错误', res.message || '拒绝部署任务失败', 'error');
      }
    } catch (error) {
      console.error('拒绝部署任务失败:', error);
      toast('错误', error.response?.data?.message || '拒绝部署任务失败', 'error');
    } finally {
      rejectLoading.value = false;
    }
  });
};

// 查看任务详情
const viewTaskDetail = (row) => {
  // 创建任务副本以便修改
  currentTask.value = { ...row };
  
  // 处理savedFiles字段
  if (currentTask.value.savedFiles) {
    if (typeof currentTask.value.savedFiles === 'string') {
      try {
        currentTask.value.savedFiles = JSON.parse(currentTask.value.savedFiles);
      } catch (e) {
        console.error('解析savedFiles失败:', e);
        currentTask.value.savedFiles = [];
      }
    }
  } else {
    currentTask.value.savedFiles = [];
  }
  
  detailDialogVisible.value = true;
};

// 查看用户信息
const viewUserInfo = async (userId) => {
  userInfoDialogVisible.value = true;
  userInfoLoading.value = true;
  userInfo.value = null;
  
  try {
    const response = await service.get(`/api/v1.0/sys/application/approval/user/${userId}`);
    if (response.code === 10000) {
      userInfo.value = response.data;
    } else {
      toast('错误', response.message || '获取用户信息失败', 'error');
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    toast('错误', '获取用户信息失败', 'error');
  } finally {
    userInfoLoading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadTasks();
};

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = '';
  currentPage.value = 1;
  loadTasks();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadTasks();
};

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadTasks();
};

// 格式化日期时间
const formatDateTime = (val) => {
  if (!val) return '无';
  const date = typeof val === 'string' ? new Date(val.replace(/-/g, '/')) : new Date(val);
  if (isNaN(date.getTime())) return val;
  return date.toLocaleString('zh-CN', { hour12: false });
};

// 格式化文件时间（处理科学计数法格式的时间戳）
const formatFileTime = (timestamp) => {
  if (!timestamp) return '无';
  
  try {
    // 处理科学计数法格式的时间戳 (1.7474714980392427E9)
    const milliseconds = parseFloat(timestamp) * 1000;
    if (!isNaN(milliseconds)) {
      const date = new Date(milliseconds);
      if (!isNaN(date.getTime())) {
        return date.toLocaleString('zh-CN', { hour12: false });
      }
    }
    
    // 如果不是时间戳格式，尝试普通日期转换
    return formatDateTime(timestamp);
  } catch (e) {
    console.error('文件时间格式化错误:', e, timestamp);
    return String(timestamp);
  }
};

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'DEPLOYING': 'info',
    'COMPLETED': 'success',
    'FAILED': 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已拒绝',
    'DEPLOYING': '部署中',
    'COMPLETED': '已完成',
    'FAILED': '部署失败'
  };
  return statusMap[status] || status;
};

// 解析任务文件列表
const getTaskFiles = (task) => {
  if (!task) return [];
  if (!task.savedFiles) return [];
  
  // 如果savedFiles是字符串，尝试解析JSON
  if (typeof task.savedFiles === 'string') {
    try {
      return JSON.parse(task.savedFiles);
    } catch (e) {
      console.error('解析savedFiles失败:', e);
      return [];
    }
  }
  
  // 如果savedFiles已经是数组
  if (Array.isArray(task.savedFiles)) {
    return task.savedFiles;
  }
  
  return [];
};

// 查看代码文件
const viewCodeFiles = (row) => {
  currentTask.value = { ...row };
  fileListDialogVisible.value = true;
  fileList.value = [];
};

// 浏览文件
const browseFiles = async () => {
  if (!currentTask.value || !currentTask.value.id) return;
  
  fileBrowserVisible.value = true;
  fileBrowserLoading.value = true;
  currentPath.value = '';
  
  try {
    await fetchDirectoryContents('');
  } catch (error) {
    console.error('获取文件列表失败:', error);
    toast('错误', '获取文件列表失败', 'error');
  } finally {
    fileBrowserLoading.value = false;
  }
};

// 获取目录内容
const fetchDirectoryContents = async (path) => {
  if (!currentTask.value || !currentTask.value.id) return;
  
  fileBrowserLoading.value = true;
  try {
    const response = await service.get(`/api/v1.0/sys/application/browse/${currentTask.value.id}`, {
      params: { path: path || '' }
    });
    
    if (response.code === 10000) {
      // 处理API返回的数据结构
      const data = response.data;
      if (data.children && Array.isArray(data.children)) {
        browserFiles.value = data.children;
      } else if (data.type === 'directory' && data.children && Array.isArray(data.children)) {
        browserFiles.value = data.children;
      } else {
        browserFiles.value = [];
      }
      currentPath.value = path;
    } else {
      toast('错误', response.message || '获取目录内容失败', 'error');
    }
  } catch (error) {
    console.error('获取目录内容失败:', error);
    toast('错误', '获取目录内容失败', 'error');
  } finally {
    fileBrowserLoading.value = false;
  }
};

// 判断是否是文本文件
const isTextFile = (filename) => {
  const textExtensions = ['.txt', '.py', '.js', '.html', '.css', '.json', '.md', '.xml', '.csv', '.log', '.ini', '.conf', '.sh', '.bat', '.yaml', '.yml', '.env'];
  const ext = '.' + filename.split('.').pop().toLowerCase();
  return textExtensions.includes(ext);
};

// 导航到文件或目录
const navigateToFile = (file) => {
  if (file.type === 'directory') {
    const newPath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name);
    fetchDirectoryContents(newPath);
  } else {
    // 判断是否是文本文件
    if (isTextFile(file.name)) {
      previewFile(file);
    } else {
      downloadBrowserFile(file);
    }
  }
};

// 预览文件
const previewFile = async (file) => {
  filePreviewLoading.value = true;
  filePreviewVisible.value = true;
  previewFileName.value = file.name;
  
  try {
    const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name);
    
    // 注意：使用后端API获取文件内容
    const response = await service.get(`/api/v1.0/sys/application/preview/${currentTask.value.id}`, {
      params: { filePath },
      responseType: 'text'
    });
    
    filePreviewContent.value = response;
  } catch (error) {
    console.error('预览文件失败:', error);
    toast('错误', `预览文件失败: ${error.message}`, 'error');
    filePreviewContent.value = '文件内容加载失败';
  } finally {
    filePreviewLoading.value = false;
  }
};

// 下载当前预览的文件
const downloadPreviewFile = () => {
  const filePath = currentPath.value ? `${currentPath.value}/${previewFileName.value}` : previewFileName.value;
  downloadSpecificFile(currentTask.value.id, filePath);
};

// 复制预览内容到剪贴板
const copyPreviewContent = async () => {
  try {
    // 确保内容是字符串格式
    let contentToCopy = filePreviewContent.value
    if (typeof contentToCopy === 'object') {
      contentToCopy = JSON.stringify(contentToCopy, null, 2)
    } else if (contentToCopy === null || contentToCopy === undefined) {
      contentToCopy = ''
    } else {
      contentToCopy = String(contentToCopy)
    }

    await navigator.clipboard.writeText(contentToCopy);
    toast('成功', '内容已复制到剪贴板', 'success');
  } catch (error) {
    console.error('复制失败:', error);
    toast('错误', '复制失败', 'error');
  }
};

// 下载文件
const downloadBrowserFile = (file) => {
  if (file.type === 'directory') return;
  
  const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name);
  downloadSpecificFile(currentTask.value.id, filePath);
};

// 下载文件列表
const downloadFiles = async () => {
  if (!currentTask.value || !currentTask.value.id) return;
  
  fileList.value = [];
  fileBrowserLoading.value = true;
  
  try {
    const response = await service.get(`/api/v1.0/sys/application/browse/${currentTask.value.id}`);
    
    if (response.code === 10000) {
      // 处理API返回的数据结构
      const data = response.data;
      if (data.children && Array.isArray(data.children)) {
        // 收集所有非目录文件
        const collectFiles = (items) => {
          let result = [];
          for (const item of items) {
            if (item.type === 'file') {
              result.push({
                name: item.name,
                path: item.path,
                size: item.size,
                modified: item.modified
              });
            } else if (item.type === 'directory' && item.children) {
              result = result.concat(collectFiles(item.children));
            }
          }
          return result;
        };
        
        fileList.value = collectFiles(data.children);
      } else {
        fileList.value = [];
      }
    } else {
      toast('错误', response.message || '获取文件列表失败', 'error');
    }
  } catch (error) {
    console.error('获取文件列表失败:', error);
    toast('错误', '获取文件列表失败', 'error');
  } finally {
    fileBrowserLoading.value = false;
  }
};

// 下载特定文件
const downloadFile = (file) => {
  const filePath = file.path || file.name;
  downloadSpecificFile(currentTask.value.id, filePath);
};

// 下载文件的通用方法
const downloadSpecificFile = async (taskId, filePath) => {
  try {
    // 显示加载动画
    const loading = ElLoading.service({
      lock: true,
      text: '正在下载文件...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 获取文件名
    const filename = filePath.split('/').pop() || 'downloaded_file';
    
    // 使用service发送请求
    const response = await service.get(`/api/v1.0/sys/application/download/${taskId}`, {
      params: { filePath },
      responseType: 'blob'
    });
    
    // 创建 Blob URL
    const blob = new Blob([response]);
    const url = window.URL.createObjectURL(blob);
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }, 100);
    
    // 关闭加载动画
    loading.close();
    
    toast('成功', `文件 ${filename} 下载成功`, 'success');
  } catch (error) {
    console.error('下载文件失败:', error);
    toast('错误', `下载文件失败: ${error.message}`, 'error');
  }
};

// 导航到上级目录
const navigateUp = () => {
  if (!currentPath.value) return;
  
  const pathParts = currentPath.value.split('/');
  pathParts.pop();
  const newPath = pathParts.join('/');
  fetchDirectoryContents(newPath);
};

// 页面加载时执行
onMounted(() => {
  loadTasks();
});
</script>

<style scoped>
.model-deploy-pending-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
}

.list-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

.task-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.file-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.user-info {
  padding: 10px;
}

.role-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.loading-container {
  padding: 20px;
}

/* 任务详情样式 */
:deep(.el-descriptions__label) {
  width: 100px !important;
  min-width: 100px !important;
}

:deep(.el-descriptions__content) {
  padding: 12px !important;
}

.file-browser {
  padding: 20px;
}

.file-browser-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.current-path {
  font-size: 18px;
  font-weight: 600;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
}

.file-item:hover {
  background-color: #f0f0f0;
}

.file-icon {
  margin-right: 8px;
}

.file-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.file-list {
  margin-top: 20px;
}

.section-subtitle {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}

/* 文件预览相关样式 */
.file-preview {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
}

.preview-actions {
  margin-bottom: 10px;
}

.preview-content {
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
}

/* 深色模式下的文件预览样式 */
html.dark .file-preview {
  background-color: #1e1e1e;
  color: #e6e6e6;
}
</style> 