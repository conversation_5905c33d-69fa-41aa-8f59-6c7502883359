package ouc.isclab.common.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import ouc.isclab.common.response.BaseResponse;

import java.io.IOException;
import java.io.InputStream;

@Slf4j
@BaseResponse
@RestController
@RequestMapping("/api/v1.0/sys/template")
public class TemplateController {

    /**
     * 下载默认应用模板
     */
    @GetMapping("/download/scrip-slim")
    public ResponseEntity<byte[]> downloadScripSlimTemplate() {
        try {
            log.info("下载默认应用模板: scrip-slim.zip");
            
            // 从classpath中读取模板文件
            Resource resource = new ClassPathResource("templates/scrip-slim.zip");
            
            if (!resource.exists()) {
                log.error("模板文件不存在: templates/scrip-slim.zip");
                return ResponseEntity.notFound().build();
            }
            
            // 读取文件内容
            byte[] fileContent;
            try (InputStream inputStream = resource.getInputStream()) {
                fileContent = inputStream.readAllBytes();
            }
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "scrip-slim.zip");
            headers.setContentLength(fileContent.length);
            
            log.info("成功下载模板文件，文件大小: {} bytes", fileContent.length);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileContent);
                    
        } catch (IOException e) {
            log.error("下载模板文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取可用的模板列表
     */
    @GetMapping("/list")
    public ResponseEntity<Object> getTemplateList() {
        try {
            log.info("获取模板列表");
            
            // 这里可以扩展为动态读取templates目录下的所有模板文件
            var templates = java.util.List.of(
                java.util.Map.of(
                    "name", "scrip-slim",
                    "displayName", "默认应用模板",
                    "description", "包含基本应用结构的默认模板",
                    "filename", "scrip-slim.zip",
                    "downloadUrl", "/api/v1.0/sys/template/download/scrip-slim"
                )
                // java.util.Map.of(
                //     "name", "scrip-main",
                //     "displayName", "应用模板使用实例",
                //     "description", "包含应用模板使用实例",
                //     "filename", "scrip-main.zip",
                //     "downloadUrl", "/api/v1.0/sys/template/download/scrip-main"
                // )
            );
            
            return ResponseEntity.ok(templates);
            
        } catch (Exception e) {
            log.error("获取模板列表失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
