<template>
  <div class="resource-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><List /></el-icon>
          <h2>我的资源</h2>
          <HelpDrawer :content="helpContent" />
        </div>
        <div class="sub-title">管理您有权访问的所有资源</div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <!-- 资源表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        highlight-current-row
      >
        <el-table-column prop="resourceType" label="资源类型" align="center" min-width="120">
          <template #default="scope">
            <el-tag :type="getResourceTypeTag(scope.row.resourceType)">
              {{ getResourceTypeName(scope.row.resourceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="nodeType" label="节点类型" align="center" width="100">
          <template #default="scope">
            <el-tag :type="getNodeTypeTag(scope.row.nodeType)" size="small">
              {{ scope.row.nodeType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="资源名称" align="center" min-width="200">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center; cursor: pointer;">
              <el-icon class="file-icon" style="margin-right: 5px;">
                <Document v-if="scope.row.resourceType === 'DATASET'" />
                <Monitor v-else-if="scope.row.resourceType === 'NODE'" />
                <Cpu v-else-if="scope.row.resourceType === 'POWER'" />
                <View v-else-if="scope.row.resourceType === 'MODEL'" />
                <Files v-else />
              </el-icon>
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="nodeId" label="节点信息" align="center" min-width="250">
          <template #default="scope">
            <div class="node-info-compact">
              <el-tag size="small" type="info" class="node-id-tag">
                {{ scope.row.nodeId }}
              </el-tag>
              <span class="node-name">{{ scope.row.nodeName }}</span>
              <span class="node-address">({{ scope.row.nodeIpAddress }}:{{ scope.row.nodePort }})</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="timeCreated" label="授权时间" align="center" width="180" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-tooltip content="查看节点详情" placement="top">
                <el-button
                  type="info"
                  size="small"
                  @click.stop="viewNodeDetail(scope.row.nodeId)"
                >
                  <el-icon><Monitor /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看数据集详情" placement="top" v-if="scope.row.resourceType === 'DATASET'">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="viewDatasetDetail(scope.row)"
                >
                  <el-icon><Document /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
    
    <!-- 节点详情对话框 -->
    <el-dialog
      v-model="nodeDetailVisible"
      title="节点详情"
      width="600px"
      destroy-on-close
    >
      <div v-loading="nodeDetailLoading">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="节点ID">{{ nodeDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="节点名称">{{ nodeDetail.name }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ nodeDetail.ipAddress }}</el-descriptions-item>
          <el-descriptions-item label="端口">{{ nodeDetail.port }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ nodeDetail.description }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ nodeDetail.timeCreated }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="nodeDetailVisible = false">关闭</el-button>
          <el-button type="primary" @click="goToNodeDetail">查看完整信息</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 数据集详情对话框 -->
    <el-dialog
      v-model="datasetDetailVisible"
      title="数据集详情"
      width="700px"
      destroy-on-close
    >
      <div v-loading="datasetDetailLoading">
        <el-descriptions v-if="datasetDetail" :column="1" border>
          <el-descriptions-item label="数据集名称">{{ datasetDetail.name }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag type="primary">{{ datasetDetail.type || '文件' }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="大小">{{ formatFileSize(datasetDetail.size) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime({timeCreated: datasetDetail.timeCreated}) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime({timeCreated: datasetDetail.timeUpdated}) }}</el-descriptions-item>
          <el-descriptions-item label="创建者">{{ datasetDetail.creatorName }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ datasetDetail.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="URL" v-if="datasetDetail.url">
            <el-link type="primary" :href="datasetDetail.url" target="_blank">{{ datasetDetail.url }}</el-link>
          </el-descriptions-item>
          <el-descriptions-item label="可用节点">
            <div v-if="datasetDetail.availableNodes && datasetDetail.availableNodes.length">
              <el-tag 
                v-for="node in datasetDetail.availableNodes" 
                :key="node.id"
                class="node-tag"
              >
                {{ node.ipAddress }} : {{ node.port }} 
              </el-tag>
            </div>
            <span v-else>没有节点可用</span>
          </el-descriptions-item>
          <el-descriptions-item label="示例数据" v-if="datasetDetail.mockData">
            <pre class="mock-data-pre">{{ datasetDetail.mockData }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="datasetDetailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  List,
  Monitor,
  Document,
  Cpu,
  Files,
  View
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

// 帮助内容
const helpContent = `
<h2>我的资源说明</h2>
<p>在联邦学习网络中，您可以查看和管理您有权访问的所有资源。资源包括以下几种类型：</p>

<h3>资源类型</h3>
<ul>
  <li><strong>计算资源</strong>：仅用于执行计算任务的节点资源</li>
  <li><strong>数据集</strong>：使用数据集进行训练和测试的节点资源</li>
</ul>

<h3>详细介绍</h3>
<ul>
  <li><strong>计算资源</strong>：在申请节点的使用权限后自动分配，可以在上面进行计算任务</li>
  <li><strong>数据集</strong>：在申请数据集使用权限后自动分配，可以在上面进行数据集的使用</li>
</ul>

<h3>注意事项</h3>
<ul>
  <li>资源列表支持分页显示，可以调整每页显示数量</li>
  <li>资源按授权时间排序，可以查看资源的授权历史</li>
  <li>部分资源可能需要特定权限才能访问</li>
  <li>请确保您有足够的权限访问所需资源</li>
</ul>
`

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 节点详情相关
const nodeDetailVisible = ref(false)
const nodeDetailLoading = ref(false)
const nodeDetail = ref({})

// 数据集详情相关
const datasetDetailVisible = ref(false)
const datasetDetailLoading = ref(false)
const datasetDetail = ref(null)

// 获取资源列表
const fetchResources = async (page = 1) => {
  loading.value = true
  try {
    const response = await service.get('/api/v1.0/sys/user/resources', {
      params: {
        page: page,
        size: pageSize.value
      }
    })
    if (response.code === 10000) {
      tableData.value = response.data.resources
      total.value = response.data.pagination.total
      pageSize.value = response.data.pagination.size
    }
  } catch (error) {
    console.error('获取资源列表失败:', error)
    toast('错误', '获取资源列表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 获取资源类型名称
const getResourceTypeName = (type) => {
  const typeMap = {
    'DATASET': '数据集',
    'POWER': '计算资源',
    'NODE': '节点',
    'MODEL': '模型'
  }
  return typeMap[type] || type
}

// 获取资源类型标签样式
const getResourceTypeTag = (type) => {
  const typeMap = {
    'DATASET': 'success',
    'POWER': 'warning',
    'NODE': 'info',
    'MODEL': 'danger'
  }
  return typeMap[type] || ''
}

// 获取节点类型标签样式
const getNodeTypeTag = (nodeType) => {
  const typeMap = {
    'Sycee': 'primary',
    'Pyxis': 'warning'
  }
  return typeMap[nodeType] || 'info'
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let index = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(2)} ${units[index]}`;
};

// 格式化日期时间
const formatDateTime = (row) => {
  if (!row || !row.timeCreated) return '';
  const date = new Date(row.timeCreated);
  return date.toLocaleString();
};

// 查看节点详情
const viewNodeDetail = async (nodeId) => {
  nodeDetailVisible.value = true
  nodeDetailLoading.value = true
  
  try {
    const response = await service.get(`/api/v1.0/sys/node/${nodeId}`)
    if (response.code === 10000) {
      nodeDetail.value = response.data
    } else {
      toast('错误', response.message || '获取节点详情失败', 'error')
    }
  } catch (error) {
    console.error('获取节点详情失败:', error)
    toast('错误', '获取节点详情失败', 'error')
  } finally {
    nodeDetailLoading.value = false
  }
}

// 跳转到节点详情页
const goToNodeDetail = () => {
  nodeDetailVisible.value = false
  router.push({
    path: '/node/detail',
    query: { id: nodeDetail.value.id }
  })
}

// 查看数据集详情
const viewDatasetDetail = async (row) => {
  if (row.resourceType === 'DATASET') {
    datasetDetailVisible.value = true
    datasetDetailLoading.value = true
    
    try {
      const response = await service.get(`/api/v1.0/sys/dataset/${row.resourceId}`)
      if (response.code === 10000) {
        datasetDetail.value = response.data
      } else {
        toast('错误', response.message || '获取数据集详情失败', 'error')
      }
    } catch (error) {
      console.error('获取数据集详情失败:', error)
      toast('错误', '获取数据集详情失败', 'error')
    } finally {
      datasetDetailLoading.value = false
    }
  } else {
    toast('提示', '只能查看数据集类型资源的详情', 'info')
  }
}

// 页码变化
const handleCurrentChange = (val) => {
  fetchResources(val)
}

// 每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchResources(1)
}

onMounted(() => {
  fetchResources()
})
</script>

<style scoped>
.resource-list-container {
  padding: 20px;
}

.list-card {
  border: none;
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-bottom: 0;
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
  border-radius: 8px;
}

:deep(.el-table--border::after),
:deep(.el-table--border .el-table__inner-wrapper::after) {
  display: none;
}

:deep(.el-table th) {
  font-weight: bold;
}

/* 深色模式样式 */
html.dark .list-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.page-header {
  display: flex;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

:deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

.file-icon {
  margin-right: 5px;
  font-size: 16px;
}

.node-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.mock-data-pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  max-height: 200px;
  overflow: auto;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.node-info-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.node-id-tag {
  font-weight: 600;
}

.node-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.node-address {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  font-family: monospace;
}
</style> 