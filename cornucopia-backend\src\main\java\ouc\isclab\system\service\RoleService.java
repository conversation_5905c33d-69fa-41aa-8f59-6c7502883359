package ouc.isclab.system.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ouc.isclab.common.exception.BaseException;
import ouc.isclab.common.response.ResponseCode;
import ouc.isclab.system.entity.RoleEntity;
import ouc.isclab.system.repository.RoleRepository;
import ouc.isclab.system.repository.UserRoleRepository;
import ouc.isclab.system.entity.PermissionEntity;
import ouc.isclab.system.repository.PermissionRepository;

import java.util.List;

@Service
public class RoleService {
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private UserRoleRepository userRoleRepository;
    @Autowired
    private PermissionRepository permissionRepository;

    /**
     * 创建角色
     */
    @Transactional
    public RoleEntity createRole(String name, String description) {
        try {
            // 获取当前最大ID
            Long maxId = roleRepository.findMaxId();
            Long newId = (maxId == null) ? 1L : maxId + 1;
            
            RoleEntity roleEntity = new RoleEntity();
            roleEntity.setId(newId);  // 手动设置ID
            roleEntity.setName(name);
            roleEntity.setDescription(description);
            return roleRepository.save(roleEntity);
        } catch (Exception e) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "创建角色失败: " + e.getMessage());
        }
    }

    /**
     * 列出所有角色
     */
    public Page<RoleEntity> listRoles(Pageable pageable) {
        return roleRepository.findAll(pageable);
    }


    /**
     * 删除角色（同时删除绑定用户）
     */
    @Transactional
    public void deleteRoleById(Long id) {
        RoleEntity roleEntity = roleRepository.getRoleEntityById(id);
        if (roleEntity != null) {
            roleRepository.deleteById(id);
            userRoleRepository.deleteByRoleId(id);
        }
    }

    /**
     * 修改角色基本信息
     */
    public RoleEntity updateRoleById(Long id, String name, String description) {
        RoleEntity roleEntity = roleRepository.getRoleEntityById(id);

        roleEntity.setName(name);
        roleEntity.setDescription(description);

        return roleRepository.save(roleEntity);
    }

    /**
     * 批量删除角色
     */
    @Transactional
    public void deleteRolesByIds(List<Long> ids) {
        for (Long id : ids) {
            RoleEntity roleEntity = roleRepository.getRoleEntityById(id);
            if (roleEntity != null) {
                // 先删除角色用户关联
                userRoleRepository.deleteByRoleId(id);
                // 再删除角色
                roleRepository.deleteById(id);
            }
        }
    }

    /**
     * 根据角色名称查询角色
     */
    public RoleEntity findRoleByName(String name) {
        try {
            // 使用新的findByName方法
            return roleRepository.findByName(name)
                .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "角色不存在"));
        } catch (Exception e) {
            throw new BaseException(ResponseCode.SERVICE_ERROR,"查询角色失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查角色
     */
    public RoleEntity findRoleById(Long id) {
        return roleRepository.findById(id)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "角色不存在"));
    }

    public RoleEntity getRoleById(Long id) {
        return roleRepository.findById(id)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "角色不存在"));
    }

    public RoleEntity getRoleByName(String name) {
        return roleRepository.findByName(name)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "角色不存在"));
    }

    /**
     * 为角色分配权限
     */
    @Transactional
    public RoleEntity assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        RoleEntity role = roleRepository.findById(roleId)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "角色不存在"));

        // 获取所有权限实体
        List<PermissionEntity> permissions = permissionRepository.findAllById(permissionIds);
        if (permissions.size() != permissionIds.size()) {
            throw new BaseException(ResponseCode.SERVICE_ERROR, "部分权限不存在");
        }

        // 清除原有权限并设置新权限
        role.getPermissions().clear();
        role.getPermissions().addAll(permissions);
        
        return roleRepository.save(role);
    }

    /**
     * 清空角色的所有权限
     */
    @Transactional
    public RoleEntity clearRolePermissions(Long roleId) {
        RoleEntity role = roleRepository.findById(roleId)
            .orElseThrow(() -> new BaseException(ResponseCode.SERVICE_ERROR, "角色不存在"));

        // 清空权限
        role.getPermissions().clear();
        
        return roleRepository.save(role);
    }
}
