<template>
  <div class="role-detail-container">
    <el-loading :full-screen="false" :body="true" v-if="loading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Lock /></el-icon>
          <h2>角色详情</h2>
        </div>
        <div class="sub-title">查看角色的详细信息</div>
      </div>
      <div class="header-right">
        <el-button 
          @click="$router.push('/user/role/list')"
          plain
          round
        >
          <el-icon><Back /></el-icon>
          返回角色列表
        </el-button>
      </div>
    </div>

    <div class="main-content">
      <el-empty 
        v-if="!hasRoleId" 
        description="未找到角色ID" 
        :image-size="200"
      >
        <el-button type="primary" @click="$router.push('/user/role/list')" round>
          返回角色列表
        </el-button>
      </el-empty>

      <template v-else>
        <el-card class="role-info-card" shadow="hover" v-if="roleData">
          <template #header>
            <div class="card-header">
              <span class="role-name">{{ roleData.name }}</span>
              <el-tag type="info" effect="plain">{{ roleData.description || '暂无描述' }}</el-tag>
            </div>
          </template>
          
          <!-- 基本信息 -->
          <div class="info-section">
            <div class="section-title">
              <el-icon><InfoFilled /></el-icon>
              <span>基本信息</span>
            </div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="创建时间">
                {{ formatDateTime(roleData.timeCreated) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatDateTime(roleData.timeUpdated) }}
              </el-descriptions-item>
              <el-descriptions-item label="关联用户数">
                {{ roleData.users?.length || 0 }} 个用户
              </el-descriptions-item>
              <el-descriptions-item label="权限数量">
                {{ roleData.permissions?.length || 0 }} 个权限
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 权限信息 -->
          <div class="permissions-section">
            <div class="section-title">
              <el-icon><Key /></el-icon>
              <span>权限信息</span>
              <el-button 
                type="primary" 
                link
                @click="$router.push(`/user/role/permission?id=${roleData.id}`)"
              >
                <el-icon><Setting /></el-icon>
                配置权限
              </el-button>
            </div>
            
            <div class="permission-groups">
              <el-empty v-if="!roleData.permissions?.length" description="暂无权限" />
              <template v-else>
                <div class="permission-list">
                  <el-tag
                    v-for="permission in roleData.permissions"
                    :key="permission.id"
                    class="permission-tag"
                    effect="plain"
                  >
                    <el-tooltip 
                      :content="permission.description || '暂无描述'"
                      placement="top"
                    >
                      <span>
                        {{ permission.name }}
                        <el-icon class="info-icon"><InfoFilled /></el-icon>
                      </span>
                    </el-tooltip>
                  </el-tag>
                </div>
              </template>
            </div>
          </div>

          <!-- 关联用户 -->
          <div class="users-section">
            <div class="section-title">
              <el-icon><User /></el-icon>
              <span>关联用户</span>
            </div>
            
            <div class="user-list">
              <el-empty v-if="!roleData.users?.length" description="暂无关联用户" />
              <template v-else>
                <el-table :data="roleData.users" border stripe>
                  <el-table-column prop="username" label="用户名">
                    <template #default="scope">
                      <el-button 
                        type="primary" 
                        link
                        @click="handleUserClick(scope.row)"
                      >
                        {{ scope.row.username }}
                      </el-button>
                    </template>
                  </el-table-column>
                  <el-table-column prop="enable" label="状态" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.enable ? 'success' : 'danger'" effect="plain">
                        {{ scope.row.enable ? '启用' : '禁用' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="最后登录" width="180">
                    <template #default="scope">
                      {{ formatDateTime(scope.row.lastLogin) }}
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </div>
          </div>
        </el-card>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Lock,
  Back,
  InfoFilled,
  Key,
  Setting,
  User
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

// 格式化日期时间
const formatDateTime = (value) => {
  if (!value) return '-'
  return value // 直接返回后端格式化好的时间字符串
}

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const hasRoleId = ref(false)
const roleData = ref(null)

// 处理用户点击
const handleUserClick = (user) => {
  router.push({
    path: '/user/detail',
    query: { id: user.id }
  })
}

// 获取角色详情
const getRoleDetail = async () => {
  try {
    loading.value = true
    const res = await service.get(`/api/v1.0/sys/role/${route.query.id}`)
    if (res.code === 10000) {
      roleData.value = res.data
    } else {
      throw new Error(res.message || '获取角色详情失败')
    }
  } catch (error) {
    toast('错误', error.message, 'error')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  if (!route.query.id) {
    hasRoleId.value = false
    toast('错误', '未找到角色ID', 'error')
    return
  }
  hasRoleId.value = true
  getRoleDetail()
})
</script>

<style scoped>
.role-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  .title-icon {
    margin-right: 8px;
    font-size: 24px;
    color: var(--el-color-primary);
  }

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
  }
}

.sub-title {
  color: #909399;
  font-size: 14px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  
  .el-icon {
    color: var(--el-color-primary);
  }
}

.info-section,
.permissions-section,
.users-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.permission-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  
  .info-icon {
    font-size: 14px;
    color: #909399;
    margin-left: 4px;
    cursor: help;
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .role-name {
    font-size: 16px;
    font-weight: 600;
  }
}

:deep(.el-descriptions) {
  padding: 12px;
  background-color: var(--el-fill-color-blank);
  border-radius: 4px;
}

:deep(.el-descriptions__cell) {
  padding: 12px 16px;
}
</style>
