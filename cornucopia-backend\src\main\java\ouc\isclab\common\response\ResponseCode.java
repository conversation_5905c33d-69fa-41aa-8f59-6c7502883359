package ouc.isclab.common.response;

/**
 * 返回状态码
 */
public enum ResponseCode {
    SUCCESS(10000, "success"),                          // 成功返回
    LOGIN_SUCCESS(10001, "login success"),              // 登录成功
    LOGIN_FAILED(10004, "login failed"),              // 登录失败
    LOGOUT_SUCCESS(10002, "logout success"),            // 注销成功
    REGISTER_SUCCESS(10005, "register success"),        // 注册成功
    REGISTER_FAILED(10006, "register failed"),          // 注册失败
    DELETE_ACCOUNT_SUCCESS(10007, "delete account success"),  // 注销账号成功
    SERVICE_ERROR(50000, "service error");              // 所有无法识别的异常默认的返回

    /**
     * 状态码
     */
    private int code;

    /**
     * 返回信息
     */
    private String message;

    ResponseCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
