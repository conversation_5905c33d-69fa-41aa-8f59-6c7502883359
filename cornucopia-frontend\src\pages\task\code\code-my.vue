<template>
    <div class="code-list-container">
      <div class="page-header">
        <div class="header-left">
          <div class="title-wrapper">
            <el-icon class="title-icon"><Document /></el-icon>
            <h2>我的代码</h2>
          </div>
          <div class="sub-title">管理您上传的所有代码</div>
        </div>
        <div class="header-right">
          <el-button
            type="danger"
            size="default"
            :disabled="selectedIds.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon> 批量删除
          </el-button>
        </div>
      </div>
  
      <el-card class="list-card" shadow="hover">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          border
          stripe
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          highlight-current-row
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="id" label="ID" align="center" width="80" />
          <el-table-column prop="funcName" label="函数名称" align="center" min-width="150">
            <template #default="scope">
              <div style="display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <el-icon class="file-icon" style="margin-right: 5px;"><Document /></el-icon>
                <template v-if="isPyxisCode(scope.row)">
                  <span class="pyxis-func-name">PYXIS_FUNC_{{ scope.row.id }}</span>
                </template>
                <template v-else>
                  <span>{{ scope.row.funcName }}</span>
                </template>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="codeType" label="代码节点类型" align="center" width="120">
            <template #default="scope">
              <el-tag :type="isPyxisCode(scope.row) ? 'primary' : 'success'" size="small">
                {{ isPyxisCode(scope.row) ? 'PYXIS' : 'SYCEE' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="nodeName" label="所属节点" align="center" min-width="150">
            <template #default="scope">
              <el-tag type="success" size="small">
                {{ scope.row.nodeIp }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="taskNames" label="所属任务" align="center" min-width="200">
            <template #default="scope">
              <div v-if="scope.row.taskNames && scope.row.taskNames.length > 0">
                <el-tag 
                  v-for="(taskName, index) in scope.row.taskNames" 
                  :key="index"
                  type="success" 
                  size="small"
                  style="margin: 2px;"
                >
                  {{ taskName }}
                </el-tag>
              </div>
              <span v-else style="color: #909399;">无关联任务</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="creatorName" label="创建者" align="center" width="120" /> -->
          <el-table-column prop="status" label="审核状态" align="center" width="120">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center" width="180" sortable>
            <template #default="scope">
              {{ formatDateTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" align="center" width="180" sortable>
            <template #default="scope">
              {{ formatDateTime(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" align="center" fixed="right">
            <template #default="scope">
              <el-button-group>
                <el-tooltip content="查看代码详情" placement="top">
                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="viewCodeDetail(scope.row)"
                  >
                    <el-icon><Document /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="查看节点详情" placement="top">
                  <el-button
                    type="info"
                    size="small"
                    @click.stop="viewNodeDetail(scope.row.nodeId)"
                  >
                    <el-icon><Monitor /></el-icon>
                  </el-button>
                </el-tooltip>
                
                <template v-if="!isPyxisCode(scope.row)">
                  <template v-if="scope.row.status === 'APPROVED'">
                    <el-tooltip content="运行代码" placement="top">
                      <el-button
                        size="small"
                        type="success"
                        @click.stop="handleRun(scope.row)"
                      >
                        <el-icon><VideoPlay /></el-icon>
                      </el-button>
                    </el-tooltip>
                  </template>
                  <template v-if="['RUNNING', 'COMPLETED', 'ERROR'].includes(scope.row.status)">
                    <el-tooltip content="查看结果" placement="top">
                      <el-button
                        size="small"
                        type="success"
                        @click.stop="viewCodeResult(scope.row)"
                      >
                        <el-icon><DataLine /></el-icon>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip content="查看日志" placement="top">
                      <el-button
                        size="small"
                        type="warning"
                        @click.stop="viewCodeLog(scope.row)"
                      >
                        <el-icon><Document /></el-icon>
                      </el-button>
                    </el-tooltip>
                  </template>
                </template>
                
                <template v-if="isPyxisCode(scope.row)">
                  <el-tooltip content="查看状态" placement="top">
                    <el-button
                      size="small"
                      type="info"
                      @click.stop="viewCodeStatus(scope.row)"
                    >
                      <el-icon><InfoFilled /></el-icon>
                    </el-button>
                  </el-tooltip>
                </template>
                
                <!-- <el-tooltip content="刷新审核状态" placement="top">
                  <el-button
                    type="info"
                    size="small"
                    @click.stop="refreshCodeStatus(scope.row)">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </el-tooltip> -->
                <el-tooltip content="删除代码" placement="top">
                  <el-button
                    type="danger"
                    size="small"
                    @click.stop="handleDelete(scope.row)"
                    :disabled="scope.row.status === 'RUNNING'"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-tooltip>
              </el-button-group>
              
              <div v-if="isPyxisCode(scope.row)" class="container-actions">
                <el-button-group class="mt-2">
                  <el-tooltip content="启动代码" placement="top">
                    <el-button
                      type="success"
                      size="small"
                      @click.stop="startCode(scope.row)"
                      :disabled="isCodeRunning(scope.row)"
                    >
                      <el-icon><VideoPlay /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="停止代码" placement="top">
                    <el-button
                      type="warning"
                      size="small"
                      @click.stop="stopCode(scope.row)"
                      :disabled="!isCodeRunning(scope.row)"
                    >
                      <el-icon><VideoPause /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="杀死代码" placement="top">
                    <el-button
                      type="danger"
                      size="small"
                      @click.stop="killCode(scope.row)"
                      :disabled="!isCodeRunning(scope.row)"
                    >
                      <el-icon><CircleClose /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="访问服务" placement="top">
                    <el-button
                      type="primary"
                      size="small"
                      @click.stop="openPyxisService(scope.row)"
                      :disabled="!isPyxisServiceAvailable(scope.row)"
                    >
                      <el-icon><Link /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="浏览文件" placement="top">
                    <el-button
                      type="primary"
                      size="small"
                      @click.stop="browseFiles(scope.row)"
                    >
                      <el-icon><Folder /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="申请重新审批" placement="top">
                    <el-button
                      type="info"
                      size="small"
                      @click.stop="requestReapproval(scope.row)"
                      :disabled="scope.row.status === 'PENDING'"
                    >
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </div>
            </template>
          </el-table-column>
        </el-table>
  
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </el-card>
      
      <!-- 节点详情对话框 -->
      <el-dialog
        v-model="nodeDetailVisible"
        title="节点详情"
        width="600px"
        destroy-on-close
      >
        <div v-loading="nodeDetailLoading">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="节点ID">{{ nodeDetail.id }}</el-descriptions-item>
            <el-descriptions-item label="节点名称">{{ nodeDetail.name }}</el-descriptions-item>
            <el-descriptions-item label="IP地址">{{ nodeDetail.ipAddress }}</el-descriptions-item>
            <el-descriptions-item label="端口">{{ nodeDetail.port }}</el-descriptions-item>
            <el-descriptions-item label="描述">{{ nodeDetail.description }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDateTime(nodeDetail.timeCreated) }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="nodeDetailVisible = false">关闭</el-button>
            <el-button type="primary" @click="goToNodeDetail">查看完整信息</el-button>
          </span>
        </template>
      </el-dialog>
      
      <!-- 代码详情对话框 -->
      <el-dialog
        v-model="codeDetailVisible"
        title="代码详情"
        width="800px"
        destroy-on-close
      >
        <div v-loading="codeDetailLoading">
          <el-descriptions v-if="codeDetail" :column="1" border>
            <el-descriptions-item label="ID">{{ codeDetail.id }}</el-descriptions-item>
            <el-descriptions-item label="函数名称">{{ codeDetail.funcName }}</el-descriptions-item>
            <el-descriptions-item label="代码类型">
              <el-tag :type="isPyxisCode(codeDetail) ? 'primary' : 'success'">
                {{ isPyxisCode(codeDetail) ? 'PYXIS' : 'SYCEE' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="节点">{{ codeDetail.nodeName }}</el-descriptions-item>
            <el-descriptions-item label="审核状态">
              <el-tag :type="getStatusType(codeDetail.status)">{{ getStatusText(codeDetail.status) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDateTime(codeDetail.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ formatDateTime(codeDetail.updateTime) }}</el-descriptions-item>
            <el-descriptions-item label="创建者ID">{{ codeDetail.creatorId }}</el-descriptions-item>
            <el-descriptions-item v-if="!isPyxisCode(codeDetail)" label="Sycee代码ID">{{ codeDetail.syceeCodeId }}</el-descriptions-item>
            <el-descriptions-item v-if="!isPyxisCode(codeDetail)" label="Sycee请求ID">{{ codeDetail.syceeRequestId }}</el-descriptions-item>
            <el-descriptions-item v-if="!isPyxisCode(codeDetail)" label="Sycee任务ID">{{ codeDetail.syceeJobId || '无' }}</el-descriptions-item>
            <el-descriptions-item v-if="isPyxisCode(codeDetail)" label="Pyxis任务ID">{{ codeDetail.pyxisTaskId || '无' }}</el-descriptions-item>
            <el-descriptions-item label="描述">{{ codeDetail.description || '无' }}</el-descriptions-item>
            <el-descriptions-item v-if="codeDetail.status === 'REJECTED'" label="拒绝原因">
              <span style="color: #f56c6c">{{ codeDetail.rejectReason }}</span>
            </el-descriptions-item>
            <el-descriptions-item v-if="codeDetail.status === 'APPROVED'" label="审批人ID">
              {{ codeDetail.approverId }}
            </el-descriptions-item>
            <el-descriptions-item v-if="isPyxisCode(codeDetail) && codeDetail.savedFiles" label="保存的文件">
              <div class="saved-files-list">
                <el-tag 
                  v-for="(file, index) in parsedSavedFiles" 
                  :key="index"
                  size="small"
                  type="info"
                  class="saved-file-tag"
                >
                  <el-icon class="file-icon"><Document /></el-icon>
                  {{ file }}
                </el-tag>
              </div>
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="code-content-wrapper" v-if="codeDetail?.codeContent">
            <div class="code-header">
              <span>代码内容</span>
            </div>
            <pre class="code-pre">{{ codeDetail?.codeContent }}</pre>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="codeDetailVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
      
      <!-- 运行代码对话框 -->
      <el-dialog v-model="runDialogVisible" title="运行代码" width="500px">
        <div v-if="currentCode">
          <p>函数名称: <strong>{{ currentCode.funcName }}</strong></p>
          
          <div v-if="parsedInputKwargs.length > 0">
            <el-form :model="runParams" label-width="120px">
              <el-form-item 
                v-for="param in parsedInputKwargs" 
                :key="param" 
                :label="param"
              >
                <div class="param-input-group">
                  <el-input 
                    v-model="runParams[param]" 
                    placeholder="请输入参数值"
                  />
                  <el-switch
                    v-model="argsTypes[param]"
                    active-text="字符串"
                    inactive-text="自动"
                    class="param-type-switch"
                  />
                </div>
              </el-form-item>
            </el-form>
          </div>
          <div v-else>
            <el-empty description="无需参数" />
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="runDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitRunCode">运行</el-button>
          </span>
        </template>
      </el-dialog>
      
      <!-- 添加结果对话框 -->
      <el-dialog
        v-model="resultDialogVisible"
        title="代码运行结果"
        width="800px"
        destroy-on-close
      >
        <div v-loading="resultLoading">
          <pre class="result-pre">{{ codeResult }}</pre>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="resultDialogVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
      
      <!-- 添加日志对话框 -->
      <el-dialog
        v-model="logDialogVisible"
        title="代码运行日志"
        width="800px"
        destroy-on-close
      >
        <div v-loading="logLoading">
          <pre class="log-pre">{{ codeLog }}</pre>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="logDialogVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
      
      <!-- 代码状态详情对话框 (PYXIS) -->
      <el-dialog
        v-model="statusDetailVisible"
        title="代码状态详情"
        width="550px"
        destroy-on-close
      >
        <div v-loading="statusDetailLoading" class="status-detail-custom">
          <template v-if="statusDetail">
            <div class="status-header">
              <div class="status-title">运行状态</div>
              <el-tag :type="getContainerStatusType(statusDetail.status)" class="status-tag">
                {{ getContainerStatusText(statusDetail.status) }}
              </el-tag>
            </div>
            
            <div class="info-section">
              <div class="info-group">
                <div class="info-item">
                  <span class="info-label">任务ID：</span>
                  <span class="info-value">{{ statusDetail.task_id }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">容器ID：</span>
                  <el-tooltip :content="statusDetail.container_id" placement="top">
                    <span class="info-value">{{ ellipsis(statusDetail.container_id, 12) }}</span>
                  </el-tooltip>
                </div>
              </div>
            </div>
            
            <div class="info-section">
              <div class="section-title">运行信息</div>
              <div class="info-group">
                <div class="info-item">
                  <span class="info-label">停止码：</span>
                  <span class="info-value">
                    <el-tag :type="getExitCodeType(statusDetail.exit_code)" v-if="statusDetail.exit_code !== null">
                      {{ statusDetail.exit_code }}
                    </el-tag>
                    <span v-else>未停止</span>
                  </span>
                </div>
                <div class="info-item">
                  <span class="info-label">错误信息：</span>
                  <span class="info-value error-text" v-if="statusDetail.error">{{ statusDetail.error }}</span>
                  <span class="info-value" v-else>无</span>
                </div>
              </div>
              
              <div class="container-action-group">
                <el-button-group>
                  <el-button 
                    type="success" 
                    size="small" 
                    @click="startCode({id: currentCodeId})"
                    :disabled="statusDetail.status === 'running'"
                  >
                    <el-icon><VideoPlay /></el-icon> 启动
                  </el-button>
                  <el-button 
                    type="warning" 
                    size="small" 
                    @click="stopCode({id: currentCodeId})"
                    :disabled="statusDetail.status !== 'running'"
                  >
                    <el-icon><VideoPause /></el-icon> 停止
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="killCode({id: currentCodeId})"
                    :disabled="statusDetail.status !== 'running'"
                  >
                    <el-icon><CircleClose /></el-icon> 杀死
                  </el-button>
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="openPyxisService(getCodeById(currentCodeId))"
                    :disabled="statusDetail.status !== 'running'"
                  >
                    <el-icon><Link /></el-icon> 访问
                  </el-button>
                  <el-button 
                    type="info" 
                    size="small" 
                    @click="requestReapproval({id: currentCodeId})"
                  >
                    <el-icon><Refresh /></el-icon> 申请重新审批
                  </el-button>
                </el-button-group>
              </div>
            </div>
            
            <div class="info-section">
              <div class="section-title">时间信息</div>
              <div class="time-grid">
                <div class="time-item">
                  <div class="time-label">创建时间</div>
                  <div class="time-value">{{ formatDateTime(statusDetail.created_time) }}</div>
                </div>
                <div class="time-item">
                  <div class="time-label">启动时间</div>
                  <div class="time-value">{{ formatDateTime(statusDetail.start_time) }}</div>
                </div>
                <div class="time-item">
                  <div class="time-label">停止时间</div>
                  <div class="time-value">{{ formatDateTime(statusDetail.stopped_at) }}</div>
                </div>
                <div class="time-item">
                  <div class="time-label">最后更新</div>
                  <div class="time-value">{{ formatDateTime(statusDetail.last_update) }}</div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="statusDetailVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
      
      <!-- 文件浏览对话框 -->
      <el-dialog
        v-model="fileBrowserVisible"
        title="文件浏览器"
        width="700px"
        destroy-on-close
      >
        <div v-loading="fileBrowserLoading" class="file-browser">
          <div class="file-browser-header">
            <div class="current-path">{{ currentPath || '/' }}</div>
            <el-button size="small" @click="navigateUp" :disabled="currentPath === ''">
              <el-icon><Back /></el-icon> 返回上级
            </el-button>
          </div>
          
          <el-table :data="browserFiles" size="small" border style="width: 100%">
            <el-table-column prop="name" label="名称">
              <template #default="scope">
                <div class="file-item" @click="navigateToFile(scope.row)">
                  <el-icon class="file-icon">
                    <Folder v-if="scope.row.type === 'directory'" />
                    <Document v-else />
                  </el-icon>
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="大小" width="100" />
            <el-table-column label="修改时间" width="180">
              <template #default="scope">
                {{ formatFileTime(scope.row.modified) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button-group>
                  <el-button 
                    size="small" 
                    type="primary" 
                    @click.stop="downloadBrowserFile(scope.row)"
                    :disabled="scope.row.type === 'directory'"
                  >
                    <el-icon><Download /></el-icon>
                  </el-button>
                  <el-button
                    size="small"
                    type="info"
                    @click.stop="previewFile(scope.row)"
                    :disabled="scope.row.type === 'directory' || !isTextFile(scope.row.name)"
                  >
                    <el-icon><ZoomIn /></el-icon>
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-dialog>

      <!-- 文件预览对话框 -->
      <el-dialog
        v-model="filePreviewVisible"
        :title="`文件预览: ${previewFileName}`"
        width="700px"
        destroy-on-close
      >
        <div v-loading="filePreviewLoading" class="file-preview">
          <div class="preview-actions">
            <el-button type="primary" @click="downloadPreviewFile">
              <el-icon><Download /></el-icon> 下载文件
            </el-button>
            <el-button type="info" @click="copyPreviewContent">
              <el-icon><DocumentCopy /></el-icon> 复制内容
            </el-button>
          </div>
          <div class="preview-content">
            <pre>{{ filePreviewContent }}</pre>
          </div>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { 
    Document,
    Monitor,
    VideoPlay,
    DataLine,
    Delete,
    Refresh,
    InfoFilled,
    VideoPause,
    CircleClose,
    Folder,
    Download,
    Back,
    ZoomIn,
    DocumentCopy,
    Link
  } from '@element-plus/icons-vue'
  import { toast, showModal } from '~/composables/util'
  import service from '~/axios'
  import { ElLoading } from 'element-plus'
  
  const router = useRouter()
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const tableData = ref([])
  
  // 节点详情相关
  const nodeDetailVisible = ref(false)
  const nodeDetailLoading = ref(false)
  const nodeDetail = ref({})
  
  // 代码详情相关
  const codeDetailVisible = ref(false)
  const codeDetailLoading = ref(false)
  const codeDetail = ref(null)
  
  // 运行代码相关
  const runDialogVisible = ref(false)
  const currentCode = ref(null)
  const runParams = ref({})
  const argsTypes = ref({})  // 新增：存储参数类型
  
  // 解析输入参数
  const parsedInputKwargs = computed(() => {
    if (!currentCode.value || !currentCode.value.inputKwargs) return []
    
    try {
      // 如果是字符串，尝试解析
      if (typeof currentCode.value.inputKwargs === 'string') {
        return JSON.parse(currentCode.value.inputKwargs)
      }
      // 如果已经是数组，直接返回
      if (Array.isArray(currentCode.value.inputKwargs)) {
        return currentCode.value.inputKwargs
      }
      return []
    } catch (error) {
      console.error('解析参数失败:', error)
      return []
    }
  })
  
  // 解析savedFiles
  const parsedSavedFiles = computed(() => {
    if (!codeDetail.value || !codeDetail.value.savedFiles) return []
    
    try {
      // 如果是字符串，尝试解析
      if (typeof codeDetail.value.savedFiles === 'string') {
        return JSON.parse(codeDetail.value.savedFiles)
      }
      // 如果已经是数组，直接返回
      if (Array.isArray(codeDetail.value.savedFiles)) {
        return codeDetail.value.savedFiles
      }
      return []
    } catch (error) {
      console.error('解析保存文件列表失败:', error)
      return []
    }
  })
  
  // 判断是否是 PYXIS 型代码
  const isPyxisCode = (code) => {
    if (!code) return false
    
    // 根据节点名称判断
    if (code.nodeName === 'Pyxis') return true
    
    // 或者根据其他特征判断
    // 如果没有syceeJobId并且没有funcName，也认为是PYXIS代码
    if (!code.syceeJobId && !code.funcName) return true
    
    return false
  }
  
  // 判断代码是否在运行中
  const isCodeRunning = (row) => {
    return row.status === 'RUNNING' || row.status === 'running'
  }
  
  // 获取代码列表
  const fetchCodes = async (page = 1) => {
    loading.value = true
    try {
      const params = {
        page: page,
        size: pageSize.value
      }
      
      const response = await service.get('/api/v1.0/sys/code/list', { params })
      if (response.code === 10000) {
        tableData.value = response.data.codes || []
        total.value = response.data.pagination.total
        pageSize.value = response.data.pagination.size
      } else {
        toast('错误', response.message || '获取代码列表失败', 'error')
      }
    } catch (error) {
      console.error('获取代码列表失败:', error)
      toast('错误', '获取代码列表失败', 'error')
    } finally {
      loading.value = false
    }
  }
  
  // 获取状态类型
  const getStatusType = (status) => {
    const statusMap = {
      'PENDING': 'warning',
      'APPROVED': 'success',
      'REJECTED': 'danger',
      'RUNNING': 'primary',
      'COMPLETED': 'success',
      'ERROR': 'danger',
      'running': 'primary',
      'exited': 'warning',
      'created': 'info',
      'killed': 'danger'
    }
    return statusMap[status] || 'info'
  }
  
  // 获取状态文本
  const getStatusText = (status) => {
    const statusMap = {
      'PENDING': '待审批',
      'APPROVED': '已批准',
      'REJECTED': '已拒绝',
      'RUNNING': '运行中',
      'COMPLETED': '已完成',
      'ERROR': '错误',
      'running': '运行中',
      'exited': '已停止',
      'created': '已创建',
      'killed': '已杀死'
    }
    return statusMap[status] || status
  }
  
  // 获取容器状态类型
  const getContainerStatusType = (status) => {
    const statusMap = {
      'created': 'info',
      'running': 'success',
      'exited': 'warning',
      'killed': 'danger',
      'kill_failed': 'danger',
      'dead': 'danger',
      'paused': 'info',
      'cancelled': 'info',
      'not_found': 'warning',
      'stopped': 'warning',
      'finished': 'success',
      'launching': 'info',
      'building image': 'info',
      'launching container': 'info',
      'launch_failed': 'danger',
      'queued': 'info',
      'error': 'danger'
    }
    return statusMap[status] || 'info'
  }
  
  // 获取容器状态文本
  const getContainerStatusText = (status) => {
    const statusMap = {
      'created': '已创建',
      'running': '运行中',
      'exited': '已停止',
      'killed': '已杀死',
      'kill_failed': '杀死失败',
      'dead': '已终止',
      'paused': '已暂停',
      'cancelled': '已取消',
      'not_found': '未找到',
      'stopped': '已停止',
      'finished': '已完成',
      'launching': '启动中',
      'building image': '构建镜像中',
      'launching container': '启动容器中',
      'launch_failed': '启动失败',
      'queued': '队列中',
      'error': '错误'
    }
    return statusMap[status] || status
  }
  
  // 获取退出码类型
  const getExitCodeType = (code) => {
    if (code === null) return 'info'
    if (code === 0) return 'success'
    if (code === 137) return 'warning' // SIGKILL
    return 'danger'
  }
  
  // 格式化日期时间
  const formatDateTime = (timestamp) => {
    if (!timestamp) return ''
    const date = new Date(timestamp)
    return date.toLocaleString()
  }
  
  // 格式化文件修改时间
  const formatFileTime = (timestamp) => {
    if (!timestamp) return '无'
    
    try {
      // 处理科学计数法格式的时间戳 (1.7474714980392427E9)
      const milliseconds = parseFloat(timestamp) * 1000
      if (!isNaN(milliseconds)) {
        const date = new Date(milliseconds)
        if (!isNaN(date.getTime())) {
          return date.toLocaleString('zh-CN', { hour12: false })
        }
      }
      
      // 如果不是时间戳格式，尝试普通日期转换
      return formatDateTime(timestamp)
    } catch (e) {
      console.error('文件时间格式化错误:', e, timestamp)
      return String(timestamp)
    }
  }
  
  // 查看节点详情
  const viewNodeDetail = async (nodeId) => {
    nodeDetailVisible.value = true
    nodeDetailLoading.value = true
    
    try {
      const response = await service.get(`/api/v1.0/sys/node/${nodeId}`)
      if (response.code === 10000) {
        nodeDetail.value = response.data
      } else {
        toast('错误', response.message || '获取节点详情失败', 'error')
      }
    } catch (error) {
      console.error('获取节点详情失败:', error)
      toast('错误', '获取节点详情失败', 'error')
    } finally {
      nodeDetailLoading.value = false
    }
  }
  
  // 跳转到节点详情页
  const goToNodeDetail = () => {
    nodeDetailVisible.value = false
    router.push({
      path: '/node/detail',
      query: { id: nodeDetail.value.id }
    })
  }
  
  // 查看代码详情
  const viewCodeDetail = async (row) => {
    codeDetailVisible.value = true
    codeDetailLoading.value = true
    
    try {
      const response = await service.get(`/api/v1.0/sys/code/${row.id}`)
      if (response.code === 10000) {
        codeDetail.value = response.data
      } else {
        toast('错误', response.message || '获取代码详情失败', 'error')
      }
    } catch (error) {
      console.error('获取代码详情失败:', error)
      toast('错误', '获取代码详情失败', 'error')
    } finally {
      codeDetailLoading.value = false
    }
  }
  
  // 处理运行代码
  const handleRun = (row) => {
    currentCode.value = row
    runParams.value = {}
    argsTypes.value = {}  // 重置参数类型
    
    // 初始化参数
    const params = parsedInputKwargs.value
    if (params && params.length > 0) {
      params.forEach(param => {
        runParams.value[param] = ''
        argsTypes.value[param] = false  // 默认为非字符串类型
      })
    }
    
    runDialogVisible.value = true
  }
  
  // 提交运行代码
  const submitRunCode = async () => {
    if (!currentCode.value) return
    
    try {
      // 格式化参数
      const formattedParams = {}
      Object.keys(runParams.value).forEach(key => {
        const value = runParams.value[key]
        // 根据用户选择决定是否保持字符串类型
        if (argsTypes.value[key]) {
          formattedParams[key] = value // 保持字符串
        } else {
          // 如果不是字符串类型且可以转换为数字，则转换
          formattedParams[key] = !isNaN(value) && value !== '' ? Number(value) : value
        }
      })

      const response = await service.post(
        `/api/v1.0/sys/code/run/${currentCode.value.id}`, 
        formattedParams
      )
      
      if (response.code === 10000) {
        toast('成功', '代码已开始运行', 'success')
        runDialogVisible.value = false
        fetchCodes(currentPage.value) // 刷新列表
      } else {
        toast('错误', response.message || '运行代码失败', 'error')
      }
    } catch (error) {
      console.error('运行代码失败:', error)
      toast('错误', '运行代码失败', 'error')
    }
  }
  
  // 页码变化
  const handleCurrentChange = (val) => {
    fetchCodes(val)
  }
  
  // 每页数量变化
  const handleSizeChange = (val) => {
    pageSize.value = val
    fetchCodes(1)
  }
  
  // 添加结果和日志相关的状态
  const resultDialogVisible = ref(false)
  const logDialogVisible = ref(false)
  const resultLoading = ref(false)
  const logLoading = ref(false)
  const codeResult = ref('')
  const codeLog = ref('')
  
  // 查看代码运行结果
  const viewCodeResult = async (row) => {
    resultDialogVisible.value = true
    resultLoading.value = true
    codeResult.value = ''
    
    try {
      const response = await service.get(`/api/v1.0/sys/code/result/${row.id}`)
      if (response.code === 10000) {
        // 格式化结果为JSON字符串
        if (typeof response.data.result === 'object') {
          codeResult.value = JSON.stringify(response.data.result, null, 2)
        } else {
          codeResult.value = response.data.result || '无结果'
        }
      } else {
        toast('错误', response.message || '获取代码运行结果失败', 'error')
      }
    } catch (error) {
      console.error('获取代码运行结果失败:', error)
      toast('错误', '获取代码运行结果失败', 'error')
    } finally {
      resultLoading.value = false
    }
  }
  
  // 查看代码运行日志
  const viewCodeLog = async (row) => {
    logDialogVisible.value = true
    logLoading.value = true
    codeLog.value = ''
    
    try {
      const response = await service.get(`/api/v1.0/sys/code/log/${row.id}`)
      if (response.code === 10000) {
        codeLog.value = response.data.log || '无日志'
      } else {
        toast('错误', response.message || '获取代码运行日志失败', 'error')
      }
    } catch (error) {
      console.error('获取代码运行日志失败:', error)
      toast('错误', '获取代码运行日志失败', 'error')
    } finally {
      logLoading.value = false
    }
  }
  
  // 添加选中行的数据
  const selectedIds = ref([])
  
  // 添加处理选择变化的方法
  const handleSelectionChange = (selection) => {
    selectedIds.value = selection.map(item => item.id)
  }
  
  // 添加删除方法
  const handleDelete = async (row) => {
    try {
      await showModal(`确定要删除代码 "${row.funcName}" 吗？`, 'warning', '提示')
      const response = await service.delete(`/api/v1.0/sys/code/${row.id}`)
      
      if (response.code === 10000) {
        toast('成功', '删除成功')
        fetchCodes(currentPage.value)
      } else {
        toast('错误', response.message || '删除失败', 'error')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除代码失败:', error)
        toast('错误', '删除失败', 'error')
      }
    }
  }
  
  // 添加批量删除方法
  const handleBatchDelete = async () => {
    if (selectedIds.value.length === 0) {
      toast('警告', '请选择要删除的代码', 'warning')
      return
    }
    
    try {
      await showModal(`确定要删除选中的 ${selectedIds.value.length} 个代码吗？`, 'warning', '提示')
      
      // 构造请求参数
      const queryParams = selectedIds.value.map(id => `ids=${id}`).join('&')
      const response = await service.delete(`/api/v1.0/sys/code/batch?${queryParams}`)
      
      if (response.code === 10000) {
        toast('成功', '批量删除成功')
        selectedIds.value = []
        fetchCodes(currentPage.value)
      } else {
        toast('错误', response.message || '批量删除失败', 'error')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量删除代码失败:', error)
        toast('错误', '批量删除失败', 'error')
      }
    }
  }
  
  // 刷新代码状态
  const refreshCodeStatus = async (row) => {
    try {
      const loading = ElLoading.service({
        lock: true,
        text: '正在刷新审核状态...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      // 直接从数据库获取最新代码信息
      const detailResponse = await service.get(`/api/v1.0/sys/code/${row.id}`)
      
      if (detailResponse.code === 10000) {
        // 更新表格中的状态
        const index = tableData.value.findIndex(item => item.id === row.id)
        if (index !== -1) {
          // 更新审核状态
          tableData.value[index].status = detailResponse.data.status
        }
        
        // 如果在状态详情页面，也更新状态详情
        if (currentCodeId.value === row.id && statusDetailVisible.value) {
          // 刷新Pyxis状态详情
          if (isPyxisCode(row)) {
            await viewCodeStatus(row)
          }
        }
        
        toast('成功', '代码审核状态已刷新', 'success')
        
        // 重新加载当前页数据以确保所有信息都是最新的
        await fetchCodes(currentPage.value)
      } else {
        toast('错误', detailResponse.message || '刷新审核状态失败', 'error')
      }
      
      loading.close()
    } catch (error) {
      console.error('刷新审核状态失败:', error)
      toast('错误', '刷新审核状态失败', 'error')
    }
  }
  
  // PYXIS 型代码相关功能
  // 状态详情相关
  const statusDetailVisible = ref(false)
  const statusDetailLoading = ref(false)
  const statusDetail = ref(null)
  const currentCodeId = ref(null)
  
  // 容器ID省略显示
  const ellipsis = (str, len = 8) => {
    if (!str) return '无'
    if (str.length <= len * 2) return str
    return str.slice(0, len) + '...' + str.slice(-len)
  }
  
  // 查看代码状态详情
  const viewCodeStatus = async (row) => {
    statusDetailVisible.value = true
    statusDetailLoading.value = true
    currentCodeId.value = row.id
    
    try {
      const response = await service.get(`/api/v1.0/sys/code/status/${row.id}`)
      
      if (response.code === 10000) {
        statusDetail.value = response.data
      } else {
        toast('错误', response.message || '获取代码状态详情失败', 'error')
      }
    } catch (error) {
      console.error('获取代码状态详情失败:', error)
      toast('错误', '获取代码状态详情失败', 'error')
    } finally {
      statusDetailLoading.value = false
    }
  }
  
  // 启动代码
  const startCode = async (row) => {
    try {
      const loading = ElLoading.service({
        lock: true,
        text: '正在启动代码...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      const response = await service.post(`/api/v1.0/sys/code/execute/${row.id}`)
      
      if (response.code === 10000) {
        toast('成功', '代码已启动', 'success')
        // 刷新状态
        // await refreshCodeStatus(row)
      } else {
        toast('错误', response.message || '启动代码失败', 'error')
      }
      
      loading.close()
    } catch (error) {
      console.error('启动代码失败:', error)
      toast('错误', '启动代码失败', 'error')
    }
  }
  
  // 停止代码
  const stopCode = async (row) => {
    try {
      await showModal('确定要停止此代码吗？', 'warning', '提示')
      
      const loading = ElLoading.service({
        lock: true,
        text: '正在停止代码...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      const response = await service.post(`/api/v1.0/sys/code/stop/${row.id}`)
      
      if (response.code === 10000) {
        toast('成功', '代码已停止', 'success')
        // 刷新状态
        await refreshCodeStatus(row)
      } else {
        toast('错误', response.message || '停止代码失败', 'error')
      }
      
      loading.close()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('停止代码失败:', error)
        toast('错误', '停止代码失败', 'error')
      }
    }
  }
  
  // 杀死代码
  const killCode = async (row) => {
    try {
      await showModal('确定要强制杀死此代码吗？这可能导致数据丢失！', 'warning', '警告')
      
      const loading = ElLoading.service({
        lock: true,
        text: '正在杀死代码...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      const response = await service.post(`/api/v1.0/sys/code/kill/${row.id}`)
      
      if (response.code === 10000) {
        toast('成功', '代码已杀死', 'success')
        // 刷新状态
        await refreshCodeStatus(row)
      } else {
        toast('错误', response.message || '杀死代码失败', 'error')
      }
      
      loading.close()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('杀死代码失败:', error)
        toast('错误', '杀死代码失败', 'error')
      }
    }
  }
  
  // 文件浏览相关
  const fileBrowserVisible = ref(false)
  const fileBrowserLoading = ref(false)
  const browserFiles = ref([])
  const currentPath = ref('')
  
  // 文件预览相关
  const filePreviewVisible = ref(false)
  const filePreviewLoading = ref(false)
  const filePreviewContent = ref('')
  const previewFileName = ref('')
  
  // 浏览文件
  const browseFiles = async (row) => {
    fileBrowserVisible.value = true
    fileBrowserLoading.value = true
    currentPath.value = ''
    currentCodeId.value = row.id
    
    try {
      await fetchDirectoryContents('')
    } catch (error) {
      console.error('获取文件列表失败:', error)
      toast('错误', '获取文件列表失败', 'error')
    } finally {
      fileBrowserLoading.value = false
    }
  }
  
  // 获取目录内容
  const fetchDirectoryContents = async (path) => {
    fileBrowserLoading.value = true
    try {
      const response = await service.get(`/api/v1.0/sys/code/browse/${currentCodeId.value}`, {
        params: { path: path || '' }
      })
      
      if (response.code === 10000) {
        // 处理API返回的数据结构
        const data = response.data
        if (data.children && Array.isArray(data.children)) {
          browserFiles.value = data.children
        } else if (data.type === 'directory' && data.children && Array.isArray(data.children)) {
          browserFiles.value = data.children
        } else {
          browserFiles.value = []
        }
        currentPath.value = path
      } else {
        toast('错误', response.message || '获取目录内容失败', 'error')
      }
    } catch (error) {
      console.error('获取目录内容失败:', error)
      toast('错误', '获取目录内容失败', 'error')
    } finally {
      fileBrowserLoading.value = false
    }
  }
  
  // 导航到文件或目录
  const navigateToFile = (file) => {
    if (file.type === 'directory') {
      const newPath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name)
      fetchDirectoryContents(newPath)
    } else {
      // 判断是否是文本文件
      if (isTextFile(file.name)) {
        previewFile(file)
      } else {
        downloadBrowserFile(file)
      }
    }
  }
  
  // 判断是否是文本文件
  const isTextFile = (filename) => {
    const textExtensions = ['.txt', '.py', '.js', '.html', '.css', '.json', '.md', '.xml', '.csv', '.log', '.ini', '.conf', '.sh', '.bat', '.yaml', '.yml', '.env']
    const ext = '.' + filename.split('.').pop().toLowerCase()
    return textExtensions.includes(ext)
  }
  
  // 导航到上级目录
  const navigateUp = () => {
    if (!currentPath.value) return
    
    const pathParts = currentPath.value.split('/')
    pathParts.pop()
    const newPath = pathParts.join('/')
    fetchDirectoryContents(newPath)
  }
  
  // 下载文件
  const downloadBrowserFile = (file) => {
    if (file.type === 'directory') return
    
    const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name)
    downloadSpecificFile(currentCodeId.value, filePath)
  }
  
  // 下载文件的通用方法
  const downloadSpecificFile = async (codeId, filePath) => {
    try {
      // 显示加载动画
      const loading = ElLoading.service({
        lock: true,
        text: '正在下载文件...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      // 获取文件名
      const filename = filePath.split('/').pop() || 'downloaded_file'
      
      // 使用service发送请求
      const response = await service.get(`/api/v1.0/sys/code/download/${codeId}`, {
        params: { filePath },
        responseType: 'blob'
      })
      
      // 创建 Blob URL
      const blob = new Blob([response])
      const url = window.URL.createObjectURL(blob)
      
      // 创建下载链接
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      
      // 清理
      setTimeout(() => {
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      }, 100)
      
      // 关闭加载动画
      loading.close()
      
      toast('成功', `文件 ${filename} 下载成功`, 'success')
    } catch (error) {
      console.error('下载文件失败:', error)
      toast('错误', `下载文件失败: ${error.message}`, 'error')
    }
  }
  
  // 预览文件
  const previewFile = async (file) => {
    filePreviewLoading.value = true
    filePreviewVisible.value = true
    previewFileName.value = file.name
    
    try {
      const filePath = file.path || (currentPath.value ? `${currentPath.value}/${file.name}` : file.name)
      
      const response = await service.get(`/api/v1.0/sys/code/preview/${currentCodeId.value}`, {
        params: { filePath },
        responseType: 'text'
      })
      
      filePreviewContent.value = response
    } catch (error) {
      console.error('预览文件失败:', error)
      toast('错误', `预览文件失败: ${error.message}`, 'error')
      filePreviewContent.value = '文件内容加载失败'
    } finally {
      filePreviewLoading.value = false
    }
  }
  
  // 下载当前预览的文件
  const downloadPreviewFile = () => {
    const filePath = currentPath.value ? `${currentPath.value}/${previewFileName.value}` : previewFileName.value
    downloadSpecificFile(currentCodeId.value, filePath)
  }
  
  // 复制预览内容到剪贴板
  const copyPreviewContent = async () => {
    try {
      // 确保内容是字符串格式
      let contentToCopy = filePreviewContent.value
      if (typeof contentToCopy === 'object') {
        contentToCopy = JSON.stringify(contentToCopy, null, 2)
      } else if (contentToCopy === null || contentToCopy === undefined) {
        contentToCopy = ''
      } else {
        contentToCopy = String(contentToCopy)
      }

      await navigator.clipboard.writeText(contentToCopy)
      toast('成功', '内容已复制到剪贴板', 'success')
    } catch (error) {
      console.error('复制失败:', error)
      toast('错误', '复制失败', 'error')
    }
  }
  
  // 判断PYXIS服务是否可用
  const isPyxisServiceAvailable = (row) => {
    return isCodeRunning(row) && row.nodeIp && row.nodePort && row.pyxisTaskId && isPyxisCode(row)
  }
  
  // 打开PYXIS服务
  const openPyxisService = (row) => {
    if (!row || !row.nodeIp || !row.nodePort) return
    
    // 从代码对象中获取服务链接所需信息
    const ipAddress = row.nodeIp
    const port = row.nodePort
    
    // 使用pyxisTaskId作为服务路径参数
    const pyxisTaskId = row.pyxisTaskId
    
    // 检查必要信息是否存在
    if (!ipAddress || !port || !pyxisTaskId) {
      toast('错误', '无法获取服务信息', 'error')
      return
    }
    
    // 构建服务URL - 添加/#结尾
    const serviceUrl = `http://${ipAddress}:${port}/service/${pyxisTaskId}/#`
    
    // 在新窗口打开链接
    window.open(serviceUrl, '_blank')
  }
  
  // 添加获取代码对象的辅助函数
  const getCodeById = (id) => {
    return tableData.value.find(item => item.id === id) || { id: id }
  }
  
  // 申请重新审批
  const requestReapproval = async (row) => {
    try {
      await showModal('确定要申请重新审批此代码吗？', 'warning', '提示')
      
      const response = await service.post(`/api/v1.0/sys/code/reapproval/${row.id}`)
      
      if (response.code === 10000) {
        toast('成功', '代码重新审批申请已提交', 'success')
        // 刷新状态
        await refreshCodeStatus(row)
      } else {
        toast('错误', response.message || '申请重新审批失败', 'error')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('申请重新审批失败:', error)
        toast('错误', '申请重新审批失败', 'error')
      }
    }
  }
  
  onMounted(() => {
    fetchCodes()
  })
  </script>
  
  <style scoped>
  .code-list-container {
    padding: 20px;
  }
  
  .list-card {
    border: none;
    border-radius: 8px;
    margin-bottom: 0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }
  
  .list-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
  
  :deep(.el-table) {
    border-radius: 8px;
    margin-bottom: 0;
    --el-table-border-color: var(--el-border-color-lighter);
  }
  
  :deep(.el-table--border) {
    border: 1px solid var(--el-table-border-color);
    border-radius: 8px;
  }
  
  :deep(.el-table--border::after),
  :deep(.el-table--border .el-table__inner-wrapper::after) {
    display: none;
  }
  
  :deep(.el-table th) {
    font-weight: bold;
  }
  
  /* 深色模式样式 */
  html.dark .list-card {
    background-color: var(--el-bg-color-overlay);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
  
  html.dark .list-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .header-left {
    flex: 1;
    .title-wrapper {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .title-icon {
        margin-right: 8px;
        font-size: 24px;
        color: var(--el-color-primary);
      }
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
    }
    
    .sub-title {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    margin-left: 20px;
    
    .el-button {
      font-size: 15px;
      padding: 10px 20px;
    }
    
    .el-icon {
      margin-right: 5px;
      font-size: 16px;
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  :deep(.el-pagination) {
    justify-content: center !important;
  }
  
  :deep(.el-pagination .el-select .el-input) {
    width: 110px;
  }
  
  .file-icon {
    margin-right: 5px;
    font-size: 16px;
  }
  
  .code-content-wrapper {
    margin-top: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .code-header {
    background-color: #f5f7fa;
    padding: 8px 12px;
    font-weight: bold;
    border-bottom: 1px solid #dcdfe6;
  }
  
  .code-pre {
    background-color: #f8f8f8;
    padding: 12px;
    margin: 0;
    max-height: 400px;
    overflow: auto;
    font-family: monospace;
    white-space: pre-wrap;
    word-break: break-all;
  }
  
  html.dark .code-pre {
    background-color: #1e1e1e;
    color: #d4d4d4;
  }
  
  html.dark .code-header {
    background-color: #2d2d2d;
    border-bottom: 1px solid #3e3e3e;
  }
  
  html.dark .code-content-wrapper {
    border: 1px solid #3e3e3e;
  }
  
  .result-pre,
  .log-pre {
    background-color: #f8f8f8;
    padding: 12px;
    margin: 0;
    max-height: 500px;
    overflow: auto;
    font-family: monospace;
    white-space: pre-wrap;
    word-break: break-all;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
  
  html.dark .result-pre,
  html.dark .log-pre {
    background-color: #1e1e1e;
    color: #d4d4d4;
    border-color: #3e3e3e;
  }
  
  .param-input-group {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .param-type-switch {
    flex-shrink: 0;
  }
  
  .param-input-group .el-input {
    flex: 1;
  }
  
  /* PYXIS 功能相关样式 */
  .container-actions {
    margin-top: 8px;
  }
  
  .mt-2 {
    margin-top: 8px;
  }
  
  /* 状态详情样式 */
  .status-detail-custom {
    padding: 10px 0;
    color: var(--el-text-color-primary);
  }
  
  .status-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .status-title {
    font-size: 16px;
    font-weight: bold;
  }
  
  .status-tag {
    font-size: 16px;
    font-weight: bold;
    padding: 6px 12px;
  }
  
  .info-section {
    margin-bottom: 20px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
    padding: 15px;
  }
  
  .section-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--el-text-color-secondary);
    position: relative;
    padding-left: 10px;
  }
  
  .section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    height: 14px;
    width: 3px;
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
  
  .info-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .info-item {
    display: flex;
    align-items: center;
  }
  
  .info-label {
    width: 80px;
    color: var(--el-text-color-secondary);
    font-weight: 500;
  }
  
  .info-value {
    word-break: break-all;
  }
  
  .error-text {
    color: var(--el-color-danger);
  }
  
  .time-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .time-item {
    padding: 8px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }
  
  .time-label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-bottom: 4px;
  }
  
  .time-value {
    font-weight: 500;
  }
  
  .container-action-group {
    margin-top: 15px;
    display: flex;
    justify-content: center;
  }
  
  /* 文件浏览样式 */
  .file-browser-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .current-path {
    font-family: monospace;
    font-weight: 500;
    padding: 4px 8px;
    background-color: var(--el-fill-color);
    border-radius: 4px;
  }
  
  .file-item {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  
  .file-item:hover {
    color: var(--el-color-primary);
  }
  
  /* 文件预览样式 */
  .file-preview {
    max-height: 500px;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .preview-actions {
    display: flex;
    gap: 10px;
  }
  
  .preview-content {
    background-color: #f8f8f8;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    flex: 1;
    overflow: auto;
  }
  
  .preview-content pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
    font-family: monospace;
  }
  
  html.dark .preview-content {
    background-color: #1e1e1e;
    color: #d4d4d4;
    border-color: #3e3e3e;
  }
  
  html.dark .time-item {
    background-color: var(--el-bg-color-overlay);
  }
  
  html.dark .info-section {
    background-color: var(--el-bg-color);
  }
  
  .pyxis-func-name {
    color: var(--el-color-primary);
    font-weight: 500;
    font-family: monospace;
  }
  
  .saved-files-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .saved-file-tag {
    display: flex;
    align-items: center;
    font-family: monospace;
  }
  
  .saved-file-tag .file-icon {
    margin-right: 4px;
  }
  </style> 