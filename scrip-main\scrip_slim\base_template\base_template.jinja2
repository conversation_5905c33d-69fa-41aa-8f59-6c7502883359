<!DOCTYPE html>
<html>

<head>
    <title>Scrip</title>
    <link rel="icon" href="data:,"><!--avoid "GET /favicon.ico"-->
    <style>
        :root {
            --primary-color: #4285f4;
            --secondary-color: #34a853;
            --error-color: #ea4335;
            --light-gray: #f5f5f5;
            --dark-gray: #333;

            /* Fluid typography */
            font-size: calc(14px + 0.3vw);
            --font-size-sm: 0.875rem;
            --font-size-md: 1rem;
            --font-size-lg: 1.25rem;
            --font-size-xl: 1.5rem;

            /* Spacing */
            --space-xs: 0.5rem;
            --space-sm: 1rem;
            --space-md: 1.5rem;
            --space-lg: 2rem;
            --space-xl: 3rem;

            /* Border radius */
            --radius-sm: 0.25rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;

            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--dark-gray);
            width: 100%;
            background-color: #f9f9f9;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .main-container {
            width: 75vw;
            box-sizing: border-box;
            margin: 2rem auto;
        }

        @media (min-width: 768px) {
            .main-container {
                width: 80vw;
            }
        }

        @media (min-width: 1024px) {
            .main-container {
                width: min(65vw, 1200px);
            }
        }

        @media (min-width: 1440px) {
            .main-container {
                width: min(60vw, 1400px);
            }
        }

        /* Common container styles */
        .container {
            background: white;
            border-radius: var(--radius-md);
            padding: var(--space-md);
            box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
            margin-bottom: var(--space-md);
            width: 100%;
            box-sizing: border-box;
        }

        /* Countdown styles */
        .countdown {
            text-align: center;
            padding: var(--space-sm);
            background-color: #fff8e1;
            color: #ff8f00;
            border-radius: var(--radius-sm);
            margin-bottom: var(--space-md);
            width: 100%;
            box-sizing: border-box;
        }

        /* Form and results common styles */
        .form-container,
        .results-container {
            margin: var(--space-md) auto;
            padding: var(--space-md);
        }

        /* Loading styles */
        .loading {
            width: 3rem;
            height: 3rem;
            border: 0.4rem solid rgba(52, 152, 219, 0.2);
            border-top: 0.4rem solid rgba(52, 152, 219, 1);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .error {
            color: var(--error-color);
        }

        /* Button styles */
        button,
        .btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: var(--font-size-md);
            transition: all 0.3s;
            display: block;
            width: 100%;
        }

        button:hover,
        .btn:hover {
            background-color: #3367d6;
        }

        /* Form group styles */
        .form-group {
            margin-bottom: var(--space-sm);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--space-xs);
            font-weight: 600;
            color: #495057;
            font-size: var(--font-size-sm);
        }

        .form-group input {
            width: 100%;
            padding: var(--space-xs);
            border: 1px solid #ced4da;
            border-radius: var(--radius-sm);
            font-size: var(--font-size-sm);
            box-sizing: border-box;
        }

        /* File upload styles */
        .file-upload-wrapper {
            position: relative;
            width: 100%;
            margin-top: var(--space-xs);
        }

        .file-upload-button {
            display: block;
            padding: var(--space-sm);
            background-color: transparent;
            border: 2px dashed #ced4da;
            border-radius: var(--radius-sm);
            color: #495057;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .file-upload-button:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
        }

        .file-upload-wrapper input[type="file"] {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-name-display {
            display: block;
            margin-top: var(--space-xs);
            font-size: var(--font-size-sm);
            color: #6c757d;
            font-style: italic;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .file-upload-wrapper.has-file .file-upload-button {
            border-color: #4e73df;
            background-color: #f8f9fa;
        }

        /* Table Styles */
        .table-wrapper {
            width: 100%;
            margin: var(--space-sm) 0;
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--font-size-sm);
            margin-bottom: var(--space-sm);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
        }

        .data-table th {
            background-color: var(--primary-color);
            color: white;
            text-align: left;
            padding: var(--space-sm);
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .data-table td {
            padding: var(--space-sm);
            border-bottom: 1px solid #e0e0e0;
            vertical-align: middle;
        }

        .data-table tr:nth-child(even) {
            background-color: var(--light-gray);
        }

        .data-table tr:hover {
            background-color: rgba(66, 133, 244, 0.1);
        }


        /* Results specific styles */
        .results {
            word-wrap: break-word;
            word-break: normal;
        }

        .results h2 {
            color: #2e59d9;
            margin-top: 0;
            padding-bottom: var(--space-sm);
            border-bottom: 1px solid #dee2e6;
            font-size: var(--font-size-lg);
        }

        .result-value {
            padding: var(--space-sm);
            background-color: #e9ecef;
            border-radius: var(--radius-sm);
            font-size: var(--font-size-md);
            color: #495057;
        }

        .results ul {
            margin: 0;
            padding: 0;
            list-style-type: none;
        }

        .results label {
            font-weight: bold;
        }

        .pre-wrap {
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-size: var(--font-size-sm);
        }

        .file-upload-wrapper.has-multiple-files .file-upload-button {
            border-color: var(--secondary-color);
            background-color: rgba(52, 168, 83, 0.05);
        }

        .radio-group {
            padding: var(--space-xs) 0;
        }

        .radio-option {
            display: flex;
            align-items: center;
            margin-bottom: var(--space-xs);
        }

        .radio-option input[type="radio"] {
            width: auto;
            margin-right: var(--space-xs);
        }

        .radio-option label {
            margin-bottom: 0;
            font-weight: normal;
            cursor: pointer;
        }

        .checkbox-group {
            padding: var(--space-xs) 0;
        }

        .checkbox-option {
            display: flex;
            align-items: center;
            margin-bottom: var(--space-xs);
        }

        .checkbox-option input[type="checkbox"] {
            width: auto;
            margin-right: var(--space-xs);
        }

        .checkbox-option label {
            margin-bottom: 0;
            font-weight: normal;
            cursor: pointer;
        }

        select {
            width: 100%;
            padding: var(--space-xs);
            border: 1px solid #ced4da;
            border-radius: var(--radius-sm);
            font-size: var(--font-size-sm);
            background-color: white;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        select:hover {
            border-color: #adb5bd;
        }

        input:focus,
        select:focus,
        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
        }

        .drag-over {
            background-color: rgba(66, 133, 244, 0.1) !important;
            border-color: var(--primary-color) !important;
        }

        .color-picker-wrapper {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .color-input {
            height: 2.5rem;
            padding: 0.15rem !important;
            border: 1px solid #ced4da !important;
            border-radius: var(--radius-sm) !important;
            cursor: pointer;
            transition: all 0.2s;
        }

        .color-input:hover {
            transform: scale(1.05);
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }

        .color-input::-webkit-color-swatch {
            border: none;
            border-radius: var(--radius-sm);
        }

        .color-input::-moz-color-swatch {
            border: none;
            border-radius: var(--radius-sm);
        }

        .color-value {
            font-family: monospace;
            font-size: 0.9em;
            color: var(--dark-gray);
            background: var(--light-gray);
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            user-select: all;
        }

        @media (max-width: 768px) {

            .radio-group,
            .checkbox-group {
                padding: var(--space-xs) 0;
            }

            .radio-option,
            .checkbox-option {
                margin-bottom: var(--space-xs);
            }

            .color-picker-wrapper {
                gap: 0.5rem;
            }

            .color-input {
                width: 2.5rem !important;
                height: 2.2rem;
            }
        }

        .api-tag {
            position: fixed;
            top: 0;
            left: 1rem;
            background-color: #add8e6;
            color: #00008b;
            padding: 0.3rem 0.5rem;
            border-radius: 0 0 4px 0;
            font-family: sans-serif;
            font-size: 1rem;
            box-shadow: 0.05rem 0.05rem 0.2rem rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }

        .api-tag a,
        .api-tag a:visited {
            text-decoration: none;
            color: inherit;
            font-size: 1rem;
        }

        .api-tag a:hover {
            color: blueviolet;
        }
    </style>
</head>

<body>

    <div class="main-container">
        <div class="countdown">
            <div>Service will automatically shut down in <span id="countdown-timer">00:00:00</span></div>
        </div>

        <div id="input-container" class="container form-container">
            {{ input_template|safe }}
        </div>

        <div id="output-container" class="container results-container" style="display:none;">
            <div id="output-content"></div>
        </div>
    </div>

    <div class="api-tag">
        <a id="api_doc" href="doc">&lt;/&gt;</a>
    </div>

    <script>
        // Countdown functionality
        const destroyTime = {{ destroy_time }};
        const runForever = "{{ run_forever }}"

        function updateCountdown() {
            const now = new Date().getTime()
            const distance = destroyTime - now

            if (distance < 0) {
                document.getElementById('countdown-timer').textContent = "00:00:00"
                document.querySelector('.countdown').style.backgroundColor = '#ffebee'
                document.querySelector('.countdown').textContent = "Service has expired, please redeploy"
                return
            }

            const hours = Math.floor(distance / (1000 * 60 * 60))
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
            const seconds = Math.floor((distance % (1000 * 60)) / 1000)

            document.getElementById('countdown-timer').textContent =
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`

            setTimeout(updateCountdown, 1000)
        }

        document.addEventListener('DOMContentLoaded', function () {
            if (runForever == "True") {
                document.querySelector('.countdown').style.backgroundColor = '#effeef'
                document.querySelector('.countdown').style.color = '#008b45'
                document.querySelector('.countdown').textContent = "Service is continuously available"
            } else {
                updateCountdown()
            }

            // Handle form submission
            const form = document.querySelector('form')
            if (form) {
                form.addEventListener('submit', function (e) {
                    e.preventDefault()

                    const outputContainer = document.getElementById('output-container')
                    const outputContent = document.getElementById('output-content')
                    const loading = document.createElement('div')
                    loading.className = 'loading'

                    outputContent.innerHTML = ''
                    outputContent.appendChild(loading)
                    outputContainer.style.display = 'block'

                    const formData = new FormData(form);

                    (async function pipe() {
                        try {
                            const response = await fetch('pipe', {
                                method: 'POST',
                                body: formData
                            })

                            const text = await response.text()

                            if (!response.ok) {
                                const json_rep = JSON.parse(text)
                                const msg =
                                    json_rep?.trace ??
                                    json_rep?.message ??
                                    "unknown error"
                                throw new Error(msg)
                            }

                            outputContent.innerHTML = text
                        } catch (error) {
                            outputContent.innerHTML =
                                `<div class="error">
                                <h2>Error</h2>
                                <pre class="pre-wrap">${(error.message)}</pre>
                            </div>`
                        }
                    })()

                })
            }
        });
    </script>
</body>

</html>