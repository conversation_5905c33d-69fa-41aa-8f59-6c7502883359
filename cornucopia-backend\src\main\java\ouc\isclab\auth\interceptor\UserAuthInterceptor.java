package ouc.isclab.auth.interceptor;

import com.google.gson.JsonObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import ouc.isclab.auth.service.TokenService;

import java.io.IOException;
import java.util.Optional;

@Slf4j
public class UserAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private TokenService tokenService;

    /**
     * preHandle 在请求处理之前被调用。如果返回true，则继续处理请求;如果返回false，则中止请求
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        String token = request.getHeader("token");
        if (token == null || token.isEmpty()) {
            handleUnauthorized(response, "未授权,请先登录");
            return false;
        }

        Optional<Long> userId = tokenService.validateToken(token);
        if (userId.isPresent()) {
            // 将用户ID存储在request中,供后续使用
            request.setAttribute("userId", userId.get());
            return true;
        } else {
            handleUnauthorized(response, "token已过期或无效");
            return false;
        }
    }

    private void handleUnauthorized(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=utf-8");
        
        JsonObject result = new JsonObject();
        result.addProperty("code", 401);
        result.addProperty("msg", message);
        
        response.getWriter().write(result.toString());
    }

    /**
     * postHandle 在请求处理之后，但在视图渲染之前被调用，可以对模型和视图进行进一步处理
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    /**
     * afterCompletion 在请求处理完成且视图渲染之后被调用，可以进行资源清理等操作
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
