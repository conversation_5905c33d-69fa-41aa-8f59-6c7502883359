package ouc.isclab.system.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import ouc.isclab.system.entity.UserEntity;
import java.util.Date;
import org.springframework.transaction.annotation.Transactional;

public interface UserRepository extends JpaRepository<UserEntity, Long> {
    UserEntity getUserEntityById(Long id);

    UserEntity getUserEntityByUsername(String username);

    boolean existsByUsername(String username);

    long countByOnlineTrue();

    long countByLastLoginAfter(Date date);

    @Modifying
    @Transactional
    @Query("UPDATE UserEntity u SET u.online = false")
    void resetAllUsersOffline();
}
