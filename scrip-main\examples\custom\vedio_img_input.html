<h2>Video Detection</h2>
<div class="video-container" style="text-align: center;">
    <video id="video-player" width="640" height="360" controls style="display: inline-block;width: 90%;height: auto;"
        poster="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='640' height='360' viewBox='0 0 640 360'><rect width='100%' height='100%' fill='%23f0f0f0'/><text x='50%' y='50%' font-size='16' text-anchor='middle' fill='%23999'>No video selected</text></svg>">
        <source src="" type="video/mp4">
        No video selected or your browser does not support videos.
    </video>
</div>

<form id="pipe-form">
    <input type="hidden" id="frame-data" name="frame_data">
    <div class="form-group">
        <label for="confidence">Confidence (0-1):</label>
        <input type="number" id="confidence" name="confidence" min="0" max="1" step="0.1" value="0.5">
    </div>
    <button type="button" id="capture-btn">Capture&Submit</button>
    <button type="submit" id="submit-btn" style="display: none;">Submit</button>
</form>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const video = document.getElementById('video-player')
        const fileInput = document.createElement('input')
        const submitBtn = document.getElementById('submit-btn')
        fileInput.type = 'file'
        fileInput.accept = 'video/*'

        video.addEventListener('click', function () {
            fileInput.click()
        })

        fileInput.addEventListener('change', function (e) {
            const file = e.target.files[0]
            if (file) {
                const videoURL = URL.createObjectURL(file)
                video.src = videoURL
            }
        })

        document.getElementById('capture-btn').addEventListener('click', function () {

            if (!video.videoWidth || !video.videoHeight) {
                alert('please click the video-player to upload a video first')
                return
            }

            const canvas = document.createElement('canvas')
            canvas.width = video.videoWidth
            canvas.height = video.videoHeight
            canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height)

            const frameData = canvas.toDataURL('image/jpeg')
            document.getElementById('frame-data').value = frameData

            submitBtn.click()
        })
    });
</script>