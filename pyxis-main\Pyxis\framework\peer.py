import os
import requests
from typing import Optional, Dict, Any
from datetime import datetime


class Peer:
    """Client for interacting with the P2P routing service.

    Args:
        routing_service_url: Base URL of the routing service
    """

    def __init__(self, routing_service_url: str = "http://pyxis-core:8000/peer"):
        self.routing_service_url = routing_service_url
        self.task_id = os.getenv("TASK_ID", "default_task")
        self.task_port = os.getenv("TASK_PORT", 8000)
        self.host_port = int(os.getenv("HOST_PORT", 8000))
        self.host_id = os.getenv("HOST_ID", "default_host")
        self.registered_info = None

    def register(self, nickname: str) -> Dict:
        """Register the current node with the routing service.

        Args:
            nickname: Unique identifier for this node

        Returns:
            Dict: Response from registration service
        """
        registration_data = {
            "nickname": nickname,
            "value": {
                "task_id": self.task_id,
                "task_port": self.task_port,
                "host_port": self.host_port,
                "host_id": self.host_id
            }
        }

        print(f"send registration_data:{registration_data}")

        try:
            response = requests.post(
                f"{self.routing_service_url}/register/",
                json=registration_data,
                timeout=10
            )
            response.raise_for_status()
            self.registered_info = response.json()
            return self.registered_info
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }

    def get_target_url(self, target_nickname: str) -> Optional[Dict]:
        """Retrieve connection information for a target node.

        Args:
            target_nickname: Nickname of the target node

        Returns:
            Optional[Dict]: Contains access_url or None if failed
        """
        try:
            response = requests.get(
                f"{self.routing_service_url}/lookup/{target_nickname}",
                timeout=10
            )
            response.raise_for_status()
            data = response.json()

            access_url = self._generate_access_url(data)

            return {
                "access_url": access_url
            }
        except Exception:
            return None

    def unregister(self, nickname: str) -> bool:
        """Unregister a node from the routing service.

        Args:
            nickname: Nickname of the node to unregister

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            response = requests.post(
                f"{self.routing_service_url}/unregister/{nickname}",
                timeout=10
            )
            response.raise_for_status()
            return True
        except Exception:
            return False

    def list_nodes(self) -> Optional[Dict[str, datetime]]:
        """Retrieve list of all registered nodes and their last access times.

        Returns:
            Optional[Dict]: Node list with last access times or None if failed
        """
        try:
            response = requests.get(
                f"{self.routing_service_url}/list_peers/",
                timeout=10
            )
            response.raise_for_status()
            return response.json()
        except Exception:
            return None

    def _generate_access_url(self, peer_data: Dict[str, Any]) -> str:
        """Generate the appropriate access URL based on network location."""
        target_ip = peer_data.get('ip')
        target_task_id = peer_data.get('task_id')
        target_task_port = peer_data.get('task_port')
        target_host_port = peer_data.get('host_port')
        target_host_id = peer_data.get("host_id")

        if target_host_id == self.host_id:
            return f"pyxis_task_{target_task_id}:{target_task_port}"
        else:
            return f"{target_ip}:{target_host_port}/service/{target_task_id}"
