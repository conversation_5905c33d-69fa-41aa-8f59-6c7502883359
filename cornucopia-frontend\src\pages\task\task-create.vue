<template>
  <div class="task-create-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Plus /></el-icon>
          <h2 class="page-title">创建任务</h2>
        </div>
        <div class="sub-title">在计算节点上执行数据分析和处理任务</div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="goToTaskList">
          <el-icon><List /></el-icon>
          我的任务
        </el-button>
      </div>
    </div>

    <el-card class="task-card" shadow="hover">
      <el-steps :active="activeStep" finish-status="success" simple>
        <el-step title="基本信息" icon="el-icon-edit" />
        <el-step title="选择资源" icon="el-icon-connection" />
        <el-step title="上传代码" icon="el-icon-upload" />
        <el-step title="任务预览" icon="el-icon-view" />
      </el-steps>

      <!-- 步骤1：基本信息 -->
      <div v-if="activeStep === 1" class="step-content">
        <el-form :model="taskForm" :rules="rules" ref="taskFormRef" label-width="120px">
          <el-form-item label="任务名称" prop="name">
            <el-input v-model="taskForm.name" placeholder="请输入任务名称"></el-input>
          </el-form-item>
          <el-form-item label="任务描述" prop="description">
            <el-input 
              v-model="taskForm.description" 
              type="textarea" 
              :rows="4" 
              placeholder="请输入任务描述"
            ></el-input>
          </el-form-item>
          <el-form-item label="保存模型到桶" prop="enableCustomModelPath">
            <el-switch v-model="taskForm.enableCustomModelPath" />
          </el-form-item>
          <el-form-item label="模型存储路径" prop="modelPath" v-if="taskForm.enableCustomModelPath">
            <el-input v-model="taskForm.modelPath" placeholder="请输入模型保存路径"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="nextStep">下一步</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤2：选择资源 -->
      <div v-if="activeStep === 2" class="step-content">
        <div class="resource-filter">
          <el-input
            v-model="resourceSearchKeyword"
            placeholder="搜索资源"
            prefix-icon="el-icon-search"
            clearable
            style="width: 250px; margin-bottom: 15px;"
            @input="filterResources"
          />
          <el-select 
            v-model="resourceTypeFilter" 
            placeholder="按类型筛选" 
            clearable
            style="width: 200px; margin-left: 10px; margin-bottom: 15px;"
            @change="filterResources"
          >
            <el-option label="全部类型" value="" />
            <el-option label="计算资源" value="POWER" />
            <el-option label="数据资源" value="DATASET" />
            <el-option label="模型资源" value="MODEL" />
          </el-select>
        </div>
        
        <el-table
          :data="filteredResources"
          style="width: 100%"
          @selection-change="handleResourceSelectionChange"
          border
          highlight-current-row
          row-key="id"
          ref="resourceTableRef"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="资源名称" min-width="200" />
          <el-table-column prop="resourceType" label="资源类型" width="120">
            <template #default="scope">
              <el-tag :type="getResourceTypeTag(scope.row.resourceType)">
                {{ getResourceTypeName(scope.row.resourceType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="nodeType" label="节点类型" width="100">
            <template #default="scope">
              <el-tag :type="getNodeTypeTag(scope.row.nodeType)" size="small">
                {{ scope.row.nodeType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="nodeId" label="节点信息" min-width="250">
            <template #default="scope">
              <div class="node-info-compact">
                <el-tag size="small" type="info" class="node-id-tag">
                  {{ scope.row.nodeId }}
                </el-tag>
                <span class="node-name">{{ scope.row.nodeName }}</span>
                <span class="node-address">({{ scope.row.nodeIpAddress }}:{{ scope.row.nodePort }})</span>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="resource-summary">
          <div class="summary-item" v-if="selectedPowerResources.length > 0">
            <el-tag type="primary" size="large">已选计算资源: {{ selectedPowerResources.length }} 个</el-tag>
          </div>
          <div class="summary-item" v-if="selectedDatasetResources.length > 0">
            <el-tag type="success" size="large">已选数据资源: {{ selectedDatasetResources.length }} 个</el-tag>
          </div>
          
          <!-- 添加简单的提示信息 -->
          <div class="resource-tip" v-if="selectedDatasetResources.length > 0">
            <el-alert
              title="提示：已选数据资源所在节点的计算资源可以被使用"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="nextStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤3：上传代码 -->
      <div v-if="activeStep === 3" class="step-content">        
        <div v-if="selectedResources.length === 0" class="empty-resource-tip">
          <el-empty description="请先选择资源" />
          <el-button type="primary" @click="prevStep">返回选择资源</el-button>
        </div>
        
        <div v-else>
          <el-tabs v-model="activeResourceTab" type="card">
            <el-tab-pane 
              v-for="resource in selectedResources" 
              :key="resource.id"
              :label="resource.name"
              :name="resource.id.toString()"
            >
              <div class="resource-code-upload">
                <div class="resource-info">
                  <el-tag :type="getResourceTypeTag(resource.resourceType)" class="resource-type-tag">
                    {{ getResourceTypeName(resource.resourceType) }}
                  </el-tag>
                  <span class="resource-node">节点: {{ resource.nodeId }}</span>
                </div>
                
                <!-- 添加MinIO配置信息显示 -->
                <div v-if="hasMinioConfig(resource.id)" class="resource-minio-info">
                  <el-collapse>
                    <el-collapse-item>
                      <template #title>
                        <div class="minio-title">
                          <el-icon><Connection /></el-icon>
                          <span>存储配置信息</span>
                        </div>
                      </template>

                      <template v-if="resource.resourceType === 'DATASET' && resourceCodeConfigs[resource.id].minioConfig.data_endpoint">
                        <el-descriptions title="数据集存储配置" :column="1" border size="small">
                          <el-descriptions-item label="数据端点">{{ resourceCodeConfigs[resource.id].minioConfig.data_endpoint }}</el-descriptions-item>
                          <el-descriptions-item label="数据访问密钥">{{ resourceCodeConfigs[resource.id].minioConfig.data_access_key }}</el-descriptions-item>
                          <el-descriptions-item label="数据密钥">{{ resourceCodeConfigs[resource.id].minioConfig.data_secret_key }}</el-descriptions-item>
                          <el-descriptions-item label="数据存储桶">{{ resourceCodeConfigs[resource.id].minioConfig.data_bucket }}</el-descriptions-item>
                          <el-descriptions-item label="数据路径">{{ resourceCodeConfigs[resource.id].minioConfig.data_path }}</el-descriptions-item>
                        </el-descriptions>
                      </template>

                      <template v-if="resourceCodeConfigs[resource.id].minioConfig.model_endpoint">
                        <el-descriptions title="模型存储配置" :column="1" border size="small" :class="{ 'mt-3': resource.resourceType === 'DATASET' }">
                          <el-descriptions-item label="模型端点">{{ resourceCodeConfigs[resource.id].minioConfig.model_endpoint }}</el-descriptions-item>
                          <el-descriptions-item label="模型访问密钥">{{ resourceCodeConfigs[resource.id].minioConfig.model_access_key }}</el-descriptions-item>
                          <el-descriptions-item label="模型密钥">{{ resourceCodeConfigs[resource.id].minioConfig.model_secret_key }}</el-descriptions-item>
                          <el-descriptions-item label="模型存储桶">{{ resourceCodeConfigs[resource.id].minioConfig.model_bucket }}</el-descriptions-item>
                          <el-descriptions-item label="模型路径">{{ resourceCodeConfigs[resource.id].minioConfig.model_path }}</el-descriptions-item>
                        </el-descriptions>
                      </template>
                    </el-collapse-item>
                  </el-collapse>
                </div>
                
                <!-- 根据节点类型显示不同的上传界面 -->
                <div v-if="resource.nodeType === 'Pyxis'" class="pyxis-upload-section">
                  <!-- Pyxis节点：文件夹上传 -->
                  <div class="upload-section-title">
                    <el-icon><Upload /></el-icon>
                    <span>Pyxis - 上传代码文件夹</span>
                  </div>
                  <el-alert
                    title="请上传完整的代码文件夹，包含所有必要的文件（如 __init__.py等）。__init__.py 中需包含 main 入口函数。"
                    type="info"
                    :closable="false"
                    show-icon
                    style="margin-bottom: 16px;"
                  />
                  
                  <div class="folder-upload-container">
                    <input
                      type="file"
                      :ref="el => resourceCodeConfigs[resource.id].folderInputRef = el"
                      @change="(event) => handleFolderSelect(event, resource.id)"
                      webkitdirectory
                      directory
                      style="display: none"
                    />
                    <el-button type="primary" @click="triggerFolderSelect(resource.id)">
                      选择文件夹
                    </el-button>
                    <div v-if="resourceCodeConfigs[resource.id].selectedFolder" class="selected-folder">
                      已选择文件夹: {{ resourceCodeConfigs[resource.id].selectedFolder }}
                    </div>
                  </div>

                  <!-- 文件列表展示 -->
                  <div v-if="resourceCodeConfigs[resource.id].codeFileList && resourceCodeConfigs[resource.id].codeFileList.length > 0" class="file-list-container">
                    <div class="file-list-header">
                      <span class="file-list-title">文件列表 ({{ resourceCodeConfigs[resource.id].codeFileList.length }} 个文件)</span>
                      <el-button 
                        type="danger" 
                        size="small" 
                        @click="clearFiles(resource.id)"
                      >
                        清空文件
                      </el-button>
                    </div>
                    <el-table :data="resourceCodeConfigs[resource.id].codeFileList.slice(0, 10)" style="width: 100%" border size="small">
                      <el-table-column prop="webkitRelativePath" label="文件路径" min-width="200">
                        <template #default="scope">
                          <span>{{ scope.row.webkitRelativePath }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="size" label="大小" width="100">
                        <template #default="scope">
                          {{ formatFileSize(scope.row.size) }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="type" label="类型" width="80">
                        <template #default="scope">
                          <el-tag size="small" :type="getFileTypeTag(scope.row.name)">
                            {{ getFileExtension(scope.row.name) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                    <div v-if="resourceCodeConfigs[resource.id].codeFileList.length > 10" class="file-list-more">
                      还有 {{ resourceCodeConfigs[resource.id].codeFileList.length - 10 }} 个文件未显示...
                    </div>
                  </div>

                  <!-- __init__.py 内容预览 -->
                  <div v-if="resourceCodeConfigs[resource.id].initContent" class="code-preview">
                    <div class="preview-title">__init__.py 文件内容预览：</div>
                    <pre><code>{{ resourceCodeConfigs[resource.id].initContent }}</code></pre>
                  </div>
                  <el-alert
                    v-if="resourceCodeConfigs[resource.id].initContent && !resourceCodeConfigs[resource.id].hasMain"
                    title="__init__.py 中未检测到 main 函数，请确保包含 main 入口函数！"
                    type="error"
                    :closable="false"
                    show-icon
                    style="margin-top: 16px;"
                  />
                </div>
                
                <div v-else class="sycee-upload-section">
                  <!-- Sycee节点：原来的单文件上传界面 -->
                  <div class="upload-section-title">
                    <el-icon><Upload /></el-icon>
                    <span>Sycee - 上传代码文件</span>
                  </div>
                  <div class="upload-tabs">
                    <el-tabs :model-value="getUploadTab(resource.id)" @update:model-value="updateUploadTab(resource.id, $event)">
                      <el-tab-pane label="手动输入" name="manual">
                        <el-form label-width="120px">
                          <el-form-item label="函数名称" required>
                            <el-input 
                              v-model="resourceCodeConfigs[resource.id].funcName" 
                              placeholder="请输入函数名称"
                              class="upload-input"
                            ></el-input>
                          </el-form-item>
                          <el-form-item label="代码描述">
                            <el-input 
                              v-model="resourceCodeConfigs[resource.id].description"
                              type="textarea"
                              :rows="3"
                              placeholder="请输入代码描述（可选）"
                              class="upload-input"
                            />
                          </el-form-item>
                          <el-form-item label="代码内容" required>
                            <el-input 
                              v-model="resourceCodeConfigs[resource.id].codeContent"
                              type="textarea"
                              :rows="15"
                              placeholder="请输入Python代码"
                              class="code-textarea"
                            />
                          </el-form-item>
                        </el-form>
                      </el-tab-pane>
                      <el-tab-pane label="文件上传" name="file">
                        <div class="file-upload-container">
                          <el-upload
                            class="file-uploader"
                            drag
                            action="#"
                            :auto-upload="false"
                            :on-change="(file) => handleFileChange(file, resource.id)"
                            :limit="1"
                            accept=".py"
                            :file-list="getResourceFileList(resource.id)"
                            :on-remove="(file) => handleRemove(file, resource.id)"
                          >
                            <el-icon class="el-icon--upload"><Upload /></el-icon>
                            <div class="el-upload__text">
                              拖拽文件到此处或 <em>点击上传</em>
                            </div>
                            <template #tip>
                              <div class="el-upload__tip">
                                只能上传 .py 文件
                              </div>
                            </template>
                          </el-upload>
                          
                          <div v-if="resourceCodeConfigs[resource.id].filePreview" class="file-preview">
                            <div class="file-info">
                              <h4>文件预览</h4>
                              <p>函数名称: <strong>{{ resourceCodeConfigs[resource.id].funcName }}</strong></p>
                            </div>
                            <div class="code-preview">
                              <pre><code class="python">{{ resourceCodeConfigs[resource.id].codeContent }}</code></pre>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                    </el-tabs>
                  </div>
                </div>
                
                <div class="upload-actions">
                  <el-button 
                    type="primary" 
                    @click="uploadResourceCode(resource.id)"
                    :disabled="!canUpload(resource.id)"
                  >
                    上传代码
                  </el-button>
                  <el-tag 
                    v-if="resourceCodeConfigs[resource.id].uploaded" 
                    type="success" 
                    class="upload-status"
                  >
                    已上传
                  </el-tag>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
          
          <div class="upload-summary">
            <div class="summary-title">上传状态</div>
            <el-progress 
              :percentage="uploadPercentage" 
              :status="uploadPercentage === 100 ? 'success' : ''"
            ></el-progress>
            <div class="summary-text">
              已上传 {{ uploadedCount }}/{{ selectedResources.length }} 个资源的代码
            </div>
          </div>

          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <!-- <el-button type="primary" @click="uploadAllCodes">批量上传</el-button> -->
            <el-button 
              type="success" 
              @click="nextStep" 
              :disabled="uploadedCount < selectedResources.length"
            >
              下一步
            </el-button>
          </div>
        </div>
      </div>

      <!-- 步骤4：任务预览 -->
      <div v-if="activeStep === 4" class="step-content">
        <!-- 可编辑的任务基本信息 -->
        <el-card class="task-info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">任务基本信息</span>
              <el-button type="text" size="small" @click="toggleEditMode">
                <el-icon><Edit /></el-icon>
                {{ editMode ? '保存' : '编辑' }}
              </el-button>
            </div>
          </template>

          <el-form :model="taskForm" :rules="rules" ref="previewTaskFormRef" label-width="120px" v-if="editMode">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="taskForm.name" placeholder="请输入任务名称"></el-input>
            </el-form-item>
            <el-form-item label="任务描述" prop="description">
              <el-input
                v-model="taskForm.description"
                type="textarea"
                :rows="4"
                placeholder="请输入任务描述"
              ></el-input>
            </el-form-item>
            <el-form-item label="保存模型到桶" prop="enableCustomModelPath">
              <el-switch v-model="taskForm.enableCustomModelPath" />
            </el-form-item>
            <el-form-item label="模型存储路径" prop="modelPath" v-if="taskForm.enableCustomModelPath">
              <el-input v-model="taskForm.modelPath" placeholder="请输入模型保存路径"></el-input>
            </el-form-item>
          </el-form>

          <el-descriptions v-else :column="1" border>
            <el-descriptions-item label="任务名称">{{ taskForm.name }}</el-descriptions-item>
            <el-descriptions-item label="任务描述">{{ taskForm.description }}</el-descriptions-item>
            <el-descriptions-item label="模型存储路径" v-if="taskForm.enableCustomModelPath">{{ taskForm.modelPath }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 选择的资源信息 -->
        <el-card class="resource-info-card" shadow="never">
          <template #header>
            <span class="card-title">选择的资源</span>
          </template>

          <div v-if="selectedResources.length > 0" class="resource-list">
            <div v-for="resource in selectedResources" :key="resource.id" class="resource-preview-item">
              <div class="resource-header">
                <el-tag :type="getResourceTypeTag(resource.resourceType)" class="resource-tag">
                  {{ getResourceTypeName(resource.resourceType) }}: {{ resource.name }}
                </el-tag>
                <el-tag :type="getNodeTypeTag(resource.nodeType)" size="small" class="node-type-tag">
                  {{ resource.nodeType }}
                </el-tag>
              </div>

              <div class="resource-details">
                <div class="detail-item">
                  <span class="detail-label">节点信息:</span>
                  <span class="detail-value">
                    {{ resource.nodeName }} ({{ resource.nodeIpAddress }}:{{ resource.nodePort }})
                  </span>
                </div>

                <div class="detail-item">
                  <span class="detail-label">代码配置:</span>
                  <span class="detail-value">
                    <template v-if="resource.nodeType === 'Pyxis'">
                      文件夹: {{ resourceCodeConfigs[resource.id]?.selectedFolder || '未上传' }}
                      ({{ resourceCodeConfigs[resource.id]?.codeFileList?.length || 0 }} 个文件)
                    </template>
                    <template v-else>
                      函数: {{ resourceCodeConfigs[resource.id]?.funcName || '未上传' }}
                    </template>
                  </span>
                </div>

                <div v-if="resource.description" class="detail-item">
                  <span class="detail-label">描述:</span>
                  <span class="detail-value">{{ resource.description }}</span>
                </div>
              </div>

              <!-- 显示资源的Minio配置信息 -->
              <div v-if="hasMinioConfig(resource.id)" class="resource-minio-config">
                <el-collapse>
                  <el-collapse-item title="存储配置信息">
                    <div class="minio-config-details">
                      <template v-if="resource.resourceType === 'DATASET' && resourceCodeConfigs[resource.id].minioConfig.data_endpoint">
                        <div class="config-item">
                          <span class="config-label">数据端点:</span>
                          <span class="config-value">{{ resourceCodeConfigs[resource.id].minioConfig.data_endpoint }}</span>
                        </div>
                        <div class="config-item">
                          <span class="config-label">数据访问密钥:</span>
                          <span class="config-value">{{ resourceCodeConfigs[resource.id].minioConfig.data_access_key }}</span>
                        </div>
                        <div class="config-item">
                          <span class="config-label">数据密钥:</span>
                          <span class="config-value">{{ resourceCodeConfigs[resource.id].minioConfig.data_secret_key }}</span>
                        </div>
                        <div class="config-item">
                          <span class="config-label">数据存储桶:</span>
                          <span class="config-value">{{ resourceCodeConfigs[resource.id].minioConfig.data_bucket }}</span>
                        </div>
                        <div class="config-item">
                          <span class="config-label">数据路径:</span>
                          <span class="config-value">{{ resourceCodeConfigs[resource.id].minioConfig.data_path }}</span>
                        </div>
                      </template>

                      <template v-if="resourceCodeConfigs[resource.id].minioConfig.model_endpoint">
                        <div class="config-item">
                          <span class="config-label">模型端点:</span>
                          <span class="config-value">{{ resourceCodeConfigs[resource.id].minioConfig.model_endpoint }}</span>
                        </div>
                        <div class="config-item">
                          <span class="config-label">模型访问密钥:</span>
                          <span class="config-value">{{ resourceCodeConfigs[resource.id].minioConfig.model_access_key }}</span>
                        </div>
                        <div class="config-item">
                          <span class="config-label">模型密钥:</span>
                          <span class="config-value">{{ resourceCodeConfigs[resource.id].minioConfig.model_secret_key }}</span>
                        </div>
                        <div class="config-item">
                          <span class="config-label">模型存储桶:</span>
                          <span class="config-value">{{ resourceCodeConfigs[resource.id].minioConfig.model_bucket }}</span>
                        </div>
                        <div class="config-item">
                          <span class="config-label">模型路径:</span>
                          <span class="config-value">{{ resourceCodeConfigs[resource.id].minioConfig.model_path }}</span>
                        </div>
                      </template>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </div>
          <div v-else class="empty-resource-message">
            <el-empty description="未选择资源" />
          </div>
        </el-card>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="createTask" :loading="submitting">创建任务</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Connection, Upload, Edit, List } from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'

const router = useRouter()
const activeStep = ref(1)
const taskFormRef = ref(null)
const previewTaskFormRef = ref(null)
const submitting = ref(false)
const resourceSearchKeyword = ref('')
const resourceTypeFilter = ref('')
const activeResourceTab = ref('')
const availableResources = ref([])
const selectedResources = ref([])
const resourceCodeConfigs = reactive({})
const editMode = ref(false)

// 表单数据
const taskForm = reactive({
  name: '',
  description: '',
  enableCustomModelPath: false,
  modelPath: '',
  nodeId: null,
  nodeResourceId: null,
  datasetIds: [],
  datasetResourceIds: [],
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入任务描述', trigger: 'blur' }
  ],
  modelPath: [
    { required: false, message: '请输入模型保存路径', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (taskForm.enableCustomModelPath && !value) {
          callback(new Error('启用自定义路径时必须填写路径名称'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const filteredResources = computed(() => {
  let result = availableResources.value
  
  // 按关键词筛选
  if (resourceSearchKeyword.value) {
    const keyword = resourceSearchKeyword.value.toLowerCase()
    result = result.filter(resource => 
      resource.name.toLowerCase().includes(keyword) || 
      (resource.description && resource.description.toLowerCase().includes(keyword))
    )
  }
  
  // 按类型筛选
  if (resourceTypeFilter.value) {
    result = result.filter(resource => resource.resourceType === resourceTypeFilter.value)
  }
  
  return result
})

const selectedPowerResources = computed(() => {
  return selectedResources.value.filter(res => res.resourceType === 'POWER')
})

const selectedDatasetResources = computed(() => {
  return selectedResources.value.filter(res => res.resourceType === 'DATASET')
})

const uploadedCount = computed(() => {
  return selectedResources.value.filter(res => 
    resourceCodeConfigs[res.id] && resourceCodeConfigs[res.id].uploaded
  ).length
})

const uploadPercentage = computed(() => {
  if (selectedResources.value.length === 0) return 0
  return Math.round((uploadedCount.value / selectedResources.value.length) * 100)
})

// 定义状态存储的变量（内存变量，不再使用localStorage）
const savedState = reactive({
  step1: {
    taskForm: {
      name: '',
      description: '',
      enableCustomModelPath: false,
      modelPath: '',
    }
  },
  step2: {
    selectedResources: []
  }
});

// 从内存变量恢复状态的函数
const restoreState = () => {
  // 只有当activeStep <= 2时才恢复状态
  if (activeStep.value <= 2) {
    // 恢复第一步的状态
    if (savedState.step1.taskForm.name) {
      taskForm.name = savedState.step1.taskForm.name;
      taskForm.description = savedState.step1.taskForm.description;
      taskForm.enableCustomModelPath = savedState.step1.taskForm.enableCustomModelPath;
      taskForm.modelPath = savedState.step1.taskForm.modelPath;
    }
    
    // 恢复第二步的状态 - 只恢复选中的资源，不恢复筛选条件
    if (activeStep.value === 2) {
      selectedResources.value = savedState.step2.selectedResources;
    }
  }
}

// 保存状态到内存变量
const saveState = () => {
  // 只保存到第二步
  if (activeStep.value === 1) {
    // 保存第一步的状态
    savedState.step1.taskForm.name = taskForm.name;
    savedState.step1.taskForm.description = taskForm.description;
    savedState.step1.taskForm.enableCustomModelPath = taskForm.enableCustomModelPath;
    savedState.step1.taskForm.modelPath = taskForm.modelPath;
  } else if (activeStep.value === 2) {
    // 保存第二步的状态 - 只保存选中的资源，不保存筛选条件
    savedState.step2.selectedResources = [...selectedResources.value];
  }
}

// 清除状态的函数
const clearState = () => {
  // 清空内存变量中的状态
  savedState.step1.taskForm.name = '';
  savedState.step1.taskForm.description = '';
  savedState.step1.taskForm.enableCustomModelPath = false;
  savedState.step1.taskForm.modelPath = '';
  savedState.step2.selectedResources = [];
  
  // 重置所有状态到初始值
  activeStep.value = 1;
  taskForm.name = '';
  taskForm.description = '';
  taskForm.nodeId = null;
  taskForm.nodeResourceId = null;
  taskForm.datasetIds = [];
  taskForm.datasetResourceIds = [];
  taskForm.enableCustomModelPath = false;
  taskForm.modelPath = '';
  selectedResources.value = [];
  activeResourceTab.value = '';
  
  // 清空资源代码配置
  Object.keys(resourceCodeConfigs).forEach(key => {
    delete resourceCodeConfigs[key];
  });
}

// 监听状态变化（只监听第一步和第二步）
watch(
  [
    () => activeStep.value,
    () => taskForm.name,
    () => taskForm.description,
    () => taskForm.enableCustomModelPath,
    () => taskForm.modelPath,
    () => selectedResources.value
  ],
  () => {
    // 只保存第一步和第二步的状态
    if (activeStep.value <= 2) {
      saveState();
    }
  },
  { deep: true }
);

// 在组件挂载时恢复状态
onMounted(() => {
  restoreState();
});

// 获取用户可用的资源
onMounted(async () => {
  await loadAvailableResources()
  
  // 资源加载完成后延时确保表格已经渲染完成再恢复选择状态
  setTimeout(() => {
    restoreTableSelection()
  }, 200)
})

// 加载可用资源
const loadAvailableResources = async () => {
  try {
    // 获取用户所有资源
    const resourceRes = await service.get('/api/v1.0/sys/user/resources', {
      params: { size: 1000 }
    });
    
    if (resourceRes.code === 10000) {
      availableResources.value = resourceRes.data.resources || [];
      console.log('加载的资源:', availableResources.value);
      
      // 资源加载完成后恢复选中状态
      setTimeout(() => {
        if (resourceTableRef.value && availableResources.value.length > 0) {
          restoreTableSelection();
          
          // 再次延时执行强制恢复所有类型资源
          setTimeout(() => {
            forceRestoreAllResources();
          }, 500);
        }
      }, 300);
    }
  } catch (error) {
    console.error('加载资源失败:', error);
    toast('错误', '加载可用资源失败', 'error');
  }
};

// 筛选资源
const filterResources = () => {
  // 当筛选条件变化时，可以添加额外逻辑
}

// 处理资源选择变化
const handleResourceSelectionChange = (selection) => {
  selectedResources.value = selection;
  
  // 初始化新选择的资源的代码配置
  selection.forEach(resource => {
    if (!resourceCodeConfigs[resource.id]) {
      resourceCodeConfigs[resource.id] = {
        codeFile: null,
        codeFileName: '',
        codeContent: '',
        funcName: '',
        description: '',
        fileList: [],
        uploaded: false,
        uploadTab: 'manual',
        filePreview: false,
        codeId: null,
        syceeCodeId: null,
        minioConfig: null
      }
    }
  });
  
  // 如果有选择的资源，设置第一个为活动标签
  if (selection.length > 0 && !activeResourceTab.value) {
    activeResourceTab.value = selection[0].id.toString();
  }
  
  // 提取节点资源
  const powerResource = selection.find(res => res.resourceType === 'POWER');
  if (powerResource) {
    taskForm.nodeId = powerResource.nodeId;
    taskForm.nodeResourceId = powerResource.id;
  } else {
    taskForm.nodeId = null;
    taskForm.nodeResourceId = null;
  }
  
  // 提取数据集资源
  const datasetResources = selection.filter(res => res.resourceType === 'DATASET');
  taskForm.datasetIds = datasetResources.map(res => res.resourceId);
  taskForm.datasetResourceIds = datasetResources.map(res => res.id);
};

// 获取上传标签页
const getUploadTab = (resourceId) => {
  if (!resourceCodeConfigs[resourceId]) {
    initResourceCodeConfig(resourceId);
  }
  return resourceCodeConfigs[resourceId].uploadTab || 'file';
};

// 初始化资源代码配置
const initResourceCodeConfig = (resourceId) => {
  if (!resourceCodeConfigs[resourceId]) {
    resourceCodeConfigs[resourceId] = {
      // 通用配置
      codeFile: null,
      codeFileName: '',
      codeContent: '',
      funcName: '',
      description: '',
      fileList: [],
      uploaded: false,
      uploadTab: 'manual',
      filePreview: false,
      codeId: null,
      syceeCodeId: null,
      minioConfig: null,
      
      // Pyxis专用配置
      selectedFolder: '',
      codeFileList: [],
      initContent: '',
      hasMain: false,
      folderInputRef: null,
      deployResult: null
    };
  }
};

// 文件夹选择相关方法
const triggerFolderSelect = (resourceId) => {
  if (resourceCodeConfigs[resourceId]?.folderInputRef) {
    resourceCodeConfigs[resourceId].folderInputRef.click();
  }
};

const handleFolderSelect = (event, resourceId) => {
  const files = event.target.files;
  if (files.length > 0) {
    resourceCodeConfigs[resourceId].selectedFolder = files[0].webkitRelativePath.split('/')[0];
    resourceCodeConfigs[resourceId].codeFileList = Array.from(files);
    
    // 优先查找根目录下的 __init__.py
    const initFile = Array.from(files).find(f => {
      const path = f.webkitRelativePath;
      // 检查是否在根目录下（路径中只有一个斜杠）
      return path.split('/').length === 2 && f.name === '__init__.py';
    });
    
    if (initFile) {
      const reader = new FileReader();
      reader.onload = (e) => {
        resourceCodeConfigs[resourceId].initContent = e.target.result;
        resourceCodeConfigs[resourceId].hasMain = /def\s+main\s*\(/.test(resourceCodeConfigs[resourceId].initContent);
      };
      reader.readAsText(initFile);
    } else {
      resourceCodeConfigs[resourceId].initContent = '';
      resourceCodeConfigs[resourceId].hasMain = false;
    }
  }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取文件扩展名
const getFileExtension = (filename) => {
  const ext = filename.split('.').pop().toLowerCase();
  return ext ? `.${ext}` : '无扩展名';
};

// 获取文件类型标签样式
const getFileTypeTag = (filename) => {
  const ext = filename.split('.').pop().toLowerCase();
  const typeMap = {
    'py': 'success',
    'jinja2': 'warning',
    'json': 'info',
    'pyc': 'info',
    'md': '',
    'txt': ''
  };
  return typeMap[ext] || 'info';
};

// 清空文件
const clearFiles = (resourceId) => {
  resourceCodeConfigs[resourceId].codeFileList = [];
  resourceCodeConfigs[resourceId].initContent = '';
  resourceCodeConfigs[resourceId].hasMain = false;
  resourceCodeConfigs[resourceId].selectedFolder = '';
};

// 更新上传标签页
const updateUploadTab = (resourceId, newTab) => {
  resourceCodeConfigs[resourceId].uploadTab = newTab;
};

// 获取资源的文件列表
const getResourceFileList = (resourceId) => {
  if (!resourceCodeConfigs[resourceId] || !resourceCodeConfigs[resourceId].fileList) {
    return [];
  }
  return resourceCodeConfigs[resourceId].fileList;
};

// 处理文件变化
const handleFileChange = (file, resourceId) => {
  if (!resourceCodeConfigs[resourceId]) {
    initResourceCodeConfig(resourceId);
  }
  
  const config = resourceCodeConfigs[resourceId];
  
  // 检查文件类型
  const isPython = file.raw.type === 'text/x-python' || file.name.endsWith('.py');
  
  if (!isPython) {
    toast('错误', '请上传Python文件', 'error');
    return;
  }
  
  const reader = new FileReader();
  reader.onload = (e) => {
    const content = e.target.result;
    config.codeContent = content;
    
    // 解析函数名
    try {
      const funcMatch = content.match(/def\s+([a-zA-Z0-9_]+)\s*\(/);
      if (funcMatch && funcMatch[1]) {
        config.funcName = funcMatch[1];
      } else {
        toast('警告', '无法从文件中解析函数名，请检查文件格式', 'warning');
      }
    } catch (error) {
      console.error('解析文件失败:', error);
      toast('错误', '解析文件失败', 'error');
    }
  };
  
  reader.readAsText(file.raw);
  
  config.codeFile = file.raw;
  config.codeFileName = file.name;
  config.fileList = [{ name: file.name, url: '' }];
  config.filePreview = true;
};

// 处理文件移除
const handleRemove = (file, resourceId) => {
  if (resourceCodeConfigs[resourceId]) {
    resourceCodeConfigs[resourceId].codeFile = null;
    resourceCodeConfigs[resourceId].codeFileName = '';
    resourceCodeConfigs[resourceId].fileList = [];
    resourceCodeConfigs[resourceId].filePreview = false;
  }
};

// 获取资源的Minio配置
const getResourceMinioConfig = async (resourceId, modelPath) => {
  try {
    const res = await service.get('/api/v1.0/sys/resource/minio-config', {
      params: {
        resourceId,
        modelPath: modelPath || undefined
      }
    });
    
    if (res.code === 10000) {
      return res.data;
    } else {
      toast('错误', res.message || '获取Minio配置失败', 'error');
      return null;
    }
  } catch (error) {
    console.error('获取Minio配置失败:', error);
    toast('错误', '获取Minio配置失败', 'error');
    return null;
  }
};

// 检查是否可以上传
const canUpload = (resourceId) => {
  if (!resourceCodeConfigs[resourceId]) {
    return false;
  }
  
  const config = resourceCodeConfigs[resourceId];
  const resource = selectedResources.value.find(res => res.id === resourceId);
  
  if (!resource) {
    return false;
  }
  
  // 根据节点类型进行不同的验证
  if (resource.nodeType === 'Pyxis') {
    // Pyxis节点：需要有文件夹和包含main函数的__init__.py
    return config.codeFileList && config.codeFileList.length > 0 && config.hasMain;
  } else {
    // Sycee节点：原来的验证逻辑
    if (config.uploadTab === 'file') {
      // 文件上传模式：需要有文件和函数名
      return config.codeFile && config.funcName;
    } else {
      // 手动输入模式：需要有代码内容和函数名
      return config.codeContent && config.funcName;
    }
  }
};

// 上传资源代码
const uploadResourceCode = async (resourceId) => {
  if (!canUpload(resourceId)) {
    toast('警告', '请先选择代码文件或输入代码内容', 'warning');
    return;
  }
  
  try {
    const config = resourceCodeConfigs[resourceId];
    const resource = selectedResources.value.find(res => res.id === resourceId);
    
    if (!resource) {
      toast('错误', '找不到对应的资源信息', 'error');
      return;
    }
    
    if (resource.nodeType === 'Pyxis') {
      // Pyxis节点：上传文件夹
      await uploadPyxisFolder(resourceId, resource, config);
    } else {
      // Sycee节点：原来的单文件上传逻辑
      await uploadSyceeCode(resourceId, resource, config);
    }
  } catch (error) {
    console.error('上传代码失败:', error);
    toast('错误', error.response?.data?.message || '上传代码失败', 'error');
  }
};

// Pyxis节点文件夹上传
const uploadPyxisFolder = async (resourceId, resource, config) => {
  // 创建 FormData 对象
  const formData = new FormData();
  
  // 添加文件
  for (const file of config.codeFileList) {
    const relativePath = file.webkitRelativePath.split('/').slice(1).join('/');
    const newFile = new File([file], relativePath, {
      type: file.type,
      lastModified: file.lastModified
    });
    formData.append('files', newFile);
  }
  
  // 添加节点ID和文件夹名称
  formData.append('nodeId', resource.nodeId);
  formData.append('folder_name', config.selectedFolder);

  // 调用应用部署接口
  const response = await service.post('/api/v1.0/sys/code/uploadfile', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });

  if (response.code === 10000) {
    config.deployResult = response.data;
    config.uploaded = true;
    config.codeId = response.data.id;
    toast('成功', 'Pyxis代码上传成功', 'success');
  } else {
    toast('错误', response.message || 'Pyxis代码上传失败', 'error');
  }
};

// Sycee节点代码上传
const uploadSyceeCode = async (resourceId, resource, config) => {
  // 构建上传数据
  const uploadData = {
    nodeId: resource.nodeId,
    resourceId: resource.id,
    funcName: config.funcName,
    codeContent: config.codeContent,
    description: config.description || ''
  };
  
  // 如果启用了自定义模型路径，则添加到请求数据中
  if (taskForm.enableCustomModelPath && taskForm.modelPath) {
    uploadData.customModelPath = taskForm.modelPath;
  }
  
  // 如果有Minio配置，也添加到请求中
  if (config.minioConfig) {
    if (resource.resourceType === 'DATASET' && config.minioConfig.data_endpoint) {
      uploadData.dataEndpoint = config.minioConfig.data_endpoint;
      uploadData.dataAccessKey = config.minioConfig.data_access_key;
      uploadData.dataSecretKey = config.minioConfig.data_secret_key;
      uploadData.dataBucket = config.minioConfig.data_bucket;
      uploadData.dataPath = config.minioConfig.data_path;
    }
    
    if (config.minioConfig.model_endpoint) {
      uploadData.modelEndpoint = config.minioConfig.model_endpoint;
      uploadData.modelAccessKey = config.minioConfig.model_access_key;
      uploadData.modelSecretKey = config.minioConfig.model_secret_key;
      uploadData.modelBucket = config.minioConfig.model_bucket;
      uploadData.modelPath = config.minioConfig.model_path;
    }
  }
  
  // 调用后端接口上传代码
  const res = await service.post('/api/v1.0/sys/code/upload', uploadData);
  
  if (res.code === 10000) {
    // 保存返回的代码ID
    config.codeId = res.data.id;
    config.syceeCodeId = res.data.syceeCodeId;
    config.uploaded = true;
    toast('成功', '代码上传成功', 'success');
  } else {
    toast('错误', res.message || '代码上传失败', 'error');
  }
};

// // 批量上传所有资源的代码
// const uploadAllCodes = async () => {
//   for (const resource of selectedResources.value) {
//     if (!resourceCodeConfigs[resource.id]?.uploaded && canUpload(resource.id)) {
//       await uploadResourceCode(resource.id);
//     }
//   }
// };

// 下一步
const nextStep = async () => {
  if (activeStep.value === 1) {
    // 验证表单
    if (!taskFormRef.value) return
    
    await taskFormRef.value.validate((valid) => {
      if (valid) {
        activeStep.value++
        // 延时执行以确保表格已经渲染完成后再恢复选中状态
        setTimeout(() => {
          if (resourceTableRef.value) {
            restoreTableSelection()
            // 再次延时执行强制恢复所有类型资源
            setTimeout(() => {
              forceRestoreAllResources()
            }, 500)
          }
        }, 300)
      }
    })
  } else if (activeStep.value === 2) {
    // 验证是否选择了资源
    if (selectedResources.value.length === 0) {
      toast('警告', '请选择至少一个资源', 'warning')
      return
    }
    
    // 获取每个资源的Minio配置并保存
    for (const resource of selectedResources.value) {
      const minioConfig = await getResourceMinioConfig(
        resource.id, 
        taskForm.enableCustomModelPath ? taskForm.modelPath : null
      );
      
      if (minioConfig) {
        // 确保资源的代码配置对象已初始化
        if (!resourceCodeConfigs[resource.id]) {
          initResourceCodeConfig(resource.id);
        }
        
        // 保存Minio配置到资源的代码配置中
        resourceCodeConfigs[resource.id].minioConfig = minioConfig;
      }
    }
    
    activeStep.value++
  } else if (activeStep.value === 3) {
    // 验证是否所有资源都上传了代码
    if (uploadedCount.value < selectedResources.value.length) {
      toast('警告', '请为所有选择的资源上传代码', 'warning')
      return
    }
    
    activeStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (activeStep.value > 1) {
    const previousStep = activeStep.value;
    activeStep.value--;
    
    // 如果回到第二步，延时执行恢复表格选中状态
    if (previousStep === 3 && activeStep.value === 2) {
      // 延长等待时间，确保DOM已完全更新
      setTimeout(() => {
        if (resourceTableRef.value) {
          restoreTableSelection();
          // 再次延时执行强制恢复所有类型资源
          setTimeout(() => {
            forceRestoreAllResources();
          }, 500);
        }
      }, 300);
    }
  }
}

// 创建任务
const createTask = async () => {
  submitting.value = true;
  
  try {
    // 验证必要条件
    if (selectedResources.value.length === 0) {
      toast('警告', '请选择至少一个资源', 'warning');
      submitting.value = false;
      return;
    }
    
    if (uploadedCount.value < selectedResources.value.length) {
      toast('警告', '请为所有选择的资源上传代码', 'warning');
      submitting.value = false;
      return;
    }
    
    // 统一创建任务，不再区分Sycee和Pyxis
    const taskData = {
      name: taskForm.name,
      description: taskForm.description,
      codeIds: selectedResources.value
        .filter(res => resourceCodeConfigs[res.id] && resourceCodeConfigs[res.id].codeId)
        .map(res => resourceCodeConfigs[res.id].codeId)
    };
    
    // 如果启用了自定义模型路径，则添加到请求数据中
    if (taskForm.enableCustomModelPath && taskForm.modelPath) {
      taskData.customModelPath = taskForm.modelPath;
    }
    
    // 发送创建任务请求
    const res = await service.post('/api/v1.0/sys/task', taskData);
    
    if (res.code !== 10000) {
      throw new Error(res.message || '创建任务失败');
    }
    
    console.log('任务创建成功:', res.data);
    
    clearState(); // 成功创建后清除状态
    toast('成功', '任务创建成功', 'success');
    
    // 跳转到任务列表页
    router.push({
      path: '/task/my',
      query: { newTask: Date.now() } // 使用时间戳作为标识
    });
    
  } catch (error) {
    console.error('创建任务失败:', error);
    toast('错误', error.response?.data?.message || '创建任务失败', 'error');
  } finally {
    submitting.value = false;
  }
};

// 获取资源类型名称
const getResourceTypeName = (type) => {
  const typeMap = {
    'POWER': '计算节点',
    'DATASET': '数据集',
    'MODEL': '模型',
    'CODE': '代码'
  };
  return typeMap[type] || type;
};

// 获取资源类型标签样式
const getResourceTypeTag = (type) => {
  const typeMap = {
    'POWER': 'primary',
    'DATASET': 'success',
    'MODEL': 'warning',
    'CODE': 'info'
  };
  return typeMap[type] || '';
};

// 获取节点类型标签样式
const getNodeTypeTag = (nodeType) => {
  const typeMap = {
    'Sycee': 'primary',
    'Pyxis': 'warning'
  };
  return typeMap[nodeType] || 'info';
};

// 检查是否有存储配置信息
const hasMinioConfig = (resourceId) => {
  const config = resourceCodeConfigs[resourceId];
  if (!config || !config.minioConfig) {
    return false;
  }

  // 检查是否有任何有效的配置信息
  const minioConfig = config.minioConfig;
  const hasDataConfig = minioConfig.data_endpoint && minioConfig.data_access_key && minioConfig.data_secret_key && minioConfig.data_bucket;
  const hasModelConfig = minioConfig.model_endpoint && minioConfig.model_access_key && minioConfig.model_secret_key && minioConfig.model_bucket;

  return hasDataConfig || hasModelConfig;
};

// 切换编辑模式
const toggleEditMode = async () => {
  if (editMode.value) {
    // 如果当前是编辑模式，点击保存时需要验证表单
    if (!previewTaskFormRef.value) return;

    try {
      await previewTaskFormRef.value.validate();
      editMode.value = false;
      toast('成功', '任务信息已保存', 'success');
    } catch (error) {
      console.error('表单验证失败:', error);
      toast('警告', '请检查表单信息', 'warning');
    }
  } else {
    // 切换到编辑模式
    editMode.value = true;
  }
};

// 添加跳转到任务列表的方法
const goToTaskList = () => {
  router.push('/task/my')
}

// 添加恢复表格选中状态的方法
const resourceTableRef = ref(null)

// 恢复表格的选中状态
const restoreTableSelection = () => {
  if (resourceTableRef.value && selectedResources.value.length > 0 && filteredResources.value.length > 0) {
    try {
      // 记录需要选中的资源ID
      const resourcesNeedSelect = selectedResources.value.map(res => res.id);
      
      // 临时清除筛选条件以确保所有资源都可见
      const tempResourceTypeFilter = resourceTypeFilter.value;
      const tempSearchKeyword = resourceSearchKeyword.value;
      
      // 暂时清除筛选以查看所有资源
      resourceTypeFilter.value = '';
      resourceSearchKeyword.value = '';
      
      // 确保表格刷新后再选中
      setTimeout(() => {
        // 清除当前选择
        resourceTableRef.value.clearSelection();
        
        // 遍历所有资源
        filteredResources.value.forEach(row => {
          if (row && resourceTableRef.value && resourcesNeedSelect.includes(row.id)) {
            resourceTableRef.value.toggleRowSelection(row, true);
          }
        });
        
        // 检查是否所有需要选择的资源都已选中
        const selectedIds = resourceTableRef.value.getSelectionRows().map(row => row.id);
        const missingIds = resourcesNeedSelect.filter(id => !selectedIds.includes(id));
        
        if (missingIds.length > 0) {
          console.log('有未能选中的资源:', missingIds);
        }
        
        // 恢复原筛选条件
        resourceTypeFilter.value = tempResourceTypeFilter;
        resourceSearchKeyword.value = tempSearchKeyword;
      }, 100);
    } catch (error) {
      console.error('恢复表格选中状态时出错:', error);
    }
  }
}

// 当筛选条件变化时，确保刷新表格选中状态
watch([resourceSearchKeyword, resourceTypeFilter], () => {
  // 延时执行以确保表格数据已更新
  setTimeout(() => {
    restoreTableSelection()
  }, 50)
})

// 添加一个强制恢复所有类型资源的函数
const forceRestoreAllResources = () => {
  // 如果没有需要恢复的资源或者表格不存在，直接返回
  if (!resourceTableRef.value || !selectedResources.value || selectedResources.value.length === 0) {
    return;
  }
  
  try {
    // 首先检查是否所有类型的资源都正确恢复了
    const currentSelected = resourceTableRef.value.getSelectionRows() || [];
    const currentSelectedIds = currentSelected.map(row => row.id);
    const expectedIds = selectedResources.value.map(res => res.id);
    
    // 如果已经恢复了全部资源，不需要进行处理
    if (expectedIds.length === currentSelectedIds.length && 
        expectedIds.every(id => currentSelectedIds.includes(id))) {
      return;
    }
    
    console.log('需要强制恢复选中状态，当前选中:', currentSelectedIds.length, '期望选中:', expectedIds.length);
    
    // 清空选择
    resourceTableRef.value.clearSelection();
    
    // 清除所有筛选条件，使所有资源可见
    const tempFilter = resourceTypeFilter.value;
    const tempSearch = resourceSearchKeyword.value;
    resourceTypeFilter.value = '';
    resourceSearchKeyword.value = '';
    
    // 等待表格重新渲染
    setTimeout(() => {
      // 遍历所有可用资源，手动设置选中状态
      availableResources.value.forEach(resource => {
        if (expectedIds.includes(resource.id)) {
          resourceTableRef.value.toggleRowSelection(resource, true);
        }
      });
      
      // 检查结果
      setTimeout(() => {
        const finalSelected = resourceTableRef.value.getSelectionRows() || [];
        console.log('强制恢复后选中:', finalSelected.length, '个资源');
        
        // 恢复筛选条件
        resourceTypeFilter.value = tempFilter;
        resourceSearchKeyword.value = tempSearch;
      }, 100);
    }, 100);
  } catch (error) {
    console.error('强制恢复资源选择状态失败:', error);
  }
};
</script>

<style scoped>
.task-create-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.title-icon {
  margin-right: 10px;
  font-size: 24px;
  color: #409EFF;
}

.page-title {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #303133;
}

.sub-title {
  color: #909399;
  font-size: 14px;
}

.task-card {
  margin-bottom: 20px;
}

.step-content {
  margin-top: 30px;
}

.step-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.resource-filter {
  display: flex;
  margin-bottom: 15px;
}

.resource-summary {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.summary-item {
  margin-bottom: 10px;
}

.resource-code-upload {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.resource-info {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.resource-type-tag {
  margin-right: 10px;
}

.resource-node {
  color: #909399;
  font-size: 14px;
}

.upload-status {
  margin-left: 10px;
}

.upload-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.summary-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.summary-text {
  margin-top: 10px;
  color: #606266;
}

.empty-resource-tip {
  text-align: center;
  padding: 30px 0;
}

.resource-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.resource-preview-item {
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  margin-bottom: 12px;
  background-color: var(--el-bg-color-overlay);
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.resource-tag {
  font-weight: 500;
}

.node-type-tag {
  margin-left: 8px;
}

.resource-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.detail-label {
  font-weight: 500;
  color: var(--el-text-color-primary);
  min-width: 80px;
  flex-shrink: 0;
}

.detail-value {
  color: var(--el-text-color-regular);
  flex: 1;
  word-break: break-all;
}

.code-uploader {
  width: 100%;
}

.upload-tabs {
  margin-bottom: 15px;
}

.file-upload-container {
  padding: 20px 0;
}

.file-uploader {
  width: 100%;
}

.file-uploader :deep(.el-upload) {
  width: 100%;
}

.file-uploader :deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.file-uploader :deep(.el-upload-dragger .el-icon--upload) {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.file-uploader :deep(.el-upload-dragger .el-upload__text) {
  font-size: 16px;
  color: var(--el-text-color-regular);
}

.file-uploader :deep(.el-upload-dragger .el-upload__text em) {
  color: var(--el-color-primary);
  font-style: normal;
  text-decoration: underline;
  cursor: pointer;
}

.file-uploader :deep(.el-upload__tip) {
  text-align: center;
  margin-top: 8px;
  color: var(--el-text-color-secondary);
}

.file-preview {
  margin-top: 24px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}

.file-preview .file-info {
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid var(--el-border-color);
}

.file-preview .file-info h4 {
  margin: 0 0 8px;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.file-preview .file-info p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.upload-input {
  width: 100%;
}

.upload-input :deep(.el-input__wrapper) {
  padding: 1px 15px;
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
}

.upload-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
}

.upload-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

.code-textarea {
  width: 100%;
}

.code-textarea :deep(.el-textarea__inner) {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 12px;
}

.upload-actions {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  align-items: center;
}

.upload-status {
  margin-left: 10px;
}

.file-form {
  padding: 12px;
}

/* 添加代码预览样式 */
.code-preview {
  max-height: 300px;
  overflow: auto;
  background-color: #f8f9fa;
}

.code-preview pre {
  margin: 0;
  padding: 16px;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
}

.resource-tip {
  margin-top: 10px;
  width: 100%;
}

.resource-minio-config {
  margin-top: 10px;
  margin-bottom: 15px;
}

.minio-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #409EFF;
}

.minio-title .el-icon {
  margin-right: 8px;
}

.mt-3 {
  margin-top: 12px;
}

/* 重写描述组件样式 */
.resource-minio-info :deep(.el-descriptions__title) {
  font-size: 14px;
  margin-bottom: 8px;
  color: #606266;
}

.resource-minio-info :deep(.el-descriptions__body) {
  background-color: #fafafa;
}

.resource-minio-info :deep(.el-descriptions__label) {
  width: 120px;
  font-weight: 600;
}

.resource-minio-info :deep(.el-descriptions__content) {
  font-family: monospace;
  word-break: break-all;
}

.resource-minio-info :deep(.el-collapse-item__header) {
  padding: 0 8px;
  height: 40px;
}

/* 移除旧的不需要的样式 */
.minio-info-title,
.minio-info-content,
.config-row,
.config-item-label {
  display: none;
}

/* Pyxis上传相关样式 */
.pyxis-upload-section {
  margin-top: 15px;
}

.sycee-upload-section {
  margin-top: 15px;
}

.upload-section-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 15px;
}

.upload-section-title .el-icon {
  margin-right: 8px;
}

.folder-upload-container {
  margin-bottom: 20px;
}

.selected-folder {
  margin-top: 10px;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.file-list-container {
  margin: 20px 0;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.file-list-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.file-list-more {
  text-align: center;
  padding: 10px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
  background-color: var(--el-fill-color-light);
}

.code-preview {
  margin-top: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
  font-family: monospace;
  font-size: 14px;
  white-space: pre-wrap;
  max-height: 300px;
  overflow: auto;
}

.preview-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: var(--el-text-color-primary);
}

/* 节点信息显示样式 */
.node-info-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.node-id-tag {
  font-family: monospace;
}

.node-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.node-address {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

/* 任务预览页面样式 */
.task-info-card {
  margin-bottom: 20px;
}

.resource-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.empty-resource-message {
  text-align: center;
  padding: 40px 20px;
}

/* 配置项样式 */
.config-item {
  display: flex;
  margin-bottom: 8px;
  padding: 4px 0;
}

.config-label {
  font-weight: 500;
  color: var(--el-text-color-primary);
  min-width: 120px;
  flex-shrink: 0;
}

.config-value {
  color: var(--el-text-color-regular);
  font-family: monospace;
  word-break: break-all;
  flex: 1;
}

.minio-config-details {
  padding: 12px;
  background-color: #fafafa;
  border-radius: 4px;
}
.node-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.node-tag {
  align-self: flex-start;
}

.node-details {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.node-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.node-address {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  font-family: monospace;
}

.node-info-compact {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-id-tag {
  font-weight: 600;
}
</style>