
from fastapi import <PERSON>AP<PERSON>, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse, RedirectResponse

from .peer import peer
from .task import task
from .users import users
from .audit import audit

from .auth_middleware import AuthMiddleware


app = FastAPI()

app.mount("/static", StaticFiles(directory="static"), name="static")
app.include_router(task, prefix="")
app.include_router(peer, prefix="/peer")
app.include_router(audit)
app.include_router(users)

app.add_middleware(AuthMiddleware)


@app.get("/")
def homepage():
    return RedirectResponse("static/task_panel.html")


@app.get("/favicon.ico")
async def get_favicon():
    return FileResponse("static/icon/favicon.ico")


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "message": exc.detail,
            "code": exc.status_code
        },
    )


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={"message": str(exc), "code": 500},
    )
