<template>
  <div class="dashboard-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <h2>欢迎回来，{{ $store.state.user.username }}</h2>
      <div class="welcome-subtitle">今天是 {{ currentDate }}</div>
    </div>

    <!-- 节点统计卡片 -->
    <div class="section-title">
      <el-icon><Monitor /></el-icon>
      <span>节点概况</span>
    </div>
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="stat in nodeStatistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户统计卡片 -->
    <div class="section-title">
      <el-icon><User /></el-icon>
      <span>用户概况</span>
    </div>
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="stat in userStatistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 在用户统计卡片后添加数据集统计卡片 -->
    <div class="section-title">
      <el-icon><DataAnalysis /></el-icon>
      <span>数据集概况</span>
    </div>
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="stat in datasetStatistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务统计卡片 -->
    <div class="section-title">
      <el-icon><List /></el-icon>
      <span>任务概况</span>
    </div>
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="stat in taskStatistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><DataLine /></el-icon>
                <span>节点状态分布</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="nodePieChartRef"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><DataLine /></el-icon>
                <span>用户角色分布</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="userPieChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 在图表区域后添加系统日志卡片 -->
    <el-row :gutter="20" class="mb-4" v-permission="['menu:log']">
      <el-col :span="24">
        <el-card shadow="hover" class="log-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><Document /></el-icon>
                <span>最新系统日志</span>
              </div>
            </div>
          </template>
          <el-table
            :data="logs"
            style="width: 100%"
            :header-cell-style="{ background: '#f5f7fa' }"
            stripe
            height="400"
          >
            <el-table-column prop="timeCreated" label="时间" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.timeCreated) }}
              </template>
            </el-table-column>
            <el-table-column prop="clientIp" label="IP地址" width="130">
              <template #default="scope">
                <el-tag size="small" type="info">{{ scope.row.clientIp }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="httpMethod" label="请求方法" width="100">
              <template #default="scope">
                <el-tag 
                  size="small"
                  :type="getMethodType(scope.row.httpMethod)"
                >
                  {{ scope.row.httpMethod }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="requestUri" label="请求路径" show-overflow-tooltip>
              <template #default="scope">
                <span class="request-uri">{{ scope.row.requestUri }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="耗时" width="100" align="right">
              <template #default="scope">
                <span :class="getDurationClass(scope.row.duration)">
                  {{ scope.row.duration }}ms
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { 
  Monitor, 
  CircleCheck, 
  Warning, 
  CircleClose,
  User,
  UserFilled,
  Lock,
  DataLine,
  Document,
  DataAnalysis,
  Link,
  Folder,
  List,
  Clock,
  WarningFilled
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'
import * as echarts from 'echarts'
import store from '~/store'  // 导入store

const pageLoading = ref(false)
const nodePieChartRef = ref(null)
const userPieChartRef = ref(null)
let nodePieChart = null
let userPieChart = null

// 获取当前日期
const currentDate = new Date().toLocaleDateString('zh-CN', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  weekday: 'long'
})

// 节点统计数据
const nodeStatistics = ref([
  {
    title: '总节点数',
    value: 0,
    type: 'primary',
    icon: Monitor
  },
  {
    title: '在线节点',
    value: 0,
    type: 'success',
    icon: CircleCheck
  },
  {
    title: '离线节点',
    value: 0,
    type: 'warning',
    icon: Warning
  },
  {
    title: '错误节点',
    value: 0,
    type: 'danger',
    icon: CircleClose
  }
])

// 用户统计数据
const userStatistics = ref([
  {
    title: '总用户数',
    value: 0,
    type: 'primary',
    icon: User
  },
  {
    title: '在线用户',
    value: 0,
    type: 'success',
    icon: CircleCheck
  },
  {
    title: '总角色数',
    value: 0,
    type: 'warning',
    icon: Lock
  },
  {
    title: '活跃用户',
    value: 0,
    type: 'info',
    icon: UserFilled
  }
])

// 新增数据集统计
const datasetStatistics = ref([
  {
    title: '总数据集数',
    value: 0,
    type: 'primary',
    icon: DataAnalysis
  },
  {
    title: '文件类型数据集',
    value: 0,
    type: 'success',
    icon: Document
  },
  {
    title: 'URL类型数据集',
    value: 0,
    type: 'warning',
    icon: Link
  },
  {
    title: '最近上传数据集',
    value: 0,
    type: 'info',
    icon: Folder
  }
])

// 新增任务统计
const taskStatistics = ref([
  {
    title: '总任务数',
    value: 0,
    type: 'primary',
    icon: List
  },
  {
    title: '已完成任务',
    value: 0,
    type: 'success',
    icon: CircleCheck
  },
  {
    title: '运行中任务',
    value: 0,
    type: 'warning',
    icon: Clock
  },
  {
    title: '失败任务',
    value: 0,
    type: 'danger',
    icon: WarningFilled
  }
])

// 日志数据
const logs = ref([])

// 从store中获取权限检查函数
const hasPermission = (permissions) => {
  return store.getters.hasAnyPermission(permissions)
}

// 初始化所有图表
const initCharts = () => {
  // 确保在DOM更新后初始化图表
  nextTick(() => {
    if (nodePieChartRef.value) {
      nodePieChart?.dispose();  // 销毁已存在的实例
      nodePieChart = echarts.init(nodePieChartRef.value);
    }
    if (userPieChartRef.value) {
      userPieChart?.dispose();  // 销毁已存在的实例
      userPieChart = echarts.init(userPieChartRef.value);
    }
    updateCharts();
  });
}

// 更新所有图表数据
const updateCharts = () => {
  if (!nodePieChart || !userPieChart) return;

  // 节点状态分布图表配置
  const nodeChartOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    series: [
      {
        name: '节点状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],  // 调整图表位置
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: nodeStatistics.value[1].value, 
            name: '在线节点',
            itemStyle: { color: '#67C23A' }
          },
          { 
            value: nodeStatistics.value[2].value, 
            name: '离线节点',
            itemStyle: { color: '#E6A23C' }
          },
          { 
            value: nodeStatistics.value[3].value, 
            name: '错误节点',
            itemStyle: { color: '#F56C6C' }
          }
        ]
      }
    ]
  };

  // 用户角色分布图表配置
  const userChartOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    series: [
      {
        name: '用户分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],  // 调整图表位置
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: userStatistics.value[1].value, 
            name: '在线用户',
            itemStyle: { color: '#67C23A' }
          },
          { 
            value: userStatistics.value[3].value, 
            name: '活跃用户',
            itemStyle: { color: '#909399' }
          },
          { 
            value: userStatistics.value[0].value - userStatistics.value[1].value, 
            name: '离线用户',
            itemStyle: { color: '#E6A23C' }
          }
        ]
      }
    ]
  };

  // 设置图表配置
  nodePieChart.setOption(nodeChartOption);
  userPieChart.setOption(userChartOption);
}

// 获取所有统计数据
const fetchAllStatistics = async () => {
  pageLoading.value = true
  try {
    const [nodeRes, userRes, datasetRes, taskRes] = await Promise.all([
      service.get('/api/v1.0/sys/node/statistics'),
      service.get('/api/v1.0/sys/user/statistics'),
      service.get('/api/v1.0/sys/dataset/statistics'),
      service.get('/api/v1.0/sys/task/statistics')
    ])
    
    if (nodeRes.code === 10000) {
      nodeStatistics.value[0].value = nodeRes.data.total || 0
      nodeStatistics.value[1].value = nodeRes.data.online || 0
      nodeStatistics.value[2].value = nodeRes.data.offline || 0
      nodeStatistics.value[3].value = nodeRes.data.error || 0
    }
    
    if (userRes.code === 10000) {
      userStatistics.value[0].value = userRes.data.totalUsers || 0
      userStatistics.value[1].value = userRes.data.onlineUsers || 0
      userStatistics.value[2].value = userRes.data.totalRoles || 0
      userStatistics.value[3].value = userRes.data.activeUsers || 0
    }
    
    // 处理数据集统计
    if (datasetRes.code === 10000) {
      datasetStatistics.value[0].value = datasetRes.data.totalDatasets || 0
      datasetStatistics.value[1].value = datasetRes.data.fileDatasets || 0
      datasetStatistics.value[2].value = datasetRes.data.urlDatasets || 0
      
      // 最近上传的数据集
      if (datasetRes.data.recentDatasets && datasetRes.data.recentDatasets.length > 0) {
        datasetStatistics.value[3].value = datasetRes.data.recentDatasets.length
      }
    }
    
    // 处理任务统计
    if (taskRes.code === 10000) {
      taskStatistics.value[0].value = taskRes.data.totalTasks || 0
      taskStatistics.value[1].value = taskRes.data.completedTasks || 0
      taskStatistics.value[2].value = taskRes.data.runningTasks || 0
      taskStatistics.value[3].value = taskRes.data.failedTasks || 0
    }
    
    updateCharts()
  } catch (error) {
    toast('错误', '获取统计数据失败', 'error')
  } finally {
    pageLoading.value = false
  }
}

// 获取最新日志
const fetchLatestLogs = async () => {
  try {
    const res = await service.get('/api/v1.0/logs', {
      params: { 
        page: 1,
        size: 10
      }
    })
    if (res.code === 10000) {
      logs.value = res.data.data || []
    }
  } catch (error) {
    console.error('获取日志失败:', error)
  }
}

// 根据请求方法返回不同的标签类型
const getMethodType = (method) => {
  const types = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return types[method] || 'info'
}

// 根据请求耗时返回不同的样式类
const getDurationClass = (duration) => {
  if (duration <= 10) return 'duration-fast'
  if (duration <= 100) return 'duration-normal'
  return 'duration-slow'
}

let refreshInterval
onMounted(async () => {
  await fetchAllStatistics()
  initCharts()
  
  // 只有在有日志权限时才获取日志和启动定时器
  if (hasPermission(['menu:log'])) {
    await fetchLatestLogs()
    // // 每30秒刷新一次日志
    // refreshInterval = setInterval(fetchLatestLogs, 30000)
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    nodePieChart?.resize()
    userPieChart?.resize()
  })
})

// 组件卸载时清理图表实例
onUnmounted(() => {
  nodePieChart?.dispose()
  userPieChart?.dispose()
  window.removeEventListener('resize', () => {
    nodePieChart?.resize()
    userPieChart?.resize()
  })
  // 清除定时器
  clearInterval(refreshInterval)
})

// 格式化日期时间
const formatDateTime = (time) => {
  if (!time) return '-'
  const date = new Date(time)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

/* 添加一个辅助类用于底部间距 */
.mb-4 {
  margin-bottom: 16px;
}

.welcome-section {
  margin-bottom: 30px;
  
  h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
  }
  
  .welcome-subtitle {
    margin-top: 8px;
    color: #909399;
    font-size: 14px;
  }
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  
  .el-icon {
    margin-right: 8px;
    color: var(--el-color-primary);
  }
}

.stat-card {
  height: 120px;
  transition: all 0.3s ease;
  border: 1px solid var(--el-border-color);
  background: linear-gradient(145deg, var(--el-bg-color-overlay), var(--el-bg-color));
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--el-color-primary-light-5);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.stat-icon {
  margin-right: 16px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-icon.primary { 
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border: 1px solid var(--el-color-primary-light-5);
}
.stat-icon.success { 
  background-color: var(--el-color-success-light-9);
  color: var(--el-color-success);
  border: 1px solid var(--el-color-success-light-5);
}
.stat-icon.warning { 
  background-color: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
  border: 1px solid var(--el-color-warning-light-5);
}
.stat-icon.danger { 
  background-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
  border: 1px solid var(--el-color-danger-light-5);
}
.stat-icon.info { 
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
  border: 1px solid var(--el-color-info-light-5);
}

.stat-info {
  flex: 1;
  
  .stat-value {
    font-size: 28px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    line-height: 1.2;
    margin-bottom: 8px;
  }
  
  .stat-title {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.chart-card {
  height: 480px;
  transition: all 0.3s ease;
  border: 1px solid var(--el-border-color);
  background: linear-gradient(145deg, var(--el-bg-color-overlay), var(--el-bg-color));
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.chart-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--el-color-primary-light-5);
}

.chart-container {
  height: calc(100% - 60px);
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  background-color: rgba(255, 255, 255, 0.01);
  border-radius: 0 0 4px 4px;
}

.card-header {
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 16px 20px;
  background-color: rgba(255, 255, 255, 0.02);
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  
  .icon {
    margin-right: 8px;
    font-size: 20px;
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
    padding: 6px;
    border-radius: 6px;
  }
}

/* 深色模式适配 */
html.dark {
  .stat-card,
  .chart-card {
    background: linear-gradient(145deg, 
      var(--el-bg-color-overlay), 
      rgba(var(--el-bg-color-rgb), 0.9)
    );
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .stat-card:hover,
  .chart-card:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
}

.log-card {
  margin-top: 20px;
  transition: all 0.3s ease;
}

.log-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.log-card :deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
  border-radius: 4px;
  overflow: hidden;
}

.log-card :deep(.el-table__header) {
  background-color: var(--el-bg-color-page);
}

.log-card :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.log-card :deep(.el-table__row:hover) {
  background-color: var(--el-color-primary-light-9) !important;
}

.request-uri {
  color: var(--el-text-color-secondary);
  font-family: monospace;
}

.duration-fast {
  color: var(--el-color-success);
  font-weight: 600;
}

.duration-normal {
  color: var(--el-color-warning);
  font-weight: 600;
}

.duration-slow {
  color: var(--el-color-danger);
  font-weight: 600;
}
</style>