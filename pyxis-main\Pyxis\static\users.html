<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management</title>
    <style>
        :root {
            --color-dark: #333;
            --color-deep: #555;
            --color-saturated: #9e9e9e;
            --color-light: #d3d3d3;
            --color-pale: #f4f4f4;
            --color-font-deep: black;
            --color-font-light: white;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            overflow: hidden;
            font-size: 1rem;
            line-height: 1rem;
        }

        .container {
            display: grid;
            grid-template-areas:
                "header header"
                "sidebar content";
            grid-template-columns: auto 1fr;
            grid-template-rows: auto 1fr;
            height: 100vh;
            width: 100vw;
        }

        .header {
            grid-area: header;
            background-color: var(--color-dark);
            color: var(--color-font-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1.25rem;
            height: 3rem;
        }

        .header .logo {
            font-size: 1.5rem;
        }

        .header .nav-items {
            display: flex;
            gap: 1.25rem;
        }

        .content {
            grid-area: content;
            padding: 1.25rem;
            background-color: var(--color-pale);
            overflow: auto;
            display: flex;
            flex-direction: column;
        }

        .button {
            display: inline-block;
            padding: 0.625rem 1.25rem;
            background-color: var(--color-dark);
            color: var(--color-light);
            text-decoration: none;
            border: none;
            border-radius: 0.25rem;
            cursor: pointer;
        }

        .userspan {
            display: inline-block;
            padding: 0.625rem 1.25rem;
            background-color: var(--color-dark);
            color: var(--color-font-light);
            text-decoration: none;
            border: none;
            border-radius: 0.25rem;
        }

        .userspan>a,
        .userspan>a:visited {
            color: var(--color-light);
            text-decoration: none;
        }

        .button:hover {
            background-color: var(--color-deep);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        th,
        td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--color-light);
            font-family: monospace;
            max-width: 40vw;
            min-width: 5em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        th {
            background-color: var(--color-dark);
            color: var(--color-font-light);
        }

        tr:hover {
            background-color: var(--color-light);
        }

        .form-group {
            margin-bottom: 1rem;
            margin-top: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
        }

        .form-group input,
        .form-group select {
            width: 40vw;
            padding: 0.5rem;
            border: 1px solid var(--color-light);
            box-sizing: border-box;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            width: 40vw;
            display: flex;
            flex-wrap: wrap;
            justify-content: center
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            width: 40vw;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .close {
            cursor: pointer;
            font-size: 1.5rem;
        }
    </style>
</head>

<body>
    <div class="container">
        <header class="header">
            <div class="logo">User Management</div>
            <div class="nav-items">
                <span id="currentUser" class="userspan"></span>
                <a href="#" class="button" onclick="window.location.reload()">Refresh</a>
                <a href="#" id="logout" class="button">Logout</a>
            </div>
        </header>
        <main class="content">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h1>Users</h1>
                <button id="addUserBtn" class="button">Add User</button>
            </div>
            <table id="usersTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Role</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </main>
    </div>

    <!-- Add/Edit User Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Add User</h2>
                <span class="close">&times;</span>
            </div>
            <form id="userForm">
                <input type="hidden" id="userId">
                <div class="form-group">
                    <label for="name">Name</label>
                    <input type="text" id="name" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" required>
                </div>
                <div class="form-group">
                    <label for="role">Role</label>
                    <select id="role" required>
                        <option value="admin">Admin</option>
                        <option value="user">User</option>
                        <option value="auditor">Auditor</option>
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" id="cancelBtn" class="button">Cancel</button>
                    <button type="submit" class="button">Save</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentUser = null
        let users = []

        // DOM Elements
        const usersTable = document.getElementById('usersTable').getElementsByTagName('tbody')[0]
        const addUserBtn = document.getElementById('addUserBtn')
        const userModal = document.getElementById('userModal')
        const modalTitle = document.getElementById('modalTitle')
        const userForm = document.getElementById('userForm')
        const closeModal = document.querySelector('.close')
        const cancelBtn = document.getElementById('cancelBtn')
        const logoutBtn = document.getElementById('logout')
        const currentUserSpan = document.getElementById('currentUser')

        // Event Listeners
        addUserBtn.addEventListener('click', () => openModal('add'))
        closeModal.addEventListener('click', () => userModal.style.display = 'none')
        cancelBtn.addEventListener('click', () => userModal.style.display = 'none')
        logoutBtn.addEventListener('click', logout)
        userModal.addEventListener('click', (e) => {
            if (e.target === userModal) {
                userModal.style.display = 'none'
            }
        })

        // Initialize
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('access_token')
            if (!token) {
                window.location.href = '/static/login.html'
                return
            }

            try {
                await fetchCurrentUser()
                if (currentUser.role !== 'admin') {
                    addUserBtn.style.display = 'none'
                }
                await fetchUsers()
            } catch (error) {
                console.error('Error:', error)
                window.location.href = '/static/login.html'
            }
        })

        // Functions
        async function fetchCurrentUser() {
            const response = await fetch('/users/me', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            })

            if (!response.ok) {
                throw new Error('Failed to fetch current user')
            }

            currentUser = await response.json()
            currentUserSpan.innerHTML = `
            <a target="_blank" href="/static/task_panel.html">
                Welcome, ${currentUser.name} (${currentUser.role})
            </a>`
        }

        async function fetchUsers() {
            const response = await fetch('/users/', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            })

            if (!response.ok) {
                throw new Error('Failed to fetch users')
            }

            users = await response.json()
            renderUsersTable()
        }

        function renderUsersTable() {
            usersTable.innerHTML = ''
            users.forEach(user => {
                const row = usersTable.insertRow()
                row.innerHTML = `
                    <td>${user.user_id}</td>
                    <td>${user.name}</td>
                    <td>${user.role}</td>
                    <td>
                        ${currentUser.role === 'admin' ? `
                        <button onclick="editUser('${user.user_id}')" class="button">Edit</button>
                        <button onclick="deleteUser('${user.user_id}')" class="button">Delete</button>
                        ` : 'No actions available'}
                    </td>
                `
            })
        }

        function openModal(mode, userId = null) {
            userForm.reset()
            if (mode === 'add') {
                modalTitle.textContent = 'Add User'
                document.getElementById('userId').value = ''
            } else {
                modalTitle.textContent = 'Edit User'
                const user = users.find(u => u.user_id === userId)
                document.getElementById('userId').value = user.user_id
                document.getElementById('name').value = user.name
                document.getElementById('role').value = user.role
                document.getElementById('password').required = false
            }
            userModal.style.display = 'flex'
        }

        userForm.addEventListener('submit', async (e) => {
            e.preventDefault()

            const userId = document.getElementById('userId').value
            const name = document.getElementById('name').value
            const password = document.getElementById('password').value
            const role = document.getElementById('role').value

            const userData = {
                name,
                role,
                ...(password && { password })
            }

            try {
                let response
                if (userId) {
                    // Update user
                    response = await fetch(`/users/${userId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                        },
                        body: JSON.stringify(userData)
                    })
                } else {
                    // Create user
                    userData.password = password
                    response = await fetch('/users/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                        },
                        body: JSON.stringify(userData)
                    })
                }

                if (!response.ok) {
                    const error = await response.json()
                    throw new Error(error.detail || 'Operation failed')
                }

                await fetchUsers()
                userModal.style.display = 'none'
            } catch (error) {
                alert(error.message)
            }
        })

        function editUser(userId) {
            openModal('edit', userId)
        }

        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user?')) return

            try {
                const response = await fetch(`/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    }
                })

                if (!response.ok) {
                    throw new Error('Failed to delete user')
                }

                await fetchUsers()
            } catch (error) {
                alert(error.message)
            }
        }

        function logout() {
            localStorage.removeItem('access_token')
            window.location.href = '/static/login.html'
        }

        // Expose functions to global scope for inline event handlers
        window.editUser = editUser
        window.deleteUser = deleteUser;
    </script>
</body>

</html>