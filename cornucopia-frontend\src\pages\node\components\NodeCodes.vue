<template>
  <div class="code-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Document /></el-icon>
          <h2>代码列表</h2>
        </div>
        <div class="sub-title">查看节点中的代码列表</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.name"
                placeholder="请输入代码Id"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
          <div class="action-wrapper">
            <el-button type="success" @click="handleUploadCode" plain round>
              <el-icon><Upload /></el-icon>上传代码
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table 
        v-loading="loading"
        :data="codes" 
        stripe 
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        border
      >
        <el-table-column prop="id" label="代码ID" width="250" align="center" show-overflow-tooltip />
        <el-table-column prop="service_func_name" label="函数名称" width="280" align="center" show-overflow-tooltip />
        <el-table-column prop="input_kwargs" label="输入参数" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.input_kwargs.join(', ') }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          prop="submit_time" 
          label="提交时间" 
          min-width="180" 
          align="center" 
          show-overflow-tooltip
          sortable
          :sort-method="sortByTime"
        />
        <el-table-column label="操作" width="320" fixed="right" align="center">
          <template #default="scope">
            <el-button-group class="operation-group">
              <el-tooltip content="刷新" placement="top">
                <el-button type="primary" size="small" @click="handleRefresh(scope.row)">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看源码" placement="top">
                <el-button type="info" size="small" @click="handleDetail(scope.row)">
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="运行代码" placement="top">
                <el-button type="success" size="small" @click="handleRun(scope.row)">
                  <el-icon><VideoPlay /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除代码" placement="top">
                <el-button type="danger" size="small" @click="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
  <el-dialog
    v-model="dialogVisible"
    title="代码详情"
    width="55%"
    class="code-dialog"
    center
  >
    <div class="code-container">
      <div class="code-wrapper">
        <div class="code-scroll-container">
          <div class="line-numbers">
            <pre><span v-for="i in codeLines" :key="i">{{ i }}</span></pre>
          </div>
          <pre class="code-content"><code class="python" v-html="highlightedCode"></code></pre>
        </div>
      </div>
    </div>
  </el-dialog>
  <el-dialog
    v-model="argsDialogVisible"
    title="输入参数"
    width="30%"
    class="args-dialog"
    center
  >
    <div class="args-container">
      <div class="args-header">
        <h3>{{ currentRow?.service_func_name }}</h3>
        <p class="args-desc" v-if="currentRow?.input_kwargs && currentRow.input_kwargs.length > 0">
          请为以下参数输入对应的值
        </p>
        <p class="args-desc" v-else>
          此代码无参数
        </p>
      </div>
      <el-form :model="argsForm" label-width="120px" v-if="currentRow?.input_kwargs && currentRow.input_kwargs.length > 0">
        <el-form-item 
          v-for="param in (currentRow?.input_kwargs || [])"
          :key="param"
          :label="param"
        >
          <div class="param-input-group">
            <el-input 
              v-model="argsForm[param]"
              :placeholder="`请输入参数 ${param} 的值`"
              size="large"
              class="args-input"
            />
            <el-switch
              v-model="argsTypes[param]"
              active-text="字符串"
              inactive-text="自动"
              class="param-type-switch"
            />
          </div>
        </el-form-item>
      </el-form>
      <div v-else class="no-params-placeholder">
        <el-empty description="无需输入参数" :image-size="100" />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button 
          @click="argsDialogVisible = false"
          size="large"
          round
        >
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleArgsSubmit"
          size="large"
          round
        >
          <el-icon class="mr-2"><VideoPlay /></el-icon>
          确认运行
        </el-button>
      </span>
    </template>
  </el-dialog>
  
  <!-- 上传代码对话框 -->
  <el-dialog
    v-model="uploadDialogVisible"
    title="上传代码"
    width="60%"
    class="upload-dialog"
    center
  >
    <div class="upload-container">
      <div class="upload-tabs">
        <el-tabs v-model="uploadTab">
          <el-tab-pane label="手动输入" name="manual">
            <el-form :model="uploadForm" label-width="120px">
              <el-form-item label="函数名称" required>
                <el-input 
                  v-model="uploadForm.func_name"
                  placeholder="请输入函数名称"
                  class="upload-input"
                />
              </el-form-item>
              <el-form-item label="代码内容" required>
                <el-input 
                  v-model="uploadForm.func_str"
                  type="textarea"
                  :rows="15"
                  placeholder="请输入Python代码"
                  class="code-textarea"
                />
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <el-tab-pane label="文件上传" name="file">
            <div class="file-upload-container">
              <el-upload
                class="file-uploader"
                drag
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :limit="1"
                accept=".py"
              >
                <el-icon class="el-icon--upload"><Upload /></el-icon>
                <div class="el-upload__text">
                  拖拽文件到此处或 <em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    只能上传 .py 文件
                  </div>
                </template>
              </el-upload>
              
              <div v-if="fileContent" class="file-preview">
                <div class="file-info">
                  <h4>文件预览</h4>
                  <p>函数名称: <strong>{{ uploadForm.func_name }}</strong></p>
                </div>
                <div class="code-preview">
                  <pre><code class="python">{{ fileContent }}</code></pre>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button 
          @click="uploadDialogVisible = false"
          size="large"
          round
        >
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleUploadSubmit"
          :loading="uploadLoading"
          size="large"
          round
        >
          <el-icon class="mr-2"><Upload /></el-icon>
          确认上传
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import { View, VideoPlay, Delete, Refresh, Document, Search, Upload } from '@element-plus/icons-vue'
import { toast, showModal } from '~/composables/util'
import sycee from '~/sycee.js'
import hljs from 'highlight.js/lib/core'
import python from 'highlight.js/lib/languages/python'
import 'highlight.js/styles/github.css'

// 注册 Python 语言
hljs.registerLanguage('python', python)

const route = useRoute()
const nodeId = ref('')
const codes = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const codeContent = ref('')
const highlightedCode = ref('')
const argsDialogVisible = ref(false)
const currentRow = ref(null)
const argsForm = ref({})
const argsTypes = ref({})
const searchForm = ref({
  name: ''
})

// 上传代码相关
const uploadDialogVisible = ref(false)
const uploadLoading = ref(false)
const uploadTab = ref('manual')
const fileContent = ref('')
const uploadForm = ref({
  func_name: '',
  func_str: ''
})

const getStatusType = (status) => {
  switch (status) {
    case 'APPROVED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'DENIED':
      return 'danger'
    default:
      return 'info'
  }
}

const handleRefresh = async (row) => {
  try {
    const res = await sycee(nodeId.value, 'get_code_by_id', { id: row.id })
    if (res.code === 10000) {
      // 更新当前行数据
      const index = codes.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        codes.value[index] = res.data
      }
      toast('成功', '代码信息已刷新', 'success')
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    console.error('刷新代码失败:', error)
    toast('错误', '刷新失败', 'error')
  }
}

const handleGetCodes = async () => {  
  if (!nodeId.value) return
  loading.value = true
  try {
    const res = await sycee(nodeId.value, 'get_all_code_for_user', {})
    if (res.code === 10000) {
      codes.value = res.data
    } else {
      toast("错误", res.data, "error")
    }
  } catch (error) {
    console.error('获取代码列表失败:', error)
    toast("错误", "获取代码列表失败", "error")
  } finally {
    loading.value = false
  }
}

const handleDetail = async (row) => {
  try {
    const res = await sycee(nodeId.value, 'get_raw_code_by_id', { id: row.id })
    if (res.code === 10000) {
      codeContent.value = res.data
      // 预处理代码高亮
      highlightedCode.value = hljs.highlight(codeContent.value, {
        language: 'python'
      }).value
      dialogVisible.value = true
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    console.error('获取代码内容失败:', error)
    toast('错误', '获取代码内容失败', 'error')
  }
}

const handleRun = async (row) => {
  currentRow.value = row
  // 解析输入参数，为每个参数创建表单字段和类型标记
  argsForm.value = {}
  argsTypes.value = {}  // 新增：存储每个参数是否为字符串类型
  row.input_kwargs.forEach(param => {
    argsForm.value[param] = ''
    argsTypes.value[param] = false  // 默认为非字符串类型
  })
  argsDialogVisible.value = true
}

const handleArgsSubmit = async () => {
  try {
    // 格式化参数
    const formattedArgs = {}
    Object.keys(argsForm.value).forEach(key => {
      const value = argsForm.value[key]
      // 根据用户选择决定是否保持字符串类型
      if (argsTypes.value[key]) {
        formattedArgs[key] = value // 保持字符串
      } else {
        // 如果不是字符串类型且可以转换为数字，则转换
        formattedArgs[key] = !isNaN(value) && value !== '' ? Number(value) : value
      }
    })
    
    const res = await sycee(nodeId.value, 'run_code', { 
      id: currentRow.value.id,
      args: formattedArgs
    })
    
    if (res.code === 10000) {
      toast("成功", "代码已开始运行", "success")
      argsDialogVisible.value = false
    } else {
      toast("错误", res.data, "error")
    }
  } catch (error) {
    toast("错误", "运行代码失败", "error")
  }
}

const handleDelete = (row) => {
  showModal(
    `确定要删除代码 ${row.service_func_name} 吗？`,
    'warning',
    '警告'
  ).then(async () => {
    try {
      const res = await sycee(nodeId.value, 'delete_code_by_id', { id: row.id })
      if (res.code === 10000) {
        toast('删除成功')
        handleGetCodes()
      } else {
        toast('删除失败', res.data, 'error')
      }
    } catch (error) {
      toast('删除失败', error.message, 'error')
    }
  }).catch(() => {
    toast('已取消操作', '', 'info')
  })
}

const handleSearch = async () => {
  if (!searchForm.value.name) {
    handleGetCodes()
    return
  }
  
  try {
    const res = await sycee(nodeId.value, 'get_code_by_id', { id: searchForm.value.name })
    if (res.code === 10000) {
      codes.value = [res.data]
      toast('成功', '搜索结果已更新', 'success')
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    console.error('搜索代码失败:', error)
    toast('错误', '搜索失败', 'error')
  }
}

const resetSearch = () => {
  searchForm.value.name = ''
  handleGetCodes()
}

const codeLines = computed(() => {
  if (!codeContent.value) return 0
  return codeContent.value.split('\n').length
})

const sortByTime = (a, b) => {
  const timeA = new Date(a.submit_time).getTime()
  const timeB = new Date(b.submit_time).getTime()
  return timeA - timeB
}

// 处理上传代码
const handleUploadCode = () => {
  uploadForm.value = {
    func_name: '',
    func_str: ''
  }
  fileContent.value = ''
  uploadTab.value = 'manual'
  uploadDialogVisible.value = true
}

// 处理文件上传
const handleFileChange = (file) => {
  if (!file || !file.raw) {
    fileContent.value = ''
    return
  }
  
  const reader = new FileReader()
  reader.onload = (e) => {
    const content = e.target.result
    fileContent.value = content
    
    // 解析函数名和代码
    try {
      // 简单解析函数定义
      const funcMatch = content.match(/def\s+([a-zA-Z0-9_]+)\s*\(/);
      if (funcMatch && funcMatch[1]) {
        uploadForm.value.func_name = funcMatch[1]
        uploadForm.value.func_str = content
      } else {
        toast('警告', '无法从文件中解析函数名，请检查文件格式', 'warning')
      }
    } catch (error) {
      console.error('解析文件失败:', error)
      toast('错误', '解析文件失败', 'error')
    }
  }
  
  reader.readAsText(file.raw)
}

// 提交上传代码
const handleUploadSubmit = async () => {
  if (!uploadForm.value.func_name || !uploadForm.value.func_str) {
    toast('警告', '请填写完整的函数名称和代码内容', 'warning')
    return
  }

  uploadLoading.value = true
  try {
    const res = await sycee(nodeId.value, 'upload_code_str', {
        func_name: uploadForm.value.func_name,
        func_str: uploadForm.value.func_str,
        dataset_getter: {},
        onmock: false
    })
    
    if (res.code === 10000) {
      toast('成功', '代码上传成功', 'success')
      uploadDialogVisible.value = false
      handleGetCodes() // 刷新代码列表
    } else {
      toast('错误', res.data, 'error')
    }
  } catch (error) {
    console.error('上传代码失败:', error)
    toast('错误', '上传代码失败', 'error')
  } finally {
    uploadLoading.value = false
  }
}

onMounted(() => {
  nodeId.value = route.query.id
  handleGetCodes()
})
</script>

<style>
.code-list-container {
  padding: 20px;
}

.list-card {
  border-radius: 8px;
  margin-bottom: 20px;
}

.code-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.code-container {
  height: 70vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.code-wrapper {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  position: relative;
}

.code-scroll-container {
  display: flex;
  width: 100%;
  overflow: auto;
}

.line-numbers {
  padding: 16px 8px;
  background-color: #f0f0f0;
  text-align: right;
  user-select: none;
  position: sticky;
  left: 0;
  z-index: 1;
  border-right: 1px solid #ddd;
  height: fit-content;
  min-height: 100%;
}

.line-numbers::after {
  content: '';
  position: absolute;
  top: 0;
  right: -1px;
  width: 4px;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
}

.line-numbers pre {
  margin: 0;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #999;
  height: 100%;
}

.line-numbers span {
  display: block;
  padding-right: 4px;
}

.code-content {
  margin: 0;
  padding: 16px;
  flex: 1;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  background: transparent;
  min-width: fit-content;
}

.code-content .hljs {
  background: transparent;
  padding: 0;
  white-space: pre;
}

.args-dialog, .upload-dialog {
  :deep(.el-dialog__header) {
    margin-right: 0;
    padding: 20px 20px 10px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 10px 20px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

.args-container {
  .args-header {
    margin-bottom: 24px;
    text-align: center;
    
    h3 {
      margin: 0 0 8px;
      font-size: 18px;
      color: var(--el-text-color-primary);
    }
    
    .args-desc {
      margin: 0;
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }
}

.param-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.param-type-switch {
  flex-shrink: 0;
}

.args-input {
  flex: 1;
}

.code-textarea {
  :deep(.el-textarea__inner) {
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
    padding: 12px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  
  .el-button {
    min-width: 100px;
    
    .el-icon {
      margin-right: 4px;
    }
  }
}

.code-list-container .page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.code-list-container .header-left {
  flex-shrink: 0;
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
    margin-left: 32px;
  }
}

.code-list-container .header-right {
  flex: 1;
  display: flex;
  justify-content: space-between;
  padding-left: 32px;

  .header-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 16px;
    flex-wrap: wrap;
  }
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
  :deep(.el-input__wrapper) {
    border-radius: 20px;
    padding: 1px 15px;
    
    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
  min-width: 88px;
}

.operation-group {
  display: flex;
  justify-content: center;
  gap: 4px;
}

.operation-group :deep(.el-button) {
  padding: 6px 8px;
}

.operation-group :deep(.el-button + .el-button) {
  margin-left: 0;  /* 覆盖 element-plus 的默认间距 */
}

.action-wrapper {
  flex-shrink: 0;
}

/* 文件上传相关样式 */
.file-upload-container {
  padding: 20px 0;
}

.file-uploader {
  width: 100%;
  
  :deep(.el-upload) {
    width: 100%;
  }
  
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    .el-icon--upload {
      font-size: 48px;
      color: var(--el-color-primary);
      margin-bottom: 16px;
    }
    
    .el-upload__text {
      font-size: 16px;
      color: var(--el-text-color-regular);
      
      em {
        color: var(--el-color-primary);
        font-style: normal;
        text-decoration: underline;
        cursor: pointer;
      }
    }
  }
  
  :deep(.el-upload__tip) {
    text-align: center;
    margin-top: 8px;
    color: var(--el-text-color-secondary);
  }
}

.file-preview {
  margin-top: 24px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
  
  .file-info {
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-bottom: 1px solid var(--el-border-color);
    
    h4 {
      margin: 0 0 8px;
      font-size: 16px;
      color: var(--el-text-color-primary);
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
  
  .code-preview {
    max-height: 300px;
    overflow: auto;
    background-color: #f8f9fa;
    
    pre {
      margin: 0;
      padding: 16px;
      font-family: monospace;
      font-size: 14px;
      line-height: 1.5;
      white-space: pre-wrap;
    }
  }
}

.upload-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }
  
  :deep(.el-tabs__item) {
    font-size: 16px;
    height: 48px;
    line-height: 48px;
  }
}

.no-params-placeholder {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}
</style>

