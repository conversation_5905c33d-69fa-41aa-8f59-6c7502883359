package ouc.isclab.application.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import ouc.isclab.application.entity.ApplicationTaskEntity;
import ouc.isclab.application.entity.ApplicationTaskEntity.DeployStatus;

import java.util.Date;
import java.util.List;

public interface ApplicationTaskRepository extends JpaRepository<ApplicationTaskEntity, Long> {

    // 查询用户创建的所有部署任务
    Page<ApplicationTaskEntity> findByCreatorId(Long creatorId, Pageable pageable);
    
    // 根据关键词搜索用户的部署任务
    @Query("SELECT t FROM ApplicationTaskEntity t WHERE t.creatorId = :creatorId AND (LOWER(t.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<ApplicationTaskEntity> findByCreatorIdAndKeyword(@Param("creatorId") Long creatorId, @Param("keyword") String keyword, Pageable pageable);

    // 根据状态统计部署任务数量
    long countByStatus(DeployStatus status);

    // 查询所有部署任务
    List<ApplicationTaskEntity> findAll();

    // 根据模型ID查询部署任务
    List<ApplicationTaskEntity> findByModelId(Long modelId);

    // 查询节点所有者需要审批的部署任务
    Page<ApplicationTaskEntity> findByNode_CreatorIdAndStatus(Long nodeOwnerId, DeployStatus status, Pageable pageable);
    
    // 查询节点所有者的所有部署任务
    Page<ApplicationTaskEntity> findByNode_CreatorId(Long nodeOwnerId, Pageable pageable);
    
    // 检查用户是否向特定节点提交过特定状态的部署任务
    boolean existsByCreatorIdAndNodeIdInAndStatusIn(Long creatorId, List<Long> nodeIds, List<DeployStatus> statuses);

    // 按时间范围查询应用任务数量
    long countByTimeCreatedBetween(Date startDate, Date endDate);
}