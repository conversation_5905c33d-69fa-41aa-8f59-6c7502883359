<template>
  <div class="task-overview-container">
    <el-loading :full-screen="false" :body="true" v-if="pageLoading" />
    
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><List /></el-icon>
          <h2>任务总览</h2>
        </div>
        <div class="sub-title">查看系统任务的整体统计信息</div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="goToCreateTask" round>
          <el-icon><Plus /></el-icon>
          创建任务
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="stat in statistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <el-icon :size="24" class="stat-icon" :class="stat.type">
              <component :is="stat.icon" />
            </el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务状态分布图表 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><PieChart /></el-icon>
                <span>任务状态分布</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="statusChartRef"></div>
        </el-card>
      </el-col>
      
      <!-- 任务趋势图表 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon class="icon"><TrendCharts /></el-icon>
                <span>任务创建趋势</span>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="trendChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  List, 
  Plus,
  Document,
  CircleCheck,
  Clock,
  WarningFilled,
  PieChart,
  TrendCharts
} from '@element-plus/icons-vue'
import { toast } from '~/composables/util'
import service from '~/axios'
import * as echarts from 'echarts'

const router = useRouter()
const pageLoading = ref(false)
const statusChartRef = ref(null)
const trendChartRef = ref(null)
let statusChart = null
let trendChart = null

// 统计数据
const statistics = ref([
  {
    title: '总任务数',
    value: 0,
    type: 'primary',
    icon: Document
  },
  {
    title: '已完成任务',
    value: 0,
    type: 'success',
    icon: CircleCheck
  },
  {
    title: '运行中任务',
    value: 0,
    type: 'warning',
    icon: Clock
  },
  {
    title: '失败任务',
    value: 0,
    type: 'danger',
    icon: WarningFilled
  }
])

// 获取任务统计数据
const fetchStatistics = async () => {
  pageLoading.value = true
  try {
    const res = await service.get('/api/v1.0/sys/task/statistics')
    if (res.code === 10000) {
      statistics.value[0].value = res.data.totalTasks || 0
      statistics.value[1].value = res.data.completedTasks || 0
      statistics.value[2].value = res.data.runningTasks || 0
      statistics.value[3].value = res.data.failedTasks || 0
      
      // 更新图表
      updateStatusChart(res.data.statusDistribution || [])
      updateTrendChart(res.data.taskTrend || [])
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    toast('错误', '获取统计数据失败', 'error')
  } finally {
    pageLoading.value = false
  }
}

// 初始化状态分布图表
const initStatusChart = () => {
  if (statusChartRef.value) {
    statusChart = echarts.init(statusChartRef.value)
  }
}

// 更新状态分布图表数据
const updateStatusChart = (statusData) => {
  if (!statusChart) return
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: statistics.value[1].value, 
            name: '已完成',
            itemStyle: { color: '#67C23A' }
          },
          { 
            value: statistics.value[2].value, 
            name: '运行中',
            itemStyle: { color: '#E6A23C' }
          },
          { 
            value: statistics.value[3].value, 
            name: '失败',
            itemStyle: { color: '#F56C6C' }
          },
          { 
            value: statistics.value[0].value - statistics.value[1].value - statistics.value[2].value - statistics.value[3].value, 
            name: '其他',
            itemStyle: { color: '#909399' }
          }
        ]
      }
    ]
  }
  
  statusChart.setOption(option)
}

// 初始化趋势图表
const initTrendChart = () => {
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
  }
}

// 更新趋势图表数据
const updateTrendChart = (trendData) => {
  if (!trendChart) return
  
  // 确保有数据可用
  const data = trendData && trendData.length > 0 ? trendData : [
    { date: '周一', count: 0 },
    { date: '周二', count: 0 },
    { date: '周三', count: 0 },
    { date: '周四', count: 0 },
    { date: '周五', count: 0 },
    { date: '周六', count: 0 },
    { date: '周日', count: 0 }
  ]
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',  // 增加底部空间以显示横轴标签
      top: '10%',     // 增加顶部空间
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date),
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        interval: 0,    // 强制显示所有标签
        rotate: 30,     // 旋转标签以避免重叠
        fontSize: 12,
        margin: 8       // 增加标签与轴的距离
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,   // 确保Y轴刻度为整数
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '新建任务数',
        type: 'bar',
        barWidth: '50%',  // 调整柱状图宽度
        data: data.map(item => item.count),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#5470c6' },
              { offset: 1, color: '#3c5fbe' }
            ])
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}'
        }
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 跳转到创建任务页面
const goToCreateTask = () => {
  router.push('/task/create')
}

onMounted(async () => {
  // 先初始化图表
  nextTick(() => {
    initStatusChart()
    initTrendChart()
  })
  
  // 然后获取数据并更新图表
  await fetchStatistics()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    statusChart?.resize()
    trendChart?.resize()
  })
})

onUnmounted(() => {
  // 销毁图表实例
  statusChart?.dispose()
  trendChart?.dispose()
  
  // 移除事件监听器
  window.removeEventListener('resize', () => {
    statusChart?.resize()
    trendChart?.resize()
  })
})
</script>

<style scoped>
.task-overview-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  display: flex;
  align-items: center;
}

.stat-card {
  height: 120px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 8px;
}

.stat-icon.primary { background-color: var(--el-color-primary-light-9); }
.stat-icon.success { background-color: var(--el-color-success-light-9); }
.stat-icon.warning { background-color: var(--el-color-warning-light-9); }
.stat-icon.danger { background-color: var(--el-color-danger-light-9); }
.stat-icon.info { background-color: var(--el-color-info-light-9); }

.stat-info {
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  
  .stat-title {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.chart-card {
  height: 450px;
  overflow: hidden;
  margin-bottom: 20px;
}

.chart-container {
  height: 390px;
  padding: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  
  .icon {
    margin-right: 8px;
    font-size: 18px;
    color: var(--el-color-primary);
  }
}

/* 深色模式适配 */
html.dark {
  .stat-card,
  .chart-card {
    background-color: var(--el-bg-color-overlay);
  }
}
</style> 