import axios from "axios"
import { toast } from '~/composables/util'
import store from '~/store'
import router from '~/router'
import { getToken } from '~/composables/auth'

const service = axios.create({
    baseURL:"/api",
    // 允许跨域请求时携带cookie
    withCredentials: true,
    // 设置请求超时时间（毫秒）
    timeout: 15000
})

// 添加请求拦截器
service.interceptors.request.use(
    config => {
        // 在请求头中添加token
        const token = getToken()
        if (token) {
            config.headers['token'] = token // 直接使用token值
        }
        return config
    },
    error => {
        return Promise.reject(error)
    }
)

// 添加响应拦截器
service.interceptors.response.use(
    response => {
        return response.data;
    },
    error => {
        // 处理请求超时的情况
        if (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1) {
            toast("错误", "请求超时，请稍后重试", "error");
        } 
        // 处理 401 状态码
        else if(error.response?.status === 401){
            // token 失效,清除用户信息并跳转到登录页
            store.dispatch("logout")
            router.push("/login")
            toast("错误", "登陆失效，请重新登录", "error")
        }
        else if (error.response) {
            const message = {
                404: "请求的资源不存在",
                403: "没有权限访问",
                500: "服务器内部错误"
            }[error.response.status] || "请求失败"
            
            toast("错误", message, "error");
        }
        else {
            toast("错误", "网络连接失败", "error");
        }
        return Promise.reject(error);
    }
)

export default service