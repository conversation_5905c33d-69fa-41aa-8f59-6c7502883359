<template>
  <div class="data-list-container">
    <div class="page-header">
      <div class="header-left">
        <div class="title-wrapper">
          <el-icon class="title-icon"><Folder /></el-icon>
          <h2>数据集管理</h2>
        </div>
        <div class="sub-title">管理系统中的数据集文件</div>
      </div>
      
      <div class="header-right">
        <div class="header-actions">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-input
                v-model="searchForm.name"
                placeholder="请输入数据集名称"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" plain round>
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch" round>
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="action-buttons">
            <el-button type="primary" plain round @click="showUploadFileDialog">
              <el-icon><Upload /></el-icon>上传文件
            </el-button>
            <el-button type="success" plain round @click="showUploadUrlDialog">
              <el-icon><Link /></el-icon>添加URL
            </el-button>
            <el-button 
              type="danger" 
              :disabled="!selectedDatasets.length"
              @click="handleBatchDelete"
              plain
              round
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-card class="list-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
        highlight-current-row
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="name" label="数据集名称" min-width="200" show-overflow-tooltip align="center">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center; cursor: pointer;">
              <el-icon class="file-icon">
                <Link v-if="scope.row.type === 'URL'" />
                <Document v-else />
              </el-icon>
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'URL' ? 'success' : 'primary'">
              {{ scope.row.type === 'URL' ? 'URL' : '文件' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="120" align="center">
          <template #default="scope">
            {{ formatFileSize(scope.row.size) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip align="center" />
        <el-table-column 
          prop="timeCreated" 
          label="创建时间" 
          width="180" 
          align="center"
          sortable
          :formatter="formatDateTime"
        />
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <el-button-group>
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleViewDetail(scope.row)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑" placement="top">
                <el-button
                  type="warning"
                  size="small"
                  @click="handleEdit(scope.row)"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="分享" placement="top">
                <el-button
                  type="success"
                  size="small"
                  @click="handleShare(scope.row)"
                >
                  <el-icon><Share /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(scope.row)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 上传文件对话框 -->
    <el-dialog
      v-model="uploadFileDialogVisible"
      title="上传数据集文件"
      width="500px"
      destroy-on-close
    >
      <el-form :model="uploadFileForm" label-width="100px" ref="uploadFileFormRef">
        <el-form-item label="数据集名称" prop="name" :rules="[{ required: true, message: '请输入数据集名称', trigger: 'blur' }]">
          <el-input v-model="uploadFileForm.name" placeholder="请输入数据集名称" />
        </el-form-item>
        <el-form-item label="数据集描述" prop="description">
          <el-input v-model="uploadFileForm.description" type="textarea" :rows="3" placeholder="请输入数据集描述" />
        </el-form-item>
        <el-form-item label="示例数据" prop="mockData">
          <el-input v-model="uploadFileForm.mockData" type="textarea" :rows="3" placeholder="请输入示例的mock数据" />
        </el-form-item>
        <el-form-item label="可用节点" prop="nodeIds">
          <el-select v-model="uploadFileForm.nodeIds" multiple placeholder="请选择可用节点" style="width: 100%">
            <el-option v-for="node in nodeOptions" :key="node.value" :label="node.label" :value="node.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="上传文件" prop="file" :rules="[{ required: true, message: '请选择文件', trigger: 'change' }]">
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :file-list="uploadFileForm.fileList"
            ref="uploadRef"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                支持任意类型文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-progress 
            v-if="uploading"
            :percentage="uploadProgress" 
            :format="percentageFormat"
            status="success"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadFileDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUploadFile" :loading="uploading">
            上传
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上传URL对话框 -->
    <el-dialog
      v-model="uploadUrlDialogVisible"
      title="添加URL数据集"
      width="500px"
      destroy-on-close
    >
      <el-form :model="uploadUrlForm" label-width="100px" ref="uploadUrlFormRef">
        <el-form-item label="数据集名称" prop="name" :rules="[{ required: true, message: '请输入数据集名称', trigger: 'blur' }]">
          <el-input v-model="uploadUrlForm.name" placeholder="请输入数据集名称" />
        </el-form-item>
        <el-form-item label="URL地址" prop="url" :rules="[{ required: true, message: '请输入URL地址', trigger: 'blur' }]">
          <el-input v-model="uploadUrlForm.url" placeholder="请输入URL地址" />
        </el-form-item>
        <el-form-item label="数据集描述" prop="description">
          <el-input v-model="uploadUrlForm.description" type="textarea" :rows="3" placeholder="请输入数据集描述" />
        </el-form-item>
        <el-form-item label="示例数据" prop="mockData">
          <el-input v-model="uploadUrlForm.mockData" type="textarea" :rows="3" placeholder="请输入示例的mock数据" />
        </el-form-item>
        <el-form-item label="可用节点" prop="nodeIds">
          <el-select v-model="uploadUrlForm.nodeIds" multiple placeholder="请选择可用节点" style="width: 100%">
            <el-option v-for="node in nodeOptions" :key="node.value" :label="node.label" :value="node.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadUrlDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUploadUrl" :loading="uploading">
            添加
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="数据集详情"
      width="700px"
    >
      <div v-if="currentDataset" class="dataset-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="数据集名称">{{ currentDataset.name }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="currentDataset.type === 'URL' ? 'success' : 'primary'">
              {{ currentDataset.type === 'URL' ? 'URL' : '文件' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="大小">{{ formatFileSize(currentDataset.size) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ new Date(currentDataset.timeCreated).toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ new Date(currentDataset.timeUpdated).toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="创建者">{{ currentDataset.creatorName }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ currentDataset.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="URL" v-if="currentDataset.type === 'URL'">
            <el-link type="primary" :href="currentDataset.url" target="_blank">{{ currentDataset.url }}</el-link>
          </el-descriptions-item>
          <el-descriptions-item label="可用节点">
            <div v-if="currentDataset.availableNodes && currentDataset.availableNodes.length">
              <el-tag 
                v-for="node in currentDataset.availableNodes" 
                :key="node.id"
                class="node-tag"
              >
                {{ node.ipAddress }} : {{ node.port }} 
              </el-tag>
            </div>
            <span v-else>没有节点可用</span>
          </el-descriptions-item>
          <el-descriptions-item label="示例数据" v-if="currentDataset.mockData">
            <pre class="mock-data-pre">{{ currentDataset.mockData }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 分享对话框 -->
    <el-dialog
      v-model="shareDialogVisible"
      title="分享数据集"
      width="500px"
    >
      <div v-if="shareInfo" class="share-info">
        <p><strong>数据集名称：</strong>{{ shareInfo.name }}</p>
        <p v-if="shareInfo.expiry">
          <strong>有效期：</strong>{{ formatExpiry(shareInfo.expiry) }}
        </p>
        <el-input
          v-model="shareInfo.url"
          readonly
          class="share-url-input"
        >
          <template #append>
            <el-button @click="copyShareUrl">复制</el-button>
          </template>
        </el-input>
      </div>
    </el-dialog>

    <!-- 编辑数据集对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑数据集"
      width="500px"
      destroy-on-close
    >
      <el-form :model="editForm" label-width="100px" ref="editFormRef">
        <el-form-item label="数据集名称" prop="name" :rules="[{ required: true, message: '请输入数据集名称', trigger: 'blur' }]">
          <el-input v-model="editForm.name" placeholder="请输入数据集名称" />
        </el-form-item>
        <el-form-item label="数据集描述" prop="description">
          <el-input v-model="editForm.description" type="textarea" :rows="3" placeholder="请输入数据集描述" />
        </el-form-item>
        <el-form-item label="示例数据" prop="mockData">
          <el-input v-model="editForm.mockData" type="textarea" :rows="3" placeholder="请输入示例的mock数据" />
        </el-form-item>
        <el-form-item label="可用节点" prop="nodeIds">
          <el-select v-model="editForm.nodeIds" multiple placeholder="请选择可用节点" style="width: 100%">
            <el-option v-for="node in nodeOptions" :key="node.value" :label="node.label" :value="node.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="URL地址" prop="url" v-if="editForm.type === 'URL'">
          <el-input v-model="editForm.url" placeholder="请输入URL地址" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit" :loading="updating">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Search, Folder, FolderAdd, Upload, Download, Delete, Share, Refresh, Document, Link, View, Edit } from '@element-plus/icons-vue';
import service from '~/axios';
import { toast, showModal } from '~/composables/util';

const router = useRouter();
const route = useRoute();

// 数据加载状态
const loading = ref(false);
const uploading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 表格数据
const tableData = ref([]);
const selectedDatasets = ref([]);

// 搜索表单
const searchForm = reactive({
  name: '',
});

// 上传文件表单
const uploadFileDialogVisible = ref(false);
const uploadFileFormRef = ref(null);
const uploadFileForm = reactive({
  name: '',
  description: '',
  mockData: '',
  nodeIds: [],
  file: null,
  fileList: [],
});

// 上传URL表单
const uploadUrlDialogVisible = ref(false);
const uploadUrlFormRef = ref(null);
const uploadUrlForm = reactive({
  name: '',
  url: '',
  description: '',
  mockData: '',
  nodeIds: [],
});

// 节点选项
const nodeOptions = ref([]);

// 详情对话框
const detailDialogVisible = ref(false);
const currentDataset = ref(null);

// 分享对话框
const shareDialogVisible = ref(false);
const shareInfo = ref(null);

// 编辑数据集对话框
const editDialogVisible = ref(false);
const editFormRef = ref(null);
const editForm = reactive({
  id: null,
  name: '',
  description: '',
  mockData: '',
  nodeIds: [],
  type: '',
  url: ''
});
const updating = ref(false);

// 添加上传相关的引用
const uploadRef = ref(null);
const uploadProgress = ref(0);

// 格式化进度百分比
const percentageFormat = (percentage) => {
  return percentage === 100 ? '上传完成' : `${percentage}%`;
};

// 处理上传进度
const handleUploadProgress = (event, file) => {
  uploadProgress.value = Math.floor(event.percent);
};

// 修改上传文件的方法
const submitUploadFile = async () => {
  if (!uploadFileForm.file) {
    toast('警告', '请选择要上传的文件', 'warning');
    return;
  }
  
  uploading.value = true;
  uploadProgress.value = 0; // 重置进度
  
  try {
    const formData = new FormData();
    formData.append('file', uploadFileForm.file);
    formData.append('name', uploadFileForm.name);
    formData.append('description', uploadFileForm.description || '');
    formData.append('mockData', uploadFileForm.mockData || '');
    
    if (uploadFileForm.nodeIds && uploadFileForm.nodeIds.length > 0) {
      uploadFileForm.nodeIds.forEach(nodeId => {
        formData.append('nodeIds', nodeId);
      });
    }
    
    // 添加调试信息
    console.log('开始上传文件，初始进度：', uploadProgress.value);
    
    const res = await service.post('/api/v1.0/sys/dataset/upload/file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 600000, // 设置超时时间为10分钟
      onUploadProgress: progressEvent => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        uploadProgress.value = percentCompleted;
        console.log('上传进度更新：', percentCompleted);
      }
    });
    
    if (res.code === 10000) {
      uploadProgress.value = 100;
      console.log('上传完成，进度：', uploadProgress.value);
      setTimeout(() => {
        toast('成功', '文件上传成功', 'success');
        uploadFileDialogVisible.value = false;
        loadDatasets();
        uploadProgress.value = 0; // 重置进度
      }, 500);
    } else {
      toast('错误', res.message || '文件上传失败', 'error');
    }
  } catch (error) {
    console.error('文件上传失败:', error);
    toast('错误', error.message || '文件上传失败', 'error');
  } finally {
    uploading.value = false;
  }
};

// 显示上传文件对话框
const showUploadFileDialog = () => {
  uploadFileForm.name = '';
  uploadFileForm.description = '';
  uploadFileForm.mockData = '';
  uploadFileForm.nodeIds = [];
  uploadFileForm.file = null;
  uploadFileForm.fileList = [];
  uploadProgress.value = 0; // 重置进度
  uploadFileDialogVisible.value = true;
};

// 显示上传URL对话框
const showUploadUrlDialog = () => {
  uploadUrlForm.name = '';
  uploadUrlForm.url = '';
  uploadUrlForm.description = '';
  uploadUrlForm.mockData = '';
  uploadUrlForm.nodeIds = [];
  uploadUrlDialogVisible.value = true;
};

// 上传URL类型的数据集
const submitUploadUrl = async () => {
  uploading.value = true;
  try {
    const res = await service.post('/api/v1.0/sys/dataset/upload/url', uploadUrlForm, {
      timeout: 60000 // 设置超时时间为1分钟
    });
    
    if (res.code === 10000) {
      toast('成功', 'URL添加成功', 'success');
      uploadUrlDialogVisible.value = false;
      loadDatasets();
    } else {
      toast('错误', res.message || 'URL添加失败', 'error');
    }
  } catch (error) {
    console.error('URL添加失败:', error);
    toast('错误', error.message || 'URL添加失败', 'error');
  } finally {
    uploading.value = false;
  }
};

// 查看数据集详情
const handleViewDetail = async (row) => {
  try {
    const res = await service.get(`/api/v1.0/sys/dataset/${row.id}`);
    
    if (res.code === 10000) {
      currentDataset.value = res.data;
      detailDialogVisible.value = true;
    } else {
      toast('错误', res.message || '获取数据集详情失败', 'error');
    }
  } catch (error) {
    console.error('获取数据集详情失败:', error);
    toast('错误', '获取数据集详情失败', 'error');
  }
};

// 编辑数据集
const handleEdit = (row) => {
  editForm.id = row.id;
  editForm.name = row.name;
  editForm.description = row.description || '';
  editForm.mockData = row.mockData || '';
  editForm.type = row.type;
  editForm.url = row.url || '';
  
  // 获取数据集详情以获取完整信息
  service.get(`/api/v1.0/sys/dataset/${row.id}`).then(res => {
    if (res.code === 10000) {
      const dataset = res.data;
      // 设置可用节点
      editForm.nodeIds = dataset.availableNodes?.map(node => node.id) || [];
      editDialogVisible.value = true;
    } else {
      toast('错误', res.message || '获取数据集详情失败', 'error');
    }
  }).catch(error => {
    console.error('获取数据集详情失败:', error);
    toast('错误', '获取数据集详情失败', 'error');
  });
};

// 分享数据集
const handleShare = async (row) => {
  try {
    const res = await service.get(`/api/v1.0/sys/dataset/${row.id}/share`);
    
    if (res.code === 10000) {
      shareInfo.value = {
        name: row.name,
        type: row.type,
        ...res.data
      };
      shareDialogVisible.value = true;
    } else {
      toast('错误', res.message || '获取分享链接失败', 'error');
    }
  } catch (error) {
    console.error('获取分享链接失败:', error);
    toast('错误', '获取分享链接失败', 'error');
  }
};

// 复制分享链接
const copyShareUrl = async () => {
  try {
    if (!shareInfo.value || !shareInfo.value.url) {
      toast('错误', '分享链接不可用', 'error');
      return;
    }
    await navigator.clipboard.writeText(shareInfo.value.url);
    toast('成功', '链接已复制到剪贴板', 'success');
  } catch (error) {
    toast('错误', '复制失败', 'error');
  }
};

// 删除数据集
const handleDelete = async (row) => {
  try {
    await showModal('确定要删除该数据集吗？', 'warning', '提示');
    const res = await service.delete(`/api/v1.0/sys/dataset/${row.id}`);
    
    if (res.code === 10000) {
      toast('成功', '删除成功');
      loadDatasets();
    } else {
      toast('错误', res.message || '删除失败', 'error');
    }
  } catch (error) {
    if (error !== 'cancel') {
      toast('错误', '删除失败', 'error');
    }
  }
};

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedDatasets.value = selection;
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadDatasets();
};

// 重置搜索
const resetSearch = () => {
  searchForm.name = '';
  currentPage.value = 1;
  loadDatasets();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadDatasets();
};

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadDatasets();
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedDatasets.value.length === 0) {
    toast('警告', '请选择要删除的数据集', 'warning');
    return;
  }
  
  try {
    await showModal(`确定要删除选中的 ${selectedDatasets.value.length} 个数据集吗？`, 'warning', '提示');
    const ids = selectedDatasets.value.map(item => item.id);
    
    const idsParam = ids.join(',');
    const res = await service.delete(`/api/v1.0/sys/dataset/batch?ids=${idsParam}`);
    
    if (res.code === 10000) {
      toast('成功', '批量删除成功');
      loadDatasets();
    } else {
      toast('错误', res.message || '批量删除数据集失败', 'error');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除数据集失败:', error);
      toast('错误', '批量删除数据集失败', 'error');
    }
  }
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let index = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(2)} ${units[index]}`;
};

// 格式化日期时间
const formatDateTime = (row, column) => {
  if (!row || !row.timeCreated) return '';
  const date = new Date(row.timeCreated);
  return date.toLocaleString();
};

// 格式化过期时间
const formatExpiry = (seconds) => {
  if (!seconds) return '永久有效';
  
  const days = Math.floor(seconds / (24 * 60 * 60));
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((seconds % (60 * 60)) / 60);
  
  if (days > 0) {
    return `${days}天${hours}小时${minutes}分钟`;
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
};

// 提交编辑
const submitEdit = async () => {
  if (!editForm.name) {
    toast('警告', '请输入数据集名称', 'warning');
    return;
  }
  
  updating.value = true;
  try {
    const updateData = {
      name: editForm.name,
      description: editForm.description,
      mockData: editForm.mockData,
      nodeIds: editForm.nodeIds
    };
    
    // 如果是URL类型，添加URL字段
    if (editForm.type === 'URL') {
      updateData.url = editForm.url;
    }
    
    const res = await service.put(`/api/v1.0/sys/dataset/${editForm.id}`, updateData);
    
    if (res.code === 10000) {
      toast('成功', '数据集更新成功', 'success');
      editDialogVisible.value = false;
      loadDatasets();
    } else {
      toast('错误', res.message || '数据集更新失败', 'error');
    }
  } catch (error) {
    console.error('数据集更新失败:', error);
    toast('错误', '数据集更新失败', 'error');
  } finally {
    updating.value = false;
  }
};

// 加载数据集列表
const loadDatasets = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
    };
    
    if (searchForm.name) {
      params.name = searchForm.name;
    }
    
    const res = await service.get('/api/v1.0/sys/dataset/my', { params });
    if (res.code === 10000) {
      tableData.value = res.data.datasets || [];
      total.value = res.data.pagination?.total || 0;
    } else {
      toast('错误', res.message || '获取数据集列表失败', 'error');
    }
  } catch (error) {
    console.error('获取数据集列表失败:', error);
    toast('错误', '获取数据集列表失败', 'error');
  } finally {
    loading.value = false;
  }
};

// 加载节点列表
const loadNodes = async () => {
  try {
    const res = await service.get('/api/v1.0/sys/nodes');
    if (res.code === 10000) {
      nodeOptions.value = res.data.nodes.map(node => ({
        value: node.id,
        label: `${node.ipAddress}:${node.port} - ${node.name}`
      }));
    } else {
      toast('错误', res.message || '获取节点列表失败', 'error');
    }
  } catch (error) {
    console.error('获取节点列表失败:', error);
    toast('错误', '获取节点列表失败', 'error');
  }
};

// 处理文件上传
const handleFileChange = (file) => {
  uploadFileForm.file = file.raw;
  return false; // 阻止自动上传
};

// 页面加载时执行
onMounted(() => {
  loadDatasets();
  loadNodes();
});
</script>

<style scoped>
.data-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .title-icon {
      margin-right: 8px;
      font-size: 24px;
      color: var(--el-color-primary);
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .sub-title {
    color: #909399;
    font-size: 14px;
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  align-items: center;
  margin: 0;
}

.search-input {
  width: 240px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 0;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button.is-round) {
  height: 36px;
  padding: 0 20px;
}

.list-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  justify-content: center !important;
}

.file-icon {
  margin-right: 5px;
  font-size: 18px;
}

.mock-data-pre {
  white-space: pre-wrap;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.node-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.share-url-input {
  margin: 15px 0;
}

.dataset-detail {
  max-height: 60vh;
  overflow-y: auto;
}

/* 深色模式适配 */
html.dark {
  .list-card {
    background-color: var(--el-bg-color-overlay);
  }
  
  .mock-data-pre {
    background-color: var(--el-bg-color);
  }
}

/* 确保进度条可见 */
:deep(.el-progress) {
  margin-top: 10px;
  margin-bottom: 10px;
}

:deep(.el-progress-bar) {
  min-width: 100px;
}

:deep(.el-progress-bar__outer) {
  background-color: #ebeef5;
  border-radius: 100px;
  overflow: hidden;
  position: relative;
  vertical-align: middle;
}
</style> 